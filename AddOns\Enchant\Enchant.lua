local EnchantRandomButtonTime = 0.1
local EnchantRandomBool = false
local EnchantRandomReq = {}
local EnchantRandomReqButton = {}
local EnchantRandomBackdropStr = {}
local EnchantRandomItemEnchantLockButton = {}
local EnchantRandomItemEnchantLockBlpButton = {}
local ItemEnchantItemEnchantButtonStatStr = {}
local EnchantRandomRollButton,EnchantRandomRollButtonStr

local EnchantRandomBackdrop = CreateFrame("Frame", "EnchantRandomBackdrop", UIParent)
EnchantRandomBackdrop:SetSize(300, 600)
EnchantRandomBackdrop:SetPoint("CENTER", 0, 0)
-- EnchantRandomBackdrop:SetBackdrop({bgFile = "Interface\\AchievementFrame\\UI-Achievement-AchievementBackground"})
EnchantRandomBackdrop:SetBackdrop({bgFile = "Interface\\Refine\\ER_BACKDROP1"})

local EnchantRandom = CreateFrame("Button", "EnchantRandom", EnchantRandomBackdrop)
EnchantRandom:SetSize(50, 50)
EnchantRandom:SetPoint("TOP", -82, -36)
EnchantRandom:EnableMouse(true)
EnchantRandom:RegisterForDrag("LeftButton");
EnchantRandom:Enable()
local EnchantRandomTex = EnchantRandom:CreateTexture("$parent".."EnchantRandomTex","ARTWORK")
-- EnchantRandomTex:SetTexture("Interface\\Buttons\\UI-EmptySlot")
EnchantRandomTex:SetSize(50, 50)
EnchantRandomTex:SetPoint("CENTER",0,0)
-- EnchantRandomTex:SetTexCoord(0.140625, 0.84375, 0.140625, 0.84375)
EnchantRandom.Click = false

local EnchantRandomCloseButton = CreateFrame("Button", "EnchantRandomCloseButton", EnchantRandomBackdrop)
EnchantRandomCloseButton:SetSize(30, 30)
EnchantRandomCloseButton:SetPoint("TOPRIGHT", 0, 0)
EnchantRandomCloseButton:EnableMouse(true)
EnchantRandomCloseButton:RegisterForDrag("LeftButton");
EnchantRandomCloseButton:Enable()
EnchantRandomCloseButton:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
EnchantRandomCloseButton:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
EnchantRandomCloseButton:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
EnchantRandomCloseButton:SetScript("OnClick",function(self) EnchantRandomBackdrop:Hide() EnchantRandomOffButton:Hide() EnchantRandomOnButton:Show()end)

local EnchantRandomOnButton = CreateFrame("Button", "EnchantRandomOnButton", UIParent)
EnchantRandomOnButton:SetSize(64, 64)
EnchantRandomOnButton:SetPoint("BOTTOMRIGHT", -380, 100)	
EnchantRandomOnButton:EnableMouse(true)
EnchantRandomOnButton:Enable()
EnchantRandomOnButton:SetNormalTexture("Interface\\Refine\\Show")

local EnchantRandomOffButton = CreateFrame("Button", "EnchantRandomOffButton", UIParent)
EnchantRandomOffButton:SetSize(64, 64)
EnchantRandomOffButton:SetPoint("BOTTOMRIGHT", -380, 100)
EnchantRandomOffButton:EnableMouse(true)
EnchantRandomOffButton:Enable()
EnchantRandomOffButton:SetNormalTexture("Interface\\Refine\\Hide")

EnchantRandomOnButton:SetScript("OnClick",function(self) EnchantRandomBackdrop:Show() EnchantRandomOffButton:Show() self:Hide() end)
EnchantRandomOffButton:SetScript("OnClick",function(self) EnchantRandomBackdrop:Hide() EnchantRandomOnButton:Show() self:Hide() end)
EnchantRandomOffButton:Hide()


function EnchantRandomCreateRollButton()
EnchantRandomRollButton = CreateFrame("Button", "EnchantRandomRollButton", EnchantRandomBackdrop)
EnchantRandomRollButton:SetSize(60, 60)
EnchantRandomRollButton:SetPoint("CENTER", 0, -260)
EnchantRandomRollButton:EnableMouse(true)
local RollButtonNtex = EnchantRandomRollButton:CreateTexture("$parent".."Ntex","ARTWORK",nil)
local RollButtonPtex = EnchantRandomRollButton:CreateTexture("$parent".."Ptex","ARTWORK",nil)
-- local RollButtonDtex = EnchantRandomRollButton:CreateTexture("$parent".."Dtex","ARTWORK",nil)
-- local RollButtonHtex = EnchantRandomRollButton:CreateTexture("$parent".."Htex","ARTWORK",nil)
RollButtonNtex:SetTexture("Interface\\Refine\\ER_refine2")
RollButtonPtex:SetTexture("Interface\\Refine\\ER_refine1")
-- RollButtonNtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Up")
-- RollButtonPtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Down")
-- RollButtonDtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Disabled")
-- RollButtonHtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Highlight")
-- RollButtonNtex:SetTexCoord(0,0.578125,0,0.75)
-- RollButtonPtex:SetTexCoord(0,0.578125,0,0.75)
-- RollButtonDtex:SetTexCoord(0,0.578125,0,0.75)
-- RollButtonHtex:SetTexCoord(0,0.625,0,0.685)
RollButtonNtex:SetAllPoints(EnchantRandomRollButton)
RollButtonPtex:SetAllPoints(EnchantRandomRollButton)
-- RollButtonDtex:SetAllPoints(EnchantRandomRollButton)
-- RollButtonHtex:SetAllPoints(EnchantRandomRollButton)
EnchantRandomRollButton:SetNormalTexture(RollButtonNtex)
EnchantRandomRollButton:SetPushedTexture(RollButtonPtex)
-- EnchantRandomRollButton:SetDisabledTexture(RollButtonDtex)
-- EnchantRandomRollButton:SetHighlightTexture(RollButtonHtex)
-- EnchantRandomRollButtonStr  = EnchantRandomRollButton:CreateFontString("EnchantRandomRollButtonStr")
-- EnchantRandomRollButtonStr:SetFont("Fonts\\ZYHei.ttf", 10)	--需要设置字体
-- EnchantRandomRollButtonStr:SetPoint("CENTER",-1,2)
-- EnchantRandomRollButtonStr:SetText("洗炼")
EnchantRandomRollButton:Hide()
end

function EnchantRandomSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

function EnchantRandomInitCreateEnchantText(Frame,Name,val,text)
	EnchantRandomBackdropStr[val] = Frame:CreateFontString("EnchantRandomBackdropStr"..val,"OVERLAY")
	-- EnchantRandomBackdropStr[val]:SetFontObject(GameFontNormalLarge)
	-- EnchantRandomBackdropStr[val]:SetFont("Fonts\\ZYHei.ttf", 13)	
	EnchantRandomBackdropStr[val]:SetFont("Fonts\\ZYHei.ttf", 13)	
	EnchantRandomBackdropStr[val]:SetSize(200,40) 			--这个设定文本框大小
	EnchantRandomBackdropStr[val]:SetJustifyH("CENTER")		--这个设置水平对齐
	EnchantRandomBackdropStr[val]:SetJustifyV("TOP")		--这个设置垂直对齐
	EnchantRandomBackdropStr[val]:SetPoint("TOP",-50,-138 - (val - 1)*70)
	_G["EnchantRandomBackdropStr"..val]:SetText(text)
end

function EnchantRandomCreateLockButton(val)
	EnchantRandomItemEnchantLockButton[val] = CreateFrame("Button", "EnchantRandomItemEnchantLockButton"..val, EnchantRandomBackdrop)
	EnchantRandomItemEnchantLockButton[val]:SetSize(80, 30)
	EnchantRandomItemEnchantLockButton[val]:SetPoint("TOP", 100, -168- (val - 1)*70)
	EnchantRandomItemEnchantLockButton[val]:EnableMouse(true)
	local Ntex = EnchantRandomItemEnchantLockButton[val]:CreateTexture("$parent".."Ntex","ARTWORK",nil)
	local Ptex = EnchantRandomItemEnchantLockButton[val]:CreateTexture("$parent".."Ptex","ARTWORK",nil)
	local Dtex = EnchantRandomItemEnchantLockButton[val]:CreateTexture("$parent".."Dtex","ARTWORK",nil)
	local Htex = EnchantRandomItemEnchantLockButton[val]:CreateTexture("$parent".."Htex","ARTWORK",nil)
	Ntex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Up")
	Ptex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Down")
	Dtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Disabled")
	Htex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Highlight")
	Ntex:SetTexCoord(0,0.578125,0,0.75)
	Ptex:SetTexCoord(0,0.578125,0,0.75)
	Dtex:SetTexCoord(0,0.578125,0,0.75)
	Htex:SetTexCoord(0,0.625,0,0.685)
	Ntex:SetAllPoints(EnchantRandomItemEnchantLockButton[val])
	Ptex:SetAllPoints(EnchantRandomItemEnchantLockButton[val])
	Dtex:SetAllPoints(EnchantRandomItemEnchantLockButton[val])
	Htex:SetAllPoints(EnchantRandomItemEnchantLockButton[val])
	EnchantRandomItemEnchantLockButton[val]:SetNormalTexture(Ntex)
	EnchantRandomItemEnchantLockButton[val]:SetPushedTexture(Ptex)
	EnchantRandomItemEnchantLockButton[val]:SetDisabledTexture(Dtex)
	EnchantRandomItemEnchantLockButton[val]:SetHighlightTexture(Htex)
	ItemEnchantItemEnchantButtonStatStr[val]  = EnchantRandomItemEnchantLockButton[val]:CreateFontString("$parent".."Str")
	ItemEnchantItemEnchantButtonStatStr[val]:SetFont("Fonts\\ZYHei.ttf", 10)	
	ItemEnchantItemEnchantButtonStatStr[val]:SetPoint("CENTER",-1,2)
	ItemEnchantItemEnchantButtonStatStr[val]:SetText("点击锁定")
	EnchantRandomItemEnchantLockButton[val].Lock = 0
	_G["EnchantRandomItemEnchantLockButton"..val]:Hide()
end

function EnchantRandomCreateLockBlpButton()
	for i = 1, 5 do
		EnchantRandomItemEnchantLockBlpButton[i] = CreateFrame("Button", "EnchantRandomItemEnchantLockBlpButton"..i, EnchantRandomBackdrop)
		EnchantRandomItemEnchantLockBlpButton[i]:SetSize(50, 50)
		EnchantRandomItemEnchantLockBlpButton[i]:SetPoint("TOP", 100, -122- (i - 1)*70)
		local EnchantRandomLockBlpButtonTex = EnchantRandomItemEnchantLockBlpButton[i]:CreateTexture("$parent".."Tex","ARTWORK")
		EnchantRandomLockBlpButtonTex:SetTexture("Interface\\Refine\\ER_Unlock")
		EnchantRandomLockBlpButtonTex:SetSize(50, 50)
		EnchantRandomLockBlpButtonTex:SetPoint("CENTER",0,0)
		EnchantRandomItemEnchantLockBlpButton[i].tex = EnchantRandomLockBlpButtonTex
		_G["EnchantRandomItemEnchantLockBlpButton"..i]:Hide()
	end
end

function EnchantRandomReqButton_OnEnter(self, button)
		GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
		GameTooltip:SetHyperlink(EnchantRandomReq[self.Id].itemlink)
end

function EnchantRandomReqButton_OnLeave(self, button)
	GameTooltip_Hide()
end

function EnchantRandomReqButtonSetScript()
	for i = 1, 5 do
		if EnchantRandomReqButton[i] ~= nil then
			EnchantRandomReqButton[i]:SetScript("OnEnter", EnchantRandomReqButton_OnEnter)
			EnchantRandomReqButton[i]:SetScript("OnLeave", EnchantRandomReqButton_OnLeave)
		end
	end
end

function EnchantRandomReqButtonHide()
	for i = 1, 5 do
		if EnchantRandomReqButton[i] ~= nil then
			EnchantRandomReqButton[i]:Hide()
		end
	end
end

function EnchantRandomCreateReqButton(Val)
	for i = 1, 5 do
		if EnchantRandomReq[i] ~= nil then
			if EnchantRandomReqButton[i] ~= nil then
				setglobal(EnchantRandomReqButton[i]:GetName(),nil)
				EnchantRandomReqButton[i] = nil
			end
			EnchantRandomReqButton[i] = CreateFrame("Button", "EnchantRandomReqButton"..i, EnchantRandomBackdrop)
			EnchantRandomReqButton[i]:SetSize(30, 30)
			EnchantRandomReqButton[i]:SetPoint("LEFT", 30 +((i-1)*52), -205)
			EnchantRandomReqButton[i]:EnableMouse(true)
			EnchantRandomReqButton[i]:Enable()
			local EnchantRandomReqButtonTex = EnchantRandomReqButton[i]:CreateTexture("$parent".."EnchantRandomTex","ARTWORK")
			EnchantRandomReqButtonTex:SetTexture(EnchantRandomReq[i].blp)
			EnchantRandomReqButtonTex:SetSize(30, 30)
			EnchantRandomReqButtonTex:SetPoint("CENTER",0,0)
			local EnchantRandomReqButtonstr = EnchantRandomReqButton[i]:CreateFontString("$parent".."Str")
			EnchantRandomReqButtonstr:SetFont("Fonts\\ZYHei.ttf", 11)	
			EnchantRandomReqButtonstr:SetPoint("CENTER",20,-10)
			EnchantRandomReqButtonstr:SetText("x"..EnchantRandomReq[i].itemcount)
			EnchantRandomReqButtonstr:SetTextColor(255,0,0)
			EnchantRandomReqButton[i].Id = i;
			if i <= Val then
				EnchantRandomReqButton[i]:Show()
			else
				EnchantRandomReqButton[i]:Hide()
			end
		end
	end
	
	EnchantRandomReqButtonSetScript()
end

for i = 1,5 do
EnchantRandomInitCreateEnchantText(EnchantRandomBackdrop,"",i,"")
EnchantRandomCreateLockButton(i)
end
EnchantRandomCreateLockBlpButton()

function EnchantRandomHandleChangeText(val,text)
	EnchantRandomBackdropStr[val]:SetText(text)
end

function EnchantRandomLockBlpButtonShow()
	for i = 1,5 do
		_G["EnchantRandomItemEnchantLockBlpButton"..i]:Show()
	end
end 

function EnchantRandomLockBlpButtonHide()
	for i = 1,5 do
		_G["EnchantRandomItemEnchantLockBlpButton"..i]:Hide()
	end
end

function EnchantRandomLockButtonShow()
	for i = 1,5 do
		_G["EnchantRandomItemEnchantLockButton"..i]:Show()
	end
end

function EnchantRandomLockButtonHide()
	for i = 1,5 do
		_G["EnchantRandomItemEnchantLockButton"..i]:Hide()
	end
end

function EnchantRandomLockButton_OnClick(self,button)
	local name = self:GetName()
	-- print("name = "..name)
	local str = _G[name.."Str"]:GetText()
	if str == "点击锁定" then
	_G[name.."Str"]:SetText("点击取消锁定")
	self.Lock = 1
	-- EnchantRandomCountLockHandleButtonShow()
	else
	_G[name.."Str"]:SetText("点击锁定")
	self.Lock = 0
	-- EnchantRandomCountLockHandleButtonShow()
	end
	EnchantRandomHandleLockBlpButton()
	EnchantRandomCountLockHandleButtonShow()
end

function EnchantRandomCountLockHandleButtonShow()
	local val = 1;
	for i = 1, 5 do
		if EnchantRandomItemEnchantLockButton[i].Lock == 1 then
			val = val + 1
		end
	end
	local count = 0;
	for j = 1, 5 do
		if j <= val then
		EnchantRandomReqButton[j]:Show()	
		else
		EnchantRandomReqButton[j]:Hide()
		end 
	end
	-- EnchantRandomCreateReqButton(val)	--这个不好用
end

function EnchantRandomHandleLockBlpButton()
 for i = 1,5 do
	 if EnchantRandomItemEnchantLockButton[i].Lock == 1 then
		local name = EnchantRandomItemEnchantLockBlpButton[i]:GetName()
		_G[name.."Tex"]:SetTexture("Interface\\Refine\\ER_Lock")
	 else
		local name = EnchantRandomItemEnchantLockBlpButton[i]:GetName()
		_G[name.."Tex"]:SetTexture("Interface\\Refine\\ER_Unlock")
	 end
 end
end

function EnchantRandomLockButtonSetScript()
	for i = 1,5 do
		_G["EnchantRandomItemEnchantLockButton"..i]:SetScript("OnClick", EnchantRandomLockButton_OnClick)
	end
end

function EnchantRandomHandleResetText()
	for i = 1,5 do
		EnchantRandomBackdropStr[i]:SetText("")
	end
end

function EnchantRandomLockButtonResetText()
	for i = 1,5 do
		local name = _G["EnchantRandomItemEnchantLockButton"..i]:GetName()
		_G[name.."Str"]:SetText("点击锁定")
		_G["EnchantRandomItemEnchantLockButton"..i].Lock = 0
	end
end

EnchantRandomCreateRollButton()
-- TexCoords left="0.140625" right="0.84375" top="0.140625" bottom="0.84375"
function EnchantRandomItemButton_OnClick(self, button)
	ClickAuctionSellItemButton(self, button);
	
	if EnchantRandom.Click then
		EnchantRandomTex:SetTexture("")
		EnchantRandomTex:SetTexCoord(0.140625, 0.84375, 0.140625, 0.84375)
		EnchantRandom.Click = false
		EnchantRandomHandleResetText()
		EnchantRandomReqButtonHide()
		EnchantRandomLockBlpButtonHide()
		EnchantRandomLockButtonResetText()
		EnchantRandomLockButtonHide()
		EnchantRandomRollButton:Hide()
		EnchantRandom:HIDE()
		EnchantRandom:SHOW()
	end
	
	if not EnchantRandom.Click then
		if EnchantRandom.itemID then
			if not EnchantRandomBool then
				return
			end
			if EnchantRandom.ItemLink then
				local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(EnchantRandom.ItemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")
				if tonumber(uniqueId) < 100000000 then
				return
				end
				SendAddonMessage("ENCHANTRANDOM_QUERY_DATA", uniqueId, "GUILD")
				EnchantRandomTex:SetTexture(GetItemIcon(EnchantRandom.itemID))
				EnchantRandomTex:SetTexCoord(0, 1, 0, 1)
				EnchantRandomCreateReqButton(1)
				EnchantRandom.Click = true
				EnchantRandomRollButton:Show()
				EnchantRandomLockButtonShow()
				EnchantRandomLockBlpButtonShow()
				EnchantRandom:HIDE()
				EnchantRandom:SHOW()
			end			
		end 
	end

end

function EnchantRandomItemButton_OnEnter(self, button)
	
	local infoType, itemID, itemLink = GetCursorInfo()
	
	if infoType == "item" then
		-- print("uniqueId = "..uniqueId)
		EnchantRandom.ItemLink = itemLink
		EnchantRandom.itemID = itemID
	end
	
	if EnchantRandom.ItemLink then
		GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
		GameTooltip:SetHyperlink(EnchantRandom.ItemLink)
	end
	
end

function EnchantRandomItemButton_OnLeave(self, button)
	
	GameTooltip_Hide()
	
	if not EnchantRandom.Click then

		EnchantRandom.itemID = 0
		EnchantRandom.ItemLink = nil;
	end
end

function EnchantRandomOnDataRecv(self, event, opcode, msg, type, sender)
	
	if event == "CHAT_MSG_ADDON" then
		if opcode == "ITEN_ENCHANTRANDOM_DATA" then
		local slot,description = strsplit(" ", msg)
		-- print("msg = "..msg)
		local val = tonumber(slot)
			if val >=7 then
				EnchantRandomHandleChangeText(val-6,description)
			end
		end
		
		if opcode == "ITEN_ENCHANTRANDOM_REQDATA" then
			-- print("msg = "..msg)
			local val,itemid,blp,itemcount,itemlink = strsplit(" ", msg)
			EnchantRandomReq[tonumber(val)] = nil
			EnchantRandomReq[tonumber(val)] = {}
			EnchantRandomReq[tonumber(val)].itemid = itemid
			EnchantRandomReq[tonumber(val)].blp = blp
			EnchantRandomReq[tonumber(val)].itemcount = itemcount
			EnchantRandomReq[tonumber(val)].itemlink = itemlink
			-- print("EnchantRandomReq["..val.."].itemid = "..EnchantRandomReq[tonumber(val)].itemid)
			-- print("EnchantRandomReq["..val.."].blp = "..EnchantRandomReq[tonumber(val)].blp)
			-- print("EnchantRandomReq["..val.."].itemcount = "..EnchantRandomReq[tonumber(val)].itemcount)
			-- print("EnchantRandomReq["..val.."].itemlink = "..EnchantRandomReq[tonumber(val)].itemlink)
		end
	end
	
end

function EnchantRandomRollButton_OnClick(self, button)
	if EnchantRandom.ItemLink then
		local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(EnchantRandom.ItemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")
		local val = {}
		local msg = ""
		for i = 1, 5 do
			val[i] = _G["EnchantRandomItemEnchantLockButton"..i].Lock 
			-- print("val["..i.."] ="..val[i])
			msg = msg..tostring(val[i]).."^"
		end
		msg = msg..uniqueId
		-- print("ENCHANTRANDOM_RANDOM_ENCHANT is msg = "..msg)
		SendAddonMessage("ENCHANTRANDOM_RANDOM_ENCHANT", msg, "GUILD")
	end
end
EnchantRandomLockButtonSetScript()
EnchantRandomRollButton:SetScript("OnClick", EnchantRandomRollButton_OnClick)
EnchantRandom:SetScript("OnClick", EnchantRandomItemButton_OnClick)
EnchantRandom:SetScript("OnEnter", EnchantRandomItemButton_OnEnter)
EnchantRandom:SetScript("OnLeave", EnchantRandomItemButton_OnLeave)
EnchantRandom:SetScript("OnUpdate",function(self,var) 
if EnchantRandomButtonTime <= 0 then 
	if CursorHasItem() then 
		EnchantRandomBool = true
		else
		EnchantRandomBool = false
	end 
	EnchantRandomButtonTime = 0.1
else
EnchantRandomButtonTime = EnchantRandomButtonTime - var 
end 
end)

local ER_RECV_FRAME = CreateFrame("Frame")
ER_RECV_FRAME:RegisterEvent("CHAT_MSG_ADDON")
ER_RECV_FRAME:SetScript("OnEvent", EnchantRandomOnDataRecv)

local ER_RELOAD_FRAME = CreateFrame("Frame")
ER_RELOAD_FRAME:RegisterEvent("PLAYER_LOGIN")
ER_RELOAD_FRAME:SetScript("OnEvent", function(self, event, ...)  SendAddonMessage("ENCHANTRANDOM_REQDATA", "", "GUILD") end)

EnchantRandomBackdrop:Hide()

function EnchantRandomMenuONOFF()
	if EnchantRandomOnButton:IsShown() then
		EnchantRandomOnButton:Hide()
	else
		if EnchantRandomOffButton:IsShown() then
			EnchantRandomOffButton:Click()
			EnchantRandomOnButton:Hide()
		else
			EnchantRandomOnButton:Show()
		end
	end
	if EnchantRandomBackdrop:IsShown() then
		EnchantRandomBackdrop:Hide()
	end
end