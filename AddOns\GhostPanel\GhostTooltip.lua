function Ghost_ReqTooltipText(tooltip,leftText,rightText, reqId,isrew,isgem)

    tooltip:AddDoubleLine("|cffcccccc┌|r", "|cffcccccc┐|r");
	tooltip.count = tooltip.count + 1;
    if leftText and rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r"..leftText, "|cFF00FF00"..rightText.."|r|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    elseif not leftText and rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r", "|cFF00FF00"..rightText.."|r|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    elseif leftText and not rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r"..leftText, "|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    end
	
	if isrew then
		tooltip:AddDoubleLine("|cffcccccc│|r".."|cFF33FFFF装备可售卖给商店获取材料|r", "|cffcccccc│|r");
	end
	
	if isgem then
		tooltip:AddDoubleLine("|cffcccccc│|r".."|cFFFF99FF可使用回收宝石回收成积分|r", "|cffcccccc│|r");
	end
	
    if Ghost.Data.Req[reqId] then
        local  level,vip,hr,faction,rank,reincarnation,achievementPoints,gold,token,xp,honor,arena,spiritPower = 
		Ghost.Data.Req[reqId]["level"],Ghost.Data.Req[reqId]["vip"],Ghost.Data.Req[reqId]["hr"],Ghost.Data.Req[reqId]["faction"],
		Ghost.Data.Req[reqId]["rank"],Ghost.Data.Req[reqId]["reincarnation"],Ghost.Data.Req[reqId]["achievementPoints"],Ghost.Data.Req[reqId]["gold"],Ghost.Data.Req[reqId]["token"],Ghost.Data.Req[reqId]["xp"],
		Ghost.Data.Req[reqId]["honor"],Ghost.Data.Req[reqId]["arena"],Ghost.Data.Req[reqId]["spiritPower"];
        local mapDesc,questDesc,achieveDesc,spellDesc = 
	    	Ghost.Data.Req[reqId]["mapDesc"],Ghost.Data.Req[reqId]["questDesc"],Ghost.Data.Req[reqId]["achieveDesc"],Ghost.Data.Req[reqId]["spellDesc"];

        if math.abs(level) > 0 or math.abs(vip) > 0 or math.abs(hr) > 0 or math.abs(faction) > 0 or math.abs(rank) > 0 or  achievementPoints > 0 or mapDesc~="" or questDesc~="" or achieveDesc~="" or spellDesc~="" then
            tooltip:AddDoubleLine("|cffcccccc│|r需满足", "|cffcccccc│|r");
			tooltip.count = tooltip.count + 1;
            if math.abs(level) > 0 then
                if level > 0 then
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[等级]|r", math.abs(level).."级".."|cffcccccc│|r");
                else
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[等级]|r", "当前是"..math.abs(level).."级".."|cffcccccc│|r");
                end
				tooltip.count = tooltip.count + 1;
            end
            if math.abs(vip) > 0 then
                if vip > 0 then
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[会员]|r", Ghost.Data.VIP[math.abs(vip)]["title"].."|cffcccccc│|r");
                else
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[会员]|r", "当前是"..Ghost.Data.VIP[math.abs(vip)]["title"].."|cffcccccc│|r");
                end
				tooltip.count = tooltip.count + 1;
            end
            if math.abs(hr) > 0 then
                local f,_ = UnitFactionGroup("player");
                if f == "Alliance" then
                    if hr > 0 then
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[军衔]|r", Ghost.Data.HRTitles[math.abs(hr)].."|cffcccccc│|r");
                    else
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[军衔]|r", "当前是"..Ghost.Data.HRTitles[math.abs(hr)].."|cffcccccc│|r");
                    end
                else
                    if hr > 0 then
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[军衔]|r", Ghost.Data.HRTitles[math.abs(hr) + 14].."|cffcccccc│|r");
                    else
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[军衔]|r", "当前是"..Ghost.Data.HRTitles[math.abs(hr) + 14].."|cffcccccc│|r");
                    end
                end
				tooltip.count = tooltip.count + 1;
            end
            if math.abs(reincarnation) > 0 and Ghost.Data.ReincarnationTitles[math.abs(reincarnation)] then
                if reincarnation > 0 then
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[转生]|r", Ghost.Data.ReincarnationTitles[math.abs(reincarnation)].."|cffcccccc│|r");
                else
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[转生]|r", "当前是"..Ghost.Data.ReincarnationTitles[math.abs(reincarnation)].."|cffcccccc│|r");
                end
				tooltip.count = tooltip.count + 1;
            end
	    	if math.abs(faction) > 0 and Ghost.Data.FactionTitles[math.abs(faction)] then
                if faction > 0 then
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[门派]|r", Ghost.Data.FactionTitles[math.abs(faction)].."|cffcccccc│|r");
                else
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[门派]|r", "当前是"..Ghost.Data.FactionTitles[math.abs(faction)].."|cffcccccc│|r");
                end
				tooltip.count = tooltip.count + 1;
            end
            if math.abs(rank) > 0 and Ghost.Data.RankTitles[math.abs(rank)] then
                if rank > 0 then
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[修炼]|r", Ghost.Data.RankTitles[math.abs(rank)].."|cffcccccc│|r");
                else
                    tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[修炼]|r", "当前是"..Ghost.Data.RankTitles[math.abs(rank)].."|cffcccccc│|r");
                end
				tooltip.count = tooltip.count + 1;
            end

            if achievementPoints > 0 then
                tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[成就点]|r", achievementPoints.."|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
            end

            if mapDesc ~= "" then
                tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[区域]|r", mapDesc.."|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
            end
            if questDesc ~= "" then
                tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[任务]|r", questDesc.."|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
            end
            if achieveDesc ~= "" then
                tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[成就]|r", achieveDesc.."|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
            end
            if spellDesc ~= "" then
                tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00[技能或光环]|r", spellDesc.."|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
            end
        end

        local reqItem = false;
        for i=1,10 do
            if Ghost.Data.Req[reqId]["item"..i] > 0 and Ghost.Data.Req[reqId]["itemCount"..i] > 0 then
                reqItem = true;
            end
        end

        -- if gold > 0 or token > 0 or xp > 0 or honor > 0 or arena > 0 or spiritPower > 0 or reqItem then
            -- tooltip:AddDoubleLine("|cffcccccc│|r将消耗", "|cffcccccc│|r");
			-- tooltip.count = tooltip.count + 1;
            -- if gold > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINBI.."|cFF00FF00[金币]|r", gold.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if token > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JIFEN.."|cFF00FF00[积分]|r", token.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if xp > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINGYAN.."|cFF00FF00[经验]|r", xp.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if honor > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_RONGYU.."|cFF00FF00[荣誉点数]|r", honor.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if arena > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINGJI.."|cFF00FF00[竞技点数]|r", arena.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if spiritPower > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINBI.."|cFF00FF00[灵力]|r", spiritPower.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- for i=1,10 do
                -- if Ghost.Data.Req[reqId]["item"..i] > 0 and Ghost.Data.Req[reqId]["itemCount"..i] > 0 then
                    -- local itemLink = GhostGetItemLink(Ghost.Data.Req[reqId]["item"..i]);
                    -- local count = Ghost.Data.Req[reqId]["itemCount"..i]
                    -- if itemLink then
                        -- tooltip:AddDoubleLine("|cffcccccc│|r".."|T"..GhostGetItemIcon(Ghost.Data.Req[reqId]["item"..i])..":15:15:20:-2|t     "..itemLink, count.."|cffcccccc│|r");
						-- tooltip.count = tooltip.count + 1;
                    -- end
                -- end
            -- end
        -- end

        for i=1,10 do
            if Ghost.Data.Req[reqId]["cmdDes"..i] ~= "0" then
                tooltip:AddDoubleLine("|cffcccccc│|r同时使", "|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
                for j=1,10 do
                    if Ghost.Data.Req[reqId]["cmdDes"..j] ~= "0" then
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00"..Ghost.Data.Req[reqId]["cmdDes"..j], "|cffcccccc│|r");
						tooltip.count = tooltip.count + 1;
                    end
                end
                break;
            end
        end
    end

    tooltip:AddDoubleLine("|cffcccccc└|r", "|cffcccccc┘|r");
	tooltip.count = tooltip.count + 1;
    tooltip:Show();
end

function Ghost_RewTooltipText(tooltip,leftText,rightText, rewId)
 
    tooltip:AddDoubleLine("|cffcccccc┌|r", "|cffcccccc┐|r");
	tooltip.count = tooltip.count + 1;
    if leftText and rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r"..leftText, "|cFF00FF00"..rightText.."|r|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    elseif not leftText and rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r", "|cFF00FF00"..rightText.."|r|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    elseif leftText and not rightText then
        tooltip:AddDoubleLine("|cffcccccc│|r"..leftText, "|cffcccccc│|r");
		tooltip.count = tooltip.count + 1;
    end

    if Ghost.Data.Rew[rewId] then
        local  gold,token,xp,honor,arena,statpoint,spell,aura = 
		Ghost.Data.Rew[rewId]["gold"],Ghost.Data.Rew[rewId]["token"],Ghost.Data.Rew[rewId]["xp"],
        Ghost.Data.Rew[rewId]["honor"],Ghost.Data.Rew[rewId]["arena"],Ghost.Data.Rew[rewId]["statpoint"],
        Ghost.Data.Rew[rewId]["spell"],Ghost.Data.Rew[rewId]["aura"];
    
        local rewItem = false;
        for i=1,10 do
            if Ghost.Data.Rew[rewId]["item"..i] > 0 and Ghost.Data.Rew[rewId]["itemCount"..i] > 0 then
                rewItem = true;
            end
        end

        for i=1,10 do
            if Ghost.Data.Rew[rewId]["spell"..i] > 0 and  GhostGetSpellLink(Ghost.Data.Rew[rewId]["spell"..i]) then
                rewSpell = true;
            end
        end

        for i=1,10 do
            if Ghost.Data.Rew[rewId]["aura"..i] > 0 and  GhostGetSpellLink(Ghost.Data.Rew[rewId]["aura"..i]) then
                rewAura = true;
            end
        end


        -- if gold > 0 or token > 0 or xp > 0 or honor > 0 or arena > 0 or statpoint > 0 or rewSpell or rewAura or rewItem then
            -- tooltip:AddDoubleLine("|cffcccccc│|r将获得", "|cffcccccc│|r");
			-- tooltip.count = tooltip.count + 1;
            -- if gold > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINBI.."|cFF00FF00[金币]|r", gold.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if token > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JIFEN.."|cFF00FF00[积分]|r", token.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if xp > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINGYAN.."|cFF00FF00[经验]|r", xp.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if honor > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_RONGYU.."|cFF00FF00[荣誉点数]|r", honor.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if arena > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_JINGJI.."|cFF00FF00[竞技点数]|r", arena.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            -- if statpoint > 0 then
                -- tooltip:AddDoubleLine("|cffcccccc│|r"..TOOLTIP_ICON_DOUQI.."|cFF00FF00[斗气点数]|r", statpoint.."|cffcccccc│|r");
				-- tooltip.count = tooltip.count + 1;
            -- end
            
            -- for i=1,10 do
                -- if Ghost.Data.Rew[rewId]["spell"..i] > 0  then
                    -- local link = GhostGetSpellLink(Ghost.Data.Rew[rewId]["spell"..i]);
                    -- tooltip:AddDoubleLine("|cffcccccc│|r".."|T"..GhostGetSpellIcon(Ghost.Data.Rew[rewId]["spell"..i])..":15:15:20:-2|t     ".."|cFF00FF00[技能]|r", link.."|cffcccccc│|r");
					-- tooltip.count = tooltip.count + 1;
                -- end
            -- end

            -- for i=1,10 do
                -- if Ghost.Data.Rew[rewId]["aura"..i] > 0  then
                    -- local link = GhostGetSpellLink(Ghost.Data.Rew[rewId]["aura"..i]);
                    -- if link then
                        -- tooltip:AddDoubleLine("|cffcccccc│|r".."|T"..GhostGetSpellIcon(Ghost.Data.Rew[rewId]["aura"..i])..":15:15:20:-2|t     ".."|cFF00FF00[光环]|r", link.."|cffcccccc│|r");
						-- tooltip.count = tooltip.count + 1;
                    -- end
                -- end
            -- end

            -- for i=1,10 do
                -- if Ghost.Data.Rew[rewId]["item"..i] > 0 and Ghost.Data.Rew[rewId]["itemCount"..i] > 0 then
                    -- local itemLink = GhostGetItemLink(Ghost.Data.Rew[rewId]["item"..i]);
                    -- local count = Ghost.Data.Rew[rewId]["itemCount"..i]
                    -- if itemLink then
                        -- tooltip:AddDoubleLine("|cffcccccc│|r".."|T"..GhostGetItemIcon(Ghost.Data.Rew[rewId]["item"..i])..":15:15:20:-2|t     "..itemLink, count.."|cffcccccc│|r");
						-- tooltip.count = tooltip.count + 1;
                    -- end
                -- end
            -- end
        -- end

        for i=1,10 do
            if Ghost.Data.Rew[rewId]["cmdDes"..i] ~= "0" then
                tooltip:AddDoubleLine("|cffcccccc│|r同时使", "|cffcccccc│|r");
				tooltip.count = tooltip.count + 1;
                for j=1,10 do
                    if Ghost.Data.Rew[rewId]["cmdDes"..j] ~= "0" then
                        tooltip:AddDoubleLine("|cffcccccc│|r|cFF00FF00"..Ghost.Data.Rew[rewId]["cmdDes"..j], "|cffcccccc│|r");
						tooltip.count = tooltip.count + 1;
                    end
                end
                break;
            end
        end
    end
    

    tooltip:AddDoubleLine("|cffcccccc└|r", "|cffcccccc┘|r");
	tooltip.count = tooltip.count + 1;
    tooltip:Show();
end

function GhostSubString(self, str)
	local num = 150
	local sNum = num or 1
    local tab = {}
    for uchar in string.gmatch(str, "[%z\1-\127\194-\244][\128-\191]*") do 
        tab[#tab+1] = uchar
    end
    local resultStr = ""
	local x = 1	
    for i=1,#tab do
		x = x + 1
		if x == num then
			resultStr = resultStr..tab[i]
			self:AddLine("|cFF00FF00"..resultStr)
            self.count = self.count + 1
			x = 1
			resultStr = ""
		else
			resultStr = resultStr..tab[i]
		end
    end
    self:AddLine("|cFF00FF00"..resultStr)
    self.count = self.count + 1
end

function GhostGetSpellDescription(spellId)
	if not SpellDescriptionTooltip then
		CreateFrame("GameTooltip","SpellDescriptionTooltip",UIParent,"GameTooltipTemplate");
		SpellDescriptionTooltip:SetOwner(UIParent,"ANCHOR_NONE");
	end
	SpellDescriptionTooltip:ClearLines();
	SpellDescriptionTooltip:SetHyperlink("spell:"..spellId);
	local des = "-".._G["SpellDescriptionTooltipTextLeft"..SpellDescriptionTooltip:NumLines()]:GetText();
	des = "|cFF00FF00"..des.."|r";	
	return des;
end

function GhostAddSpellDescription(self, spellId)
	local spellLink = GhostGetSpellLink(spellId)
	
	if not spellLink then
		return
	end
	
	if not SpellDescriptionTooltip then
		CreateFrame("GameTooltip","SpellDescriptionTooltip",UIParent,"GameTooltipTemplate");
		SpellDescriptionTooltip:SetOwner(UIParent,"ANCHOR_NONE");
	end
	SpellDescriptionTooltip:ClearLines();
	SpellDescriptionTooltip:SetHyperlink(spellLink);
	
	self:AddLine("|cFF5EAED1·"..spellLink.."|r")
    self.count = self.count + 1
	GhostSubString(self,(_G["SpellDescriptionTooltipTextLeft"..SpellDescriptionTooltip:NumLines()]:GetText()))
end

function GhostAddItemDescription(self, itemId)
	if itemId == "0" then
		self:AddLine("|cFF999999·[未镶嵌符文]|r")
        self.count = self.count + 1
	end
	
	local itemLink = GhostGetItemLink(itemId)
	local itemName = GhostGetItemName(itemId)
	if not itemLink then
		return
	end
	
	if not ItemSpellTooltip then
		CreateFrame("GameTooltip","ItemSpellTooltip",UIParent,"GameTooltipTemplate");
		ItemSpellTooltip:SetOwner(UIParent,"ANCHOR_NONE");
	end
	ItemSpellTooltip:ClearLines();
	ItemSpellTooltip:SetHyperlink(itemLink)
	
	self:AddLine("|cFFFF6600·["..itemName.."]|r")
	self.count = self.count + 1
	for i=1,ItemSpellTooltip:NumLines()do 
		local t = _G["ItemSpellTooltipTextLeft"..i]:GetText()
		if string.find(t, "^+") or string.find(t, "^装备：") or string.find(t, "^使用：") then
			GhostSubString(self,t)
		end
	end
end

function GhostAddRunesTexture(self,k, itemId, n)
    if not _G[self:GetName().."CusItemTextureFrame"..k] then 
        self:CreateTexture(self:GetName().."CusItemTextureFrame"..k) 
        _G[self:GetName().."CusItemTextureFrame"..k].r = 0 
    end
    
    if not _G[self:GetName().."CusItemIconFrame"..k] then 
        self:CreateTexture(self:GetName().."CusItemIconFrame"..k) 
    end
       
    local f1 = _G[self:GetName().."CusItemTextureFrame"..k]
	f1.r = f1.r + 0.15 if f1.r >= 1000 then f1.r = 0 end
	f1:SetTexture("Interface\\AddOns\\GhostPanel\\Icons\\EnchantBorder_Yellow")
	--f1:SetPoint("BOTTOMLEFT", self, k * 25 - 22, n * 15.3)
    f1:SetPoint("BOTTOMLEFT", _G[self:GetName().."TextLeft"..(n)], "BOTTOMLEFT", k * 25 - 30, -50)
	f1:SetSize(40, 40)
	f1:SetRotation(f1.r)
	f1:Show()
	if itemId ~= nil then
	local f2 = _G[self:GetName().."CusItemIconFrame"..k]
	f2:SetTexture(GhostGetItemIcon(itemId))
	f2:SetPoint("CENTER", f1)
	f2:SetSize(10, 10)
	f2:Show()
	end
end

do
ButtonBar = CreateFrame("Button", "ButtonBar", GameTooltip)
ButtonBar:SetSize(200, 30)
ButtonBar:SetPoint("TOPLEFT", 50, -32)
ButtonBar:SetNormalTexture("Interface\\GLUES\\LoadingBar\\Loading-BarBorder")
ButtonBar:Hide()

MyStatusBarOne = CreateFrame("StatusBar", "MyStatusBarOne", ButtonBar, nil)
MyStatusBarOne:SetSize(172, 9)
MyStatusBarOne:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar")
MyStatusBarOne:SetStatusBarColor(51/255, 102/255, 255/255, 1)
MyStatusBarOne:SetPoint("CENTER")
MyStatusBarOne:SetMinMaxValues(0, 1000)
MyStatusBarOne:Hide()

status = MyStatusBarOne:CreateFontString("status", "OVERLAY", "TextStatusBarText")
status:SetSize(172, 4)
status:SetPoint("CENTER", 0, 1)
end

function GhostOnTooltipShow(self)
    
    --Hook Item
	local _, itemLink = self:GetItem()
	
	if itemLink then
		self.count = 0
		for i=1,100 do
            local f1 = _G[self:GetName().."CusItemTextureFrame"..i]
            local f2 = _G[self:GetName().."CusItemIconFrame"..i]
            if f1 then f1:Hide() end
            if f2 then f2:Hide() end
        end
		--print(string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?"))
		local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")
		
        if tonumber(uniqueId) > 100000000 then	
            self:SetHyperlink("item:"..uniqueId..":"..enchantId..":"..jewelId1..":"..jewelId2..":"..jewelId3..":"..jewelId4..":".."0"..":".."0"..":"..linkLevel)

            if tonumber(suffixId) >= 2000 then   
                local text = ""
                local guid = tonumber(uniqueId) - 100000000

                if Ghost.Data.Item.GUID[guid] then
					text = "   ★★★★★随机附魔洗练★★★★★|n"
                    for i=1,8 do
                        if Ghost.Data.Item.GUID[guid]["slot"..i] then
							if Ghost.Data.Enchant[Ghost.Data.Item.GUID[guid]["slot"..i]] then
								text = text..string.format("|cFF00FF00%s|r|n",Ghost.Data.Enchant[Ghost.Data.Item.GUID[guid]["slot"..i]])
							end
                        end
                    end
				else
					Ghost_SendData("GC_C_NOT_ITEMENCHANTINFO",uniqueId);							
                end


                local exsit = false
                if text and text ~= "" then
                    for i=1,self:NumLines() do
                        local s = _G[self:GetName().."TextLeft"..i]:GetText()
                        if s then 
                            if string.find(s, "需要等级") then
                                _G[self:GetName().."TextLeft"..i]:SetText(text..s)
                                exsit = true
                            end
                        end
                    end

                    if not exsit then
                        for i=1,self:NumLines() do
                            local s = _G[self:GetName().."TextLeft"..i]:GetText()
                            if s then 
                                if string.find(s, "：")  then
                                    _G[self:GetName().."TextLeft"..i]:SetText(text..s)
                                    exsit = true
                                    break
                                end
                            end
                        end
                    end

                    if not exsit then 
                        self:AddLine(text);
						self.count = self.count + 1
                    end
                end

                local s = tostring(tonumber(suffixId) - 2000)
                
                local bind = tonumber(string.sub(s,-9,-9))

                if bind and  tonumber(bind) == 1 then
                    for i=1,self:NumLines() do
                        local s = _G[self:GetName().."TextLeft"..i]:GetText()
                        if s then 
                            if string.find(s, "绑定") then
                                _G[self:GetName().."TextLeft"..i]:SetText("已绑定")
                            end
                        end
                    end
                end
            end
        end
		
		if not Ghost.Data.Item.Entry[itemId] then
			Ghost_SendData("GC_C_ITEMENTRY",itemId);
			
			Ghost.Data.Item.Entry[itemId] = {
				["des"]                 = "",
				["heroText"]            = "",
				["daylimit"]            = 0,
				["maxGems"]             = 0,
				["exchange1"]           = 0,
				["exchangeReqId1"]      = 0,
				["exchange2"]           = 0,
				["exchangeReqId2"]      = 0,
				["unbindReqId"]         = 0,
				["useReqId"]            = 0,
				["equipReqId"]          = 0,
				["buyReqId"]            = 0,
				["sellRewId"]           = 0,
				["recoveryRewId"]       = 0,
				["gs"]                  = 0,
			}
		end
		
        if Ghost.Data.Item.Entry[itemId] then
            if Ghost.Data.Item.Entry[itemId]["des"] ~="" then
                self:AddLine(Ghost.Data.Item.Entry[itemId]["des"]);
				self.count = self.count+1;
            end
        end		
		
		local IsGem = false
		if Ghost.Data.Item.Entry[itemId] then
			if Ghost.Data.Item.Entry[itemId]["recoveryRewId"] ~=0 then
                IsGem = true
            end
		end

        if tonumber(uniqueId) > 100000000 then
            if tonumber(suffixId) > 2000 then
                local s = tostring(tonumber(suffixId) - 2000)
				local compound = tonumber(string.sub(s,-1,-1))
                local req = tonumber(string.sub(s,-5,-2))
                local rew = tonumber(string.sub(s,-8,-6))
				local IsRew = false
				if rew and rew~= 0 then
					IsRew = true
				end
				
                if req and req~= 0 then
                    --Ghost_ReqTooltipText(self,"升级",GhostGetItemLink(uniqueId),req);
					Ghost_ReqTooltipText(self,"装备可升级为",GhostGetItemLink(uniqueId),req,IsRew,IsGem);
                end
			
                if compound and compound~= 0 then
                --  Ghost_ReqTooltipText(self,"合成 X 2",GhostGetItemLink(uniqueId));
                end

                -- if rew and rew~= 0 then
                    -- Ghost_RewTooltipText(self,"","装备可售卖给商店获取材料",rew);
                -- end
            end
        end


		if tonumber(uniqueId) > 100000000 then
			local guid = tonumber(uniqueId) - 100000000
			if Ghost.Data.ItemLegend[guid] ~= nil then
				local toolTip = _G[self:GetName().."TextLeft1"]
				if toolTip:GetText() ~= nil then
					if string.find(toolTip:GetText(),"|cFF33CCFF〈成长神器〉") == nil then
						toolTip:SetText(toolTip:GetText().."|cFF33CCFF〈成长神器〉\n \n升级经验                                                  \n ")
					end
					if self:GetWidth() < 270 then
						self:SetWidth(270)
					end
					ButtonBar:SetPoint("TOPLEFT", 80, -32)
					ButtonBar:Show()
					MyStatusBarOne:Show()
					MyStatusBarOne:SetMinMaxValues(0, tonumber(Ghost.Data.ItemLegend[guid].MaxExp))
					MyStatusBarOne:SetValue(Ghost.Data.ItemLegend[guid].Exp)
					status:SetText("Lv"..Ghost.Data.ItemLegend[guid].Lv..":"..Ghost.Data.ItemLegend[guid].Exp.." / "..Ghost.Data.ItemLegend[guid].MaxExp)
				end
			end
		end
		
		local itemguid = tonumber(uniqueId) - 100000000
		if Ghost.Data.ItemRuneSlot[itemguid] ~= nil then
			
			local slot = Ghost.Data.ItemRuneSlot[itemguid];
			local maxslot = Ghost.Data.ItemRuneMaxSlot[itemguid];
			-- print("slot = "..slot)
			-- print("maxslot = "..maxslot)
			self:AddLine("符文插槽 ："..slot.."/"..maxslot)
			self:AddLine(" ")
			self:AddLine(" ")
			local i = self:NumLines();
			for j = 1,slot do
				local itemid = nil;
				if  Ghost.Data.ItemRunes[itemguid] ~=nil  then
				local t = Ghost.Data.ItemRunes[itemguid]
					if type(t) == "table" then 
						if t[j] ~= nil then
							itemid = t[j]
						end
					else
						if j == 1 then
							itemid = t;
						end
					end
				end
				GhostAddRunesTexture(self,j,itemid,i - 3);
			end
			self:SetMinimumWidth(25*slot);
		end
		
		if Ghost.Data.ItemRunes[itemguid] ~= nil then
			local t = Ghost.Data.ItemRunes[itemguid]
			local n = 0
			for i=1,self:NumLines() do
				local x = _G[self:GetName().."TextLeft"..i]:GetText()
				if string.find(x, "符文插槽") and n == 0 then
					n = self:NumLines() - i + 1
				end
			end
			
			if type(t) == "table" then 
				for k,v in pairs(t) do
					if n > 0 then			
						GhostAddItemDescription(self,tonumber(v));
					end
				end
			else
				GhostAddItemDescription(self,tonumber(t));
			end
		end
		
		if Ghost.Data.ItemRuneEffects[itemguid] ~= nil then
			local t = Ghost.Data.ItemRuneEffects[itemguid]
			if t ~= "0" then
				self:AddLine("|cFFFE8252〓 符文组合 〓")
			end
			if type(t) == "table" then 
				for k,v in pairs(t) do
					GhostAddSpellDescription(self,tonumber(v))
				end
			else
				GhostAddSpellDescription(self,tonumber(t))
			end
		end
		
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|T"..GhostGetSpellIcon(118)..":15:15:0:-2|t",GhostGetSpellLink(118));
        -- self:AddDoubleLine("|T"..GhostGetSpellIcon(126)..":15:15:0:-2|t",GhostGetSpellLink(126));
        -- self:AddDoubleLine("|T"..GhostGetSpellIcon(145)..":15:15:0:-2|t",GhostGetSpellLink(145));
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42404:15:15:0:-2|t",GhostGetSpellLink(33));
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t","未激活");
        -- self:AddDoubleLine("|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_1401:15:15:0:-2|t","符文之语");

        --local x = "|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42403:15:15:0:-2|t";
        --local y = "|TInterface\\AddOns\\GhostPanel\\3.0\\Icons\\i_42404:15:15:0:-2|t";
--
        --self:AddLine("");
        --self:AddLine(x..x..x..y..x..y..x..y..x..y);
	
        if Ghost.Data.Item.Entry[itemId] then
            if Ghost.Data.Item.Entry[itemId]["daylimit"] ~=0 then
                if not Ghost.Char.DayLimitItem[itemId] then
                    Ghost_ReqTooltipText(self,"每日上限","0/"..Ghost.Data.Item.Entry[itemId]["daylimit"],0);
                else
                    Ghost_ReqTooltipText(self,"每日上限",Ghost.Char.DayLimitItem[itemId].."/"..Ghost.Data.Item.Entry[itemId]["daylimit"],0);
                end
            end

            if Ghost.Data.Item.Entry[itemId]["maxGems"] ~=0 then
                Ghost_ReqTooltipText(self,"最大镶嵌数量",Ghost.Data.Item.Entry[itemId]["maxGems"],0);
            end
        
            if Ghost.Data.Item.Entry[itemId]["exchange1"] ~=0 then
                Ghost_ReqTooltipText(self,"升级",GhostGetItemLink(Ghost.Data.Item.Entry[itemId]["exchange1"]),Ghost.Data.Item.Entry[itemId]["exchangeReqId1"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["unbindReqId"] ~=0 then
                Ghost_ReqTooltipText(self,"","解绑",Ghost.Data.Item.Entry[itemId]["unbindReqId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["equipReqId"] ~=0 then
                Ghost_ReqTooltipText(self,"","装备",Ghost.Data.Item.Entry[itemId]["equipReqId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["useReqId"] ~=0 then
                Ghost_ReqTooltipText(self,"","使用",Ghost.Data.Item.Entry[itemId]["useReqId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["buyReqId"] ~=0 then
                -- Ghost_ReqTooltipText(self,"","购买",Ghost.Data.Item.Entry[itemId]["buyReqId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["sellRewId"] ~=0 then
                -- Ghost_RewTooltipText(self,"","售卖",Ghost.Data.Item.Entry[itemId]["sellRewId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["recoveryRewId"] ~=0 then
                -- Ghost_RewTooltipText(self,"","可使用回收宝石回收成积分",Ghost.Data.Item.Entry[itemId]["recoveryRewId"]);
            end
        
            if Ghost.Data.Item.Entry[itemId]["exchange2"] ~=0 then
                Ghost_ReqTooltipText(self,"升级",GhostGetItemLink(Ghost.Data.Item.Entry[itemId]["exchange2"]),Ghost.Data.Item.Entry[itemId]["exchangeReqId2"]);
            end
        end

        Ghost_SetItemDes(self,itemId);

        GameTooltip_ClearMoney(self);
        local price = GhostGetItemPrice(itemId);
        if price and price > 0 then
            GameTooltip_OnTooltipAddMoney(self, GhostGetItemPrice(itemId));
        end
    end
    
    --Hook Spell
    local _,_, spellId = self:GetSpell();
    if spellId then
        Ghost_GS_SetSpellGS(self,spellId);
    end
end

function GhostOnTooltipHide(self)
	for i=1,100 do
        local f1 = _G[self:GetName().."CusItemTextureFrame"..i]
        local f2 = _G[self:GetName().."CusItemIconFrame"..i]
        if f1 then f1:Hide() end
        if f2 then f2:Hide() end
    end
end

GameTooltip:HookScript("OnShow", GhostOnTooltipShow);
ItemRefTooltip:HookScript("OnShow", GhostOnTooltipShow);
GameTooltip:HookScript("OnHide", function(self) GhostOnTooltipHide(self) ButtonBar:Hide() MyStatusBarOne:Hide()end);
ItemRefTooltip:HookScript("OnHide", function(self) GhostOnTooltipHide(self) ButtonBar:Hide() MyStatusBarOne:Hide()end);

-- GossipExtend
local function GossipExtend(frame)
	-- itemString 服务端生成 "Hitem:XXXX"
	-- spellString 服务端生成 "Hspell:XXXX"
	local itemString = string.match(frame:GetText(), "Hitem[%-?%d:]+")
	local spellString = string.match(frame:GetText(), "Hspell[%-?%d:]+")
	if itemString then
		local _, itemId = strsplit(":", itemString)
		SetCursor("ATTACK_CURSOR")
		GameTooltip:SetOwner(_G["GossipFrame"], "ANCHOR_RIGHT", -30) 
		GameTooltip:ClearLines() 
		GameTooltip:SetHyperlink("Hitem:"..itemId)
		GameTooltip:Show()
	end
	
	if spellString then
		local _, spellId = strsplit(":", spellString)
		SetCursor("ATTACK_CURSOR")
		GameTooltip:SetOwner(_G["GossipFrame"], "ANCHOR_RIGHT", -30) 
		GameTooltip:ClearLines()
		GameTooltip:SetSpellByID(spellId)
		GameTooltip:Show()
	end 
end

for i=1,32 do if _G["GossipTitleButton"..i] then _G["GossipTitleButton"..i]:SetScript("OnEnter", GossipExtend) _G["GossipTitleButton"..i]:SetScript("OnLeave", function() GameTooltip:Hide()end) end end


-- function TESTTT()
--     local locX, locY, locZ = GetCurrentPosition("player");
--     DEFAULT_CHAT_FRAME:AddMessage(locX .. ", " .. locY .. ", " .. locZ);
-- end


-- local buffName = "神圣之灵"
-- 
-- local UnitAura, UIParent = UnitAura, UIParent
-- local tooltip = CreateFrame("GameTooltip","BuffCheckDisplayTooltip",UIParent,"GameTooltipTemplate")
-- 
-- function tooltip:Update()
-- 	local i=1
-- 	local aura = UnitAura("player",i)
-- 	while aura and (aura ~= buffName) do
-- 		i=i+1
-- 		aura = UnitAura("player",i)
-- 	end
-- 	if aura then
-- 		tooltip:ClearLines()
-- 		tooltip:SetOwner(UIParent,"ANCHOR_PRESERVE")
--         tooltip:SetUnitAura("player",i)
--         local px,py=GetPlayerMapPosition("player")
-- 
--         px = px - 32.07
--         py = py - 49.96
--         tooltip:ClearAllPoints();
--         tooltip:SetPoint("CENTER", 32.07 - px, 49.96 - py)
-- 		tooltip:Show()
-- 	else
-- 		tooltip:Hide()
-- 	end
-- end
-- 
-- CreateFrame("Frame"):SetScript("OnUpdate",function(self,elapsed) self.elapsed = (self.elapsed or 0)+elapsed if self.elapsed > 0.5 then self.elapsed = 0 tooltip:Update() end end)
-- 
-- 
-- 
-- 
-- function setMyFrame(f,x,y)
--     f:SetSize(288,100)
--     f:SetPoint("TOPLEFT",UIParent,"TOPLEFT",x,y) 
--     f.text = f.text or f:CreateFontString(nil,"ARTWORK","QuestFont_Shadow_Huge")   
--     f.text:SetAllPoints(true)     
--  end
-- 
-- ctotel = 0
-- creft = 0.1
-- function myCoords(f,i)
--    ctotel = ctotel + i
--    if ctotel >= creft then
--       px,py=GetPlayerMapPosition("player")
-- 
--       f.text:SetText(format("( %s ) [%f , %f]",GetZoneText(), px *100, py *100))
--       ctotel = 0
--    end
-- end
-- 
-- myCoordsFrame = CreateFrame("Frame","MyCoordsFrame",UIParent)
-- setMyFrame(myCoordsFrame, 500, 0)
-- myCoordsFrame:SetScript("OnUpdate", myCoords)

