<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">

	<ScrollFrame name="ScenarioBlocksFrame" parent="ObjectiveTrackerBlocksFrame" hidden="true">
		<Size x="212" y="10"/>
		<ScrollChild>
			<Frame parentKey="ScrollContents">
				<Size x="10" y="10"/>
				<Frames>
					<Frame name="ScenarioObjectiveBlock" hidden="true">
						<Size x="192" y="10"/>
					</Frame>
					<Frame name="ScenarioStageBlock" hidden="true">
						<Size x="201" y="83"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="NormalBG" atlas="ScenarioTrackerToast" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<Texture parentKey="FinalBG" atlas="ScenarioTrackerToast-FinalFiligree" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="4" y="-4"/>
									</Anchors>
								</Texture>
								<Texture parentKey="GlowTexture" atlas="ScenarioTrackerToast" useAtlasSize="true" alpha="0" alphaMode="ADD">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
									<Animations>
										<AnimationGroup parentKey="AlphaAnim">
											<Alpha fromAlpha="0" toAlpha="1" duration="0.266" order="1"/>
											<Alpha endDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.333" order="2"/>
										</AnimationGroup>
									</Animations>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<FontString parentKey="Stage" inherits="QuestTitleFont" wordwrap="true" justifyH="LEFT" mixin="ShrinkUntilTruncateFontStringMixin">
									<Size x="172" y="18"/>
									<Anchors>
										<Anchor point="TOPLEFT" x="15" y="-10"/>
									</Anchors>
									<Color r="1" g="0.914" b="0.682"/>
									<Shadow>
										<Offset>
											<AbsDimension x="1" y="-1"/>
										</Offset>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString parentKey="CompleteLabel" inherits="QuestTitleFont" text="STAGE_COMPLETE" hidden="true">
									<Anchors>
										<Anchor point="LEFT" x="15" y="3"/>
									</Anchors>
									<Color r="1" g="0.914" b="0.682"/>
									<Shadow>
										<Offset>
											<AbsDimension x="1" y="-1"/>
										</Offset>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString parentKey="Name" inherits="GameFontNormal" justifyH="LEFT" justifyV="TOP" spacing="2">
									<Size x="172" y="28"/>
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.Stage" relativePoint="BOTTOMLEFT" x="0" y="-4"/>
									</Anchors>
									<Color r="1" g="0.831" b="0.380"/>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Button parentKey="RewardButton" hidden="true">
								<Size x="48" y="48"/>
								<Anchors>
									<Anchor point="BOTTOMRIGHT" x="50" y="-3"/>
								</Anchors>
								<Layers>
									<Layer level="OVERLAY" textureSubLevel="1">
										<Texture parentKey="RewardRing" atlas="legioninvasion-scenario-rewardring" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER" relativeKey="$parent.RewardRing"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="OVERLAY" textureSubLevel="0">
										<Texture parentKey="RewardIcon">
											<Size x="29" y="29"/>
											<Anchors>
												<Anchor point="CENTER" relativeKey="$parent.RewardRing"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter function="ScenarioRewardButton_OnEnter"/>
									<OnLeave function="ScenarioRewardButton_OnLeave"/>
								</Scripts>
							</Button>
						</Frames>
						<Scripts>
							<OnLoad>
								self.Stage:SetFontObjectsToTry(QuestTitleFont, Fancy16Font, SystemFont_Med1);
							</OnLoad>
							<OnEnter function="ScenarioObjectiveStageBlock_OnEnter"/>
							<OnLeave>
								GameTooltip:Hide();
							</OnLeave>
						</Scripts>
					</Frame>
					<Frame name="ScenarioChallengeModeBlock" hidden="true">
						<Size x="251" y="87"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="TimerBGBack" atlas="ChallengeMode-TimerBG-Back" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="13"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BACKGROUND" textureSubLevel="1">
								<Texture parentKey="TimerBG" atlas="ChallengeMode-TimerBG" useAtlasSize="true">
									<Anchors>
										<Anchor point="BOTTOM" x="0" y="13"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="OVERLAY">
								<Texture atlas="challengemode-timer" setAllPoints="true"/>
								<FontString parentKey="Level" inherits="GameFontNormalMed2" justifyH="LEFT">
									<Anchors>
										<Anchor point="TOPLEFT" x="28" y="-18"/>
									</Anchors>
								</FontString>
								<FontString parentKey="TimeLeft" inherits="GameFontHighlightHuge" justifyH="LEFT">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.Level" relativePoint="BOTTOMLEFT" x="0" y="-8"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<Frame parentKey="StartedDepleted" hidden="true" enableMouse="true">
								<Size x="19" y="20"/>
								<Anchors>
									<Anchor point="LEFT" relativeKey="$parent.Level" relativePoint="RIGHT" x="4" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture atlas="ChallengeMode-icon-chest" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK" textureSubLevel="1">
										<Texture atlas="ChallengeMode-icon-redline" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter>
										GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
										GameTooltip:SetText(CHALLENGE_MODE_DEPLETED_KEYSTONE, 1, 1, 1);
										GameTooltip:AddLine(CHALLENGE_MODE_KEYSTONE_DEPLETED_AT_START, nil, nil, nil, true);
										GameTooltip:Show();
									</OnEnter>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<Frame parentKey="TimesUpLootStatus" hidden="true" enableMouse="true">
								<Size x="19" y="20"/>
								<Anchors>
									<Anchor point="LEFT" relativeKey="$parent.TimeLeft" relativePoint="RIGHT" x="4" y="0"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture atlas="ChallengeMode-icon-chest" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
									<Layer level="ARTWORK" textureSubLevel="1">
										<Texture parentKey="NoLoot" atlas="ChallengeMode-icon-redline" useAtlasSize="true">
											<Anchors>
												<Anchor point="CENTER"/>
											</Anchors>
										</Texture>
									</Layer>
								</Layers>
								<Scripts>
									<OnEnter function="Scenario_ChallengeMode_TimesUpLootStatus_OnEnter"/>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<Frame parentKey="DeathCount" hidden="true" enableMouse="true" mixin="ScenarioChallengeDeathCountMixin">
								<Size x="20" y="16"/>
								<Anchors>
									<Anchor point="TOPLEFT" relativeTo="$parent" relativePoint="BOTTOMRIGHT" x="-47" y="43"/>
								</Anchors>
								<Layers>
									<Layer level="ARTWORK">
										<Texture parentKey="Icon" atlas="poi-graveyard-neutral" useAtlasSize="true">
											<Anchors>
												<Anchor point="LEFT"/>
											</Anchors>
										</Texture>
										<FontString parentKey="Count" inherits="GameFontHighlightSmall2">
											<Anchors>
												<Anchor point="LEFT" relativeKey="$parent.Icon" relativePoint="RIGHT" x="1" y="0"/>
											</Anchors>
										</FontString>
									</Layer>
								</Layers>
								<Scripts>
									<OnLoad method="OnLoad"/>
									<OnEvent method="OnEvent"/>
									<OnEnter method="OnEnter"/>
									<OnLeave function="GameTooltip_Hide"/>
								</Scripts>
							</Frame>
							<StatusBar parentKey="StatusBar" useParentLevel="true">
								<Size x="207" y="13"/>
								<Anchors>
									<Anchor point="BOTTOM" x="0" y="10"/>
								</Anchors>
								<BarTexture atlas="ChallengeMode-TimerFill"/>
							</StatusBar>
							<Frame hidden="true" inherits="ScenarioChallengeModeAffixTemplate"/>
						</Frames>
					</Frame>
					<Frame name="ScenarioProvingGroundsBlock" hidden="true">
						<Size x="201" y="77"/>
						<Layers>
							<Layer level="BACKGROUND">
								<Texture parentKey="BG" atlas="ScenarioTrackerToast" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="0" y="0"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="BORDER">
								<Texture parentKey="GoldCurlies" atlas="ScenarioTrackerToast-FinalFiligree" useAtlasSize="true">
									<Anchors>
										<Anchor point="TOPLEFT" x="4" y="-4"/>
									</Anchors>
								</Texture>
							</Layer>
							<Layer level="ARTWORK">
								<Texture parentKey="MedalIcon" alphaMode="ADD" file="Interface\Challenges\challenges-plat">
									<Size x="52" y="52"/>
									<Anchors>
										<Anchor point="LEFT" x="5" y="-1"/>
									</Anchors>
								</Texture>
								<FontString inherits="QuestFont_Large" parentKey="WaveLabel" text="PROVING_GROUNDS_WAVE">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.MedalIcon" relativePoint="TOPRIGHT" x="1" y="-4"/>
									</Anchors>
									<Color r="1.0" g="0.82" b="0"/>
									<Shadow>
										<Offset x="1" y="-1"/>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString inherits="GameFontHighlightLarge" parentKey="Wave" text="0">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativeKey="$parent.WaveLabel" relativePoint="BOTTOMRIGHT" x="4" y="-1"/>
									</Anchors>
								</FontString>
								<FontString inherits="QuestFont_Large" parentKey="ScoreLabel" text="PROVING_GROUNDS_SCORE" hidden="true">
									<Anchors>
										<Anchor point="TOPLEFT" relativeKey="$parent.WaveLabel" relativePoint="BOTTOMLEFT" x="0" y="-3"/>
									</Anchors>
									<Color r="1.0" g="0.82" b="0"/>
									<Shadow>
										<Offset x="1" y="-1"/>
										<Color r="0" g="0" b="0"/>
									</Shadow>
								</FontString>
								<FontString inherits="GameFontHighlightLarge" parentKey="Score" text="0" hidden="true">
									<Anchors>
										<Anchor point="BOTTOMLEFT" relativeKey="$parent.ScoreLabel" relativePoint="BOTTOMRIGHT" x="4" y="-1"/>
									</Anchors>
								</FontString>
							</Layer>
						</Layers>
						<Frames>
							<StatusBar parentKey="StatusBar" useParentLevel="true">
								<Size x="177" y="15"/>
								<Layers>
									<Layer level="OVERLAY">
										<Texture atlas="challenges-timerborder">
											<Size x="184" y="25"/>
											<Anchors>
												<Anchor point="CENTER" x="0" y="0"/>
											</Anchors>
										</Texture>
										<FontString inherits="GameFontHighlight" justifyH="CENTER" parentKey="TimeLeft" />
									</Layer>
								</Layers>
								<BarTexture file="Interface\TargetingFrame\UI-StatusBar"/>
								<BarColor r="0" g="0.33" b="0.61"/>
							</StatusBar>
						</Frames>
					</Frame>
				</Frames>
			</Frame>
		</ScrollChild>
		<Scripts>
			<OnLoad function="ScenarioBlocksFrame_OnLoad"/>
			<OnEvent function="ScenarioBlocksFrame_OnEvent"/>
		</Scripts>
	</ScrollFrame>

	<Frame name="ObjectiveTrackerCheckLineTemplate" virtual="true">
		<Size x="232" y="16" />
		<Layers>
			<Layer level="ARTWORK">
				<FontString parentKey="Text" inherits="ObjectiveFont">
					<Anchors>
						<Anchor point="TOPLEFT" x="20" y="0" />
					</Anchors>
				</FontString>
				<Texture parentKey="IconAnchor">
					<Size x="16" y="16" />
					<Anchors>
						<Anchor point="TOPLEFT" x="1" y="2" />
					</Anchors>
				</Texture>
				<Texture parentKey="Icon">
					<Size x="16" y="16" />
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.IconAnchor" />
					</Anchors>
				</Texture>
				<Texture parentKey="Glow" file="Interface\Scenarios\Objective-Lineglow" alpha="0"
					alphaMode="ADD">
					<Size x="80" y="0" />
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Text" x="-2" y="0" />
						<Anchor point="TOP" x="0" y="0" />
						<Anchor point="BOTTOM" x="0" y="-4" />
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Scale parentKey="ScaleGlow" startDelay="0.067" scaleX="3" scaleY="1"
								duration="0.433" order="1">
								<Origin point="LEFT" />
							</Scale>
							<Alpha startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.1"
								order="1" />
							<Alpha startDelay="0.467" fromAlpha="1" toAlpha="0" duration="0.267"
								order="1" />
						</AnimationGroup>
					</Animations>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture parentKey="CheckFlash" file="Interface\Scenarios\ScenarioIcon-Check"
					alphaMode="ADD" alpha="0" hidden="true">
					<Size x="16" y="16" />
					<Anchors>
						<Anchor point="CENTER" relativeKey="$parent.Icon" />
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Alpha fromAlpha="0" toAlpha="1" duration="0.067" order="1" />
							<Scale scaleX="1.25" scaleY="1.25" duration="0.2" order="2" />
							<Alpha fromAlpha="1" toAlpha="0" startDelay="0.167" duration="0.23"
								order="2" />
						</AnimationGroup>
					</Animations>
				</Texture>
				<Texture parentKey="Sheen" file="Interface\Scenarios\Objective-Sheen" alpha="0">
					<Size x="32" y="0" />
					<Anchors>
						<Anchor point="LEFT" relativeKey="$parent.Glow" />
						<Anchor point="TOP" x="0" y="2" />
						<Anchor point="BOTTOM" x="0" y="-14" />
					</Anchors>
					<Animations>
						<AnimationGroup parentKey="Anim">
							<Translation startDelay="0.067" offsetX="155" offsetY="0"
								duration="0.467" order="1" />
							<Alpha startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.133"
								order="1" />
							<Alpha startDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.133"
								order="1" smoothing="IN" />
						</AnimationGroup>
					</Animations>
				</Texture>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self.Text:SetWidth(OBJECTIVE_TRACKER_TEXT_WIDTH);
			</OnLoad>
			<OnHide function="ObjectiveTrackerCheckLine_OnHide" />
		</Scripts>
	</Frame>
</Ui>