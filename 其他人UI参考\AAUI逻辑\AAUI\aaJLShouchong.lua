aaJLShouchong = {};
 aaJLShouchong.LevelViews = {};
 aaJLShouchong.levels = {};
 local titletop = -100;
local titleleft = 50;
 function aaJLShouchong:ClickDoneBtn() aaData:sendAddonMsg(10004, "0", "GUILD");
 end;
 aaJLShouchong.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaJLShouchong.mainView:SetFrameStrata("TOOLTIP");
 aaJLShouchong.mainView:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-scjl"});
 aaJLShouchong.mainView:SetWidth(512);
 aaJLShouchong.mainView:SetHeight(512);
 aaJLShouchong.mainView:SetPoint("CENTER",0,0);
 aaJLShouchong.mainView:SetMovable(1);
 aaJLShouchong.mainView:EnableMouse();
 aaJLShouchong.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaJLShouchong.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaJLShouchong.mainView:Hide();
 end;
 local titlewidth = 422;
local titletop = -150;
 aaJLShouchong.title = aaJLShouchong.mainView:CreateFontString("aaJLShouchong.title", "OVERLAY", "GameFontNormal") do 
 aaJLShouchong.title:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 21);
 aaJLShouchong.title:SetPoint("TOPLEFT", aaJLShouchong.mainView, "TOPLEFT", 45,titletop);
 aaJLShouchong.title:SetWidth(titlewidth);
 aaJLShouchong.title:SetHeight(20);
 aaJLShouchong.title:SetSpacing(5);
 end;
 aaJLShouchong.detail = aaJLShouchong.mainView:CreateFontString("aaJLShouchong.detail", "OVERLAY", "GameFontNormal") do 
 aaJLShouchong.detail:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLShouchong.detail:SetPoint("TOPLEFT", aaJLShouchong.mainView, "TOPLEFT", 45,titletop-30);
 aaJLShouchong.detail:SetWidth(titlewidth);
 aaJLShouchong.detail:SetHeight(250);
 aaJLShouchong.detail:SetSpacing(5);
 end;
 aaJLShouchong.btn = CreateFrame('Button', nil, aaJLShouchong.mainView, 'UIPanelButtonTemplate') do 
 aaJLShouchong.btn:SetPoint('TOPLEFT', aaJLShouchong.mainView, 'TOPLEFT', 512/2-100/2, titletop-30-10-250);
 aaJLShouchong.btn:SetSize(100, 40);
 aaJLShouchong.btn:Disable();
 aaJLShouchong.btn:SetScript('OnClick', function() aaJLShouchong:ClickDoneBtn();
 end);
 end;
 function aaJLShouchong:reload() if aa_jlshouchong~=nil and aa_jlshouchong~="" and aa_jlshouchong~={} then 
 local isOk = aa_jlshouchong[1];
 local level = aa_jlshouchong[2];
 local reward = aa_jlshouchong[3];
 aaJLShouchong.title:SetText(aaData.color_yellows.."今日首次充值"..aaData.color_blue..level..worldconf[15].."|r，可获得以下奖励：");
 aaJLShouchong.detail:SetText(reward);
 if isOk == 0 then aaJLShouchong.btn:SetText("领取奖励");
 aaJLShouchong.btn:Enable();
 elseif isOk == 1 then aaJLShouchong.btn:SetText("已领取");
 aaJLShouchong.btn:Disable();
 elseif isOk == 2 then aaJLShouchong.btn:SetText("未完成");
 aaJLShouchong.btn:Disable();
 end;
 else aaJLShouchong.LevelViews[index]:Hide();
 end;
 end;
 local CancelButton = CreateFrame('Button', nil, aaJLShouchong.mainView, 'UIPanelButtonTemplate') do 
 CancelButton:SetPoint('TOPRIGHT', aaJLShouchong.mainView, 'TOPRIGHT', -30, -20);
 CancelButton:SetSize(60, 30);
 CancelButton:SetText("关闭");
 CancelButton:SetScript('OnClick', function() aaJLShouchong:hide();
 end);
 end;
 function aaJLShouchong:show() aaJLShouchong.mainView:Show();
 aaJLShouchong:reload();
 end;
 function aaJLShouchong:hide() aaJLShouchong.mainView:Hide();
 aaJLShouchong:reload();
 end;