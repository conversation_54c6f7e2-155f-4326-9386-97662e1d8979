
print("666")

local DONGD_DELAY_ONE_MINUTE = 1

local DONGD_DELAY_ONE_MINUTES = 60
--ipairs=>遍历数组, pairs==>便利数组 and 表
--在线加载可能也有table风险
--记得在核心做请求发送数据次数限制
--在线加载时本地表变量需要重置
local IsOK,IsAllCreate = false,false

local LMall,RMall,MallTitle,MenuOnButton,MenuOffButton,HomePage,HomeTex	--非数组容器

local LScroll,LSlider,LSlidertex,LScrollChild,LScrollUpButton,LScrollDownButton,LScrollTexTop,LScrollTexMiddle,LScrollTexBottom

local RScroll,RSlider,RSlidertex,RScrollChild,RScrollUpButton,RScrollDownButton,RScrollTexTop,RScrollTexMiddle,RScrollTexBottom

local LHScroll,LHSlider,LHSlidertex,LHScrollChild,LHScrollUpButton,LHScrollDownButton,LHTex,LHScrollTexTop,LHScrollTexMiddle,LHScrollTexBottom

--local LPScroll,LPSlider,LPSlidertex,LPScrollChild,LPScrollUpButton,LPScrollDownButton

--local LSScroll,LSSlider,LSSlidertex,LSScrollChild,LSScrollUpButton,LSScrollDownButton

local MallItemId,MallClass,MallPClass,MallSClass

local CloseButton

local RMallButtonsInfo = {}

local RMallButtons = {}

local RMallClass = {}

local SubFrame = {}

--local SubFramSellInfo = {}

local T1 --测试用

local T = 
{
	ParentFrameBackgroundLeft	= "Interface\\ICONS\\progress_frame_left",
	ParentFrameBackgroundRight	= "Interface\\ICONS\\progress_frame_right",
	ParentFrameTitle			= "Interface\\ICONS\\bt",
	SubFrameBackground			= "Interface\\ICONS\\yuanjiaolan",
	TabClassFrameBackground		= "Interface\\ICONS\\LineTab1",
	ItemClassButton				= "Interface\\ICONS\\_button_h",
	FontBackground				= "Interface\\ICONS\\languangditu",
	ButtonHighlighLight1		= "Interface\\BUTTONS\\CheckButtonHilight",
	ButtonHighlighLight2		= "Interface\\ICONS\\button_h2",
	ButtonHighlighLight3		= "Interface\\Glues\\Common\\Glue-Panel-Button-Highlight",
	Font						= "Interface\\Fonts\\FZZY.TTF",
	SupermarketButton			= "Interface\\ICONS\\shangchengon",
	SupermarketOffButton		= "Interface\\ICONS\\shangchengoff",
	JiFen						= "Interface\\ICONS\\jf1",
	JinBi						= "Interface\\ICONS\\jinbi",
	Vip							= "Interface\\ICONS\\Vip",
	ItemExchange				= "Interface\\ICONS\\wpdh",
	AlliancePVP					= "Interface\\ICONS\\lmry",
	HordePVP					= "Interface\\ICONS\\blry",
	AlliancePVPPoint			= "Interface\\ICONS\\lmryd",
	HordePVPPoint				= "Interface\\ICONS\\blryd",
	ArenaPVP					= "Interface\\ICONS\\jjc",
	ArenaPVPPoint				= "Interface\\ICONS\\jjd",
	EXP							= "Interface\\ICONS\\bigxp",
	XP							= "Interface\\ICONS\\xp",
	Tab							= "Interface\\ICONS\\tabb",
	CS1							= "Interface\\ICONS\\ces1",
	Loottoastatlas				= "Interface\\ICONS\\loottoastatlas",
	ToolBack					= "Interface\\MINIMAP\\TooltipBackdrop-Background",
	HB							= "Interface\\ICONS\\background_11",
	HBBK						= "Interface\\DialogFrame\\UI-DialogBox-Gold-Border",
	CCS							= "Interface\\ICONS\\scenariohordealliance",
	TSBK						= "Interface\\ICONS\\spellpush-frame-ysera",
	cuxiao1						= "Interface\\ICONS\\cuxiao1",	--热销
	cuxiao2						= "Interface\\ICONS\\cuxiao2",	--推荐
	cuxiao3						= "Interface\\ICONS\\cuxiao3",	--限时
	cuxiao4						= "Interface\\ICONS\\cuxiao4",	--新品
	cuxiao5						= "Interface\\ICONS\\cuxiao5",	--折扣
}

local TCoord = 
{
	--TooltipBackdrop-Background
	["Itemborder-gold"]={0.000976562, 0.0576172, 0.691406, 0.804688},
	["Itemborder-blue"]={0.000976562, 0.0576172, 0.574219, 0.6875, false, false},
	["Itemborder-glow"]={0.833008, 0.899414, 0.00195312, 0.134766, false, false},
	["Itemborder-green"]={0.000976562, 0.0576172, 0.808594, 0.921875, false, false},
	["Itemborder-orange"]={0.118164, 0.174805, 0.574219, 0.6875, false, false},
	["Itemborder-purple"]={0.176758, 0.233398, 0.574219, 0.6875, false, false},
	["Itemborder-heirloom"]={0.0595703, 0.116211, 0.574219, 0.6875, false, false},
	["Itemborder-artifact"]={0.901367, 0.958008, 0.00195312, 0.115234, false, false},
	--ScenarioHordeAlliance
	["AllianceScenario-TitleBG"]={ 0.00195312, 0.914062, 0.00195312, 0.277344},
	["AllianceScenario-TrackerHeader"]={0.00195312, 0.476562, 0.560547, 0.710938},
	["HordeScenario-TitleBG"]={0.00195312, 0.914062, 0.28125, 0.556641},
	["HordeScenario-TrackerHeader"]={0.480469, 0.955078, 0.560547, 0.710938},
}


--计算table有多少元素
function CountTB(tabledate)
	local count = 0
	if tabledate then
		for i,v in pairs(tabledate) do
		 count = count + 1
		end
	end
	return count
end


--给table的元素去重
function removeRepeateds(zz)
local aa={}
  for key,val in pairs(zz) do
     aa[val]=true
  end
local bb={}
  for key,val in pairs(aa) do
     table.insert(bb,key)                
  end
return bb
end


--[[比较两个时间，返回相差多少时间]]
function DONGD_timediff(long_time,short_time)
	local n_short_time,n_long_time,carry,diff = date('*t',short_time),date('*t',long_time),false,{}
	local colMax = {60,60,24,date('*t',time{year=n_short_time.year,month=n_short_time.month+1,day=0}).day,12,0}
	n_long_time.hour = n_long_time.hour - (n_long_time.isdst and 1 or 0) + (n_short_time.isdst and 1 or 0) -- handle dst
	for i,v in ipairs({'sec','min','hour','day','month','year'}) do
		diff[v] = n_long_time[v] - n_short_time[v] + (carry and -1 or 0)
		carry = diff[v] < 0
		if carry then
			diff[v] = diff[v] + colMax[i]
		end
	end
	return diff
end


--重置SubFrame内所有Button元素并设置全局名称为nil
function DONGD_ForSetSubFrameElement(t)
	for k,v in pairs(t) do
		for i,j in pairs(v) do
			--是需要重置所有Button的全局变量 但是包含ReqItemButton是table,需要在下面遍历
			if string.match(i,"Button") and not string.match(i,"ReqItemButton") then
				local name = j:GetName()
				setglobal(name,nil)
			end

			if string.match(i,"ReqItemButton") then
				for n,m in pairs(j) do
					local name = m:GetName()
						-- print("name = "..name )
						-- print("n = "..n)
						setglobal(name,nil)
				end
			end
 
		end
	end
end


--按字符拆分字符串并返回table
function Split(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 


--细节需要优化并处理
function Event(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
	
		local NewMsg = string.format("消息头 = %s 消息 = %s", h, msg)
		--print(NewMsg)
	
		--local list = Split(msg,"；：；")
		local list = Split(msg,";:;")	--节省传输过来的字符串大小
		
		if h == "DUEL_ACCEPT" and msg == "OK" then
			AcceptDuel();						--这个可以用核心函数搞定
			StaticPopup_Hide("DUEL_REQUESTED") 	--隐藏接受决斗框
		end
		
		--贩卖物品所有元素
		if h == "MALL_UI_FIRST_GROUP_DATA" then
				
			--print("msg的长度 = "..string.len(msg))	
			--print(list[1]) --实验正确
			--根据需要进行 tostring tonumber的转换
			local i = tonumber(list[1])
			--SubFrame[list[1]] = {}  --按序号定义成Table
			SubFrame[i] = {}  --按序号定义成Table
			local s1, s2, s3, s4, s5, s6, s7, s8, s9, s10 = strsplit(":", string.match(list[2], "item[%-?%d:]+")) --拆解传过来的itemlink
			
			print(type(s2))								--其结果为string  注意同意类型
			--SubFrame[list[1]].itemlink = list[2]			--贩售物品的link
			--SubFrame[list[1]].itemid = tonumber(s2)		--贩售物品的ID
			--SubFrame[list[1]].pclass = list[3]			--贩售物品所属物品分类父类
			--SubFrame[list[1]].sclass = list[4]			--贩售物品所属物品分类子类
			--SubFrame[list[1]].reqvip = list[5]			--购买贩售物品所需vip
			--SubFrame[list[1]].reqmoney = list[6]			--购买贩售物品所需金币
			--SubFrame[list[1]].reqexp = list[7]			--购买贩售物品所需经验值
			--SubFrame[list[1]].reqhonor = list[8]			--购买贩售物品所需荣誉值
			--SubFrame[list[1]].reqarena = list[9]			--购买贩售物品所需竞技点
			--SubFrame[list[1]].IsHomeShow = list[10]		--贩售物品是否在主页展示
			--SubFrame[list[1]].PromotionType = list[11]	--贩卖物品促销类型
			--SubFrame[list[1]].Ppos = list[12]				--贩售物品在父类窗口显示位置
			--SubFrame[list[1]].Spos = list[13]				--贩售物品在子类窗口显示位置
			--SubFrame[list[1]].Hpos = list[14]				--贩售物品在主页显示的位置
			
			SubFrame[i].id = tonumber(list[1])				--贩售物品的唯一id
			SubFrame[i].itemlink = list[2]					--贩售物品的link
			SubFrame[i].itemid = tonumber(s2)				--贩售物品的ID
			SubFrame[i].pclass = list[3]					--贩售物品所属物品分类父类
			SubFrame[i].sclass = list[4]					--贩售物品所属物品分类子类
			SubFrame[i].reqvip = list[5]					--购买贩售物品所需vip
			SubFrame[i].reqmoney = list[6]					--购买贩售物品所需金币
			SubFrame[i].reqexp = list[7]					--购买贩售物品所需经验值
			SubFrame[i].reqhonor = list[8]					--购买贩售物品所需荣誉值
			SubFrame[i].reqarena = list[9]					--购买贩售物品所需竞技点
			SubFrame[i].IsHomeShow = list[10]				--贩售物品是否在主页展示
			SubFrame[i].PromotionType = list[11]			--贩卖物品促销类型
			SubFrame[i].Ppos = tonumber(list[12])			--贩售物品在父类窗口显示位置
			SubFrame[i].Spos = tonumber(list[13])			--贩售物品在子类窗口显示位置
			SubFrame[i].Hpos = tonumber(list[14])			--贩售物品在主页显示的位置
			SubFrame[i].ElementNum = tonumber(list[15])		--贩售物品贩卖信息元素的个数
			local Dis = tonumber(list[16])/100				--折扣百分比
			SubFrame[i].Dis = Dis
			SubFrame[i].s_time = tonumber(list[17])			--限时活动开始时间戳
			SubFrame[i].e_time = tonumber(list[18])			--限时活动结束时间戳
			SubFrame[i].reqjf = list[19]					--购买贩售物品所需积分
			SubFrame[i].DELAY = 1							--1秒延迟
			SubFrame[i].IsDis = false
			
			
			SubFrame[i].SubFramSellInfo	= {}				--初始化一定要在这里,初始化贩卖物品所需消耗的字符串信息 "money" 依此返回对应窗口
			SubFrame[i].reqitemid = {}						--初始化一定要在这里
			SubFrame[i].reqitemlink = {}					--初始化一定要在这里
			SubFrame[i].reqitemval = {}						--初始化一定要在这里
			SubFrame[i].ReqItemButton = {}					--初始化一定要在这里,初始化贩卖物品所需物品按钮table
			SubFrame[i].ReqItemFont	= {}					--初始化一定要在这里,初始化贩卖物品所需物品数量文本table
			
			
			--print("SubFrame["..list[1].."].itemid = "..list[2])
			-- print("SubFrame["..list[1].."].itemlink = "..SubFrame[i].itemlink)
			-- print("SubFrame["..list[1].."].itemid = "..SubFrame[i].itemid)
			-- print("SubFrame["..list[1].."].pclass = "..SubFrame[i].pclass)
			-- print("SubFrame["..list[1].."].sclass = "..SubFrame[i].sclass)
			-- print("SubFrame["..list[1].."].reqvip = "..SubFrame[i].reqvip)
			-- print("SubFrame["..list[1].."].reqmoney = "..SubFrame[i].reqmoney)
			-- print("SubFrame["..list[1].."].reqexp = "..SubFrame[i].reqexp)
			-- print("SubFrame["..list[1].."].reqhonor = "..SubFrame[i].reqhonor)
			-- print("SubFrame["..list[1].."].reqarena = "..SubFrame[i].reqarena)
			-- print("SubFrame["..list[1].."].IsHomeShow = "..SubFrame[i].IsHomeShow)
			-- print("SubFrame["..list[1].."].PromotionType = "..SubFrame[i].PromotionType)
			-- print("SubFrame["..list[1].."].Ppos = "..SubFrame[i].Ppos)
			-- print("SubFrame["..list[1].."].Spos = "..SubFrame[i].Spos)
			-- print("SubFrame["..list[1].."].Hpos = "..SubFrame[i].Hpos)
			-- print("SubFrame["..list[1].."].ElementNum = "..SubFrame[i].ElementNum)
			-- print("SubFrame["..list[1].."].Dis = "..SubFrame[i].Dis)
			-- print("SubFrame["..list[1].."].s_time = "..SubFrame[i].s_time)
			-- print("SubFrame["..list[1].."].e_time = "..SubFrame[i].e_time)
			
		end
		
		-- next(SubFrame[list[1]]) == nil 
		
		if h == "MALL_UI_SECOND_GROUP_DATA" then
			--print("msg的长度 = "..string.len(msg))
			--print("MALL_UI_SECOND_GROUP_DATA = "..msg)
			local i = tonumber(list[1])	--贩售物品的序号
			local j = tonumber(list[2])	--传过来的table的key(下标,从1开始)
			
			if list[3] ~= nil then
			local ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, ss9, ss10 = strsplit(":", string.match(list[3], "item[%-?%d:]+")) --拆解传过来的itemlink
				SubFrame[i].reqitemid[j] = tonumber(ss2)	--购买贩售物品所需消耗的物品的ID
				SubFrame[i].reqitemlink[j] = list[3]		--购买贩售物品所需消耗的物品的link
				SubFrame[i].reqitemval[j] = list[4]			--购买贩售物品所需消耗物品的数量
				
				-- print("SubFrame["..list[1].."].reqitemid["..list[2].."] = "..SubFrame[i].reqitemid[j])
				-- print("SubFrame["..list[1].."].reqitemlink["..list[2].."] = "..SubFrame[i].reqitemlink[j])
				-- print("SubFrame["..list[1].."].reqval["..list[2].."] = "..SubFrame[i].reqitemval[j])
			end
		end
		
		--MALL_UI_FOURTH_GROUP_DATA 
		
		
		--[===[
		if h == "MALL_UI_SECOND_GROUP_DATA" then
			
			if list[2] ~= nil then
			SubFrame[list[1]].reqitem1 = list[2]
			SubFrame[list[1]].reqval1 = list[3]
			print("SubFrame["..list[1].."].reqitem1 = "..SubFrame[list[1]].reqitem1)
			print("SubFrame["..list[1].."].reqval1 = "..SubFrame[list[1]].reqval1)
			end 
			
			if list[4] ~= nil then
			SubFrame[list[1]].reqitem2 = list[4]
			SubFrame[list[1]].reqval2 = list[5]
			print("SubFrame["..list[1].."].reqitem2 = "..SubFrame[list[1]].reqitem2)
			print("SubFrame["..list[1].."].reqval2 = "..SubFrame[list[1]].reqval2)
			end
			
			if linst[6] ~= nil then
			SubFrame[list[1]].reqitem3 = list[6]
			SubFrame[list[1]].reqval3 = list[7]
			print("SubFrame["..list[1].."].reqitem3 = "..SubFrame[list[1]].reqitem3)
			print("SubFrame["..list[1].."].reqval3 = "..SubFrame[list[1]].reqval3)
			end
			
			if list[8] ~= nil then
			SubFrame[list[1]].reqitem4 = list[8]
			SubFrame[list[1]].reqval4 = list[9]
			print("SubFrame["..list[1].."].reqitem4 = "..SubFrame[list[1]].reqitem4)
			print("SubFrame["..list[1].."].reqval4 = "..SubFrame[list[1]].reqval4)
			end
			
			if list[10] ~= nil then
			SubFrame[list[1]].reqitem5 = list[10]
			SubFrame[list[1]].reqval5 = list[11]
			print("SubFrame["..list[1].."].reqitem5 = "..SubFrame[list[1]].reqitem5)
			print("SubFrame["..list[1].."].reqval5 = "..SubFrame[list[1]].reqval5)
			end
			
		end
		
		
		local Message = ""
		for k, v in pairs(list) do
			Message = Message.."+"..v 
		end
		print(Message)
		]===]
		
		if h == "MALL_UI_THIRD_GROUP_DATA" then
			--print("MALL_UI_THIRD_GROUP_DATA is msg"..msg)
			--local list = Split(msg,"；：；")
			
			--table.insert(RMallButtonsInfo,list[1])
			RMallButtonsInfo[list[1]] = 1			--用于存储父级类别的名称,其值没有意义不会被调用
			
			if RMallClass[list[1]] == nil then
				RMallClass[list[1]] = {}			--以父级类别名称为key,其值为Table
				
				table.insert(RMallClass[list[1]],list[2])	--将属于本父级类别的子类别全部存储在以此父级类别为key的Table中
			else
				table.insert(RMallClass[list[1]],list[2])
			end
		end
		
		--MALL_UI_FOURTH_GROUP_DATA 
		
		if h == "MALL_ALL_DATA" and msg == "OK" then
		 
			IsOK = true

		end
		 
		 --由此证明服务器过来的是可以显示的,核心内写图片路径需要"\\"双斜杠，而读取数据库则"\"单斜杠就可以了
		 if h == "MALL_UI_T1" then
			T1 = msg
			print(type(T1))
			print(T1)
		 end
		 
		 if h == "MALL_DATA_RESET" and msg == "OK" then
		 
			print("开始重置了")
			--如果开始重置 则不可用_G 来使用全局变量
			--还需要将下面的表给清空重置
			-- 这里需要重新写一下重置 以放table泄漏内存
			LMall:Hide()
			setglobal(LMall:GetName(),nil)
			setglobal(RMall:GetName(),nil)
			setglobal(LScroll:GetName(),nil)
			setglobal(LScrollChild:GetName(),nil)
			MenuOffButton:Hide()
			MenuOnButton:Show()
			DONGD_ForSetSubFrameElement(SubFrame)
			SubFrame = nil
			SubFrame = {}
			RMallButtonsInfo = nil
			RMallButtonsInfo = {}
			RMallButtons = nil
			RMallButtons = {}
			RMallClass = nil
			RMallClass = {}
			IsOK = false
			IsAllCreate = false
			print("到这来了嘛？")
		 end
    end
end

local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("CHAT_MSG_ADDON")
MsgReceivers:SetScript("OnEvent", Event)


--
function pp(t)

 for k,v in pairs(t) do
	for i,j in pairs(t[k]) do
		print("Pclass = "..k.."; Sclass = "..j)
	end
 end

end


--获取相对窗口对应复杂版
function GetSubFrameC(Category,pos)
	
	if Category == "ParentC" then
		for k,v in pairs(SubFrame) do	--这里的k是list[1],也就是传过来的序号 
			if SubFrame[k].Ppos == (pos-3) then
				if SubFrame[k].PFrame ~= nil then
				return SubFrame[k].PFrame
				end
			end
		end
	end
	
	if Category == "SubC" then
		for k,v in pairs(SubFrame) do	--这里的k是list[1],也就是传过来的序号 
			if SubFrame[k].Spos == (pos-3) then
				if SubFrame[k].SFrame ~= nil then
				return SubFrame[k].SFrame
				end
			end
		end
	end
	
	if Category == "Home" then
		--排版
	end
	

end


--获取相对窗口对应精简版
function GetSubFrameS(Category,pos)
	
	if Category == "ParentS" then
		for k,v in pairs(SubFrame) do	--这里的k是list[1],也就是传过来的序号 
			if SubFrame[k].Ppos == (pos-3) then
				if SubFrame[k].Frame ~= nil then
				return SubFrame[k].Frame
				end
			end
		end
	end
	
	if Category == "SubS" then
		for k,v in pairs(SubFrame) do	--这里的k是list[1],也就是传过来的序号 
			if SubFrame[k].Spos == (pos-3) then
				if SubFrame[k].Frame ~= nil then
				return SubFrame[k].Frame
				end
			end
		end
	end
	
	if Category == "Home" then
		--排版
	end
	

end


--获取贩售物品信息相对窗口精简版
function GetSubFrameSellInfoFrameS(i,j)
	local S = SubFrame[i] -- i是物品的序号即是唯一值
		
		if j == 0 then
			return nil
		end
	
	
	-- 这里的j 应该是大于1的数 减去1
	for k,v in pairs(S.SubFramSellInfo) do
		if k == j then
			if v == "money" then
				return S.ReqMoneyButton
			elseif v == "exp" then
				return S.ReqExpButton
			elseif v == "honor" then
				return S.ReqHonorButton
			elseif v == "arena" then
				return S.ReqArenaButton
			elseif v == "item1" then
				return S.ReqItemButton[1]
			elseif v == "item2" then
				return S.ReqItemButton[2]
			elseif v == "item3" then
				return S.ReqItemButton[3]
			elseif v == "item4" then
				return S.ReqItemButton[4]
			elseif v == "item5" then
				return S.ReqItemButton[5]
			elseif v == "jifen" then
				return S.ReqJFButton
			end
		end
	end
end


--构建商城主窗口 --细节需要优化
function DONGD_CreateMallParentFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)
		
local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	
	
	f:SetSize(Length, Width)
	
	
	
	if BgFile ~= ""  and string.match(Name,"Scroll") == nil then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
	end
		
	if Name == "RMall" then
		f:SetPoint("LEFT",LMall,"RIGHT",Ofsx,Ofsy)
	else
		f:SetPoint(Point,Ofsx,Ofsy)
	end
	
	if string.match(Name,"Scroll") ~= nil then
		local ScrollBcakdrop = f:CreateTexture("$parent".."ScrollBcakdrop","BACKGROUND",nil)
		ScrollBcakdrop:SetAllPoints(f)
		--ScrollBcakdrop:SetPoint("CENTER")
		ScrollBcakdrop:SetTexture(T["ParentFrameBackgroundLeft"])
		ScrollBcakdrop:SetTexCoord(0.3,0.8,0.3,0.8)
		--ScrollBcakdrop:SetTexture(1.0,1.0,1.0)
	end
		
	--只有主窗口才能移动
	if ParentFrame == UIParent then
		f:RegisterForDrag("LeftButton")
		f:SetToplevel(true)
		f:SetClampedToScreen(true)
		f:SetMovable(true)
		f:EnableMouse(true)
		f:SetScript("OnDragStart", f.StartMoving)
		f:SetScript("OnHide", f.StopMovingOrSizing)
		f:SetScript("OnDragStop", f.StopMovingOrSizing)
	end
	
	if ParentFrame == UIParent then
		f:Hide()
	end
	
	--if ParentFrame == LMall then
		--f:SetBackdropColor(255/255,255/255,255/255,0.25)
		--f:Hide()
	--end
	
	if string.match(Name,"Time") ~= nil then
	f:SetBackdropColor(255/255,255/255,255/255,0.15)
	end
		
	return f
end


--构建商城小窗口
function DONGD_CreateMallSubFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile,object,number,ShowCategory) --
		
local f = CreateFrame(FrameType,"$parent"..Name,ParentFrame,InheritsFrame)
	
	f:SetSize(Length, Width)
	
	if ShowCategory == "ALL" then
		if number <= 3 then
			if number == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif number == 2 then
				f:SetPoint("TOP",0,0)
			elseif number == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			f:SetPoint("TOP",object[(number-3)].TestFrame,"BOTTOM",0,25)	
		end
	end
	
	if ShowCategory == "ParentC" then	--这是未完成的复杂版本的 -->DONGD_CreateMallALLSubFrameOfCategory_Complicated
		local i = object[number].Ppos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local PFrame = GetSubFrameC(ShowCategory,i)
			f:SetPoint("TOP",PFrame,"BOTTOM",0,25)	
		end
	end
	
	if ShowCategory == "SubC" then		--这是未完成的复杂版本的 -->DONGD_CreateMallALLSubFrameOfCategory_Complicated
		local i = object[number].Spos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local SFrame = GetSubFrameC(ShowCategory,i)
			f:SetPoint("TOP",SFrame,"BOTTOM",0,25)	
		end
	end
	
	if ShowCategory == "ParentS" then	--这里对应的是精简版本的 -->DONGD_CreateMallALLSubFrameOfCategory_Simplify
		local i = object[number].Ppos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local Frame = GetSubFrameS(ShowCategory,i)
			f:SetPoint("TOP",Frame,"BOTTOM",0,0)	
		end
	end
	
	if ShowCategory == "SubS" then		--这里对应的是精简版本的 -->DONGD_CreateMallALLSubFrameOfCategory_Simplify
		local i = object[number].Spos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local Frame = GetSubFrameS(ShowCategory,i)
			f:SetPoint("TOP",Frame,"BOTTOM",0,0)	
		end
	end
	
										--两者之间，精简版本或许会引发内存泄漏，复杂版本想采用的是创建一次以后，只进行隐藏和显示的操作并不会再次创建(在线加载除外)
	if BgFile ~= "" then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 10, insets = { left =0, right = 0, top = 5, bottom = 5 }});
	end
		

	f:SetBackdropColor(255/255,255/255,255/255,0.25)
	
	--f:Hide()
	return f
	
end


--构建普通按钮
function DONGD_CreateButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,IsHideButton,IsScrollB)
			
		local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
		if BgFile ~= "" then 
			Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
		end
		
		if IsScrollB then
			if string.match(Name,"DownButton") ~= nil then
			Button:SetNormalTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Down")
			Button:SetDisabledTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Disabled")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Highlight","ADD")
			else
			Button:Disable()
			Button:SetNormalTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Down")
			Button:SetDisabledTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Disabled")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Highlight","ADD")
			end
		end
		
		Button:SetSize(Length, Width)
		
		
		if Name == "HomePage" then
		Button:SetPoint("TOPLEFT", ParentFrame,"TOPRIGHT",Ofsxm, Ofsym)
		else
		Button:SetPoint(Point, Ofsxm, Ofsym)
		end
		
		--Button:SetPoint(Point, Ofsxm, Ofsym)

		Button:EnableMouse(true)
		
		if BgFile == T["ItemClassButton"] then
			Button:SetHighlightTexture(T["ButtonHighlighLight2"],"ADD")
		elseif  Text == "" and BgFile == "" and Name == "CloseButton" then
			Button:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
		else
			Button:SetHighlightTexture(T["ButtonHighlighLight1"],"ADD")
		end
		
		
		if string.match(Name,"TT") ~= nil then
		
		local tex = DONGD_CreateTex(Button,"LHTex","ARTWORK",nil,"TOP",nil,"",0,0,T["CCS"],Length,Width)
		
		--LHTex1 = DONGD_CreateTex(LMall,"LMall".."LHTex","ARTWORK",nil,"TOP",nil,"",-5,-45,T["HB"],820,220)
		tex:SetTexCoord(0.00195312, 0.476562, 0.560547, 0.710938)
		end
		
		
		
		local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
		
		ButtonFontStr:SetFont(T["Font"], 15)	--需要设置字体
		
		ButtonFontStr:SetAllPoints(Button)
		
		ButtonFontStr:SetText(Text)
		
		--if normaltexture ~= "" then
			--Button:SetNormalTexture(normaltexture)
		--end
		
		if IsHideButton == true then
			Button:Hide()
		end
		
		return Button;
end


--构建贩卖物品窗口内按钮
function DONGD_CreateButtonInSubFrame(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,BgFile,...)
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	Button:SetSize(Length, Width)
	
	Sell,ElementNum = ...
	
	if not ElementNum then
		if not Sell then
			if relativeFrame == nil and relativePoint == "" then
				Button:SetPoint(Point, Ofsxm, Ofsym)
			else
				Button:SetPoint(Point,relativeFrame,relativePoint,Ofsxm, Ofsym)
			end	
		elseif Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	elseif ElementNum > 5 then
		if ElementNum > 6 then
			if Sell == 1 then
				Button:SetPoint("LEFT", 5, 25)
			elseif Sell == 6 then
				Button:SetPoint("LEFT", 130, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		else
			if Sell == 1 then
				Button:SetPoint("LEFT", 5, 25)
			elseif Sell == 4 then
				Button:SetPoint("LEFT", 130, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		end
	else
		if Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	end
	
	if string.match(Name,"SellButton") then
		local Ntex = Button:CreateTexture("$parent".."Ntex","ARTWORK",nil)
		local Ptex = Button:CreateTexture("$parent".."Ptex","ARTWORK",nil)
		local Dtex = Button:CreateTexture("$parent".."Dtex","ARTWORK",nil)
		local Htex = Button:CreateTexture("$parent".."Htex","ARTWORK",nil)
		
		Ntex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Up")
		Ptex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Down")
		Dtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Disabled")
		Htex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Highlight")
		
		Ntex:SetTexCoord(0,0.578125,0,0.75)
		Ptex:SetTexCoord(0,0.578125,0,0.75)
		Dtex:SetTexCoord(0,0.578125,0,0.75)
		Htex:SetTexCoord(0,0.625,0,0.685)
		
		Ntex:SetAllPoints(Button)
		Ptex:SetAllPoints(Button)
		Dtex:SetAllPoints(Button)
		Htex:SetAllPoints(Button)
		
		Button:SetNormalTexture(Ntex)
		Button:SetPushedTexture(Ptex)
		Button:SetDisabledTexture(Dtex)
		Button:SetHighlightTexture(Htex)
		else
		Button:SetHighlightTexture(T["ButtonHighlighLight1"])
	end
	

	Button:EnableMouse(true)	
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	--需要设置字体
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)
	
	ButtonFontStr:SetTextColor(1.0,0.843,0)
	
	return Button
end


--构建类别按钮
function DONGD_CreateClassButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,num,object)
		
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	--print("num = "..num)
	
	Button:SetSize(Length, Width)
	
	if num == 1 then 
		Button:SetPoint("TOP",Ofsxm,Ofsym)
	else
		Button:SetPoint("TOP",object[num - 1],"BOTTOM",0,0)
	end
	
	Button:SetHighlightTexture(T["ButtonHighlighLight2"],"ADD") --设置高亮
	
	Button:EnableMouse(true)
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	--需要设置字体
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)

	return Button;

end


--构建滚动窗口	--细节需要优化
function DONGD_CreateScrollFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)

	local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	
	f:SetSize(Length,Width)
	
	f:SetPoint(Point,ParentFrame,"CENTER",Ofsx,Ofsy)
	--f:SetPoint(Point,Ofsx,Ofsy)
	
	if BgFile ~= "" then
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	else
		local textest = f:CreateTexture("$parent".."Tex","BACKGROUND",nil)
		--textest:SetTexture(T["ParentFrameBackgroundLeft"])
		--textest:SetTexCoord(0.3,0.8,0.3,0.8)
		textest:SetAllPoints(f)
		textest:SetTexture(0.0,0.0,0.0,1.0)
	end

	return f
end


--构建滑块及其贴图
function DONGD_CreateSliderFrame(FrameType,ParentName,ParentFrame,InheritsFrame,Orientation,Type)

	local Slider = CreateFrame(FrameType,"$parent".."Slider",ParentFrame,InheritsFrame)
	Slider:SetOrientation(Orientation)
	Slider:SetMinMaxValues(0,100)
	Slider:SetValue(0)
	Slider:SetValueStep(1.0)
	if Type == "L" then
	Slider:SetPoint("LEFT",ParentFrame,"RIGHT",-38,0)
	else
	Slider:SetPoint("LEFT",ParentFrame,"RIGHT",-28,-12)
	end
	Slider:SetSize(25,630)
	local tt = Slider:CreateTexture("tt","HIGHLIGHT",nil)
	tt:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
	tt:SetSize(32, 32)
	local Slidertex = Slider:SetThumbTexture(tt)
	
	return Slider,Slidertex
	
end


--构建滑块条边框贴图
function DONGD_CreateScrollTex(Frame,Name,Layer,InheritsFrame,High,Type)
	
	local ScrollTexTop = Frame:CreateTexture("$parent"..Name.."ScrollTexTop",Layer,InheritsFrame)
		-- ScrollTexTop:SetPoint("TOPRIGHT",-10,-20)
		ScrollTexTop:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexTop:SetSize(31,256)
		ScrollTexTop:SetTexCoord(0,0.484375,0,1.0)
	
	local ScrollTexMiddle = Frame:CreateTexture("$parent"..Name.."ScrollTexMiddle",Layer,InheritsFrame)
		-- ScrollTexMiddle:SetPoint("TOPRIGHT",-10,-20)
		ScrollTexMiddle:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexMiddle:SetSize(31,600)
		ScrollTexMiddle:SetTexCoord(0,0.484375,0.2,1.0)
		
	local ScrollTexBottom = Frame:CreateTexture("$parent"..Name.."ScrollTexBottom",Layer,InheritsFrame)
		-- ScrollTexBottom:SetPoint("BOTTOMRIGHT",-10,20)
		ScrollTexBottom:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexBottom:SetSize(31,106)
		ScrollTexBottom:SetTexCoord(0.515625,1.0,0,0.4140625)
		
		
		if Type == "L" then
			ScrollTexTop:SetPoint("TOPRIGHT",-10,-20)
			ScrollTexMiddle:SetPoint("TOPRIGHT",-10,-20)
			ScrollTexBottom:SetPoint("BOTTOMRIGHT",-10,20)
		else
			ScrollTexTop:SetPoint("TOPRIGHT",0,0)
			ScrollTexMiddle:SetPoint("TOPRIGHT",0,0)
			ScrollTexBottom:SetPoint("BOTTOMRIGHT",0,0)
		end
		
		return ScrollTexTop,ScrollTexMiddle,ScrollTexBottom
end


--设置大图标中要显示的小图标的坐标系
function DONGD_TexSetTexCoord(Tex,Coord)

	Tex:SetTexCoord(Coord[1],Coord[2],Coord[3],Coord[4])
	
end


--构建贴图
function DONGD_CreateTex(Frame,Name,Layer,InheritsFrame,Point,relativeFrame,relativePoint,ofsx,ofsy,filename,sizex,sizey)

	local tex = Frame:CreateTexture("$parent"..Name,Layer,InheritsFrame)
	
	tex:SetTexture(filename)
	
	tex:SetSize(sizex,sizey)	--这个是必须要写的选项
	
	if relativeFrame ~= nil and relativePoint ~= "" then
		tex:SetPoint(Point,relativeFrame,relativePoint,ofsx,ofsy)	
	elseif ofsx == 0 and ofsy == 0 then
		tex:SetAllPoints(Frame)
	else
		tex:SetPoint(Point, ofsx, ofsy)	
	end
	
	return tex

end


--构建显示文本
function DONGD_CreateFontString(Frame,Name,layer,Inherits,Fonts,Fsize,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,Msg,rgb,IsIf)
	local fs
	
	if layer ~= "" and Inherits ~= "" then
		fs = Frame:CreateFontString(Name,layer,Inherits)
	else
		fs = Frame:CreateFontString(Name)
	end
	
	if Fonts == "" then 
		fs:SetFontObject(GameFontNormalLarge)
		else
		fs:SetFont(Fonts, Fsize)
	end
	
	if rgb ~= "" then
		local r,g,b = self:ColorPicker(strsplit(",",rgb))
		fs:SetTextColor(r,g,b)
	end	
	
	if IsIf then
	fs:SetSize(200,30) 			--这个设定文本框大小
	fs:SetJustifyH("CENTER")
	else
	fs:SetJustifyH("RIGHT")		--这个设置水平对齐
	end
	
	--fs:SetJustifyH("RIGHT")	--这个设置水平对齐
	fs:SetJustifyV("TOP")		--这个设置垂直对齐
	
	if relativePoint ~= nil then
	fs:SetPoint(Point,relativeFrame,relativePoint,Ofsxm,Ofsym)
	else
	fs:SetPoint(Point,Ofsxm,Ofsym)
	end
	
	fs:SetText(Msg)
	
	return fs
	
end


--批量处理脚本
function PrepareScript(object, text, script, itemlink,IsScrollButton)
	
	  if text then	--如果text存在 那么显示在对象的右边并对齐(ANCHOR_RIGHT)
        object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
	  end
	  	  
	  if type(script) == "function" then	--type(script) 返回script的类型 以字符串形式
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end

end


--处理按钮脚本专用
function HandleButtonScript(Button,Type)

	if Type == "MenuOnButton" then
	
		if IsOK == false then
			print("请求发送数据")
			SendAddonMessage("MALLUI","All","GUILD", UnitName("player"))
			--SendAddonMessage("MALLUI","CLASS","GUILD", UnitName("player"))
			return
		end
		
		if IsAllCreate == false then
			print("开始初始化商城")
			DONGD_OnInitialize()
			IsAllCreate = true
		end
		
		MenuOnButton:Hide()		
		
		MenuOffButton:Show()
		
		LMall:Show()
		
		
	end

	if Type == "MenuOffButton" then
	
		MenuOnButton:Show()
		
		MenuOffButton:Hide()
		
		LMall:Hide()
		
	end

	if Type == "HomePage" then
		--显示主页内容
	end
	
	if Type == "LScrollUpButton" then
		--print("LSlider:GetValue() = "..tostring(LSlider:GetValue()))
		--print("LSlider:GetHeight() / 20 = "..tostring(LSlider:GetHeight() / 20))
		--print("LSlider:GetValue() - (LSlider:GetHeight() / 2 = "..tostring(LSlider:GetValue() - (LSlider:GetHeight() / 2)))
		--LSlider:SetValue(LSlider:GetValue()-5)
		LSlider:SetValue(math.ceil(LSlider:GetValue()-(LSlider:GetHeight() / 60)))
		
		PlaySound("UChatScrollButton")
	end
	
	if Type == "LScrollDownButton" then
		--LSlider:SetValue(LSlider:GetValue()+(LSlider:GetHeight() / 60))
		LSlider:SetValue(math.ceil(LSlider:GetValue()+(LSlider:GetHeight() / 60))) --设置滑动条的位置(其位置影响滚动子窗口) 取整数 
		PlaySound("UChatScrollButton")
	end
	
	if Type == "RScrollUpButton" then
	
		RSlider:SetValue(math.ceil(RSlider:GetValue()-(RSlider:GetHeight() / 60)))
		
		PlaySound("UChatScrollButton")
	end
	
	if Type == "RScrollDownButton" then
	
		RSlider:SetValue(math.ceil(RSlider:GetValue()+(RSlider:GetHeight() / 60))) --设置滑动条的位置(其位置影响滚动子窗口) 取整数 
		
		PlaySound("UChatScrollButton")
	end	
	
	if Type == "HomePage" then
		print("开始创建F1")
		local F1 = DONGD_CreateMallParentFrame("Frame","F1",LMall,nil,300,300,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
		F1:SetFrameStrata("TOOLTIP") --可以让窗口在最前面
		print("创建F1完毕")
		print("开始输出数组")
		pp(RMallClass)
		print("输出数组完毕")
	end
	
	if string.match(Type,"RMallButton") then
		if Button.Type == "P" then					--点击的按钮是父级类别按钮
			LScroll:Show()							--滚动窗口显示
			LHTex:Hide()							--关掉测试海报窗口
			--以下操作是显示类别按钮的
			local showsub = Button.ShowSub
			local showsubframe = Button.ShowSubFrame
			local name = Button.Name
			
			--重置所有按钮
			for k,v in pairs(RMallButtons) do		
				local name = v:GetName()
				setglobal(name,nil)	
				v:Hide()							--所有按钮隐藏(父子按钮)
				v = nil								--所有按钮清空
			end										--先把这做完 再来写另一种实现的办法
			
			--下面的操作是点击父类按钮的显示切换	--这里可能有table内存泄漏风险
			if not showsub then						--当showsub为false的时候,即是现在只有父级分类按钮没有显示任何子按钮的情况下
			DONGD_CreateRMallAllClassButton(name) 	--按照传过来的父级分类按钮的名字创建对应的子级分类按钮以及全部父级按钮
			else									--当showsub为true的时候,即是当前父级分类按钮有在显示其包含的所有子按钮的情况下
			DONGD_CreateRMallAllClassButton()		--创建所有父级分类按钮(不含任何子级分类按钮),并初始化所有父级分类按钮的showsub为false
			end
			
			
			DONGD_ForSetSubFrameElement(SubFrame)
			--以下操作是显示贩卖物品的
			for i,v in pairs(SubFrame) do  						--在这里就开始清空所有的贩卖物品窗口了 是适用于精简版
				if SubFrame[i].Frame ~= nil then				--如果整个窗口存在
				-- DONGD_ForSetSubFrameElement(SubFrame)		--重置贩卖窗口内所有按钮的办法,暂不能修复
					local name = SubFrame[i].Frame:GetName()	--先获取需要重置的贩卖窗口的正确全局变量名
					setglobal(name,nil)							--这个能解决table溢出
					SubFrame[i].Frame:Hide()					--隐藏这个窗口
					SubFrame[i].Frame = nil						--清空这个窗口
				end
			end
			
			LSlider:SetValue(0)	--这个要在这里才会触发 --每次打开新的窗口 都拉回顶部
			
			DONGD_CreateMallALLSubFrameOfCategory_Simplify(name,nil)	--创建父分类所有贩卖物品窗口的元素
			
			DONGD_HandleHideLSiderAllElement(true,"L")			--显示滚动条及滚动窗口
			
			--还有按钮高亮没写
			
		end
		
		if Button.Type == "S" then
			local name = Button.Name							--子类的名称
			local pname = Button.PName							--子类按钮所属父类的名称
			DONGD_ForSetSubFrameElement(SubFrame)
			for i,v in pairs(SubFrame) do  						--在这里就开始清空所有的贩卖物品窗口了 是适用于精简版
				if SubFrame[i].Frame ~= nil then				--如果整个窗口存在
				-- DONGD_ForSetSubFrameElement(SubFrame)		--重置贩卖窗口内所有按钮的办法,暂不能修复
				local name = SubFrame[i].Frame:GetName()		--先获取需要重置的贩卖窗口的正确全局变量名
					setglobal(name,nil)							--这个能解决table溢出	
					SubFrame[i].Frame:Hide()					--隐藏这个窗口
					SubFrame[i].Frame = nil						--清空这个窗口
				end
			end
			
			LSlider:SetValue(0)	--这个要在这里才会触发 --每次打开新的窗口 都拉回顶部
			
			DONGD_CreateMallALLSubFrameOfCategory_Simplify(pname,name)	--创建父分类所有贩卖物品窗口的元素
		
			--还有按钮高亮没写
		end
	end
	
end


--初始化商城菜单按钮
function DONGD_InitMallMenuButtons()

	--商城按钮
	PrepareScript(MenuOnButton,	"打开商城",		function()	HandleButtonScript(MenuOnButton,"MenuOnButton") end)
	 
	 --关闭商城按钮
	PrepareScript(MenuOffButton,	"关闭商城",		function()	HandleButtonScript(MenuOffButton,"MenuOffButton") end)

end


--初始化商城标签按钮
function DONGD_InitMallTabButton()

	--主页按钮
	PrepareScript(HomePage,	"主页",		function()	HandleButtonScript(HomePage,"HomePage") end)

end


--初始化商城分类按钮
function DONGD_InitMallClassButton()

	print("开始初始化类别按钮脚本")
	local i = 1
	--分类按钮的脚本注册
	for k,v in pairs(RMallButtons) do
		PrepareScript(v,	"",		function()	HandleButtonScript(v,v:GetName())	end)
		--i = i + 1
	end
	print("初始化类别按钮脚本完毕")
	
end


--初始化商城滚动窗口UP/DOWN按钮
function DONGD_InitMallScrollButton()

	--主窗口(左边)UP按钮
	PrepareScript(LScrollUpButton,	"",		function()	HandleButtonScript(LScrollUpButton,"LScrollUpButton")	end)
	
	--主窗口(左边)Down按钮
	PrepareScript(LScrollDownButton,	"",		function()	HandleButtonScript(LScrollDownButton,"LScrollDownButton") end)
	
	--
	PrepareScript(RScrollUpButton,	"",		function()	HandleButtonScript(LScrollUpButton,"RScrollUpButton")	end)
	
	--
	PrepareScript(RScrollDownButton,	"",		function()	HandleButtonScript(LScrollDownButton,"RScrollDownButton") end)

end


--初始化商城滚动窗口滑块
function DONGD_InitMallSlider()

	
	LSlider:SetScript("OnValueChanged",
	
	function(self,offset) 
	
	--子窗口的大小改变后 这里也需要改变 这里是设置滑动条偏移值
	LScroll:SetVerticalScroll(math.ceil((LScrollChild:GetHeight()-LScroll:GetHeight())/100 * self:GetValue()))
	lmin, lmax = self:GetMinMaxValues();
	-- print("LScroll:GetVerticalScroll() = "..(LScroll:GetVerticalScroll()))
	-- print("LSlider:GetValue() = "..math.ceil(self:GetValue()))
	-- print("LScrollChild:GetHeight()-LScroll:GetHeight() = "..math.ceil((LScrollChild:GetHeight()-LScroll:GetHeight())))
	-- print("LScroll:GetValue() = "..LScroll:GetHeight())
		
		--设定Up按钮可点击/不可点击状态
		if ( offset == 0 ) then
			LScrollUpButton:Disable();
		else
			LScrollUpButton:Enable();
		end
		
		--设定Down按钮可点击/不可点击状态
		if (math.ceil(self:GetValue() - lmax)) == 0 then
			LScrollDownButton:Disable();
		else
			LScrollDownButton:Enable();
		end
	
	end)
	
	LScroll:SetScript("OnMouseWheel", 
		function(self,val)  

			--往上滑 val是正数	方向：上，左
			--往下滑 val是负数	方向：下，右
			
			local s1 = LSlider	-- 滚动条
			--获取最小最大值
			local minv,maxv = s1:GetMinMaxValues()
			--获取滚动条当前值
			local cv = s1:GetValue()
			--计算新数值
			local nv = cv - ( val * 5 )
			--新数值等于 nv 和 minv中最大的那个
			nv = max(nv, minv)
			--新数值等于 nv 和 maxv中最小的那个
			nv = min(nv, maxv)
			
			--如果新值 不等于 旧值 
			--设置滚动条的值为新值  -->设置滚动到哪个点(值)
			if ( nv ~= cv ) then
			--print("3")
				s1:SetValue(nv);
			end
			
		end)
		
	--LScroll:EnableMouseWheel(true) --不在初始化商城滑块的时候启用，需在贩卖物品小窗口创建后依照数量多少再决定是否启用
	

		
	--LScroll:EnableMouseWheel(true) --不在初始化商城滑块的时候启用，需在贩卖物品小窗口创建后依照数量多少再决定是否启用
end


--创建商城所有分类按钮
function DONGD_CreateRMallAllClassButton(t)
	
	local i = 1
		
	for k,v in pairs(RMallButtonsInfo) do
		
		if not t then 						-- 如果t不存在,是指最原始的状态,初始化状态,便是没有任何父类按钮被激活
			-- print("果然没有 t")
			RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,260,30,k,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
			RMallButtons[i].Name = k				--即是父类型的显示名
			RMallButtons[i].Type = "P"				--用于区分父子类型
			RMallButtons[i].ShowSub = false 		--初始化按钮的子按钮显示状态,即是现在只有父级分类按钮没有显示任何子按钮
			RMallButtons[i].ShowSubFrame = false	--初始化按钮的贩卖物品窗口显示状态,即是现在只有父级分类按钮没有显示任何子级分类 这个的用处待定
			i = i + 1
		else
			RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,260,30,k,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
			RMallButtons[i].Name = k
			RMallButtons[i].Type = "P"
			if t == k then
				RMallButtons[i].ShowSub = true		--这个父级按钮显示子按钮状态为 true,这个位置才是正确的不能移动,因为 i 会递增的缘故
				RMallButtons[i].ShowSubFrame = true --这个父级按钮显示子贩卖物品窗口状态为 true 这个的用处待定
				for k1,v1 in pairs(RMallClass) do
					if t == k1 then
						-- print("t = "..t..";k1 = "..k1)
						for k2,v2 in pairs(RMallClass[k1]) do
							-- print("v2 = "..v2)
							i = i + 1
							RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,240,30,v2,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
							RMallButtons[i].Name = v2		--即是子类型的显示名 注意这里需要的值是v2 而不是v1 v1的值是{}
							RMallButtons[i].PName = k1							
							RMallButtons[i].Type = "S"		--用于区分父子类型
						end			
					end
				end
				
			end
			i = i + 1
		end
		--print(RMallButtons[i-1]:GetName())
	end

	for n,m in pairs(RMallButtons) do
		if i >= 21 then
			if n == 1 then
			RMallButtons[n]:SetPoint("TOPLEFT",-2,0)
			end
				if RMallButtons[n].Type == "P" then
				RMallButtons[n]:SetSize(230,30)
				else
				RMallButtons[n]:SetSize(210,30)
				end
			--创建右边滑动窗口
			else
			--隐藏右边的滑块滑条框架等等
		end
	end

	DONGD_InitMallClassButton()
end


--创建商城所有主窗口
function DONGD_CreateMallAllParentFrame()

	--创建左边窗口
	LMall = DONGD_CreateMallParentFrame("Frame","LMall",UIParent,nil,900,700,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")

	--创建右边窗口
	RMall = DONGD_CreateMallParentFrame("Frame","RMall",LMall,nil,300,728,"",-22,13.7,T["ParentFrameBackgroundRight"],"")
	
	--创建标题
	MallTitle = DONGD_CreateMallParentFrame("Frame","MallTitle",LMall,nil,700,180,"TOP",0,120,T["ParentFrameTitle"],"")
	
	--叉叉按钮
	CloseButton = DONGD_CreateButton("Button","CloseButton",LMall,nil,36,33,"","TOPRIGHT",268,-10,"","")
	
	DONGD_CreateMallAllScrollFrame()
	
	DONGD_CreateMallAllSliderFrame()
	
	DONGD_CreateRMallAllClassButton()
	
	--设置叉叉按钮的脚本	
	PrepareScript(CloseButton,	"关闭",		function() MenuOffButton:Click()	end)
	--PrepareScript(CloseButton,	"关闭",		function() LMall:Hide() --[[ LScroll:Hide()]] MenuOnButton:Show() MenuOffButton:Hide() end)

end


--创建商城所有滚动窗口
function DONGD_CreateMallAllScrollFrame()
		
	LScroll = DONGD_CreateScrollFrame("ScrollFrame","LScroll",LMall,nil,830,600,"CENTER",-3,-5,"","")
	
	LScrollChild = DONGD_CreateMallParentFrame("Frame","LScrollChild",LScroll,nil,830,1100,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	
	LScroll:SetScrollChild(LScrollChild)
	
	LScroll:Hide() --在主页创建后 就需要用 还要找个地方触发显示 这里是隐藏

	RScroll = DONGD_CreateScrollFrame("ScrollFrame","RScroll",RMall,nil,258,636,"CENTER",-3,-20,"","")
	
	RScrollChild = DONGD_CreateMallParentFrame("Frame","RScrollChild",RScroll,nil,258,636,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")

	RScroll:SetScrollChild(RScrollChild)
	
	--海报
	LHTex = DONGD_CreateMallParentFrame("Frame","LHTex",LMall,nil,830,415,"TOP",-5,-30,T["HB"],T["HBBK"])
	
	--主页还没有Hide动作
	TTBUTTON = DONGD_CreateButton("Button","SubFrame".."TTBUTTON",LMall,nil,150,60,"超级神器","BOTTOMLEFT",35,190,"","")
	LHTex01 = DONGD_CreateTex(LMall,"LMall".."LHTex01","ARTWORK",nil,"TOP",TTBUTTON,"BOTTOM",55,10,T["TSBK"],256,128)
	LHTex02 = DONGD_CreateTex(LMall,"LMall".."LHTex02","ARTWORK",nil,"TOP",TTBUTTON,"BOTTOM",55+30+LHTex01:GetWidth(),10,T["TSBK"],256,128)
	LHTex03 = DONGD_CreateTex(LMall,"LMall".."LHTex03","ARTWORK",nil,"TOP",TTBUTTON,"BOTTOM",55+60+LHTex01:GetWidth()*2,10,T["TSBK"],256,128)
	-- LHTex04 = DONGD_CreateTex(LMall,"LMall".."LHTex04","ARTWORK",nil,"TOP",TTBUTTON,"BOTTOM",35+LHTex01:GetWidth()*3,0,T["TSBK"],230,115)
	--LHTex:SetBackdropColor(1.0,1.0,0.7,0.8)
	--LHTex:SetBlendMode("ADD")
	--LHTex:SetTexCoord(0,1.0,0,0.60)
	--LHScroll,LHSlider,LHSlidertex,LHScrollChild,LHScrollUpButton,LHScrollDownButton
	--LHScroll = DONGD_CreateScrollFrame("ScrollFrame","LHScroll",LMall,nil,830,400,"CENTER",-5,-115,"","")
	--LModel = DONGD_CreateMallParentFrame("Model","LModel",LMall,nil,300,300,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	--LHTex1 = DONGD_CreateTex(LMall,"LMall".."LHTex","ARTWORK",nil,"TOP",nil,"",-5,-45,T["HB"],820,220)
	--LHTex1:SetTexCoord(0,1.0,0,0.60)
	--"Interface\\AchievementFrame\\UI-Achievement-WoodBorder"
	--DONGD_CreateMallALLSubFrame_Test()
	--DONGD_CreateMallALLSubFrame()
	
end


--创建商城所有滑块条
function DONGD_CreateMallAllSliderFrame()

	--创建滑块条边框贴图
	LScrollTexTop,LScrollTexMiddle,LScrollTexBottom = DONGD_CreateScrollTex(LMall,"L","ARTWORK",nil,0,"L")
	
	--创建所有滚动按钮并初始化按钮脚本
	DONGD_CreateMallALLScrollButton()
	
	--创建滑块及其贴图
	LSlider,LSlidertex = DONGD_CreateSliderFrame("Slider","LMall",LMall,nil,"VERTICAL","L")
	
	
	
	
	
	RScrollTexTop,RScrollTexMiddle,RScrollTexBottom = DONGD_CreateScrollTex(RScrollChild,"L","ARTWORK",nil,0,"R")
	
	RSlider,RSlidertex = DONGD_CreateSliderFrame("Slider","RMall",RScrollChild,nil,"VERTICAL","R")
	
	--初始化商城滚动窗口滑块
	DONGD_InitMallSlider()	

	DONGD_HandleHideLSiderAllElement(false,"L")
end


--处理隐藏LSider所有元素
function DONGD_HandleHideLSiderAllElement(b,Type)

if not b then
	if Type == "L" then
		LScrollTexTop:Hide()
		
		LScrollTexMiddle:Hide()
		
		LScrollTexBottom:Hide()
		
		LSlider:Hide()
		
		LScrollUpButton:Hide()
		
		LScrollDownButton:Hide()
	end
else
	if Type == "L" then
		LScrollTexTop:Show()
		
		LScrollTexMiddle:Show()
		
		LScrollTexBottom:Show()
		
		LSlider:Show()
		
		LScrollUpButton:Show()
		
		LScrollDownButton:Show()
	end
end


	--LSlidertex:Hide()
end


--创建商场所有贩卖窗口	--按照按下的按钮类型名称创建创建创建 对应SubFrame --复杂版本(未完成)
function DONGD_CreateMallALLSubFrameOfCategory_Complicated(PCategory,SCategory)

	local num = 0
	local displayedHeight 
	
	
	if PCategory ~= nil and SCategory == nil then
		-- print("开始创建父级分类物品贩卖窗口")
		for i,v in pairs(SubFrame) do  
			if PCategory == SubFrame[i].pclass then
			print("SubFrame[i] = "..i)
			SubFrame[i].PFrame = DONGD_CreateMallSubFrame("Frame","SubSFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["SubFrameBackground"],"",SubFrame,i,"ParentC")	
			num = num + 1
			end
		end
		-- print("创建父级分类物品贩卖窗口完毕")	--创建之后 SubFrame[i].Frame ~= nil
		displayedHeight = math.ceil((num/3) * SubFrame[1].SubFrame:PFrame())
	end
	
	
	--如果要重置的话 估计也要重新清空SubFrame[i].SFrame了  创建逻辑不够严谨 请参考自己写的类别按钮创建逻辑
	if PCategory ~=nil and SCategory ~= nil then
		-- print("开始创建子级分类物品贩卖窗口")
		for i,v in pairs(SubFrame) do
			if PCategory == SubFrame[i].pclass and SCategory == SubFrame[i].pclass then
				SubFrame[i].SFrame = DONGD_CreateMallSubFrame("Frame","SubPFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["SubFrameBackground"],"",SubFrame,i,"SubC")
				num = num + 1
			end
		end
		-- print("开始创建子级分类物品贩卖窗口")
		displayedHeight = math.ceil((num/3) * SubFrame[1].SubFrame:GetHeight())
	end
	
	LScrollChild:SetHeight(displayedHeight)
	
end


--创建商场所有贩卖窗口(精简版)
function DONGD_CreateMallALLSubFrameOfCategory_Simplify(PCategory,SCategory)
	
	local num = 0
	local displayedHeight,val
	-- local SubFrameRealHeight = 300
	
	if PCategory ~= nil and SCategory == nil then
		for i,v in pairs(SubFrame) do  
			if PCategory == SubFrame[i].pclass then
				SubFrame[i].Frame = DONGD_CreateMallSubFrame("Frame","SubPFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["ParentFrameBackgroundLeft"],"",SubFrame,i,"ParentS")	
				DONGD_CreateMallSubFrameAllElement_Simplify(i)		--创建贩卖物品窗口内的信息元素
				num = num + 1
				val = i
			end
		end
		--  经过最终测试,只要图片是铺满整个画板的(上下没有空隙),那么用以下公式,和滑动脚本的公式,其滑动效果将正确显示。
		displayedHeight = math.ceil(num/3) * math.ceil(SubFrame[val].Frame:GetHeight())
	end
	
	
	--如果要重置的话 估计也要重新清空SubFrame[i].Frame了  创建逻辑不够严谨 请参考自己写的类别按钮创建逻辑
	--不用在这里清空 在按钮处理那里清空 而且这里不用把父分类和子分类分成PFrame 和 SFrame 统一弄成Frame 因为毕竟是精简版,直接清空后再创建，而不是隐藏和显示的切换
	if PCategory ~=nil and SCategory ~= nil then
		-- print("开始创建子级分类物品贩卖窗口")
		for i,v in pairs(SubFrame) do
			if PCategory == SubFrame[i].pclass and SCategory == SubFrame[i].sclass then
				SubFrame[i].Frame = DONGD_CreateMallSubFrame("Frame","SubSFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["ParentFrameBackgroundLeft"],"",SubFrame,i,"SubS")
				DONGD_CreateMallSubFrameAllElement_Simplify(i)
				num = num + 1
				val = i
			end
		end
		-- print("开始创建子级分类物品贩卖窗口")
		displayedHeight = math.ceil(num/3) * math.ceil(SubFrame[val].Frame:GetHeight())
		-- print("(num/3) = "..math.ceil(num/3))
		-- print("(num/9) = "..math.ceil(num/9))
		-- print("SubFrame[val].Frame:GetHeight() = "..SubFrame[val].Frame:GetHeight())
		-- print("displayedHeight = "..displayedHeight)
	end
	
	if num > 6 then 
		LScrollChild:SetHeight(displayedHeight)			-- 调整最合适尺寸 6行115 5行 4行 65 3行40
		LScroll:EnableMouseWheel(true)					-- 只有超过3行的时候才能激活
		LScrollDownButton:Enable()						-- 贩卖物品的滚动窗口向下按钮启用
		--LScrollUpButton:Enable()
	else
		LScrollDownButton:Disable()
		LScrollUpButton:Disable()
		LScroll:EnableMouseWheel(false)					--小于等于3行的都无法激活
	end

	--LSlider:SetValue(0)	--在这里是不触发的

end


--创建商城贩售物品小窗口内的所有元素(精简版)
function DONGD_CreateMallSubFrameAllElement_Simplify(i)
		--print("开始创建贩卖物品窗口元素")
		local S = SubFrame[i]
		local itemid = S.itemid
		local f = S.Frame
		local c = 1
		local SellInfo = SubFramSellInfo
		
		SubFrame[i].ItemButton = DONGD_CreateButton("Button","SubFrame"..tostring(i).."ItemButton",f,nil,60,60,"|T"..GetItemIcon(itemid)..":50|t","TOPLEFT",20,-30,"","")
		
		PrepareScript(S.ItemButton,	"",function() if ( IsModifiedClick("DRESSUP") ) then  DressUpItemLink(S.itemlink); end end,S.itemlink)	--函数的意义即ctrl+左键=>试衣间,移动到按钮出现物品的GameTooltip的信息
		
		--光标改变为放大镜进入离开触发
		S.ItemButton:SetScript("OnUpdate",function(self) if( GameTooltip:IsOwned(self)) then if (IsModifiedClick("DRESSUP"))then SetCursor("INSPECT_CURSOR") else ResetCursor() end end end)	
		
		SubFrame[i].ItemNameFont = DONGD_CreateFontString(f,"SubFrame"..tostring(i).."ItemNameFont","","",T["Font"],15,"TOPLEFT",S.ItemButton,"TOPRIGHT",0,-5,S.itemlink,"",true)
		
		--S.ItemNameFont:SetAlphaGradient(0,5) --渐变效果
		--售价信息文本显示
		--SubFrame[i].SellFont = DONGD_CreateFontString(f,"SubFrame"..tostring(i).."SellFont","","",T["Font"],15,"TOP",S.ItemNameFont,"BOTTOM",-35,-20,"售价信息","",true)
		
		
		if S.reqvip ~= "0" then		--所需VIP,注意req存储的都是字符串
			--(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,BgFile)
			SubFrame[i].ReqVipButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."VipButton",f,nil,26,26,"|T"..T["Vip"]..":20|t","TOP",S.ItemNameFont,"CENTER",-30,-3,"")
			SubFrame[i].ReqVipFont = DONGD_CreateFontString(S.ReqVipButton,"SubFrame"..tostring(i).."ReqVipFont","","",T["Font"],13,"LEFT",S.ReqVipButton,"LEFT",-55,-8,"*"..S.reqvip,"",true)
			PrepareScript(S.ReqVipButton,	"需求VIP",		nil)
		end
			
		if S.reqjf ~= "0" then		--所需积分,注意req存储的都是字符串
		local rf = GetSubFrameSellInfoFrameS(i,c-1)
		tinsert(S.SubFramSellInfo,"jifen")
		SubFrame[i].ReqJFButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqJFButton",f,nil,26,26,"|T"..T["JiFen"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
		SubFrame[i].ReqJFFont = DONGD_CreateFontString(S.ReqJFButton,"SubFrame"..tostring(i).."ReqJFFont","","",T["Font"],13,"LEFT",S.ReqJFButton,"LEFT",30,0,"*"..S.reqjf,"")
		PrepareScript(S.ReqJFButton,	"需求积分",		nil)
		c = c + 1
		end
		
		if S.reqmoney ~= "0" then 	--所需金币,注意req存储的都是字符串
			local rf = GetSubFrameSellInfoFrameS(i,c-1) --这里i=>贩卖物品唯一序号，c=>递增的序号 记住这里要用c - 1 因为寻找的相对坐标对象是前一个
			tinsert(S.SubFramSellInfo,"money") --将特定字符串写入数组
			SubFrame[i].ReqMoneyButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqMoneyButton",f,nil,26,26,"|T"..T["JinBi"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqMoneyFont = DONGD_CreateFontString(S.ReqMoneyButton,"SubFrame"..tostring(i).."ReqMoneyFont","","",T["Font"],13,"LEFT",S.ReqMoneyButton,"LEFT",30,0,"*"..S.reqmoney,"")
			PrepareScript(S.ReqMoneyButton,	"需求金币",		nil)
			c = c + 1
		end
		
		if S.reqexp ~= "0" then		--所需经验值,注意req存储的都是字符串
			local rf = GetSubFrameSellInfoFrameS(i,c-1) --这里i=>贩卖物品唯一序号，c=>递增的序号 记住这里要用c - 1 因为寻找的相对坐标对象是前一个
			tinsert(S.SubFramSellInfo,"exp") --将特定字符串写入数组
			SubFrame[i].ReqExpButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqExpButton",f,nil,26,26,"|T"..T["XP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqExpFont = DONGD_CreateFontString(S.ReqExpButton,"SubFrame"..tostring(i).."ReqExpFont","","",T["Font"],13,"LEFT",S.ReqExpButton,"LEFT",30,0,"*"..S.reqexp,"")
			PrepareScript(S.ReqExpButton,	"需求经验",		nil)
			c = c + 1
		end
		
		if S.reqhonor ~= "0" then	--所需荣誉点,注意req存储的都是字符串
			local rf = GetSubFrameSellInfoFrameS(i,c-1) --这里i=>贩卖物品唯一序号，c=>递增的序号 记住这里要用c - 1 因为寻找的相对坐标对象是前一个
			tinsert(S.SubFramSellInfo,"honor") --将特定字符串写入数组
			local tex 
			local englishFaction, localizedFaction = UnitFactionGroup("player")
			if englishFaction == "Alliance" then
				tex = T["AlliancePVP"]
			else
				tex = T["HordePVP"]
			end
			SubFrame[i].ReqHonorButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqHonorButton",f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqHonorFont = DONGD_CreateFontString(S.ReqHonorButton,"SubFrame"..tostring(i).."ReqHonorFont","","",T["Font"],13,"LEFT",S.ReqHonorButton,"LEFT",30,0,"*"..S.reqhonor,"")
			PrepareScript(S.ReqHonorButton,	"需求荣誉值",		nil)
			c = c + 1
		end
		
		if S.reqarena ~= "0" then	--所需竞技点,注意req存储的都是字符串
			local rf = GetSubFrameSellInfoFrameS(i,c-1) --这里i=>贩卖物品唯一序号，c=>递增的序号 记住这里要用c - 1 因为寻找的相对坐标对象是前一个
			tinsert(S.SubFramSellInfo,"arena") --将特定字符串写入数组
			SubFrame[i].ReqArenaButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqArenaButton",f,nil,26,26,"|T"..T["ArenaPVP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqArenaFont = DONGD_CreateFontString(S.ReqArenaButton,"SubFrame"..tostring(i).."ReqArenaFont","","",T["Font"],13,"LEFT",S.ReqArenaButton,"LEFT",30,0,"*"..S.reqarena,"")
			PrepareScript(S.ReqArenaButton,	"需求竞技点",		nil)
			c = c + 1
		end
			
		for j = 1,5 do
			if SubFrame[i].reqitemid[j] ~= nil then
				local rf = GetSubFrameSellInfoFrameS(i,c-1) --这里i=>贩卖物品唯一序号，c=>递增的序号 记住这里要用c - 1 因为寻找的相对坐标对象是前一个
				local str = "item"..tostring(j)
				tinsert(S.SubFramSellInfo,str) --将特定字符串写入数组
				local tex = GetItemIcon(S.reqitemid[j])
				local itemlink = SubFrame[i].reqitemlink[j]
				SubFrame[i].ReqItemButton[j] = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqItemButton"..tostring(j),f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
				SubFrame[i].ReqItemFont[j] = DONGD_CreateFontString(S.ReqItemButton[j],"SubFrame"..tostring(i).."ReqItemButtonFont"..tostring(j),"","",T["Font"],13,"LEFT",S.ReqItemButton[j],"LEFT",30,0,"需求： "..S.reqitemval[j].."个","")
				PrepareScript(S.ReqItemButton[j],"",nil,itemlink)
				c = c + 1
			end
		end
		
		--购买按钮
		SubFrame[i].SellButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."SellButton",f,nil,100,42,"立即购买","BOTTOMLEFT",nil,"",15,15,"")
		
		if S.PromotionType ~= "无" then
		
			local PromTabTex	= DONGD_GetPromTabTex(S.PromotionType)
			
			SubFrame[i].ItemDisTab = DONGD_CreateTex(S.ItemButton,"SubFrame"..tostring(i).."ItemDisTab","ARTWORK",nil,"TOPLEFT",nil,"",5,-5,PromTabTex,50,25)	
				
		end
		
		if S.PromotionType == "限时" then	--如果是限时活动,则在这里操作
		
			S.ItemDisTab:Hide()	--如果是限时标签 那么将在活动开始的时候限时
			
			SubFrame[i].TimeFrame = DONGD_CreateMallParentFrame("Button","SubFrame"..tostring(i).."TimeFrame",f,nil,150,38,"CENTER",15,52,T["ToolBack"],"")
			
			PrepareScript(S.TimeFrame,	"限时活动剩余时间",		nil)
			
			--SubFrame[i].TimeFrame.i = i
			
			SubFrame[i].Frame.i = i		--为传送序号参数,配合OnUpdate
			
			SubFrame[i].TimeFont = DONGD_CreateFontString(S.TimeFrame,"SubFrame"..tostring(i).."ItemNameFont","","",T["Font"],12,"CENTER",S.TimeFrame,"CENTER",0,-8,"","",true)
			
			S.TimeFrame:Hide()
			
			S.Frame:SetScript("OnUpdate", CTime)
						
			--S.TimeFrame:SetScript("OnUpdate", CTime)	--不能用这个,因为这个一旦隐藏就不会触发更新了
		end
		
		SubFrame[i].ItemBorder = DONGD_CreateTex(S.ItemButton,"SubFrame"..tostring(i).."ItemBorder","OVERLAY",nil,"CENTER",nil,"",0,0,T["Loottoastatlas"],68,68)	--物品边框
		
		DONGD_TexSetTexCoord(S.ItemBorder,TCoord["Itemborder-heirloom"])	--这行和上面一行贩卖物品的边框
		--print(format("%02d",5))
		--print("创建贩卖物品窗口元素完毕")
end


--根据促销标签的类型,获取不同贴图
function DONGD_GetPromTabTex(Type)
	
	local t = Type
	
	local tex = ""
	
	if t == "热卖" then
		tex = T["cuxiao1"]
	elseif t == "折扣" then
		tex = T["cuxiao5"]
	elseif t == "新品" then
		tex = T["cuxiao4"]
	elseif t == "限时" then
		tex = T["cuxiao3"]
	elseif t == "推荐" then
		tex = T["cuxiao2"]
	end
	
	return tex

end


--设置折扣价格
function SetDisPrice(i)

local S = SubFrame[i]
	--print("现在的时间"..date("%Y%m%d,%H:%M:%S",time()))
	--print("活动结束的时间"..date("%Y%m%d,%H:%M:%S",S.e_time))
	if S.reqjf ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqjf) * S.Dis)
	S.ReqJFFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqmoney ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqmoney) * S.Dis)
	S.ReqMoneyFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqexp ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqexp) * S.Dis)
	S.ReqExpFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqhonor ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqhonor) * S.Dis)
	S.ReqHonorFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqarena ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqarena) * S.Dis)
	S.ReqArenaFont:SetText("*"..tostring(DisPrice))
	end
	
	for j = 1,5 do
		if SubFrame[i].reqitemid[j] ~= nil then
		local DisPrice = math.modf(tonumber(S.reqitemval[j]) * S.Dis)
			S.ReqItemFont[j]:SetText("需求： "..tostring(DisPrice).."个")
		end
	end
	
	S.IsDis = true
end


--恢复原来价格
function SetNormPrice(i)

local S = SubFrame[i]

	if S.reqjf ~= "0" then
	S.ReqJFFont:SetText("*"..S.reqjf)
	end
	
	if S.reqmoney ~= "0" then
	S.ReqMoneyFont:SetText("*"..S.reqmoney)
	end
	
	if S.reqexp ~= "0" then
	S.ReqExpFont:SetText("*"..S.reqexp)
	end
	
	if S.reqhonor ~= "0" then
	S.ReqHonorFont:SetText("*"..S.reqhonor)
	end
	
	if S.reqarena ~= "0" then
	S.ReqArenaFont:SetText("*"..S.reqarena)
	end
	
	for j = 1,5 do
		if SubFrame[i].reqitemid[j] ~= nil then
			S.ReqItemFont[j]:SetText("需求： "..S.reqitemval[j].."个")
		end
	end
	
	S.IsDis = false

end


--活动倒计时的操作	--尝试优化成0.1秒限时
function CTime(self,var)

local i = self.i

local timer = SubFrame[i].DELAY

	if timer > 0 then
	
		timer = timer - var
		
		SubFrame[i].DELAY = timer
	
	else
	
		local s_time = SubFrame[i].s_time
	
		if time() >= s_time then					--当前时间大于等于活动开始时间(时间戳)
		
			local e_time = SubFrame[i].e_time		
			
			local diff_tiem = e_time - time()		--活动结束时间减去当前时间
		
			if diff_tiem > 0 then					--活动结束时间减去当前时间>0的话
				--local str = tostring(date("%H:%M:%S",diff_tiem))	--这个不对
				--local ntime = date(time())
				local etime = date(e_time)
				local ttime = DONGD_timediff(etime,date(time()))
				local str = string.format("%02d", ttime.hour)..":"..string.format("%02d", ttime.min)..":"..string.format("%02d", ttime.sec)
				SubFrame[i].TimeFont:SetText(str)
				if SubFrame[i].IsDis == false then
					SetDisPrice(i)					--活动开始,显示促销价格,IsDis也变为true了
					SubFrame[i].TimeFrame:Show()	--活动开始,显示倒计时
					SubFrame[i].ItemDisTab:Show()	--活动开始,显示限时标签
				end
			else
				if SubFrame[i].IsDis == true then
					SetNormPrice(i)						--活动结束,恢复原始价格显示
					SubFrame[i].TimeFont:SetText("0")	--活动结束,倒计时显示为0
					SubFrame[i].TimeFrame:Hide()		--活动结束,倒计时窗口隐藏
					SubFrame[i].ItemDisTab:Hide()		--活动结束,限时标签隐藏
				end
			end
		end
	
	SubFrame[i].DELAY = 1	--重置秒数
	
	end
	
end


--创建商城菜单按钮
function DONGD_CreateAndShowMallMenuButton()

	MenuOnButton = DONGD_CreateButton("Button","MenuOnButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketButton"],"")

	MenuOffButton = DONGD_CreateButton("Button","MenuOffButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketOffButton"],"",true)
	
	DONGD_InitMallMenuButtons()
	
end


--创建商城所有滚动窗口按钮
function DONGD_CreateMallALLScrollButton()

	--OnLoad事件只对 xml有效

	LScrollUpButton = DONGD_CreateButton("Button","LScrollUpButton",LMall,nil,35,35,"","TOPRIGHT",-8,-15,"","",false,true)
	
	LScrollDownButton = DONGD_CreateButton("Button","LScrollDownButton",LMall,nil,35,35,"","BOTTOMRIGHT",-8,12,"","",false,true)	
	
	RScrollUpButton = DONGD_CreateButton("Button","RScrollUpButton",RScrollChild,nil,35,35,"","TOPRIGHT",2,5,"","",false,true)
	
	RScrollDownButton = DONGD_CreateButton("Button","RScrollDownButton",RScrollChild,nil,35,35,"","BOTTOMRIGHT",2,-6,"","",false,true)

	DONGD_InitMallScrollButton()
	
end


--创建商城所有标签按钮
function DONGD_CreateMallALLTabButton()
	
	HomePage = DONGD_CreateButton("Button","HomePage",RMall,nil,70,70,"","",-10,-50,T["JiFen"],"")

	DONGD_InitMallTabButton()
	
end


--创建商城所有标签背景贴图
function DONGD_CreateMallTabTex()
	
	HomeTex = DONGD_CreateTex(RMall,"HomeTex","ARTWORK",nil,"TOPLEFT",RMall,"TOPRIGHT",-12,-35,T["TabClassFrameBackground"],80,100)
	
end


--商城初始化
function DONGD_OnInitialize()

	--DONGD_CreateAndShowMallMenuButton()
	
	DONGD_CreateMallAllParentFrame()
	
	DONGD_CreateMallALLTabButton()

	DONGD_CreateMallTabTex()
		
end


DONGD_CreateAndShowMallMenuButton()

--DONGD_OnInitialize()






