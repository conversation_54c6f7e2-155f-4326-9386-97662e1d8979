/*
SQLyog Ultimate
MySQL - 5.7.35-log 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

create table `store_navigation_ui` (
	`ID` int (11),
	`Name` varchar (765),
	`Texture` text ,
	`Coord_Y` float ,
	`FrameShow` varchar (765),
	`Account_Rank` float 
); 
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('1','Vip Ranks','Vip','180','VIP_FRAME','0');
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('2','Transmogs','Transmog','140','TRANSMOG_FRAME','0');
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('3','Items','Items','100','CLOTH_FRAME','0');
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('4','Mounts','Mounts','60','MOUNTS_FRAME','0');
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('5','Bundles','Bundles','20','BUNDLES_FRAME','0');
insert into `store_navigation_ui` (`ID`, `Name`, `Texture`, `Coord_Y`, `FrameShow`, `Account_Rank`) values('6','Services','Services','-20','SERVICES_FRAME','0');
