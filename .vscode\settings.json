{"Lua.runtime.version": "Lua 5.1", "Lua.runtime.builtin": {"basic": "disable", "debug": "disable", "io": "disable", "math": "disable", "os": "disable", "package": "disable", "string": "disable", "table": "disable", "utf8": "disable"}, "Lua.workspace.library": ["~\\.vscode\\extensions\\ketho.wow-api-0.20.8\\Annotations\\Core"], "Lua.diagnostics.globals": ["SlashCmdList", "ItemRefTooltip", "ItemRefShoppingTooltip1", "ItemRefShoppingTooltip2", "ShoppingTooltip1", "ShoppingTooltip2", "GameTooltipTextLeft2", "GameTooltipTextLeft1", "GuildBankFrame", "UISpecialFrames", "GameTooltip_Hide", "GameTooltip_ClearMoney", "DEFAULT_CHAT_FRAME", "GameTooltip_OnTooltipAddMoney", "StaticPopupDialogs", "ContainerFrame1MoneyFrame", "GameTooltip_ShowStatusBar", "GhostFrame", "DressUpItemLink", "StaticPopup_Hide", "StaticPopup_Visible", "UIFrameFlash", "PanelTemplates_SetNumTabs", "PanelTemplates_SetTab", "MerchantFrame", "StaticPopup_Show", "MerchantFramePortrait", "MerchantPageText", "MerchantBuyBackItem", "MerchantFrameBottomLeftBorder", "MerchantItem11", "MerchantItem12", "MerchantItem3", "MerchantItem5", "MerchantItem7", "MerchantItem9", "MerchantRepairAllButton", "MerchantRepairItemButton", "MerchantPrevPageButton", "MerchantNextPageButton", "MerchantGuildBankRepairButton", "OpenBackpack", "CloseBackpack", "SetItemButtonCount", "SetItemButtonStock", "SetItemButtonTexture", "HandleModifiedItemClick", "GameTooltip_ShowCompareItem", "SetDesaturation", "SetItemButtonNameFrameVertexColor", "SetItemButtonSlotVertexColor", "MerchantBuyBackItemName", "MerchantBuyBackItemItemButton", "MerchantBuyBackItemMoneyFrame", "ShowInspectCursor", "SetItemButtonTextureVertexColor", "SetItemButtonNormalTextureVertexColor", "ContainerFrame_GetExtendedPriceString", "UpdateCoinPickupFrame", "ContainerFrame1", "RaiseFrameLevel", "UIDropDownMenu_Initialize", "UIDropDownMenu_SetSelectedValue", "UIDropDownMenu_SetWidth", "UIDropDownMenu_JustifyText", "UpdateUIPanelPositions", "FauxScrollFrame_Update", "HybridScrollFrame_OnLoad", "HybridScrollFrame_CreateButtons", "CharacterLevelText", "UIDropDownMenu_AddButton", "Character<PERSON><PERSON>e", "PaperDollFrame", "SetItemButtonDesaturated", "SetTooltipMoney", "CursorUpdate", "UIDropDownMenu_CreateInfo", "EquipmentManager_GetItemInfoByLocation", "EquipmentManager_RunAction", "GameTooltip_SetDefaultAnchor", "FauxScrollFrame_OnVerticalScroll", "FauxScrollFrame_GetOffset", "HybridScrollFrame_GetOffset", "HybridScrollFrame_Update", "UIErrorsFrame", "UnitHasMana", "EquipmentManager_UnequipItemInSlot", "EquipmentManager_EquipItemByLocation", "EquipmentManager_EquipSet", "PanelTemplates_GetSelectedTab", "MovieFrame", "AddonTooltip", "AddonList", "AddonDialogButton1", "AddonDialogBackground", "AddonDialogText", "AddonDialogButton2", "CloseDropDownMenus", "GameMenuFrame", "MainMenuMicroButton", "GetBindingFromClick", "<PERSON><PERSON><PERSON><PERSON>", "ReloadUI", "WorldFrame", "ChatFrame1", "MountJournal", "SOUNDKIT", "TimerTracker", "IsInLFDBattlefield", "PLAYER_FACTION_GROUP", "FreeTimerTrackerTimer", "HONOR_POINTS", "ARENA_POINTS", "GOLD_AMOUNT_SYMBOL", "SILVER_AMOUNT_SYMBOL", "COPPER_AMOUNT_SYMBOL", "GOLD_AMOUNT_TEXTURE", "SILVER_AMOUNT_TEXTURE", "COPPER_AMOUNT_TEXTURE", "TIMER_NUMBERS_SETS", "StartTimer_SetGoTexture", "StartTimer_BigNumberOnUpdate", "UNSPENT_TALENT_POINTS", "HIGHLIGHT_FONT_COLOR_CODE", "FONT_COLOR_CODE_CLOSE"], "Lua.type.weakUnionCheck": true}