
local IV = {}

IV.mainWindow = Create<PERSON>rame("Frame", IV.mainWindow, UIParent)
  IV.mainWindow:SetSize(300, 600)
  IV.mainWindow:SetMovable(true)
  IV.mainWindow:EnableMouse(true)
  IV.mainWindow:RegisterForDrag("Right_Button")
  IV.mainWindow:SetPoint("CENTER", 0, 50)
  IV.mainWindow:Hide()

IV.mainWindowTexture = IV.mainWindow:CreateTexture()
  IV.mainWindowTexture:SetAllPoints(IV.mainWindow)
  IV.mainWindowTexture:SetTexture("interface/IV/IV_frame")
  IV.mainWindowTexture:SetTexCoord(0.58154296875, 0.96435546875, 0.04052734375, 0.81201171875)

IV.mainTitle = CreateFrame("Frame", IV.mainTitle, IV.mainWindow)
  IV.mainTitle:SetSize(150, 45)
  IV.mainTitle:SetPoint("TOP", 0, 20)
  IV.mainTitle:SetFrameLevel(IV.mainWindow:GetFrameLevel() + 1)

IV.mainTitleTexture = IV.mainWindow:CreateTexture()
  IV.mainTitleTexture:SetAllPoints(IV.mainTitle)
  IV.mainTitleTexture:SetTexture("interface/IV/IV_frame")
  IV.mainTitleTexture:SetParent(IV.mainTitle)
  IV.mainTitleTexture:SetTexCoord(0.23486328125, 0.48779296875, 0.33251953125, 0.40966796875)

IV.mainTitleText = IV.mainTitle:CreateFontString(IV.mainTitleText)
  IV.mainTitleText:SetFont("Fonts\\ZYHei.TTF", 13)
  IV.mainTitleText:SetSize(190, 5)
  IV.mainTitleText:SetPoint("CENTER", 0, 3)
  IV.mainTitleText:SetText("|CFF000000额外潜力点系统|r")

IV.mainWindowArt = CreateFrame("Button", IV.mainWindowArt, IV.mainWindow)
  IV.mainWindowArt:SetSize(140, 100)
  IV.mainWindowArt:SetPoint("TOP", -70, -80)
  IV.mainWindowArt:SetFrameLevel(1)
  IV.mainWindowArt:SetAlpha(0.4)
  IV.mainWindowArt:SetFrameLevel(2)

IV.mainWindowArtTexture = IV.mainWindowArt:CreateTexture()
  IV.mainWindowArtTexture:SetAllPoints(IV.mainWindowArt)
  IV.mainWindowArtTexture:SetTexture("interface/IV/IV_frame")
  IV.mainWindowArtTexture:SetTexCoord(0.00048828125, 0.35009765625, 0.00048828125, 0.21435546875)

IV.levelWindow = CreateFrame("Frame", IV.levelWindow, IV.mainWindow)
  IV.levelWindow:SetSize(95, 90)
  IV.levelWindow:SetPoint("TOP", 0, -30)
  IV.levelWindow:SetFrameLevel(2)

IV.levelWindowTexture = IV.levelWindow:CreateTexture()
  IV.levelWindowTexture:SetAllPoints(IV.levelWindow)
  IV.levelWindowTexture:SetTexture("interface/IV/IV_frame")
  IV.levelWindowTexture:SetTexCoord(0.05419921875, 0.17431640625, 0.31201171875, 0.42724609375)

IV.levelText = IV.levelWindow:CreateFontString(IV.levelText)
  IV.levelText:SetFont("Fonts\\ZYHei.ttf", 18)
  IV.levelText:SetSize(190, 3)
  IV.levelText:SetPoint("CENTER", -2, 1)
  IV.levelText:SetShadowColor(0.156, 0.2, 0.2)
  IV.levelText:SetShadowOffset(0.5, 0)
  --IV.levelText:SetText("111111111111111111111111111111111111")
  
 IV.RWText = IV.levelWindow:CreateFontString(IV.RWText)
  IV.RWText:SetFont("Fonts\\ZYHei.TTF", 18)
  IV.RWText:SetSize(190, 3)
  IV.RWText:SetPoint("CENTER", -2, -120)
  IV.RWText:SetShadowColor(0.156, 0.2, 0.2)
  IV.RWText:SetShadowOffset(0.5, 0) 
  
  

IV.closeButton = CreateFrame("Button", IV.closeButton, IV.mainWindow, "UIPanelCloseButton")
  IV.closeButton:SetPoint("TOPRIGHT", 1.5, 3)
  IV.closeButton:EnableMouse(true)
  IV.closeButton:SetSize(26, 26)
  IV.closeButton:SetFrameLevel(2)

IV.expIcon = CreateFrame("Frame", IV.expIcon, IV.mainWindow)
  IV.expIcon:SetSize(39, 39)
  IV.expIcon:SetBackdrop({
    bgFile = "Interface/Icons/garr_currencyicon-xp",
    insets = { left = 0, right = 0, top = 0, bottom = 0 }
  })
  IV.expIcon:SetPoint("TOP", 0, -115)
  IV.expIcon:SetFrameLevel(6)


IV.expText = IV.mainWindow:CreateFontString(IV.expText)
  IV.expText:SetFont("Fonts\\ZYHei.TTF", 16)
  IV.expText:SetSize(190, 3)
  IV.expText:SetPoint("TOP", 0, -165)
  IV.expText:SetShadowColor(0.156, 0.2, 0.2)
  IV.expText:SetShadowOffset(0.5, 0)


IV.buttonsCoords = {
  global = {
    pos_y = 67
  }
}

IV.leftButtons = {}
IV.leftButtonsTexture = {}

IV.leftButtonsArt = {}

IV.centerButtons = {}
IV.centerText = {}

IV.rightButtons = {}
IV.rightButtonsTexture = {}
IV.rightText = {}

IV.spellsList = {
  [7464] = {name = '力量', icon = '_D3mantraofconviction'},
  [7471] = {name = '敏捷', icon = '_D3mantraofevasion'},
  [7477] = {name = '耐力', icon = '_D3mantraofretribution'},
  [7468] = {name = '智力', icon = '_D3mantraofhealing'},
  [7456] = {name = '精神', icon = '_D3mantraofhealing'},
}

for id, subtable in pairs(IV.spellsList) do
  IV.leftButtons[id] = CreateFrame("Frame", IV.leftButtons[id], IV.mainWindow)
    IV.leftButtons[id]:SetSize(50, 50)
    IV.leftButtons[id]:SetPoint("LEFT", 20, IV.buttonsCoords.global.pos_y)
    IV.leftButtons[id]:SetFrameLevel(1000)
    IV.leftButtons[id]:SetFrameLevel(3)

  IV.leftButtonsTexture[id] = IV.leftButtons[id]:CreateTexture()
    IV.leftButtonsTexture[id]:SetAllPoints(IV.leftButtons[id])
    IV.leftButtonsTexture[id]:SetTexture("Interface/IV/ButtonBorder")

  IV.leftButtonsArt[id] = CreateFrame("Frame", IV.leftButtonsArt[id], IV.leftButtons[id], nil)
  IV.leftButtonsArt[id]:SetSize(35, 35)
  IV.leftButtonsArt[id]:SetBackdrop(
    {
      bgFile = "Interface/Icons/"..subtable.icon,
      insets = {left = 0, right = 0, top = 0, bottom = 0}
    }
  )
  IV.leftButtonsArt[id]:SetPoint("CENTER", 0, 0)
  IV.leftButtonsArt[id]:SetFrameLevel(2)

  IV.centerButtons[id] = CreateFrame("Button", IV.centerButtons[id], IV.mainWindow, nil)
    IV.centerButtons[id]:SetSize(170, 55)
    IV.centerButtons[id]:SetPoint("CENTER", 0, IV.buttonsCoords.global.pos_y)
    IV.centerButtons[id]:SetNormalTexture("Interface/IV/LargeButtonBorder")
    IV.centerButtons[id]:SetHighlightTexture("Interface/IV/LargeButtonBorder_Hover")
    IV.centerButtons[id]:SetPushedTexture("Interface/IV/LargeButtonBorder_Push")
    IV.centerButtons[id]:EnableMouseWheel(1)
    IV.centerButtons[id]:SetFrameLevel(3)

  IV.centerButtons[id]:SetScript("OnMouseUp", function(self, button, down)
    if (button == "LeftButton") then
      -- 发送属性对应加点数值到客户端（这样交互是不是会太多了）
      SendAddonMessage("IV_SETSTATS_INFO_ADD",id.."#"..1,"GUILD", UnitName("player"))
      -- AIO.Handle("AIO_IV", "setStatsInformation", id, 1, true)
    elseif (button == "RightButton") then
      -- AIO.Handle("AIO_IV", "setStatsInformation", id, 1, false)
      -- 发送属性对应减点数值到客户端（这样交互是不是会太多了）
      SendAddonMessage("IV_SETSTATS_INFO_CUT",id.."#"..1,"GUILD", UnitName("player"))
    elseif (button == "MiddleButton") then
      -- 发送属性对应加点数值到客户端（这样交互是不是会太多了）
      SendAddonMessage("IV_SETSTATS_INFO_ADD",id.."#"..10,"GUILD", UnitName("player"))
      -- AIO.Handle("AIO_IV", "setStatsInformation", id, 10, true)
    end
  end)

  -- IV.centerButtons[id]:SetScript("OnMouseWheel", function(self, value)
  --   if (value > 0) then
  --     AIO.Handle("AIO_IV", "setStatsInformation", id, 1, true)
	  
  --   else
  --     AIO.Handle("AIO_IV", "setStatsInformation", id, 1, false)
  --   end
  -- end)

  IV.centerText[id] = IV.centerButtons[id]:CreateFontString(IV.centerText[id])
    IV.centerText[id]:SetFont("Fonts\\ZYHei.TTF", 14)
    IV.centerText[id]:SetSize(190, 3)
    IV.centerText[id]:SetPoint("CENTER", -1, 1)
    IV.centerText[id]:SetText("|CFFFFFFFF"..subtable.name.."|r")
    IV.centerText[id]:SetShadowColor(0, 0, 0)
    IV.centerText[id]:SetShadowOffset(0.5, 0.5)

  IV.rightButtons[id] = CreateFrame("Frame", IV.rightButtons[id], IV.mainWindow)
    IV.rightButtons[id]:SetSize(50, 50)
    IV.rightButtons[id]:SetPoint("Right", -20, IV.buttonsCoords.global.pos_y)
    IV.rightButtons[id]:SetFrameLevel(1000)
    IV.rightButtons[id]:SetFrameLevel(3)

  IV.rightText[id] = IV.rightButtons[id]:CreateFontString(IV.rightText[id])
    IV.rightText[id]:SetFont("Fonts\\ZYHei.TTF", 14)
    IV.rightText[id]:SetSize(190, 3)
    IV.rightText[id]:SetPoint("CENTER", 0.5, 0)
    IV.rightText[id]:SetShadowColor(0, 0, 0)
    IV.rightText[id]:SetShadowOffset(0.5, 0)

  IV.rightButtonsTexture[id] = IV.rightButtons[id]:CreateTexture()
    IV.rightButtonsTexture[id]:SetAllPoints(IV.rightButtons[id])
    IV.rightButtonsTexture[id]:SetTexture("Interface/IV/ButtonBorder")

  IV.buttonsCoords.global.pos_y = IV.buttonsCoords.global.pos_y - 60
end

IV.pointsLeft = IV.mainWindow:CreateFontString(IV.pointsLeft)
  IV.pointsLeft:SetFont("Fonts\\ZYHei.TTF", 16)
  IV.pointsLeft:SetSize(999, 3)
  IV.pointsLeft:SetPoint("BOTTOM", 0, 75)
  IV.pointsLeft:SetShadowColor(0, 0, 0)
  IV.pointsLeft:SetShadowOffset(1, 1)

IV.saveButton = CreateFrame("Button", IV.saveButton, IV.mainWindow)
  IV.saveButton:SetSize(150, 35)
  IV.saveButton:SetNormalTexture("Interface/buttons/ui-dialogbox-button-gold-up")
  IV.saveButton:SetHighlightTexture("Interface/buttons/ui-dialogbox-button-highlight")
  IV.saveButton:SetPushedTexture("Interface/buttons/ui-dialogbox-button-gold-down")
  IV.saveButton:SetPoint("BOTTOM", 0, 20)
  IV.saveButton:SetFrameLevel(2)

IV.saveButton:SetScript("OnMouseUp", function(self, button, down)
  if (button == "LeftButton") then
    IV.mainWindow:Hide()
    -- AIO.Handle("AIO_IV", "setStats")
    SendAddonMessage("IV_SETSTATS_INFO_SAVE","","GUILD", UnitName("player"))
    --发送保存命令到服务端
  end
end)

IV.saveButtonText = IV.saveButton:CreateFontString(IV.saveButtonText)
  IV.saveButtonText:SetFont("Fonts\\ZYHei.TTF", 12)
  IV.saveButtonText:SetSize(180, 3)
  IV.saveButtonText:SetPoint("CENTER", 0, 6)
  IV.saveButtonText:SetText("|CFFFFFFFF确认加点|r")
  IV.saveButtonText:SetShadowColor(0, 0, 0)
  IV.saveButtonText:SetShadowOffset(0.5, 0.5)

IV.characterFrameContainer = CreateFrame("Frame", IV.characterFrameContainer, CharacterFrame);
  IV.characterFrameContainer:SetSize(55, 55)
  IV.characterFrameContainer:RegisterForDrag("LeftButton")
  IV.characterFrameContainer:SetPoint("TOP", 181, -32)
  IV.characterFrameContainer:SetBackdrop({
    bgFile = "Interface/bankframe/bank-background",
    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
    edgeSize = 20,
    insets = { left = 5, right = 5, top = 5, bottom = 5 }
  })
  IV.characterFrameContainer:SetFrameLevel(5)
  IV.characterFrameContainer:SetMovable(true)
  IV.characterFrameContainer:EnableMouse(true)
  IV.characterFrameContainer:SetClampedToScreen(true)
  IV.characterFrameContainer:SetScript("OnDragStart", IV.characterFrameContainer.StartMoving)
  IV.characterFrameContainer:SetScript("OnHide", IV.characterFrameContainer.StopMovingOrSizing)
  IV.characterFrameContainer:SetScript("OnDragStop", IV.characterFrameContainer.StopMovingOrSizing)

IV.characterFrameBorder = CreateFrame("Button", IV.characterFrameBorder, IV.characterFrameContainer)
  IV.characterFrameBorder:SetSize(50, 50)
  IV.characterFrameBorder:SetNormalTexture("Interface/IV/ButtonBorder")
  IV.characterFrameBorder:SetHighlightTexture("Interface/IV/ButtonBorder_Hover")
  IV.characterFrameBorder:SetPushedTexture("Interface/IV/ButtonBorder_Push")
  IV.characterFrameBorder:SetPoint("CENTER", 0, 0)
  IV.characterFrameBorder:EnableMouseWheel(1)
  IV.characterFrameBorder:SetFrameLevel(1000)
  IV.characterFrameBorder:SetFrameLevel(7)



  IV.characterFrameBorder:SetScript("OnEnter", function(self, button, down)
    GameTooltip:SetOwner(UIParent, "ANCHOR_CURSOR", 1, 5)
    GameTooltip:AddLine("|cFFFFFF00额外潜力系统|r", 1, 1, 1)
    GameTooltip:AddLine("|cFF33FFFF打开/关闭额外潜力系统|r\n\n|cFFFFFF00潜力经验值|r=|cFFFF3399完成任务数*敌人等级数*系数|r\n\n"..IV.RWText:GetText().."\n")

    for spellid, subtable in pairs(IV.spellsList) do
      GameTooltip:AddLine("|CFFFFFFFF+ "..IV.rightText[spellid]:GetText().."|CFFFFFFFF "..subtable.name.."|r");
    end
    GameTooltip:AddLine("\n|CFFFFFFFF"..IV.expText:GetText().."|r");

    GameTooltip:Show()
  end)


  IV.characterFrameBorder:SetScript("OnLeave", function (self, button, down)
    GameTooltip:Hide()
  end)

  IV.characterFrameBorder:SetScript("OnMouseUp", function (self, button, down)
    if(IV.mainWindow:IsShown())then
      IV.mainWindow:Hide()
    else
      IV.mainWindow:Show()
    end
  end)

IV.characterFrameBackground = CreateFrame("Frame", IV.characterFrameBackground, IV.characterFrameBorder)
  IV.characterFrameBackground:SetSize(39, 39)
  IV.characterFrameBackground:SetBackdrop({
    bgFile = "Interface/Icons/INV_Mount_WhimsyshireCloudMount01",
	--bgFile = "Interface/Icons/_LDAKnowledge",
    insets = { left = 0, right = 0, top = 0, bottom = 0 }
  })
  IV.characterFrameBackground:SetPoint("CENTER", 0, 0)
  IV.characterFrameBackground:SetFrameLevel(6)

-- 这里的变量估计要是动态的
-- 这里的接收考虑在登录的时候发送一次请求接收一次?
-- 这里变量的值在改变的时候是不是也由服务端发送客户端接收一次?
-- function IV_addon.setInfo(player, stats, level, points, exps,quest)
--   for statid, value in pairs(stats) do
--     IV.rightText[statid]:SetText("|CFF00CE00" .. value)
--   end
	
--   IV.levelText:SetText("|CFFFFFFFF" .. level)
--   IV.RWText:SetText("当前完成任务数：|C000EEEEE" .. quest)
--   IV.pointsLeft:SetText("你还剩 |CFF00CE00" .. points .. "|r 潜力点.")

--   IV.expText:SetText("|CFFC758FE(".. exps.exp .. " / " .. exps.exp_max .. ")")
-- end

function IVSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

function IVGetSpellsList(val)
  if val == 1 then
    return 7464;
  elseif val == 2 then
    return 7471;
  elseif val == 3 then
    return 7477;
  elseif val == 4 then
    return 7468;
  elseif val ==5 then
    return 7456;
  end
end

function IVEvent(self, event, h, msg, classtype, sender)	

  if event == "CHAT_MSG_ADDON" then
    if h == "IV_INFO" then
      print(msg)
      local list = IVSplit(msg,"#")
      for i=1,5,1 do
        local value = list[i]
        local id = IVGetSpellsList(i)
        IV.rightText[id]:SetText("|CFF00CE00" .. value)
      end
      local level = list[6]
      local points = list[7]
      local quest = list[8]
      local exp = list[9]
      local exp_max = list[10]
      IV.levelText:SetText("|CFFFFFFFF" .. level)
      IV.RWText:SetText("当前完成任务数：|C000EEEEE" .. quest)
      IV.pointsLeft:SetText("你还剩 |CFF00CE00" .. points .. "|r 潜力点.")
      IV.expText:SetText("|CFFC758FE(".. exp .. " / " .. exp_max .. ")")
    end
  end

end

function IVSendQueryOp(self, event,...)
  SendAddonMessage("IV_QUERY_INFO","","GUILD", UnitName("player"))
end


local MsgReceiversIV = CreateFrame("Frame")
MsgReceiversIV:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiversIV:SetScript("OnEvent", IVEvent)

local PLOADIV = CreateFrame("Frame")
PLOADIV:RegisterEvent("PLAYER_LOGIN")
PLOADIV:SetScript("OnEvent", IVSendQueryOp)