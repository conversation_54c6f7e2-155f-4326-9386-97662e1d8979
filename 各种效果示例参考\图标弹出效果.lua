local frame = CreateFrame("Frame", nil, UIParent)
frame:SetSize(200, 500)
frame:SetPoint("CENTER", 0, 0)
frame:SetFrameStrata("BACKGROUND")
frame:SetFrameLevel(0)

local texture = frame:CreateTexture()
texture:SetAllPoints()
texture:SetTexture("Interface\\Draft\\CardBack2")
-- texture:SetVertexColor(1, 0, 1)

-- 创建一个动画组，用于管理一系列动画效果
local animationGroup = frame:CreateAnimationGroup()

-- 设置动画组循环方式为重复循环，以达到持续动画效果
animationGroup:SetLooping("REPEAT")

-- 在动画组中创建一个动画效果
local animation = animationGroup:CreateAnimation()

-- 设置动画持续时间为2秒除以300，这通常用于创建平滑的动画效果
-- 这里的注释解释了动画的时间设置是基于每秒300帧的假设，以实现每2秒移动100像素的视觉效果
animation:SetDuration(2 / 1200)    -- 2 seconds to shift 100 pixels   -- 2 seconds to shift 100 pixels

local offset, offsetAdjust = -200, 10

-- 设置动画循环执行的脚本
animationGroup:SetScript("OnLoop", function(self)
    -- 增加偏移量以实现动画效果
    offset = offset + offsetAdjust
    -- 当偏移量达到或超过上限时，停止动画
    if offset >= 200 then
        offset = 200
        self:Stop()
    -- 当偏移量达到或低于下限时，停止动画并改变纹理颜色
    elseif offset <= -200 then
        offset = -200
        self:Stop()
        -- texture:SetVertexColor(1, 0, 1)
    end
    -- 根据偏移量调整框架位置，以实现动画效果
    frame:SetPoint("LEFT", offset, 0)
end)

frame:SetScript("OnEvent", function(self, event)
    if event == "PLAYER_REGEN_DISABLED" then
        if offset < 0 then
        print("PLAYER_REGEN_DISABLED")
            offsetAdjust = 3
            animationGroup:Play()
            -- texture:SetVertexColor(1, 1, 0)
        end
    elseif offset > 0 then
        print("PLAYER_REGEN_ENABLED")
        offsetAdjust = -3
        animationGroup:Play()
        -- texture:SetVertexColor(1, 0, 1)
    end
end)
frame:RegisterEvent("PLAYER_REGEN_DISABLED")    --进入战斗状态
frame:RegisterEvent("PLAYER_REGEN_ENABLED")     --推出战斗状态