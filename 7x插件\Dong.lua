local gradientColors = {"|cFFFFCC00","|cFFFFC404","|cFFFFBB08","|cFFFFB20D","|cFFFFAA11","|cFFFFA215","|cFFFF991A","|cFFFF901E","|cFFFF8822","|cFFFF8026","|cFFFF772A"}
local ColSize = 10;

-- 将每个字符存入表中,并且区分汉字和非汉字
local function countChineseAndNonChinese(str)
    local chineseCount = 0
    local nonChineseCount = 0
	local allChars = {}
    for utf8Char in string.gmatch(str, "([%z\1-\127\194-\244][\128-\191]*)") do
		table.insert(allChars, utf8Char)  -- 将每个字符存入表中
        if utf8Char:match("[\228-\233][\128-\191][\128-\191]") then
            chineseCount = chineseCount + 1
        else
            nonChineseCount = nonChineseCount + 1
        end
    end
    return chineseCount, nonChineseCount, allChars
end

-- 将每个字符存入表中
local function splitStringToTable(str)
	local allChars = {}
    for utf8Char in string.gmatch(str, "([%z\1-\127\194-\244][\128-\191]*)") do
		table.insert(allChars, utf8Char)  -- 将每个字符存入表中
    end
	return allChars
end

--给每个字符前添加渐变颜色代码
local function JianBian(msg)
	if ColSize > 1 then ColSize = ColSize - 1; else ColSize = 10; end
	msg = "超级无敌"..msg
	local msg = splitStringToTable(msg)
	local coloredString = ""
	for i = 1, #msg do
		local colorIndex = (i + ColSize - 1) % #gradientColors + 1  -- 计算颜色索引
		local color = gradientColors[colorIndex]
		coloredString = coloredString .. color ..msg[i]
	end
	return coloredString
end

--修改物品提示框名字
function ItemChangeName(self,uid)
	--local t_icons = "|TInterface\\Draft\\rarity_3:16|t"
	local itemName = self:GetItem()
	local tooltipname = self:GetName()
	local strs;
	local m = string.find(tooltipname,"Shopping")
	itemName = JianBian(itemName)
	if m then
		strs = _G[self:GetName().."TextLeft"..2]:GetText() --第一行是图标+名字 或名字
		local pos = string.find(strs, "|t")
		if pos ~= nil then
			local Iconpic = string.sub(strs, 1, pos +1)
			_G[self:GetName().."TextLeft"..2]:SetText(Iconpic..itemName);
		else
			_G[self:GetName().."TextLeft"..2]:SetText(itemName);
		end
	else
		strs = _G[self:GetName().."TextLeft"..1]:GetText() --第一行是图标+名字 或名字
		local pos = string.find(strs, "|t")
		if pos ~= nil then
			local Iconpic = string.sub(strs, 1, pos +1)
			_G[self:GetName().."TextLeft"..1]:SetText(Iconpic..itemName);
		else
			_G[self:GetName().."TextLeft"..1]:SetText(itemName);
		end
	end
end

local function GetTooltipLines(self)
	local tooltip = self
	local textLines = {}
	local regions = {tooltip:GetRegions()}
	print(regions)
	local count = 1
	for k, r in ipairs(regions) do
	  if r:IsObjectType("FontString") then
		table.insert(textLines, r:GetText())
		--print(count.."="..r:GetText())
		print(count)
		print(r:GetText())
		count = count + 1
	  end
	end
	return textLines
end
 
local function findLastLeadingPlus(self, strkey, rule)  
    local lines = {}  

	for i=1,self:NumLines() do
		local f = _G[self:GetName().."TextLeft"..i]
		local s = f:GetText()
		table.insert(lines, s)  
	end

    local lastLeadingPlusIndex = nil  
	if rule == 1 then	--从后往前遍历 倒叙 第一个字符相等
		for i = #lines, 1, -1 do  
			if lines[i]:sub(1, 1) == strkey then  
				lastLeadingPlusIndex = i  
				break  
			end  
		end  
	end
	
	if rule == 2 then	--从前往后遍历 顺序
		if not lastLeadingPlusIndex then
			for i = 1, #lines, 1 do
				if string.find(lines[i], strkey) then
					lastLeadingPlusIndex = i
					break
				end
			end
		end
	end

	if rule == 3 then	--从后往前遍历 倒叙
		if not lastLeadingPlusIndex then
			for i = #lines, 1, -1 do
				if string.find(lines[i], strkey) then
					lastLeadingPlusIndex = i
					break
				end
			end
		end
	end

    return lastLeadingPlusIndex  
end  

function GetDesStartIndex(self,...)
	 local Index = findLastLeadingPlus(self,"附魔：",2);
	 local isSiff = 1;
	
	 if not Index then
		Index = findLastLeadingPlus(self,"插槽", 2);
		if Index then
			for i = Index - 2, Index - 3, -1 do	-- 这个地方还有待修改 用倒叙吧
				local f = _G[self:GetName().."TextLeft"..i]
				local s = f:GetText()
				if s:sub(1,1) == "+" then	
					Index = i;
					isSiff = 0;
					break
				end
			end
		end		
	 end

	 if not Index then	-- 找不到"插槽"证明插槽已镶嵌
		Index = findLastLeadingPlus(self,"+", 1); -- 寻找+属性（遇到白板装备可能为nil）
		if Index then
			isSiff = 0;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"装备：", 3);
		if Index then
			isSiff = 1;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"使用：", 3);
		if Index then
			isSiff = 1;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"（%d+%/%d+）", 2);
		if Index then
			isSiff = 1;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"点护甲", 2);
		if Index then
			isSiff = 0;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"\"", 1);
		if Index then
			isSiff = 1;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"每秒伤害", 2);
		if Index then
			isSiff = 0;
		end
	 end

	 if not Index then	
		Index = findLastLeadingPlus(self,"物品等级", 2);
		if Index then
			isSiff = 0;
		end
	 end

	 --print(_G[self:GetName().."TextLeft"..Index]:GetText())
	 return Index, isSiff

end


local function GhostAddCustomStat(self)
	local index = nil
	local isSiff = nil;
	index,isSiff = GetDesStartIndex(self);
	if not index then
		return;
	end

	local f = _G[self:GetName().."TextLeft"..index]
	local s = f:GetText()
end

local function GhostAddSpellDescriptions(self, spelldes, spellLink, desLink)
	if not spellLink then return end
	if not spelldes then return end
	if not desLink then return end
	
	local index = nil
	local isSiff = nil;
	index,isSiff = GetDesStartIndex(self);
	if not index then return end

	local f = _G[self:GetName().."TextLeft"..index]
	local s = f:GetText()
	-- print("start width :"..f:GetWrappedWidth())
	f:SetWidth(300) -- 限制大小

	if SwitchConf and SwitchConf[DongConf.CONF_RandomSpell] then
		local value = SwitchConf[DongConf.CONF_RandomSpell]
		if value == "1" then
			if isSiff == 1 then
				f:SetText(spellLink.."\n\r"..s)
			elseif isSiff == 0 then
				f:SetText(s.."\n\r"..spellLink)
			elseif isSiff == nil then
				f:SetText(s.."\n\r"..spellLink)
			end
		elseif value == "2" then
			if isSiff == 1 then
				f:SetText("|cFF00FF00"..spelldes.."\n\r"..s) 
			elseif isSiff == 0 then
				f:SetText(s.."\n\r|cFF00FF00"..spelldes)
			elseif isSiff == nil then
				f:SetText(s.."\n\r|cFF00FF00"..spelldes)
			end
		elseif value == "3" then
			if spelldes then
				if isSiff == 1 then
					f:SetText(desLink.."\n\r"..s)
				elseif isSiff == 0 then
					f:SetText(s.."\n\r"..desLink)
				elseif isSiff == nil then
					f:SetText(s.."\n\r"..desLink)
				end
			end
		elseif value == nil then
			if isSiff == 1 then
				f:SetText(desLink.."\n\r"..s)
			elseif isSiff == 0 then
				f:SetText(s.."\n\r"..desLink)
			elseif isSiff == nil then
				f:SetText(s.."\n\r"..desLink)
			end
		end
	end
end

--根据spelllink显示法术信息
local function GhostAddSpellDescription(self, spellId)
	local spellLink = GhostGetSpellLink(spellId)

	if not spellLink then
		return
	end

	-- if not SpellDescriptionTooltip then
	-- 	CreateFrame("GameTooltip","SpellDescriptionTooltip",UIParent,"GameTooltipTemplate");
	-- 	SpellDescriptionTooltip:SetOwner(UIParent,"ANCHOR_NONE");
	-- end
	-- SpellDescriptionTooltip:ClearLines();
	-- SpellDescriptionTooltip:SetHyperlink(spellLink);
	-- print(SpellDescriptionTooltip:NumLines());
	-- local spelldess = _G["SpellDescriptionTooltipTextLeft"..SpellDescriptionTooltip:NumLines()]:GetText();
	-- print(spelldess)
	
	local index = nil
	local isSiff = nil;
	index,isSiff = GetDesStartIndex(self);
	if not index then
		return;
	end

	local f = _G[self:GetName().."TextLeft"..index]
	local s = f:GetText()
	-- print("start width :"..f:GetWrappedWidth())
	f:SetWidth(300) -- 限制大小

	local spelldes = GetSpellDescription(spellId)

	if SwitchConf and SwitchConf[DongConf.CONF_RandomSpell] then
		local value = SwitchConf[DongConf.CONF_RandomSpell]
		if value == "1" then
			if isSiff == 1 then
				f:SetText(spellLink.."\n\r"..s)
			elseif isSiff == 0 then
				f:SetText(s.."\n\r"..spellLink)
			elseif isSiff == nil then
				f:SetText(s.."\n\r"..spellLink)
			end
		elseif value == "2" then
			if isSiff == 1 then
				f:SetText("|cFF00FF00"..spelldes.."\n\r"..s) -- 当spelldes在前面时，法术插槽对应法术描述是在后面的也就是说[1][2]变成[2][1]，要解决的话可能要在函数外就获取spelldes然后在这里拼
			elseif isSiff == 0 then
				f:SetText(s.."\n\r|cFF00FF00"..spelldes)
			elseif isSiff == nil then
				f:SetText(s.."\n\r|cFF00FF00"..spelldes)
			end
		elseif value == "3" then
			if spelldes then
				if isSiff == 1 then
					f:SetText(spellLink.."\r|cFF00FF00"..spelldes.."\n\r"..s)
				elseif isSiff == 0 then
					f:SetText(s.."\n\r"..spellLink.."\r|cFF00FF00"..spelldes)
				elseif isSiff == nil then
					f:SetText(s.."\n\r"..spellLink.."\r|cFF00FF00"..spelldes)
				end
			end
		elseif value == nil then
			if isSiff == 1 then
				f:SetText(spellLink.."\r|cFF00FF00"..spelldes.."\n\r"..s)
			elseif isSiff == 0 then
				f:SetText(s.."\n\r"..spellLink.."\r|cFF00FF00"..spelldes)
			elseif isSiff == nil then
				f:SetText(s.."\n\r"..spellLink.."\r|cFF00FF00"..spelldes)
			end
		end
	end
end

--处理显示物品法术描述
local function HandleShowSpellDescription(self, spellids)
	if spellids == nil or type(spellids) ~= "table" then return end
	local spelldes, spellLink, spelldesLink = nil, nil, nil
	for key, value in pairs(spellids) do
		if spelldes ~= nil then
			spelldes = spelldes.."\n\r"..GetSpellDescription(tonumber(value))
		else
			spelldes = GetSpellDescription(tonumber(value))
		end

		if spellLink ~= nil then
			spellLink = spellLink.."\n\r"..GhostGetSpellLink(tonumber(value))
		else
			spellLink = GhostGetSpellLink(tonumber(value)) or ""
		end

		if spelldesLink ~= nil then
			spelldesLink = spelldesLink.."\n\r"..GhostGetSpellLink(tonumber(value)).."\r|cFF00FF00"..GetSpellDescription(tonumber(value))
		else
			spelldesLink = (GhostGetSpellLink(tonumber(value)) or "").."\r|cFF00FF00"..GetSpellDescription(tonumber(value))
		end
	end

	GhostAddSpellDescriptions(self, spelldes, spellLink, spelldesLink)

end

--根据自定义描述显示法术信息
local function GhostAddSpellDes(self, guid)
	local index = nil
	local isSiff = nil;
	index,isSiff = GetDesStartIndex(self);

	if not index then
		return;
	end

	local f = _G[self:GetName().."TextLeft"..index]
	local s = f:GetText()

	if isSiff == 0 or isSiff == nil then
		s = s.."\n";
	end

	for key, value in pairs(DongBar[guid].spells.spelldes) do
		-- local des = "装备："..value
		local des = value
		if isSiff == 1 then
			s = "|cFF00FF00"..des.."\r"..s
		elseif isSiff == 0 then
			s = s.."\r|cFF00FF00"..des
		elseif isSiff == nil then
			s = s.."\r|cFF00FF00"..des
		end
	end

	f:SetText(s)
end

--显示物品的随机法术描述
local function ShowRandomSpellDes(self, guid)
	if DongBar[guid] ~= nil and next(DongBar[guid]) then
		if DongBar[guid].spells.spelldes ~= nil and next(DongBar[guid].spells.spelldes) ~= nil then
			GhostAddSpellDes(self,guid)
		elseif DongBar[guid].spells.spellid ~= nil and next(DongBar[guid].spells.spellid) ~= nil then
			HandleShowSpellDescription(self,DongBar[guid].spells.spellid)
		end
	end
end

--显示物品的随机附魔
local function ShowRandomEnchantInfo(self,guid)
	local index, isSiff = GetDesStartIndex(self)
	if  not index then return end
	local f = _G[self:GetName() .. "TextLeft" .. index]
	local s = f:GetText()
	local str = ""
	if DongBar[guid].enchants ~= nil and next(DongBar[guid].enchants) ~= nil then
		if SwitchConf and SwitchConf[DongConf.CONF_RandomEnchant] then
			str = SwitchConf[DongConf.CONF_RandomEnchant]
		end
		for k, v in pairs(DongBar[guid].enchants) do
			str = str .. "\t\r" .. v;
		end
		if SwitchConf and SwitchConf[DongConf.CONF_RandomEnchant] then
			str = str .. SwitchConf[DongConf.CONF_RandomEnchant]
		end
	end
	if str == "" then
		return;
	end
	if isSiff == 1 then
		f:SetText(str .. "\r" .. s)
	elseif isSiff then
		f:SetText(s .. "\r" .. str)
	elseif isSiff == nil then
		f:SetText(s .. "\r" .. str)
	end
end

--显示拍卖行的随机附魔
local function ShowAHRandomEnchantInfo(self, guid, needShow)
	if needShow then
		if not DongBar or not DongBar[guid] or (DongBar[guid].updatestat == 1) then
			Ghost_SendData("CLIENT_ITEMGUID",tostring(guid))
		else
			local a = DongBar[guid].enchants
			if a == nil or next(a) == nil then
				return;
			end
			if DongBar[guid].enchants ~= nil then
				ShowRandomEnchantInfo(self,guid)
			end
		end
	end
end

--显示非拍卖行的随机附魔
local function ShowOtherRandomEnchantInfo(self, guid,needShow,isChat)
	if not needShow then
		if not DongBar or not DongBar[guid] or (DongBar[guid].updatestat == 1) then
			Ghost_SendData("CLIENT_ITEMGUID",tostring(guid)) 
		else
			local a = DongBar[guid].enchants
			if a == nil or next(a) == nil then
				return;
			end
			local palyerinfo = UnitGUID("player")
			local _, _, pguid = strsplit("-", palyerinfo)
			local ownerGuid = HexStrToDec(pguid)
			if DongBar[guid].OwnerGuid ~= ownerGuid then
				ShowRandomEnchantInfo(self,guid)
			elseif DongBar[guid].OwnerGuid == ownerGuid and isChat == 999999 then --显示聊天框的随机附魔
				ShowRandomEnchantInfo(self,guid)
			end
		end
	end
end

function GhostOnTooltipShow(self)
	local itemName, itemLink = self:GetItem()
	local itemSplits={strsplit(":", itemLink)};
	local itemid = tonumber(itemSplits[2]) or 0;
	local guid=tonumber(itemSplits[9]) or 0;
	local isChat = tonumber(itemSplits[8]) or 0;

	if guid < ITEMCUSENTRY then
		return;
	else
		guid = guid - ITEMCUSENTRY;
	end
	local needShow = (AuctionFrame and AuctionFrame:IsShown());		--拍卖行 可以判断
	local bankShow = (GuildBankFrame and GuildBankFrame:IsShown());	--公会银行 可以判断
	ItemChangeName(self,guid)
	--GhostAddCustomStat(self)
	ShowAHRandomEnchantInfo(self,guid, needShow)
	ShowOtherRandomEnchantInfo(self,guid, needShow,isChat)
	ShowRandomSpellDes(self,guid)

end

function GhostOnShoppingTooltipShow(self)
	local itemName, itemLink = self:GetItem()
	local itemSplits={strsplit(":", itemLink)};
	local itemid = tonumber(itemSplits[2]) or 0;
	local guid=tonumber(itemSplits[9]) or 0;
	self:AddLine("物品ID: " .. itemid, 0, 1, 1);
	if guid < ITEMCUSENTRY then
		return;
	else
		guid = guid - ITEMCUSENTRY;
	end
	ShowRandomSpellDes(self,guid)
end

GameTooltip:HookScript("OnTooltipSetItem", GhostOnTooltipShow)	-- 735这个是实时更新	--API网站搜索script handlers
ItemRefTooltip:HookScript("OnTooltipSetItem",GhostOnTooltipShow)
ShoppingTooltip1:HookScript("OnTooltipSetItem", GhostOnShoppingTooltipShow)
ShoppingTooltip2:HookScript("OnTooltipSetItem", GhostOnShoppingTooltipShow)


local JBelapsed = 0
local function ItemNameJBOnUpdate(self, elapsed)
	local _, itemLink = self:GetItem()
	if not itemLink then
		return
	end
	local itemSplits={strsplit(":", itemLink)};
	local guid=tonumber(itemSplits[9]) or 0;
	if guid < ITEMCUSENTRY then
		return;
	else
		guid = guid - ITEMCUSENTRY;
	end
	if JBelapsed >= 10 then
		ItemChangeName(self)
		JBelapsed = 0
	end
	JBelapsed = JBelapsed + 1
end

GameTooltip:HookScript("OnUpdate",ItemNameJBOnUpdate)

print("获取大秘境")