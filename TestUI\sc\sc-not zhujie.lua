
local GL = "Interface\\Spellbook\\UI-GlyphFrame"

local DONGD_DELAY_ONE_MINUTE = 1

local DONGD_DELAY_ONE_MINUTES = 60

local IsOK,IsAllCreate = false,false

local LMall,RMall,MallTitle,MenuOnButton,MenuOffButton,HomePage,HomeTex	

local LScroll,LSlider,LSlidertex,LScrollChild,LScrollUpButton,LScrollDownButton,LScrollTexTop,LScrollTexMiddle,LScrollTexBottom

local RScroll,RSlider,RSlidertex,RScrollChild,RScrollUpButton,RScrollDownButton,RScrollTexTop,RScrollTexMiddle,RScrollTexBottom

local LHScroll,LHSlider,LHSlidertex,LHScrollChild,LHScrollUpButton,LHScrollDownButton,LHTex,LHScrollTexTop,LHScrollTexMiddle,LHScrollTexBottom

local MallItemId,MallClass,MallPClass,MallSClass

local CloseButton,HomeFrameCloseButton

local RMallButtonsInfo = {}

local RMallButtons = {}

local RMallClass = {}

local SubFrame = {}

local HPClassPos = {}

local HomeShowFrame

local oldI 

local T1 

local T = 
{
	ParentFrameBackgroundLeft	= "Interface\\ICONS\\progress_frame_left",
	ParentFrameBackgroundRight	= "Interface\\ICONS\\progress_frame_right",
	ParentFrameTitle			= "Interface\\ICONS\\bt",
	SubFrameBackground			= "Interface\\ICONS\\yuanjiaolan",
	TabClassFrameBackground		= "Interface\\ICONS\\LineTab1",
	ItemClassButton				= "Interface\\ICONS\\_button_h",
	FontBackground				= "Interface\\ICONS\\languangditu",
	ButtonHighlighLight1		= "Interface\\BUTTONS\\CheckButtonHilight",
	ButtonHighlighLight2		= "Interface\\ICONS\\button_h2",
	ButtonHighlighLight3		= "Interface\\Glues\\Common\\Glue-Panel-Button-Highlight",
	Font						= "Fonts\\ZYHei.ttf",
	SupermarketButton			= "Interface\\ICONS\\shangchengon",
	SupermarketOffButton		= "Interface\\ICONS\\shangchengoff",
	JiFen						= "Interface\\ICONS\\jf1",
	JinBi						= "Interface\\ICONS\\jinbi",
	Vip							= "Interface\\ICONS\\Vip",
	ItemExchange				= "Interface\\ICONS\\wpdh",
	AlliancePVP					= "Interface\\ICONS\\lmry",
	HordePVP					= "Interface\\ICONS\\blry",
	AlliancePVPPoint			= "Interface\\ICONS\\lmryd",
	HordePVPPoint				= "Interface\\ICONS\\blryd",
	ArenaPVP					= "Interface\\ICONS\\jjc",
	ArenaPVPPoint				= "Interface\\ICONS\\jjd",
	EXP							= "Interface\\ICONS\\bigxp",
	XP							= "Interface\\ICONS\\xp",
	Tab							= "Interface\\ICONS\\tabb",
	CS1							= "Interface\\ICONS\\ces1",
	Loottoastatlas				= "Interface\\ICONS\\loottoastatlas",
	ToolBack					= "Interface\\MINIMAP\\TooltipBackdrop-Background",
	HB							= "Interface\\ICONS\\background_11",
	HBBK						= "Interface\\DialogFrame\\UI-DialogBox-Gold-Border",
	CCS							= "Interface\\ICONS\\scenariohordealliance",
	TSBK						= "Interface\\ICONS\\spellpush-frame-ysera",
	cuxiao1						= "Interface\\ICONS\\cuxiao1",	--热销
	cuxiao2						= "Interface\\ICONS\\cuxiao2",	--推荐
	cuxiao3						= "Interface\\ICONS\\cuxiao3",	--限时
	cuxiao4						= "Interface\\ICONS\\cuxiao4",	--新品
	cuxiao5						= "Interface\\ICONS\\cuxiao5",	--折扣
}

local SYTB = 
{
	t1						= "Interface\\ICONS\\000001",
	t2						= "Interface\\ICONS\\000002",
	t3						= "Interface\\ICONS\\000003",
	t4						= "Interface\\ICONS\\000004",
	t5						= "Interface\\ICONS\\000005",
	t6						= "Interface\\ICONS\\000006",
	t7						= "Interface\\ICONS\\000007",
}

local TCoord = 
{
	--TooltipBackdrop-Background
	["Itemborder-gold"]={0.000976562, 0.0576172, 0.691406, 0.804688},
	["Itemborder-blue"]={0.000976562, 0.0576172, 0.574219, 0.6875, false, false},
	["Itemborder-glow"]={0.833008, 0.899414, 0.00195312, 0.134766, false, false},
	["Itemborder-green"]={0.000976562, 0.0576172, 0.808594, 0.921875, false, false},
	["Itemborder-orange"]={0.118164, 0.174805, 0.574219, 0.6875, false, false},
	["Itemborder-purple"]={0.176758, 0.233398, 0.574219, 0.6875, false, false},
	["Itemborder-heirloom"]={0.0595703, 0.116211, 0.574219, 0.6875, false, false},
	["Itemborder-artifact"]={0.901367, 0.958008, 0.00195312, 0.115234, false, false},
	--ScenarioHordeAlliance
	["AllianceScenario-TitleBG"]={ 0.00195312, 0.914062, 0.00195312, 0.277344},
	["AllianceScenario-TrackerHeader"]={0.00195312, 0.476562, 0.560547, 0.710938},
	["HordeScenario-TitleBG"]={0.00195312, 0.914062, 0.28125, 0.556641},
	["HordeScenario-TrackerHeader"]={0.480469, 0.955078, 0.560547, 0.710938},
}



function CountTB(tabledate)
	local count = 0
	if tabledate then
		for i,v in pairs(tabledate) do
		 count = count + 1
		end
	end
	return count
end



function removeRepeateds(zz)
local aa={}
  for key,val in pairs(zz) do
     aa[val]=true
  end
local bb={}
  for key,val in pairs(aa) do
     table.insert(bb,key)                
  end
return bb
end



function DONGD_timediff(long_time,short_time)
	local n_short_time,n_long_time,carry,diff = date('*t',short_time),date('*t',long_time),false,{}
	local colMax = {60,60,24,date('*t',time{year=n_short_time.year,month=n_short_time.month+1,day=0}).day,12,0}
	n_long_time.hour = n_long_time.hour - (n_long_time.isdst and 1 or 0) + (n_short_time.isdst and 1 or 0) -- handle dst
	for i,v in ipairs({'sec','min','hour','day','month','year'}) do
		diff[v] = n_long_time[v] - n_short_time[v] + (carry and -1 or 0)
		carry = diff[v] < 0
		if carry then
			diff[v] = diff[v] + colMax[i]
		end
	end
	return diff
end



function DONGD_ForSetSubFrameElement(t)
	for k,v in pairs(t) do
		for i,j in pairs(v) do
			
			if string.match(i,"Button") and not string.match(i,"ReqItemButton") and not string.match(i,"HomeFrame") then
				local name = j:GetName()
				setglobal(name,nil)
			end

			if string.match(i,"ReqItemButton") and not string.match(i,"HomeFrame") then
				for n,m in pairs(j) do
					local name = m:GetName()
					setglobal(name,nil)
				end
			end
		end
	end
end



function DONGD_ReSetHomePClassButton(Type)	
	if Type == "Hbutton" then
		for k,v in pairs(HPClassPos) do
			local name = v.button:GetName()
			setglobal(name,nil)
		end
	end
end



function DONGD_ResetHomeFrameElement(i)
	
	for i,j in pairs(SubFrame[i]) do
		if string.match(i,"HomeFrame") and not string.match(i,"HomeFrameReqItemButton") and string.match(i,"Button") then	
			local name = j:GetName()
				setglobal(name,nil)
		end
		
		if string.match(i,"HomeFrameReqItemButton") then
			for n,m in pairs(j)do
				local name = m:GetName()
					setglobal(name,nil)
			end
		end
	end
	
end


function Split(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 


function Event(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
	
		local NewMsg = string.format("消息头 = %s 消息 = %s", h, msg)

		local list = Split(msg,";:;")	

		if h == "DUEL_ACCEPT" and msg == "OK" then
			AcceptDuel();						
			StaticPopup_Hide("DUEL_REQUESTED") 	
		end
		
		
		if h == "MALL_UI_FIRST_GROUP_DATA" then
			local i = tonumber(list[1])
			SubFrame[i] = {}  
			local s1, s2, s3, s4, s5, s6, s7, s8, s9, s10 = strsplit(":", string.match(list[2], "item[%-?%d:]+")) 

			SubFrame[i].id = tonumber(list[1])				
			SubFrame[i].itemlink = list[2]					
			SubFrame[i].itemid = tonumber(s2)				
			SubFrame[i].pclass = list[3]					
			SubFrame[i].sclass = list[4]					
			SubFrame[i].reqvip = list[5]					
			SubFrame[i].reqmoney = list[6]					
			SubFrame[i].reqexp = list[7]					
			SubFrame[i].reqhonor = list[8]					
			SubFrame[i].reqarena = list[9]					
			SubFrame[i].IsHomeShow = list[10]				
			SubFrame[i].PromotionType = list[11]			
			SubFrame[i].Ppos = tonumber(list[12])			
			SubFrame[i].Spos = tonumber(list[13])			
			SubFrame[i].Hpos = tonumber(list[14])			
			SubFrame[i].ElementNum = tonumber(list[15])		
			local Dis = tonumber(list[16])/100				
			SubFrame[i].Dis = Dis
			SubFrame[i].s_time = tonumber(list[17])			
			SubFrame[i].e_time = tonumber(list[18])			
			SubFrame[i].reqjf = list[19]					
			SubFrame[i].DELAY = 1							
			SubFrame[i].IsDis = false
			SubFrame[i].SubFramSellInfo	= {}				
			SubFrame[i].reqitemid = {}						
			SubFrame[i].reqitemlink = {}					
			SubFrame[i].reqitemval = {}						
			SubFrame[i].ReqItemButton = {}					
			SubFrame[i].ReqItemFont	= {}					
			
		end
		
		if h == "MALL_UI_SECOND_GROUP_DATA" then
			
			local i = tonumber(list[1])	
			local j = tonumber(list[2])	
			
			if list[3] ~= nil then
			local ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, ss9, ss10 = strsplit(":", string.match(list[3], "item[%-?%d:]+")) 
				SubFrame[i].reqitemid[j] = tonumber(ss2)	
				SubFrame[i].reqitemlink[j] = list[3]		
				SubFrame[i].reqitemval[j] = list[4]			
			end
		end
		
		if h == "MALL_UI_THIRD_GROUP_DATA" then
			RMallButtonsInfo[list[1]] = 1			
			
			if RMallClass[list[1]] == nil then
				RMallClass[list[1]] = {}			
				table.insert(RMallClass[list[1]],list[2])	
			else
				table.insert(RMallClass[list[1]],list[2])
			end
		end
		
		if h == "MALL_UI_FOURTH_GROUP_DATA" then
			
			HPClassPos[list[1]] = {}
			
			HPClassPos[list[1]].num = tonumber(list[3])
			
		end
		
		if h == "MALL_ALL_DATA" and msg == "OK" then
		 
			IsOK = true

		end
		 
		 
		if h == "MALL_UI_T1" then
			T1 = msg
		end
	 
		if h == "MALL_DATA_RESET" and msg == "OK" then
			LMall:Hide()
			setglobal(LMall:GetName(),nil)
			setglobal(RMall:GetName(),nil)
			setglobal(LScroll:GetName(),nil)
			setglobal(LScrollChild:GetName(),nil)
			MenuOffButton:Hide()
			MenuOnButton:Show()
			DONGD_ForSetSubFrameElement(SubFrame)
			SubFrame = nil
			SubFrame = {}
			RMallButtonsInfo = nil
			RMallButtonsInfo = {}
			RMallButtons = nil
			RMallButtons = {}
			RMallClass = nil
			RMallClass = {}
			IsOK = false
			IsAllCreate = false
		 end

	end
end

local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("CHAT_MSG_ADDON")
MsgReceivers:SetScript("OnEvent", Event)



function pp(t)

 for k,v in pairs(t) do
	for i,j in pairs(t[k]) do
		print("Pclass = "..k.."; Sclass = "..j)
	end
 end

end


function texrand()
	
	local val = random(1,7);
	
	if val == 1 then
	return SYTB["t1"]
	elseif  val == 2 then
	return SYTB["t2"]
	elseif  val == 3 then
	return SYTB["t3"]
	elseif  val == 4 then
	return SYTB["t4"]
	elseif  val == 5 then
	return SYTB["t5"]
	elseif  val == 6 then
	return SYTB["t6"]
	elseif  val == 7 then
	return SYTB["t7"]
	end
end



function GetPClassPosIsFrame(pos)
	
	if pos == 1 then
		if LHTex ~= nil then
			return LHTex
		else
			return LHScrollChild
		end
	else
		local poss = pos - 1
		local button = GetPClassPosFrame(poss)
		return button
	end

end



function GetPClassPosFrame(pos)
	for k,v in pairs(HPClassPos)do
		if v.pos == pos then
			return v.button
		end
	end
end



function GetPCliassPosHigh(pos)

	if pos == 0 then
		return 0
	end
	
	for k,v in pairs(HPClassPos)do
		if v.pos == pos then
			local num = math.ceil(v.num/3)
			local high = 100 * num + 50 
			return high
		end
	end
end



function GetLHScrollChildResetHigh()

	local LHTexhigh = 0
	
	if LHTex ~= nil then
		LHTexhigh = LHTex:GetHeight()		
	end
	
	local num = 0							
	
	local buttonhigh						
	
	local poshigh = 0						
	
	for k,v in pairs(HPClassPos) do
		num = num + 1						
		
		buttonhigh = v.button:GetHeight()	
		
		local count = math.ceil(v.num/3)	

		poshigh = poshigh + count * 100	+ 50	
	end

	local buttonallhigh = buttonhigh * num	
	
	local high = LHTexhigh + buttonallhigh + poshigh
	
	return high
end



function GetSubFrameS(Category,pos,pname)
	if Category == "ParentS" then
		for k,v in pairs(SubFrame) do	
			if SubFrame[k].Ppos == (pos-3) then
				if SubFrame[k].Frame ~= nil then
				return SubFrame[k].Frame
				end
			end
		end
	end
	
	if Category == "SubS" then
		for k,v in pairs(SubFrame) do	
			if SubFrame[k].Spos == (pos-3) then
				if SubFrame[k].Frame ~= nil then
				return SubFrame[k].Frame
				end
			end
		end
	end
	
	if Category == "Home" then
		for k,v in pairs(SubFrame) do	
			if SubFrame[k].Hpos == (pos-3) then
				if SubFrame[k].pclass == pname then
					if SubFrame[k].HomeShowButton ~= nil then
						return SubFrame[k].HomeShowButton
					end
				end
			end
		end
	end
end



function GetSubFrameSellInfoFrameS(i,j,m)
	local S = SubFrame[i] 
		
		if j == 0 then
			return nil
		end
	
	
	for k,v in pairs(S.SubFramSellInfo) do
		if k == j then
			if v == "money" then
			if not m then return S.ReqMoneyButton else return S.HomeFrameReqMoneyButton end
			elseif v == "exp" then
			if not m then return S.ReqExpButton else return S.HomeFrameReqExpButton end
			elseif v == "honor" then
			if not m then return S.ReqHonorButton else return S.HomeFrameReqHonorButton end
			elseif v == "arena" then
			if not m then return S.ReqArenaButton else return S.HomeFrameReqArenaButton end
			elseif v == "item1" then
			if not m then return S.ReqItemButton[1] else return S.HomeFrameReqItemButton[1] end
			elseif v == "item2" then
			if not m then return S.ReqItemButton[2] else return S.HomeFrameReqItemButton[2] end
			elseif v == "item3" then
			if not m then return S.ReqItemButton[3] else return S.HomeFrameReqItemButton[3] end
			elseif v == "item4" then
			if not m then return S.ReqItemButton[4] else return S.HomeFrameReqItemButton[4] end
			elseif v == "item5" then
			if not m then return S.ReqItemButton[5] else return S.HomeFrameReqItemButton[5] end
			elseif v == "jifen" then
			if not m then return S.ReqJFButton else return S.HomeFrameReqJFButton end
			end
		end
	end

	
end



function DONGD_CreateMallParentFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)
		
local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	
	
	f:SetSize(Length, Width)
	
	if BgFile ~= ""  and string.match(Name,"Scroll") == nil then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
	end
		
	if Name == "RMall" then
		f:SetPoint("LEFT",LMall,"RIGHT",Ofsx,Ofsy)
	else
		f:SetPoint(Point,Ofsx,Ofsy)
	end
	
	if string.match(Name,"Scroll") ~= nil then
		local ScrollBcakdrop = f:CreateTexture("$parent".."ScrollBcakdrop","BACKGROUND",nil)
		ScrollBcakdrop:SetAllPoints(f)
		
		ScrollBcakdrop:SetTexture(T["ParentFrameBackgroundLeft"])
		ScrollBcakdrop:SetTexCoord(0.3,0.8,0.3,0.8)
		
	end	
	
	if ParentFrame == UIParent or string.match(Name,"HomeShowFrame") then
		f:RegisterForDrag("LeftButton")
		f:SetToplevel(true)
		f:SetClampedToScreen(true)
		f:SetMovable(true)
		f:EnableMouse(true)
		f:SetScript("OnDragStart", f.StartMoving)
		f:SetScript("OnHide", f.StopMovingOrSizing)
		f:SetScript("OnDragStop", f.StopMovingOrSizing)
	end
	
	if ParentFrame == UIParent then
		f:Hide()
	end
	
	if string.match(Name,"Time") ~= nil then
	f:SetBackdropColor(255/255,255/255,255/255,0.15)
	end
	
	return f
end



function DONGD_CreateMallSubFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile,object,number,ShowCategory) 
		
local f = CreateFrame(FrameType,"$parent"..Name,ParentFrame,InheritsFrame)
	
	f:SetSize(Length, Width)
	
	if ShowCategory == "ALL" then
		if number <= 3 then
			if number == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif number == 2 then
				f:SetPoint("TOP",0,0)
			elseif number == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			f:SetPoint("TOP",object[(number-3)].TestFrame,"BOTTOM",0,25)	
		end
	end
	
	if ShowCategory == "ParentS" then	
		local i = object[number].Ppos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local Frame = GetSubFrameS(ShowCategory,i)
			f:SetPoint("TOP",Frame,"BOTTOM",0,0)	
		end
	end
	
	if ShowCategory == "SubS" then		
		local i = object[number].Spos
		if i <= 3 then
			if i == 1 then
				f:SetPoint("TOPLEFT",0,0)
			elseif i == 2 then
				f:SetPoint("TOP",0,0)
			elseif i == 3 then
				f:SetPoint("TOPRIGHT",0,0)
			end
		else
			local Frame = GetSubFrameS(ShowCategory,i)
			f:SetPoint("TOP",Frame,"BOTTOM",0,0)	
		end
	end
	
	if BgFile ~= "" then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 10, insets = { left =0, right = 0, top = 5, bottom = 5 }});
	end
		

	f:SetBackdropColor(255/255,255/255,255/255,0.25)

	return f
	
end



function DONGD_CreateButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,IsHideButton,IsScrollB)
			
		local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
		if BgFile ~= "" then 
			Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
		end
		
		if IsScrollB then
			if string.match(Name,"DownButton") ~= nil then
			Button:SetNormalTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Down")
			Button:SetDisabledTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Disabled")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-ScrollBar-ScrollDownButton-Highlight","ADD")
			else
			Button:Disable()
			Button:SetNormalTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Down")
			Button:SetDisabledTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Disabled")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-ScrollBar-ScrollUpButton-Highlight","ADD")
			end
		end
		
		Button:SetSize(Length, Width)
		
		
		if Name == "HomePage" then
		Button:SetPoint("TOPLEFT", ParentFrame,"TOPRIGHT",Ofsxm, Ofsym)
		else
		Button:SetPoint(Point, Ofsxm, Ofsym)
		end
		
		

		Button:EnableMouse(true)
		
		if BgFile == T["ItemClassButton"] then
			Button:SetHighlightTexture(T["ButtonHighlighLight2"],"ADD")
		elseif  Text == "" and BgFile == "" and string.match(Name,"CloseButton") then
			Button:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
			Button:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
			Button:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
		else
			Button:SetHighlightTexture(T["ButtonHighlighLight1"],"ADD")
		end
		
		
		if string.match(Name,"TT") ~= nil then
		
		local tex = DONGD_CreateTex(Button,"LHTex","ARTWORK",nil,"TOP",nil,"",0,0,T["CCS"],Length,Width)

		tex:SetTexCoord(0.00195312, 0.476562, 0.560547, 0.710938)
		end
		
		
		
		local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
		
		ButtonFontStr:SetFont(T["Font"], 15)	
		
		ButtonFontStr:SetAllPoints(Button)
		
		ButtonFontStr:SetText(Text)
		
		if IsHideButton == true then
			Button:Hide()
		end
		
		return Button;
end



function DONGD_CreateButtonInSubFrame(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,BgFile,...)
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	Button:SetSize(Length, Width)
	
	Sell,ElementNum = ...
	
	if not ElementNum then
		if not Sell then
			if relativeFrame == nil and relativePoint == "" then
				Button:SetPoint(Point, Ofsxm, Ofsym)
			else
				Button:SetPoint(Point,relativeFrame,relativePoint,Ofsxm, Ofsym)
			end	
		elseif Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	elseif ElementNum > 5 then
		if ElementNum > 6 then
			if Sell == 1 then
				Button:SetPoint("LEFT", 5, 25)
			elseif Sell == 6 then
				Button:SetPoint("LEFT", 130, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		else
			if Sell == 1 then
				Button:SetPoint("LEFT", 5, 25)
			elseif Sell == 4 then
				Button:SetPoint("LEFT", 130, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		end
	else
		if Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	end
	
	if string.match(Name,"SellButton") then
		local Ntex = Button:CreateTexture("$parent".."Ntex","ARTWORK",nil)
		local Ptex = Button:CreateTexture("$parent".."Ptex","ARTWORK",nil)
		local Dtex = Button:CreateTexture("$parent".."Dtex","ARTWORK",nil)
		local Htex = Button:CreateTexture("$parent".."Htex","ARTWORK",nil)
		
		Ntex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Up")
		Ptex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Down")
		Dtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Disabled")
		Htex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Highlight")
		
		Ntex:SetTexCoord(0,0.578125,0,0.75)
		Ptex:SetTexCoord(0,0.578125,0,0.75)
		Dtex:SetTexCoord(0,0.578125,0,0.75)
		Htex:SetTexCoord(0,0.625,0,0.685)
		
		Ntex:SetAllPoints(Button)
		Ptex:SetAllPoints(Button)
		Dtex:SetAllPoints(Button)
		Htex:SetAllPoints(Button)
		
		Button:SetNormalTexture(Ntex)
		Button:SetPushedTexture(Ptex)
		Button:SetDisabledTexture(Dtex)
		Button:SetHighlightTexture(Htex)
		else
		Button:SetHighlightTexture(T["ButtonHighlighLight1"])
	end
	

	Button:EnableMouse(true)	
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)
	
	ButtonFontStr:SetTextColor(1.0,0.843,0)
	
	return Button
end



function DONGD_CreateButtonInHomeShowFrame(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,BgFile,...)
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	Button:SetSize(Length, Width)
	
	Sell,ElementNum = ...
	
	if not ElementNum then
		if not Sell then
			if relativeFrame == nil and relativePoint == "" then
				Button:SetPoint(Point, Ofsxm, Ofsym)
			else
				Button:SetPoint(Point,relativeFrame,relativePoint,Ofsxm, Ofsym)
			end	
		elseif Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	elseif ElementNum > 5 then
		if ElementNum > 6 then
			if Sell == 1 then
				Button:SetPoint("LEFT", 20, 25)
			elseif Sell == 6 then
				Button:SetPoint("LEFT", 140, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		else
			if Sell == 1 then
				Button:SetPoint("LEFT", 20, 25)
			elseif Sell == 4 then
				Button:SetPoint("LEFT", 140, 25)
			else
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
			end
		end
	else
		if Sell == 1 then
			Button:SetPoint("CENTER", -40, 25)
		else
			Button:SetPoint("TOP",relativeFrame,"BOTTOM",0, 0)
		end
	end
	
	if string.match(Name,"SellButton") then
		local Ntex = Button:CreateTexture("$parent".."Ntex","ARTWORK",nil)
		local Ptex = Button:CreateTexture("$parent".."Ptex","ARTWORK",nil)
		local Dtex = Button:CreateTexture("$parent".."Dtex","ARTWORK",nil)
		local Htex = Button:CreateTexture("$parent".."Htex","ARTWORK",nil)
		
		Ntex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Up")
		Ptex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Down")
		Dtex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Disabled")
		Htex:SetTexture("Interface\\Glues\\Common\\Glue-Panel-Button-Highlight")
		
		Ntex:SetTexCoord(0,0.578125,0,0.75)
		Ptex:SetTexCoord(0,0.578125,0,0.75)
		Dtex:SetTexCoord(0,0.578125,0,0.75)
		Htex:SetTexCoord(0,0.625,0,0.685)
		
		Ntex:SetAllPoints(Button)
		Ptex:SetAllPoints(Button)
		Dtex:SetAllPoints(Button)
		Htex:SetAllPoints(Button)
		
		Button:SetNormalTexture(Ntex)
		Button:SetPushedTexture(Ptex)
		Button:SetDisabledTexture(Dtex)
		Button:SetHighlightTexture(Htex)
		else
		Button:SetHighlightTexture(T["ButtonHighlighLight1"])
	end
	

	Button:EnableMouse(true)	
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)
	
	ButtonFontStr:SetTextColor(1.0,0.843,0)
	
	return Button
end



function DONGD_CreateClassButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,num,object)
		
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	Button:SetSize(Length, Width)
	
	if num == 1 then 
		Button:SetPoint("TOP",Ofsxm,Ofsym)
	else
		Button:SetPoint("TOP",object[num - 1],"BOTTOM",0,0)
	end
	
	Button:SetHighlightTexture(T["ButtonHighlighLight2"],"ADD") 
	
	Button:EnableMouse(true)
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)

	return Button;

end



function DONGD_CreateHomeClassAndItemButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,BgFile,...)
	
	local classname,object,val,pname = ...
	
	local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		
	if BgFile ~= "" then 
		Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	end
	
	Button:SetSize(Length, Width)
	
	if classname ~= "show" then
		if relativeFrame == nil then
			Button:SetPoint(Point, Ofsxm, Ofsym)
		else
			Button:SetPoint(Point,relativeFrame,relativePoint, Ofsxm, Ofsym)
		end
	end
	
	Button:EnableMouse(true)	
	
	if classname == "PClass" then
	
		local tex = DONGD_CreateTex(Button,Name.."LHTex","ARTWORK",nil,"TOP",nil,"",0,0,T["CCS"],Length,Width)
		
		tex:SetTexCoord(0.00195312, 0.476562, 0.560547, 0.710938)
	
	end
	
	if classname == "show" then
		local i = object[val].Hpos
		
		if i <= 3 then
			if i == 1 then
				Button:SetPoint("TOPLEFT",relativeFrame,"BOTTOMLEFT",-220,-50)
			elseif i == 2 then
				Button:SetPoint("TOP",relativeFrame,"BOTTOM",0,-50)
			elseif i == 3 then
				Button:SetPoint("TOPRIGHT",relativeFrame,"BOTTOMRIGHT",220,-50)
			end
		else
			local sFrame = GetSubFrameS("Home",i,pname)
			
			Button:SetPoint("TOP",sFrame,"BOTTOM",0,-50)	
		end
	end
	
	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
	
	ButtonFontStr:SetFont(T["Font"], 15)	
	
	ButtonFontStr:SetAllPoints(Button)
	
	ButtonFontStr:SetText(Text)
	
	return Button
	
end



function DONGD_CreateScrollFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)

	local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	
	f:SetSize(Length,Width)
	
	f:SetPoint(Point,ParentFrame,"CENTER",Ofsx,Ofsy)

	if BgFile ~= "" then
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
	else
		local textest = f:CreateTexture("$parent".."Tex","BACKGROUND",nil)

		textest:SetAllPoints(f)
		textest:SetTexture(0.0,0.0,0.0,0.0)
	end

	return f
end



function DONGD_CreateSliderFrame(FrameType,ParentName,ParentFrame,InheritsFrame,Orientation,Type)

	local Slider = CreateFrame(FrameType,"$parent".."Slider",ParentFrame,InheritsFrame)
	Slider:SetOrientation(Orientation)
	Slider:SetMinMaxValues(0,100)
	Slider:SetValue(0)
	Slider:SetValueStep(1.0)
	
	if Type == "L" then
		Slider:SetPoint("LEFT",ParentFrame,"RIGHT",-38,0)
		Slider:SetSize(25,630)
	elseif Type == "R" then
		Slider:SetPoint("TOPLEFT",ParentFrame,"TOPRIGHT",-28,-12)
		Slider:SetSize(25,610)
	elseif Type == "H" then
		Slider:SetPoint("LEFT",ParentFrame,"RIGHT",-38,0)
		Slider:SetSize(25,630)
	end
	
	
	local tt = Slider:CreateTexture("tt","HIGHLIGHT",nil)
	tt:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
	tt:SetSize(32, 32)
	local Slidertex = Slider:SetThumbTexture(tt)
	
	return Slider,Slidertex
	
end



function DONGD_CreateScrollTex(Frame,Name,Layer,InheritsFrame,High,Type)
	
	local ScrollTexTop = Frame:CreateTexture("$parent"..Name.."ScrollTexTop",Layer,InheritsFrame)
		ScrollTexTop:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexTop:SetSize(31,256)
		ScrollTexTop:SetTexCoord(0,0.484375,0,1.0)
	
	local ScrollTexMiddle = Frame:CreateTexture("$parent"..Name.."ScrollTexMiddle",Layer,InheritsFrame)
		ScrollTexMiddle:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexMiddle:SetSize(31,600)
		ScrollTexMiddle:SetTexCoord(0,0.484375,0.2,1.0)
		
	local ScrollTexBottom = Frame:CreateTexture("$parent"..Name.."ScrollTexBottom",Layer,InheritsFrame)
		ScrollTexBottom:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
		ScrollTexBottom:SetSize(31,106)
		ScrollTexBottom:SetTexCoord(0.515625,1.0,0,0.4140625)
		
		if Type == "L" then
			ScrollTexTop:SetPoint("TOPRIGHT",-10,-20)
			ScrollTexMiddle:SetPoint("TOPRIGHT",-10,-20)
			ScrollTexBottom:SetPoint("BOTTOMRIGHT",-10,20)
		else
			ScrollTexTop:SetPoint("TOPRIGHT",0,0)
			ScrollTexMiddle:SetPoint("TOPRIGHT",0,0)
			ScrollTexBottom:SetPoint("BOTTOMRIGHT",0,0)
		end
		
		return ScrollTexTop,ScrollTexMiddle,ScrollTexBottom
end



function DONGD_TexSetTexCoord(Tex,Coord)

	Tex:SetTexCoord(Coord[1],Coord[2],Coord[3],Coord[4])
	
end



function DONGD_CreateTex(Frame,Name,Layer,InheritsFrame,Point,relativeFrame,relativePoint,ofsx,ofsy,filename,sizex,sizey)

	local tex = Frame:CreateTexture("$parent"..Name,Layer,InheritsFrame)
	
	tex:SetTexture(filename)
	
	tex:SetSize(sizex,sizey)	
	
	if relativeFrame ~= nil and relativePoint ~= "" then
		tex:SetPoint(Point,relativeFrame,relativePoint,ofsx,ofsy)	
	elseif ofsx == 0 and ofsy == 0 then
		tex:SetAllPoints(Frame)
	else
		tex:SetPoint(Point, ofsx, ofsy)	
	end
	
	return tex

end



function DONGD_CreateFontString(Frame,Name,layer,Inherits,Fonts,Fsize,Point,relativeFrame,relativePoint,Ofsxm,Ofsym,Msg,rgb,IsIf)
	local fs
	
	if layer ~= "" and Inherits ~= "" then
		fs = Frame:CreateFontString(Name,layer,Inherits)
	else
		fs = Frame:CreateFontString(Name)
	end
	
	if Fonts == "" then 
		fs:SetFontObject(GameFontNormalLarge)
		else
		fs:SetFont(Fonts, Fsize)
	end
	
	if rgb ~= "" then
		local r,g,b = self:ColorPicker(strsplit(",",rgb))
		fs:SetTextColor(r,g,b)
	end	
	
	if IsIf then
	fs:SetSize(200,30) 			
	fs:SetJustifyH("CENTER")
	else
	fs:SetJustifyH("RIGHT")		
	end

	fs:SetJustifyV("TOP")		
	
	if relativePoint ~= nil then
	fs:SetPoint(Point,relativeFrame,relativePoint,Ofsxm,Ofsym)
	else
	fs:SetPoint(Point,Ofsxm,Ofsym)
	end
	
	fs:SetText(Msg)
	
	return fs
	
end



function PrepareScript(object, text, script, itemlink,IsScrollButton)
	
	  if text then	

		if string.match(object:GetName(),"HomeFrame") and not string.match(object:GetName(),"Sell") then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(HomeShowFrame, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(HomeShowFrame, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
		else
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
		end
      end
	  
	  if itemlink then
		if string.match(object:GetName(),"HomeFrame") and not string.match(object:GetName(),"Sell") then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(HomeShowFrame, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(HomeShowFrame, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
		else
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
		end
	  end
	  	  
	  if type(script) == "function" then	
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end

end



function HandleButtonScript(Button,Type)

	if Type == "MenuOnButton" then
	
		if IsOK == false then
			SendAddonMessage("MALLUI","All","GUILD", UnitName("player"))
			return
		end
		
		if IsAllCreate == false then
			DONGD_OnInitialize()
			IsAllCreate = true
		end
		
		MenuOnButton:Hide()		
		
		MenuOffButton:Show()
		
		LMall:Show()
		
		
	end

	if Type == "MenuOffButton" then
	
		MenuOnButton:Show()
		
		MenuOffButton:Hide()
		
		LMall:Hide()
		
	end
	
	if Type == "LScrollUpButton" then
		LSlider:SetValue(math.ceil(LSlider:GetValue()-(LSlider:GetHeight() / 60)))
		PlaySound("UChatScrollButton")
	end
	
	if Type == "LScrollDownButton" then
		LSlider:SetValue(math.ceil(LSlider:GetValue()+(LSlider:GetHeight() / 60))) 
		PlaySound("UChatScrollButton")
	end
	
	if Type == "RScrollUpButton" then
	
		RSlider:SetValue(math.ceil(RSlider:GetValue()-(RSlider:GetHeight() / 60)))
		
		PlaySound("UChatScrollButton")
	end
	
	if Type == "RScrollDownButton" then
	
		RSlider:SetValue(math.ceil(RSlider:GetValue()+(RSlider:GetHeight() / 60))) 
		
		PlaySound("UChatScrollButton")
	end	
	
	if Type == "LHScrollUpButton" then
		LHSlider:SetValue(math.ceil(LHSlider:GetValue()-(LHSlider:GetHeight() / 60)))
		PlaySound("UChatScrollButton")
	end
	
	if Type == "LHScrollDownButton" then
		LHSlider:SetValue(math.ceil(LHSlider:GetValue()+(LHSlider:GetHeight() / 60))) 
		PlaySound("UChatScrollButton")
	end
	
	if Type == "HomePage" then
		DONGD_HandleHideAllScrollFrame(false,"L")
		DONGD_HandleHideAllScrollFrame(true,"H")
		DONGD_HandleHideSiderAllElement(false,"L")
		DONGD_HandleHideSiderAllElement(true,"H")
		DONGD_ReSetHomePClassButton("Hbutton")
		DONGD_ForSetSubFrameElement(SubFrame)
		for i,v in pairs(SubFrame) do 
			if v.HomeShowButton ~= nil then
				v.HomeShowButton:Hide()				
				v.HomeShowButton = nil					
			end
		end
		
		for k,v in pairs(HPClassPos) do
			if v.button ~= nil then
				v.button:Hide()				
				v.button = nil
			end
		end
		
		DONGD_CreateLHAllElement()
	end
	
	if string.match(Type,"RMallButton") then
		if Button.Type == "P" then							
			
			DONGD_HandleHideAllScrollFrame(false,"H")		
			DONGD_HandleHideAllScrollFrame(true,"L")		
			DONGD_HandleHideSiderAllElement(true,"L")
			DONGD_HandleHideSiderAllElement(false,"H")
			
			
			local showsub = Button.ShowSub
			local showsubframe = Button.ShowSubFrame
			local name = Button.Name
			
			
			for k,v in pairs(RMallButtons) do		
				local name = v:GetName()
				setglobal(name,nil)							
				v:Hide()									
				v = nil										
			end												
			
			
			if not showsub then								
			DONGD_CreateRMallAllClassButton(name,name) 		
			else											
			DONGD_CreateRMallAllClassButton(nil,name)	
			end
			
			DONGD_ForSetSubFrameElement(SubFrame)			
			for i,v in pairs(SubFrame) do  					
				if SubFrame[i].Frame ~= nil then			
					local name = SubFrame[i].Frame:GetName()
					setglobal(name,nil)						
					SubFrame[i].Frame:Hide()				
					SubFrame[i].Frame = nil					
				end
			end
			
			LSlider:SetValue(0)								
			RSlider:SetValue(0)								
			
			DONGD_CreateMallALLSubFrameOfCategory_Simplify(name,nil)	
			
			DONGD_HandleHideSiderAllElement(true,"L")					
			
		end
		
		if Button.Type == "S" then										
			local name = Button.Name									
			local pname = Button.PName									
			DONGD_ForSetSubFrameElement(SubFrame)						
			for i,v in pairs(SubFrame) do  								
				if SubFrame[i].Frame ~= nil then						
				local name = SubFrame[i].Frame:GetName()				
					setglobal(name,nil)									
					SubFrame[i].Frame:Hide()							
					SubFrame[i].Frame = nil								
				end
			end
			
			LSlider:SetValue(0)											
			
			DONGD_CreateMallALLSubFrameOfCategory_Simplify(pname,name)	
			
			
			for k,v in pairs(RMallButtons) do
				if v.Type == "S" then									
					v:UnlockHighlight()
				end
			end
			
			Button:LockHighlight()										

		end
	end
	
	if string.match(Type,"SellButton") then
		local i = Button.num
		SendAddonMessage("MALLUI_BUYITEM",i,"GUILD", UnitName("player"))
	end
	
	if string.match(Type,"HomeShowButton") then
			
			
		if HomeShowFrame ~= nil then
			local i = oldI
			
			DONGD_ResetHomeFrameElement(i)
			local name = HomeShowFrame:GetName()
			setglobal(name,nil)
			HomeShowFrame:Hide()
			HomeShowFrame = nil
			
		end
		
		
		HomeShowFrame = DONGD_CreateMallParentFrame("Frame","HomeShowFrame",LMall,nil,300,300,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
		
		
		HomeShowFrame:SetFrameStrata("TOOLTIP") 
		
		
		HomeFrameCloseButton = DONGD_CreateButton("Button","HomeFrameCloseButton",HomeShowFrame,nil,36,36,"","TOPRIGHT",-5,-5,"","")
		
		PrepareScript(HomeFrameCloseButton,	"关闭",		function() HomeShowFrame:Hide()	end)
		
		
		local i = Button.num 
		DONGD_CreateHomeShowFrameAllElement_Simplify(i)
		
	end
end



function DONGD_InitMallMenuButtons()

	
	PrepareScript(MenuOnButton,	"打开商城",		function()	HandleButtonScript(MenuOnButton,"MenuOnButton") end)
	 
	 
	PrepareScript(MenuOffButton,	"关闭商城",		function()	HandleButtonScript(MenuOffButton,"MenuOffButton") end)

end



function DONGD_InitMallTabButton()

	
	PrepareScript(HomePage,	"主页",		function()	HandleButtonScript(HomePage,"HomePage") end)

end



function DONGD_InitMallClassButton()

	
	local i = 1
	
	for k,v in pairs(RMallButtons) do
		PrepareScript(v,	"",		function()	HandleButtonScript(v,v:GetName())	end)
		
	end
	
	
end



function DONGD_InitMallScrollButton()

	
	PrepareScript(LScrollUpButton,	"",		function()	HandleButtonScript(LScrollUpButton,"LScrollUpButton")	end)
	
	
	PrepareScript(LScrollDownButton,	"",		function()	HandleButtonScript(LScrollDownButton,"LScrollDownButton") end)
	
	
	PrepareScript(RScrollUpButton,	"",		function()	HandleButtonScript(LScrollUpButton,"RScrollUpButton")	end)
	
	
	PrepareScript(RScrollDownButton,	"",		function()	HandleButtonScript(LScrollDownButton,"RScrollDownButton") end)
	
	
	PrepareScript(LHScrollUpButton,	"",		function()	HandleButtonScript(LScrollUpButton,"LHScrollUpButton")	end)
	
	
	PrepareScript(LHScrollDownButton,	"",		function()	HandleButtonScript(LScrollDownButton,"LHScrollDownButton") end)

end



function DONGD_InitMallSlider()

	LSlider:SetScript("OnValueChanged",
	
	function(self,offset) 
	
	
	LScroll:SetVerticalScroll(math.ceil((LScrollChild:GetHeight()-LScroll:GetHeight())/100 * self:GetValue()))
	lmin, lmax = self:GetMinMaxValues();
		
		if ( offset == 0 ) then
			LScrollUpButton:Disable();
		else
			LScrollUpButton:Enable();
		end
		
		
		if (math.ceil(self:GetValue() - lmax)) == 0 then
			LScrollDownButton:Disable();
		else
			LScrollDownButton:Enable();
		end
	
	end)
	
	LScroll:SetScript("OnMouseWheel", 
		function(self,val)  

			local s1 = LSlider	
			
			local minv,maxv = s1:GetMinMaxValues()
			
			local cv = s1:GetValue()
			
			local nv = cv - ( val * 5 )
			
			nv = max(nv, minv)
			
			nv = min(nv, maxv)
			
			
			
			if ( nv ~= cv ) then
			
				s1:SetValue(nv);
			end
			
		end)
		
	RSlider:SetScript("OnValueChanged",
	
	function(self,offset) 
	
	
	RScroll:SetVerticalScroll(math.ceil((RScrollChild:GetHeight()-RScroll:GetHeight())/100 * self:GetValue()))
	lmin, lmax = self:GetMinMaxValues();
		
		if ( offset == 0 ) then
			RScrollUpButton:Disable();
		else
			RScrollUpButton:Enable();
		end
		
		
		if (math.ceil(self:GetValue() - lmax)) == 0 then
			RScrollDownButton:Disable();
		else
			RScrollDownButton:Enable();
		end
	
	end)
	
	RScroll:SetScript("OnMouseWheel", 
		function(self,val)  

			local s1 = RSlider	
			
			local minv,maxv = s1:GetMinMaxValues()
			
			local cv = s1:GetValue()
			
			local nv = cv - ( val * 5 )
			
			nv = max(nv, minv)
			
			nv = min(nv, maxv)

			if ( nv ~= cv ) then
				s1:SetValue(nv);
			end
			
		end)
		
	LHSlider:SetScript("OnValueChanged",
	
	function(self,offset) 
	LHScroll:SetVerticalScroll(math.ceil((LHScrollChild:GetHeight()-LHScroll:GetHeight())/100 * self:GetValue()))
	lmin, lmax = self:GetMinMaxValues();

		if ( offset == 0 ) then
			LHScrollUpButton:Disable();
		else
			LHScrollUpButton:Enable();
		end
		
		
		if (math.ceil(self:GetValue() - lmax)) == 0 then
			LHScrollDownButton:Disable();
		else
			LHScrollDownButton:Enable();
		end
	
	end)
	
	LHScroll:SetScript("OnMouseWheel", 
		function(self,val)  

			local s1 = LHSlider	
			
			local minv,maxv = s1:GetMinMaxValues()
			
			local cv = s1:GetValue()
			
			local nv = cv - ( val * 5 )
			
			nv = max(nv, minv)
			
			nv = min(nv, maxv)

			if ( nv ~= cv ) then
				s1:SetValue(nv);
			end
			
		end)
		
end



function DONGD_CreateRMallAllClassButton(t,isk)
	
	local i = 1
		
	for k,v in pairs(RMallButtonsInfo) do
		
		if not t then 						
			
			RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,260,30,k,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
			RMallButtons[i].Name = k				
			RMallButtons[i].Type = "P"				
			RMallButtons[i].ShowSub = false 		
			RMallButtons[i].ShowSubFrame = false	
			i = i + 1
		else
			RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,260,30,k,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
			RMallButtons[i].Name = k
			RMallButtons[i].Type = "P"
			if t == k then
				RMallButtons[i].ShowSub = true		
				RMallButtons[i].ShowSubFrame = true 
				for k1,v1 in pairs(RMallClass) do
					if t == k1 then
						
						for k2,v2 in pairs(RMallClass[k1]) do
							
							i = i + 1
							RMallButtons[i] = DONGD_CreateClassButton("Button","RMallButton"..tostring(i),RScrollChild,nil,240,30,v2,"",-3,0,T["ItemClassButton"],"",i,RMallButtons)
							RMallButtons[i].Name = v2		
							RMallButtons[i].PName = k1							
							RMallButtons[i].Type = "S"		
						end			
					end
				end
				
			end
			i = i + 1
		end
		
	end

	
	if isk ~= nil then
		for k,v in pairs(RMallButtons) do
			if v.Type == "P" then
				if v.Name ~= isk then
				v:UnlockHighlight()
				else
				v:LockHighlight()
				end
			end
		end
	end
	
	
	if i > 21 then
		
		DONGD_HandleHideSiderAllElement(true,"R")
		for n,m in pairs(RMallButtons) do
				if n == 1 then
					RMallButtons[n]:SetPoint("TOPLEFT",-2,0)
				end
				
				if RMallButtons[n].Type == "P" then
					RMallButtons[n]:SetSize(230,30)
				else
					RMallButtons[n]:SetSize(210,30)
				end
			end
		local j = i - 21
		local h = RScrollChild:GetHeight()
		
		RScrollChild:SetSize(225,30*(i-1))
		RScroll:EnableMouseWheel(true)					
		RScrollDownButton:Enable()						
	else
		RScrollChild:SetSize(258,650)
		DONGD_HandleHideSiderAllElement(false,"R")		
		RScrollDownButton:Disable()
		RScrollUpButton:Disable()
		RScroll:EnableMouseWheel(false)					
		
	end

	DONGD_InitMallClassButton()
end



function DONGD_CreateMallAllParentFrame()

	
	LMall = DONGD_CreateMallParentFrame("Frame","LMall",UIParent,nil,900,700,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")

	
	RMall = DONGD_CreateMallParentFrame("Frame","RMall",LMall,nil,300,728,"",-22,13.7,T["ParentFrameBackgroundRight"],"")
	
	
	MallTitle = DONGD_CreateMallParentFrame("Frame","MallTitle",LMall,nil,700,180,"TOP",0,120,T["ParentFrameTitle"],"")
	
	
	CloseButton = DONGD_CreateButton("Button","CloseButton",LMall,nil,36,33,"","TOPRIGHT",265,-12,"","")
	
	DONGD_CreateMallAllScrollFrame()
	
	DONGD_CreateMallAllSliderFrame()
	
	DONGD_CreateRMallAllClassButton()
	
	
	PrepareScript(CloseButton,	"关闭",		function() MenuOffButton:Click()	end)

end



function DONGD_CreateMallAllScrollFrame()
		
	LScroll = DONGD_CreateScrollFrame("ScrollFrame","LScroll",LMall,nil,830,600,"CENTER",-3,-5,"","")
	
	LScrollChild = DONGD_CreateMallParentFrame("Frame","LScrollChild",LScroll,nil,830,1100,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	
	LScroll:SetScrollChild(LScrollChild)
	
	LScroll:Hide() 

	RScroll = DONGD_CreateScrollFrame("ScrollFrame","RScroll",RMall,nil,258,636,"CENTER",-3,-20,"","")
	
	RScrollChild = DONGD_CreateMallParentFrame("Frame","RScrollChild",RScroll,nil,258,700,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")

	RScroll:SetScrollChild(RScrollChild)
	
	LHScroll = DONGD_CreateScrollFrame("ScrollFrame","LHScroll",LMall,nil,850,650,"CENTER",10,0,"","")
	
	LHScrollChild = DONGD_CreateMallParentFrame("Frame","LHScrollChild",LScroll,nil,820,1100,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")

	LHScroll:SetScrollChild(LHScrollChild)
	
	
	LHTex = DONGD_CreateMallParentFrame("Frame","LHTex",LHScrollChild,nil,820,415,"TOP",0,0,T["HB"],T["HBBK"])
	
	
	DONGD_CreateLHAllElement()
end



function DONGD_CreateLHAllElement()
	
	local _pos = 1
	
	if next(HPClassPos) ==nil then
		
		return;
	end
	
	for k,v in pairs(HPClassPos) do
		v.pos = _pos
		
		local pos = v.pos
		
		local num = v.num
		
		local name = k
		
		local rFrame = GetPClassPosIsFrame(pos)
		
		local high = GetPCliassPosHigh(pos-1)	
		
		v.button = DONGD_CreateHomeClassAndItemButton("Button","HPClassPos"..pos,LHScrollChild,nil,150,60,name,"TOP",rFrame,"BOTTOM",0,-high,"","PClass")
		
		_pos = _pos +1
	end
	
	for k1,v1 in pairs(SubFrame) do
		local S = SubFrame[k1]
		local hpos = S.Hpos
		local itemid = S.itemid
		local rFrame 
		if hpos ~= 0 then
			if hpos <=3 then
				for k2,v2 in pairs(HPClassPos) do
					if S.pclass == k2 then
						rFrame = v2.button
					end
				end
			end
			S.HomeShowButton = DONGD_CreateHomeClassAndItemButton("Button","SubFrame"..tostring(k1).."HomeShowButton",LHScrollChild,nil,50,50,"|T"..GetItemIcon(itemid)..":40|t","",rFrame,"",0,0,"","show",SubFrame,k1,S.pclass)
			
			DONGD_CreateTex(LHScrollChild,"SubFrame"..tostring(k1).."HomeShowBorder","OVERLAY",nil,"CENTER",S.HomeShowButton,"CENTER",-3,0,T["TSBK"],256,128)
			
			local stex = texrand()
			S.HomeShowBorder = DONGD_CreateTex(LHScrollChild,"SubFrame"..tostring(k1).."HomeShowBorder","OVERLAY",nil,"CENTER",S.HomeShowButton,"CENTER",-3,0,stex,256,128)
			S.HomeShowButton.num = k1
			
			PrepareScript(S.HomeShowButton,	"",function() if ( IsModifiedClick("DRESSUP") ) then  DressUpItemLink(S.itemlink); end HandleButtonScript(S.HomeShowButton,S.HomeShowButton:GetName()) end,S.itemlink)	

			
			S.HomeShowButton:SetScript("OnUpdate",function(self) if( GameTooltip:IsOwned(self)) then if (IsModifiedClick("DRESSUP"))then SetCursor("INSPECT_CURSOR") else ResetCursor() end end end)
		end
			
			

	end
	local allhigh = GetLHScrollChildResetHigh()
	
	LHScrollChild:SetHeight(allhigh)

end



function DONGD_CreateHomeShowFrameAllElement_Simplify(i)
		oldI = i
		local S = SubFrame[i]
		local itemid = S.itemid
		local f = HomeShowFrame
		local c = 1
		local SellInfo = SubFramSellInfo
		
		
		SubFrame[i].HomeFrameItemButton = DONGD_CreateButton("Button","SubFrame"..tostring(i).."HomeFrameItemButton",f,nil,60,60,"|T"..GetItemIcon(itemid)..":50|t","TOPLEFT",20,-30,"","")
		
		
		
		PrepareScript(S.HomeFrameItemButton,	"",function() if ( IsModifiedClick("DRESSUP") ) then  DressUpItemLink(S.itemlink); end end,S.itemlink)	
		
		
		
		S.HomeFrameItemButton:SetScript("OnUpdate",function(self) if( GameTooltip:IsOwned(self)) then if (IsModifiedClick("DRESSUP"))then SetCursor("INSPECT_CURSOR") else ResetCursor() end end end)	
		
		
		
		SubFrame[i].HomeFrameItemNameFont = DONGD_CreateFontString(f,"SubFrame"..tostring(i).."HomeFrameItemNameFont","","",T["Font"],15,"TOPLEFT",S.HomeFrameItemButton,"TOPRIGHT",0,-5,S.itemlink,"",true)
		
		
		
		if S.reqvip ~= "0" then		
			SubFrame[i].HomeFrameReqVipButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameVipButton",f,nil,26,26,"|T"..T["Vip"]..":20|t","TOP",S.HomeFrameItemNameFont,"CENTER",-30,-3,"")
			SubFrame[i].HomeFrameReqVipFont = DONGD_CreateFontString(S.HomeFrameReqVipButton,"SubFrame"..tostring(i).."HomeFrameReqVipFont","","",T["Font"],13,"LEFT",S.HomeFrameReqVipButton,"LEFT",-55,-8,"*"..S.reqvip,"",true)
			PrepareScript(S.HomeFrameReqVipButton,	"需求VIP",		nil)
		end
		
		
		
		if S.reqjf ~= "0" then		
		local rf = GetSubFrameSellInfoFrameS(i,c-1,true)
		tinsert(S.SubFramSellInfo,"jifen")
		SubFrame[i].HomeFrameReqJFButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqJFButton",f,nil,26,26,"|T"..T["JiFen"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
		SubFrame[i].HomeFrameReqJFFont = DONGD_CreateFontString(S.HomeFrameReqJFButton,"SubFrame"..tostring(i).."HomeFrameReqJFFont","","",T["Font"],13,"LEFT",S.HomeFrameReqJFButton,"LEFT",30,0,"*"..S.reqjf,"")
		PrepareScript(S.HomeFrameReqJFButton,	"需求积分",		nil)
		c = c + 1
		end
		
		if S.reqmoney ~= "0" then 	
			local rf = GetSubFrameSellInfoFrameS(i,c-1,true) 
			tinsert(S.SubFramSellInfo,"money") 
			SubFrame[i].HomeFrameReqMoneyButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqMoneyButton",f,nil,26,26,"|T"..T["JinBi"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].HomeFrameReqMoneyFont = DONGD_CreateFontString(S.HomeFrameReqMoneyButton,"SubFrame"..tostring(i).."HomeFrameReqMoneyFont","","",T["Font"],13,"LEFT",S.HomeFrameReqMoneyButton,"LEFT",30,0,"*"..S.reqmoney,"")
			PrepareScript(S.HomeFrameReqMoneyButton,	"需求金币",		nil)
			c = c + 1
		end
		
		if S.reqexp ~= "0" then		
		
			local rf = GetSubFrameSellInfoFrameS(i,c-1,true) 
			
			tinsert(S.SubFramSellInfo,"exp") 
			SubFrame[i].HomeFrameReqExpButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqExpButton",f,nil,26,26,"|T"..T["XP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].HomeFrameReqExpFont = DONGD_CreateFontString(S.HomeFrameReqExpButton,"SubFrame"..tostring(i).."HomeFrameReqExpFont","","",T["Font"],13,"LEFT",S.HomeFrameReqExpButton,"LEFT",30,0,"*"..S.reqexp,"")
			PrepareScript(S.HomeFrameReqExpButton,	"需求经验",		nil)
			c = c + 1
		end
		
		if S.reqhonor ~= "0" then	
			local rf = GetSubFrameSellInfoFrameS(i,c-1,true) 
			tinsert(S.SubFramSellInfo,"honor") 
			local tex 
			local englishFaction, localizedFaction = UnitFactionGroup("player")
			if englishFaction == "Alliance" then
				tex = T["AlliancePVP"]
			else
				tex = T["HordePVP"]
			end
			SubFrame[i].HomeFrameReqHonorButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqHonorButton",f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].HomeFrameReqHonorFont = DONGD_CreateFontString(S.HomeFrameReqHonorButton,"SubFrame"..tostring(i).."HomeFrameReqHonorFont","","",T["Font"],13,"LEFT",S.HomeFrameReqHonorButton,"LEFT",30,0,"*"..S.reqhonor,"")
			PrepareScript(S.HomeFrameReqHonorButton,	"需求荣誉值",		nil)
			c = c + 1
		end
		
		if S.reqarena ~= "0" then	
			local rf = GetSubFrameSellInfoFrameS(i,c-1,true) 
			tinsert(S.SubFramSellInfo,"arena") 
			SubFrame[i].HomeFrameReqArenaButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqArenaButton",f,nil,26,26,"|T"..T["ArenaPVP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].HomeFrameReqArenaFont = DONGD_CreateFontString(S.HomeFrameReqArenaButton,"SubFrame"..tostring(i).."HomeFrameReqArenaFont","","",T["Font"],13,"LEFT",S.HomeFrameReqArenaButton,"LEFT",30,0,"*"..S.reqarena,"")
			PrepareScript(S.HomeFrameReqArenaButton,	"需求竞技点",		nil)
			c = c + 1
		end
			
		if SubFrame[i].reqitemid[1] ~= nil then 
			SubFrame[i].HomeFrameReqItemButton = {}
			SubFrame[i].HomeFrameReqItemFont = {}
		end
			
			
		for j = 1,5 do
			if SubFrame[i].reqitemid[j] ~= nil then
				local rf = GetSubFrameSellInfoFrameS(i,c-1,true) 
				local str = "item"..tostring(j)
				tinsert(SubFrame[i].SubFramSellInfo,str) 
				local tex = GetItemIcon(S.reqitemid[j])
				local itemlink = SubFrame[i].reqitemlink[j]
				SubFrame[i].HomeFrameReqItemButton[j] = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameReqItemButton"..tostring(j),f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
				
				SubFrame[i].HomeFrameReqItemFont[j] = DONGD_CreateFontString(S.HomeFrameReqItemButton[j],"SubFrame"..tostring(i).."HomeFrameReqItemButtonFont"..tostring(j),"","",T["Font"],13,"LEFT",S.HomeFrameReqItemButton[j],"LEFT",30,0,"需求： "..S.reqitemval[j].."个","")
				PrepareScript(S.HomeFrameReqItemButton[j],"",nil,itemlink)
				c = c + 1
			end
		end
		
		
		
		SubFrame[i].HomeFrameSellButton = DONGD_CreateButtonInHomeShowFrame("Button","SubFrame"..tostring(i).."HomeFrameSellButton",f,nil,100,42,"立即购买","BOTTOM",nil,"",0,10,"")
		SubFrame[i].HomeFrameSellButton.num = i 
		PrepareScript(SubFrame[i].HomeFrameSellButton,	"",		function()	HandleButtonScript(SubFrame[i].HomeFrameSellButton,SubFrame[i].HomeFrameSellButton:GetName())	end)
		
		if S.PromotionType ~= "无" then
		
			local PromTabTex	= DONGD_GetPromTabTex(S.PromotionType)
			
			SubFrame[i].HomeFrameItemDisTab = DONGD_CreateTex(S.ItemButton,"SubFrame"..tostring(i).."HomeFrameItemDisTab","OVERLAY",nil,"TOPLEFT",nil,"",5,-5,PromTabTex,50,25)	
			
			if S.PromotionType == "限时" then
				S.ItemDisTab:Hide()
			end
		end
		
		SubFrame[i].HomeFrameItemBorder = DONGD_CreateTex(S.HomeFrameItemButton,"SubFrame"..tostring(i).."HomeFrameItemBorder","OVERLAY",nil,"CENTER",nil,"",0,0,T["Loottoastatlas"],68,68)	
		
		DONGD_TexSetTexCoord(S.HomeFrameItemBorder,TCoord["Itemborder-heirloom"])	
		
end



function DONGD_HandleHideAllScrollFrame(b,Type)
	if not b then
		if Type == "H" then
			LHScroll:Hide()
		end
		
		if Type == "L" then
			LScroll:Hide()
		end
		
		if Type == "R" then
			RScroll:Hide()
		end
	else
		if Type == "H" then
			LHScroll:Show()
		end
		
		if Type == "L" then
			LScroll:Show()
		end
		
		if Type == "R" then
			RScroll:Show()
		end
	end
end



function DONGD_CreateMallAllSliderFrame()

	
	LScrollTexTop,LScrollTexMiddle,LScrollTexBottom = DONGD_CreateScrollTex(LMall,"L","ARTWORK",nil,0,"L")
	
	
	LSlider,LSlidertex = DONGD_CreateSliderFrame("Slider","LMall",LMall,nil,"VERTICAL","L")
	
	
	RScrollTexTop,RScrollTexMiddle,RScrollTexBottom = DONGD_CreateScrollTex(RScroll,"L","ARTWORK",nil,0,"R")
	
	
	RSlider,RSlidertex = DONGD_CreateSliderFrame("Slider","RMall",RScroll,nil,"VERTICAL","R")
	
	
	LHScrollTexTop,LHScrollTexMiddle,LHScrollTexBottom = DONGD_CreateScrollTex(LMall,"LH","ARTWORK",nil,0,"L")
	
	
	LHSlider,LHSlidertex = DONGD_CreateSliderFrame("Slider","LMall",LMall,nil,"VERTICAL","L")
		
		
	LHScroll:EnableMouseWheel(true)
	
	DONGD_CreateMallALLScrollButton()
	
	
	DONGD_InitMallSlider()
	
	
	DONGD_HandleHideSiderAllElement(false,"R")
	
	
	DONGD_HandleHideSiderAllElement(false,"L")

end



function DONGD_HandleHideSiderAllElement(b,Type)

	if not b then
		if Type == "L" then
			LScrollTexTop:Hide()
			
			LScrollTexMiddle:Hide()
			
			LScrollTexBottom:Hide()
			
			LSlider:Hide()
			
			LScrollUpButton:Hide()
			
			LScrollDownButton:Hide()
		end
		
		if Type == "R" then
			RScrollTexTop:Hide()
			
			RScrollTexMiddle:Hide()
			
			RScrollTexBottom:Hide()
			
			RSlider:Hide()
			
			RScrollUpButton:Hide()
			
			RScrollDownButton:Hide()
		end
		
		if Type == "H" then
			LHScrollTexTop:Hide()
			
			LHScrollTexMiddle:Hide()
			
			LHScrollTexBottom:Hide()
			
			LHSlider:Hide()
			
			LHScrollUpButton:Hide()
			
			LHScrollDownButton:Hide()
		end
	else
		if Type == "L" then
			LScrollTexTop:Show()
			
			LScrollTexMiddle:Show()
			
			LScrollTexBottom:Show()
			
			LSlider:Show()
			
			LScrollUpButton:Show()
			
			LScrollDownButton:Show()
		end
		
		if Type == "R" then
			RScrollTexTop:Show()
			
			RScrollTexMiddle:Show()
			
			RScrollTexBottom:Show()
			
			RSlider:Show()
			
			RScrollUpButton:Show()
			
			RScrollDownButton:Show()	
		end
		
		if Type == "H" then
			LHScrollTexTop:Show()
			
			LHScrollTexMiddle:Show()
			
			LHScrollTexBottom:Show()
			
			LHSlider:Show()
			
			LHScrollUpButton:Show()
			
			LHScrollDownButton:Show()
		end
	end

	
end



function DONGD_CreateMallALLSubFrameOfCategory_Simplify(PCategory,SCategory)
	
	local num = 0
	local displayedHeight,val
	
	
	if PCategory ~= nil and SCategory == nil then
		for i,v in pairs(SubFrame) do  
			if PCategory == SubFrame[i].pclass then
				SubFrame[i].Frame = DONGD_CreateMallSubFrame("Frame","SubPFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["ParentFrameBackgroundLeft"],"",SubFrame,i,"ParentS")	
				DONGD_CreateMallSubFrameAllElement_Simplify(i)		
				num = num + 1
				val = i
			end
		end
		
		displayedHeight = math.ceil(num/3) * math.ceil(SubFrame[val].Frame:GetHeight())
	end
	
	
	
	
	if PCategory ~=nil and SCategory ~= nil then
		
		for i,v in pairs(SubFrame) do
			if PCategory == SubFrame[i].pclass and SCategory == SubFrame[i].sclass then
				SubFrame[i].Frame = DONGD_CreateMallSubFrame("Frame","SubSFrame"..tostring(i),LScrollChild,nil,270,300,"",0,0,T["ParentFrameBackgroundLeft"],"",SubFrame,i,"SubS")
				DONGD_CreateMallSubFrameAllElement_Simplify(i)
				num = num + 1
				val = i
			end
		end
		
		displayedHeight = math.ceil(num/3) * math.ceil(SubFrame[val].Frame:GetHeight())

	end
	
	if num > 6 then 
		LScrollChild:SetHeight(displayedHeight)			
		LScroll:EnableMouseWheel(true)					
		LScrollDownButton:Enable()						
	else
		LScrollDownButton:Disable()
		LScrollUpButton:Disable()
		LScroll:EnableMouseWheel(false)					
	end

end



function DONGD_CreateMallSubFrameAllElement_Simplify(i)
		
		local S = SubFrame[i]
		local itemid = S.itemid
		local f = S.Frame
		local c = 1
		local SellInfo = SubFramSellInfo
		
		SubFrame[i].ItemButton = DONGD_CreateButton("Button","SubFrame"..tostring(i).."ItemButton",f,nil,60,60,"|T"..GetItemIcon(itemid)..":50|t","TOPLEFT",20,-30,"","")
		
		UP") ) then  DressUpItemLink(S.itemlink); end end,S.itemlink)	
		
		
		S.ItemButton:SetScript("OnUpdate",function(self) if( GameTooltip:IsOwned(self)) then if (IsModifiedClick("DRESSUP"))then SetCursor("INSPECT_CURSOR") else ResetCursor() end end end)	
		
		SubFrame[i].ItemNameFont = DONGD_CreateFontString(f,"SubFrame"..tostring(i).."ItemNameFont","","",T["Font"],15,"TOPLEFT",S.ItemButton,"TOPRIGHT",0,-5,S.itemlink,"",true)
		
		if S.reqvip ~= "0" then		
			SubFrame[i].ReqVipButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."VipButton",f,nil,26,26,"|T"..T["Vip"]..":20|t","TOP",S.ItemNameFont,"CENTER",-30,-3,"")
			SubFrame[i].ReqVipFont = DONGD_CreateFontString(S.ReqVipButton,"SubFrame"..tostring(i).."ReqVipFont","","",T["Font"],13,"LEFT",S.ReqVipButton,"LEFT",-55,-8,"*"..S.reqvip,"",true)
			PrepareScript(S.ReqVipButton,	"需求VIP",		nil)
		end
			
		if S.reqjf ~= "0" then		
		local rf = GetSubFrameSellInfoFrameS(i,c-1)
		tinsert(S.SubFramSellInfo,"jifen")
		SubFrame[i].ReqJFButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqJFButton",f,nil,26,26,"|T"..T["JiFen"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
		SubFrame[i].ReqJFFont = DONGD_CreateFontString(S.ReqJFButton,"SubFrame"..tostring(i).."ReqJFFont","","",T["Font"],13,"LEFT",S.ReqJFButton,"LEFT",30,0,"*"..S.reqjf,"")
		PrepareScript(S.ReqJFButton,	"需求积分",		nil)
		c = c + 1
		end
		
		if S.reqmoney ~= "0" then 	
			local rf = GetSubFrameSellInfoFrameS(i,c-1) 
			tinsert(S.SubFramSellInfo,"money") 
			SubFrame[i].ReqMoneyButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqMoneyButton",f,nil,26,26,"|T"..T["JinBi"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqMoneyFont = DONGD_CreateFontString(S.ReqMoneyButton,"SubFrame"..tostring(i).."ReqMoneyFont","","",T["Font"],13,"LEFT",S.ReqMoneyButton,"LEFT",30,0,"*"..S.reqmoney,"")
			PrepareScript(S.ReqMoneyButton,	"需求金币",		nil)
			c = c + 1
		end
		
		if S.reqexp ~= "0" then		
			local rf = GetSubFrameSellInfoFrameS(i,c-1) 
			tinsert(S.SubFramSellInfo,"exp") 
			SubFrame[i].ReqExpButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqExpButton",f,nil,26,26,"|T"..T["XP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqExpFont = DONGD_CreateFontString(S.ReqExpButton,"SubFrame"..tostring(i).."ReqExpFont","","",T["Font"],13,"LEFT",S.ReqExpButton,"LEFT",30,0,"*"..S.reqexp,"")
			PrepareScript(S.ReqExpButton,	"需求经验",		nil)
			c = c + 1
		end
		
		if S.reqhonor ~= "0" then	
			local rf = GetSubFrameSellInfoFrameS(i,c-1) 
			tinsert(S.SubFramSellInfo,"honor") 
			local tex 
			local englishFaction, localizedFaction = UnitFactionGroup("player")
			if englishFaction == "Alliance" then
				tex = T["AlliancePVP"]
			else
				tex = T["HordePVP"]
			end
			SubFrame[i].ReqHonorButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqHonorButton",f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqHonorFont = DONGD_CreateFontString(S.ReqHonorButton,"SubFrame"..tostring(i).."ReqHonorFont","","",T["Font"],13,"LEFT",S.ReqHonorButton,"LEFT",30,0,"*"..S.reqhonor,"")
			PrepareScript(S.ReqHonorButton,	"需求荣誉值",		nil)
			c = c + 1
		end
		
		if S.reqarena ~= "0" then	
			local rf = GetSubFrameSellInfoFrameS(i,c-1) 
			tinsert(S.SubFramSellInfo,"arena") 
			SubFrame[i].ReqArenaButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqArenaButton",f,nil,26,26,"|T"..T["ArenaPVP"]..":20|t","",rf,"",0,0,"",c,S.ElementNum)
			SubFrame[i].ReqArenaFont = DONGD_CreateFontString(S.ReqArenaButton,"SubFrame"..tostring(i).."ReqArenaFont","","",T["Font"],13,"LEFT",S.ReqArenaButton,"LEFT",30,0,"*"..S.reqarena,"")
			PrepareScript(S.ReqArenaButton,	"需求竞技点",		nil)
			c = c + 1
		end
			
		for j = 1,5 do
			if SubFrame[i].reqitemid[j] ~= nil then
				local rf = GetSubFrameSellInfoFrameS(i,c-1) 
				local str = "item"..tostring(j)
				tinsert(S.SubFramSellInfo,str) 
				local tex = GetItemIcon(S.reqitemid[j])
				local itemlink = SubFrame[i].reqitemlink[j]
				SubFrame[i].ReqItemButton[j] = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."ReqItemButton"..tostring(j),f,nil,26,26,"|T"..tex..":20|t","",rf,"",0,0,"",c,S.ElementNum)
				SubFrame[i].ReqItemFont[j] = DONGD_CreateFontString(S.ReqItemButton[j],"SubFrame"..tostring(i).."ReqItemButtonFont"..tostring(j),"","",T["Font"],13,"LEFT",S.ReqItemButton[j],"LEFT",30,0,"需求： "..S.reqitemval[j].."个","")
				PrepareScript(S.ReqItemButton[j],"",nil,itemlink)
				c = c + 1
			end
		end
		
		
		SubFrame[i].SellButton = DONGD_CreateButtonInSubFrame("Button","SubFrame"..tostring(i).."SellButton",f,nil,100,42,"立即购买","BOTTOMLEFT",nil,"",15,15,"")
		SubFrame[i].SellButton.num = i 
		PrepareScript(SubFrame[i].SellButton,	"",		function()	HandleButtonScript(SubFrame[i].SellButton,SubFrame[i].SellButton:GetName())	end)
		
		if S.PromotionType ~= "无" then
		
			local PromTabTex	= DONGD_GetPromTabTex(S.PromotionType)
			
			SubFrame[i].ItemDisTab = DONGD_CreateTex(S.ItemButton,"SubFrame"..tostring(i).."ItemDisTab","OVERLAY",nil,"TOPLEFT",nil,"",5,-5,PromTabTex,50,25)	
			
			if S.PromotionType == "限时" then
				S.ItemDisTab:Hide()
			end
		end
		
		if time() <= SubFrame[i].e_time	then
			if S.PromotionType == "限时" then	
			
				S.ItemDisTab:Show()	
				
				SubFrame[i].TimeFrame = DONGD_CreateMallParentFrame("Button","SubFrame"..tostring(i).."TimeFrame",f,nil,150,38,"CENTER",15,52,T["ToolBack"],"")
				
				PrepareScript(S.TimeFrame,	"限时活动剩余时间",		nil)
				
				SubFrame[i].Frame.i = i		
				
				SubFrame[i].TimeFont = DONGD_CreateFontString(S.TimeFrame,"SubFrame"..tostring(i).."ItemNameFont","","",T["Font"],12,"CENTER",S.TimeFrame,"CENTER",0,-8,"","",true)
				
				S.TimeFrame:Show()
				
				S.Frame:SetScript("OnUpdate", CTime)
				
				
				
				
			end
		end
		
		
		SubFrame[i].ItemBorder = DONGD_CreateTex(S.ItemButton,"SubFrame"..tostring(i).."ItemBorder","OVERLAY",nil,"CENTER",nil,"",0,0,T["Loottoastatlas"],68,68)	
		
		DONGD_TexSetTexCoord(S.ItemBorder,TCoord["Itemborder-heirloom"])	
		
		
end



function DONGD_GetPromTabTex(Type)
	
	local t = Type
	
	local tex = ""
	
	if t == "热卖" then
		tex = T["cuxiao1"]
	elseif t == "折扣" then
		tex = T["cuxiao5"]
	elseif t == "新品" then
		tex = T["cuxiao4"]
	elseif t == "限时" then
		tex = T["cuxiao3"]
	elseif t == "推荐" then
		tex = T["cuxiao2"]
	end
	
	return tex

end



function SetDisPrice(i)

local S = SubFrame[i]
	
	
	if S.reqjf ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqjf) * S.Dis)
	S.ReqJFFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqmoney ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqmoney) * S.Dis)
	S.ReqMoneyFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqexp ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqexp) * S.Dis)
	S.ReqExpFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqhonor ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqhonor) * S.Dis)
	S.ReqHonorFont:SetText("*"..tostring(DisPrice))
	end
	
	if S.reqarena ~= "0" then
	local DisPrice = math.modf(tonumber(S.reqarena) * S.Dis)
	S.ReqArenaFont:SetText("*"..tostring(DisPrice))
	end
	
	for j = 1,5 do
		if SubFrame[i].reqitemid[j] ~= nil then
		local DisPrice = math.modf(tonumber(S.reqitemval[j]) * S.Dis)
			S.ReqItemFont[j]:SetText("需求： "..tostring(DisPrice).."个")
		end
	end
	
	S.IsDis = true
end



function SetNormPrice(i)

local S = SubFrame[i]

	if S.reqjf ~= "0" then
	S.ReqJFFont:SetText("*"..S.reqjf)
	end
	
	if S.reqmoney ~= "0" then
	S.ReqMoneyFont:SetText("*"..S.reqmoney)
	end
	
	if S.reqexp ~= "0" then
	S.ReqExpFont:SetText("*"..S.reqexp)
	end
	
	if S.reqhonor ~= "0" then
	S.ReqHonorFont:SetText("*"..S.reqhonor)
	end
	
	if S.reqarena ~= "0" then
	S.ReqArenaFont:SetText("*"..S.reqarena)
	end
	
	for j = 1,5 do
		if SubFrame[i].reqitemid[j] ~= nil then
			S.ReqItemFont[j]:SetText("需求： "..S.reqitemval[j].."个")
		end
	end
	
	S.IsDis = false

end



function CTime(self,var)

local i = self.i

local timer = SubFrame[i].DELAY

	if timer > 0 then
	
		timer = timer - var
		
		SubFrame[i].DELAY = timer
	
	else
	
		local s_time = SubFrame[i].s_time
	
		if time() >= s_time then					
		
			local e_time = SubFrame[i].e_time		
			
			local diff_tiem = e_time - time()		
		
			if diff_tiem > 0 then					
				
				local etime = date(e_time)
				local ttime = DONGD_timediff(etime,date(time()))
				local str = string.format("%02d", ttime.hour)..":"..string.format("%02d", ttime.min)..":"..string.format("%02d", ttime.sec)
				SubFrame[i].TimeFont:SetText(str)
				if SubFrame[i].IsDis == false then
					SetDisPrice(i)					
					SubFrame[i].TimeFrame:Show()	
					SubFrame[i].ItemDisTab:Show()	
				end
			else
				if SubFrame[i].IsDis == true then
					SetNormPrice(i)						
					SubFrame[i].TimeFont:SetText("0")	
					SubFrame[i].TimeFrame:Hide()		
					SubFrame[i].ItemDisTab:Hide()		
				end
			end
		else
			SubFrame[i].TimeFont:SetText("0")	
			SubFrame[i].TimeFrame:Hide()		
			SubFrame[i].ItemDisTab:Hide()		
		end
	
	SubFrame[i].DELAY = 1	
	
	end
	
end



function DONGD_CreateAndShowMallMenuButton()

	MenuOnButton = DONGD_CreateButton("Button","MenuOnButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketButton"],"")

	MenuOffButton = DONGD_CreateButton("Button","MenuOffButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketOffButton"],"",true)
	
	DONGD_InitMallMenuButtons()
	
end



function DONGD_CreateMallALLScrollButton()


	LScrollUpButton = DONGD_CreateButton("Button","LScrollUpButton",LMall,nil,35,35,"","TOPRIGHT",-8,-15,"","",false,true)
	
	LScrollDownButton = DONGD_CreateButton("Button","LScrollDownButton",LMall,nil,35,35,"","BOTTOMRIGHT",-8,12,"","",false,true)	
	
	RScrollUpButton = DONGD_CreateButton("Button","RScrollUpButton",RScroll,nil,35,35,"","TOPRIGHT",2,5,"","",false,true)
	
	RScrollDownButton = DONGD_CreateButton("Button","RScrollDownButton",RScroll,nil,35,35,"","BOTTOMRIGHT",2,-6,"","",false,true)

	LHScrollUpButton = DONGD_CreateButton("Button","LHScrollUpButton",LMall,nil,35,35,"","TOPRIGHT",-8,-15,"","",false,true)
	
	LHScrollDownButton = DONGD_CreateButton("Button","LHScrollDownButton",LMall,nil,35,35,"","BOTTOMRIGHT",-8,12,"","",false,true)	
	
	DONGD_InitMallScrollButton()
	
end



function DONGD_CreateMallALLTabButton()
	
	HomePage = DONGD_CreateButton("Button","HomePage",RMall,nil,70,70,"","",-10,-50,T["JiFen"],"")

	DONGD_InitMallTabButton()
	
end



function DONGD_CreateMallTabTex()
	
	HomeTex = DONGD_CreateTex(RMall,"HomeTex","ARTWORK",nil,"TOPLEFT",RMall,"TOPRIGHT",-12,-35,T["TabClassFrameBackground"],80,100)
	
end



function DONGD_OnInitialize()

	
	
	DONGD_CreateMallAllParentFrame()
	
	DONGD_CreateMallALLTabButton()

	DONGD_CreateMallTabTex()
		
end


DONGD_CreateAndShowMallMenuButton()
