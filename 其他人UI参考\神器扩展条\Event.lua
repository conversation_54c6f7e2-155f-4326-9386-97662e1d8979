
function string:split(_Src, sep)
    if type(_Src) ~= "string" then
        return nil
    end

    local t = {}
    sep = sep or " "
    
    for str in string.gmatch(_Src, "([^"..sep.."]+)") do
        assert(type(str) == "string")
	    table.insert(t, str)
    end
    
    return t
end

local loaded = CreateFrame("Frame", "local")
loaded:RegisterEvent("ADDON_LOADED")
loaded:SetScript("OnEvent", function(self, event, name, ...)
    if event == "ADDON_LOADED" and name == "SzerothUI" then        
        if (type(ItemLevelSet) ~= "table") then
            ItemLevelSet = {}
        end
		
		if (type(EntryLevelSet) ~= "table") then
            EntryLevelSet = {}
        end
	end
end)

local addon = CreateFrame("Frame")
addon:RegisterEvent("CHAT_MSG_ADDON")
addon:SetScript("OnEvent", function(self, event, prefix, msg, msgType, sender)

    if prefix == "level" then
        local ts = string:split(msg, "^")
        for k,v in pairs(ts) do
            local t = string:split(v, "#")
            local guid = t[1]
            ItemLevelSet[guid] = {guid = guid, exp = t[2], maxExp = t[3], yyy = -30}
        end
        return
    end
	
	if prefix == "query" then
		local ts = string:split(msg, "^")
		for k,v in pairs(ts) do
			local t = string:split(v, "#")
			local entry = t[4]
			EntryLevelSet[entry] = {guid = entry, exp = t[2], maxExp = t[3], yyy = -30}
        end
        return
    end
end)