UIFrameFadeIn(frame, timeToFade, startAlpha, endAlpha)	--淡入
UIFrameFadeOut(frame, timeToFade, startAlpha, endAlpha)	--淡出

--[[
实参
(frame, timeToFade, startAlpha, endAlpha) 

frame Frame - 需要淡入的窗口/帧

timeToFade Number 淡入或淡出所需的时间(秒)

startAlpha Number - 起始Alpha值(介于0和1之间)

endAlpha Number - 结束Alpha值(介于0和1之间)
]]--


UIFrameFlash(frame, 30, 30, 5, true, 2, 2)	--淡入淡出效果

--[[
实参
(frame, fadeInTime, fadeOutTime, flashDuration, showWhenDone, flashInHoldTime, flashOutHoldTime)
frame 
Frame Pointer - 需要淡入/淡出的窗口/帧

fadeInTime 
Number - 淡入效果的持续时间

fadeOutTime 
Number - 淡出效果的持续时间

flashDuration 
Number - 持续重复淡入/淡出周期的秒数 P.S这个时间为总时间 循环次数 = flashDuration / (flashInHoldTime + fadeInTime + flashOutHoldTime + fadeOutTime)

showWhenDone 
Boolean - 框架在末尾是否可见	P.S这个为总时间到了以后窗口是否关闭

flashInHoldTime
Number - 保持完全隐藏状态的秒数	这个是淡入效果之前完全隐藏的秒数

flashOutHoldTime 
Number - 保持完全可见状态的秒数 这个是淡出效果之前完全显示的秒数

]]--