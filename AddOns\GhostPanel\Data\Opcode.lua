LMSG_A_HEADER = "$r@";
LMSG_Q_HEADER = "$q@";
LMSG_Q_SPECIALTY = 1;
LMSG_Q_MEMORY_POINT = 2;
LMSG_A_LEARN_SPECIALTY = 3;
-- LMSG_Q_SPELLINFO = 4;
-- LMSG_Q_LEARNABLE_SPELL_LIST = 5;
-- LMSG_A_LEARN_SPELL = 6;
-- LMSG_Q_SPELL_CACHE = 7;
-- LMSG_Q_SPEC_COUNT = 8;
-- LMSG_A_LEVELUP = 11;
-- LMSG_Q_GP_FOR_NEXT_LEVEL = 12;
LMSG_A_RESET_SPECIALTY = 13;
-- LMSG_A_RESET_SPELLS = 14;
-- LMSG_Q_MAX_LEVEL = 15;
-- LMSG_Q_ALL_SPELLINFO = 16;
-- LMSG_Q_MF_RATE = 17;
LMSG_Q_REWARD_COUNT = 18;
LMSG_A_COLLECT_REWARD = 19;
LMSG_Q_REWARD_INFO = 20;
-- LMSG_Q_SPELLMOD_INFO = 21;
LMSG_A_LEARN_SPELLMOD = 22;
LMSG_A_GAIN_GP = 23;
LMSG_Q_RUNE0_INFO = 24;
LMSG_Q_RUNE1_INFO = 25;
-- LMSG_Q_UNLOCKED_SPELL_INFO = 26;
-- LMSG_A_UNLOCK_SPELL = 27;
LMSG_NOTIFY = 28;
-- LMSG_A_REPLACE_SPELL = 29;
LMSG_Q_RUNE2_INFO = 30;
LMSG_Q_LEGACY_ITEM_INFO = 31;
-- LMSG_A_FETCH_LEGACY_ITEM = 32;
LMSG_Q_MARKET_ITEM_INFO = 34;
LMSG_A_FETCH_MARKET_ITEM = 35;
LMSG_Q_ACCOUNT_INFO = 36;
LMSG_Q_GUILD_BONUS_INFO = 37;
LMSG_Q_GUILD_RANK_INFO = 38;
LMSG_A_REQUEST_GUILD_SPELL = 39;
LMSG_A_SELECT_GUILD_SPELL = 40;
LMSG_A_BUY_GOLD = 41;
LMSG_Q_GOLD_RATIO = 42;
-- LMSG_Q_UNLOCKED_SPELL_COUNT = 43;
LMSG_Q_TRANSMOGS = 45;
LMSG_Q_TRANSMOG_COLLECTIONS = 46;
LMSG_A_ACTIVATE_TRANSMOG_FOR_SLOT = 47;
LMSG_A_CLEAR_TRANSMOG_FOR_SLOT = 48;
LMSG_Q_OWNED_MARKET_SPELLS = 49;
LMSG_Q_MARKET_SPELL_INFO = 50;
LMSG_A_FETCH_MARKET_SPELL = 51;
LMSG_Q_REPUTATION_SPELL_INFO = 52;
LMSG_Q_REPUTATION_SUPPLY_INFO = 53;
LMSG_Q_SINGLE_TRANSMOG_SLOT = 54;
LMSG_Q_SINGLE_TRANSMOG = 55;
LMSG_MSG = 56;
-- LMSG_A_TOGGLE_MF_STATE = 57;
LMSG_A_REMOVE_RUNE = 58;
LMSG_Q_ACTIVATION_KEY = 59;
LMSG_Q_MARKET_BUFF_INFO = 60;
LMSG_Q_MARKET_BUFF_DATA = 61;
LMSG_A_FETCH_MARKET_BUFF = 62;
LMSG_Q_MEMORIZED_SPELL = 63;
LMSG_Q_ACTIVATED_SPELL = 64;
LMSG_Q_CLASS_SKILL = 65;
LMSG_Q_CLASS_SPELLMOD = 66;
LMSG_Q_CLASS_SKILL_POINT = 67;
LMSG_A_ACTIVATE_CLASS_SPELL = 68;
LMSG_A_REMOVE_CLASS_SPELL = 69;
LMSG_A_LEARN_CLASS_SKILL = 70;
LMSG_Q_CLASS_SKILL_BONUS = 71;
LMSG_Q_TRANSMOG_SLOTS = 72;
LMSG_A_COLLECT_TRANSMOG = 73;
LMSG_Q_SINGLE_TRANSMOG_COLLECTION = 74;
LMSG_Q_PTR = 75;
LMSG_A_MEMORIZE_SPELL = 76;
LMSG_A_RESET_CLASSSKILL = 77;
LMSG_Q_MAX_SPELL_SLOT = 78;
LMSG_Q_REPUTATION_ITEM_INFO = 79;
LMSG_Q_REPUTATION_SPELL_OWNED = 80;
LMSG_A_FETCH_REPUTATION_SPELL = 81;
LMSG_A_FETCH_REPUTATION_ITEM = 82;

----------------------------------------------

LEGACY_NOTIFY_UNLOCK_SPELL = "%s已解锁。";

LNOTIFY_UNLOCK_SPELL = 1;
LNOTIFY_CANT_INHERIT_LEGACY = 2;
LNOTIFY_NOT_QUALIFIED_TO_INHERIT_LEGACY = 3;
LNOTIFY_NOT_ENOUGH_EMBEM_TO_INHERIT = 4;
LNOTIFY_INHERITED_LEGACY = 5;
LNOTIFY_CANT_AFFORD_TO_BUY = 6;
LNOTIFY_BOUGHT_ITEM = 7;
LNOTIFY_NEED_NETHER_SCROLL_TO_REPLACE_SPELL = 8;
LNOTIFY_BOUGHT_GOLD = 9;
LNOTIFY_NOT_ENOUGH_CURRENCY = 10;
LNOTIFY_MARKET_NOT_AVAILABLE = 11;
LNOTIFY_ITEM_VARIATION_POOL_FULL = 12;
LNOTIFY_GAIN_TP = 13;
LNOTIFY_COLLECT_TRANSMOG = 14;
LNOTIFY_RIFT_LEADER_BOARD = 15;
LNOTIFY_NEW_REWARD = 16;
LNOTIFY_GAIN_CP = 17;
LNOTIFY_GAIN_CURRENCY = 18;
LNOTIFY_RECEIVED_COSMIC_MAIL = 19;
LNOTIFY_MF_STATE_CHANGED = 20;
LNOTIFY_MF_SEAL_FAILED_NO_MF_LEVEL = 21;
LNOTIFY_GUILD_LEVEL_UP = 22;
LNOTIFY_GUILD_LEVEL_DOWN = 23;
LNOTIFY_NOT_ENOUGH_XP_TO_ACTIVATE_GUILD_BONUS = 24;
LNOTIFY_NOT_MEET_GUILD_LEVEL_REQ_TO_ACTIVATE_BONUS = 25;
LNOTIFY_NOT_ENOUGH_MEMORY_SHARD = 26;
LNOTIFY_NOT_ENOUGH_MEMORY_POWER = 27;
LNOTIFY_RECEIVED_NEW_ACTIVATION_KEY = 28;
LNOTIFY_NOT_ENOUGH_CP = 29;
LNOTIFY_REQUIRE_RESTING = 30;
LNOTIFY_NOT_ENOUGH_GOLD = 31;
LNOTIFY_SOME_SPELLS_IN_COOLDOWN = 32;
LNOTIFY_CLASSSKIL_RESET = 33;
LNOTIFY_SOCKET_MISMATCH = 34;
LNOTIFY_ACHI13515_STARTED = 35;
LNOTIFY_ACHI13515_STOPPED = 36;

LEGACY_NOTIFICATIONS = 
{
	[LNOTIFY_UNLOCK_SPELL] = "%s已解锁。花费修研点数：%d。",
	[LNOTIFY_CANT_INHERIT_LEGACY] = "你不能继承这项遗产。",
	[LNOTIFY_NOT_QUALIFIED_TO_INHERIT_LEGACY] = "你没有继承无价遗产的能力。",
	[LNOTIFY_NOT_ENOUGH_EMBEM_TO_INHERIT] = "你没有足够的信念徽章。",
	[LNOTIFY_INHERITED_LEGACY] = "你成为了一项遗产的继承人。",
	[LNOTIFY_CANT_AFFORD_TO_BUY] = "你的资金不足。",
	[LNOTIFY_BOUGHT_ITEM] = "你购得了一件物品。",
	[LNOTIFY_NEED_NETHER_SCROLL_TO_REPLACE_SPELL] = "你需要使用%d个幽冥之书才可以执行这项动作。",
	[LNOTIFY_BOUGHT_GOLD] = "你成功购得了%d"..LEGACY_MONEY_GOLD_ICON.." %d"..LEGACY_MONEY_SILVER_ICON.." %d"..LEGACY_MONEY_COPPER_ICON.."。",
	[LNOTIFY_NOT_ENOUGH_CURRENCY] = "你的资金不足。",
	[LNOTIFY_MARKET_NOT_AVAILABLE] = "市场系统当前不可用。",
	[LNOTIFY_ITEM_VARIATION_POOL_FULL] = "无法完成鉴定。物品ID：[%d]。请将此ID反馈给开发者。",
	[LNOTIFY_GAIN_TP] = "你获得了%d点修研点数。",
	[LNOTIFY_COLLECT_TRANSMOG] = "%s加入了你的幻化收藏。",
	[LNOTIFY_RIFT_LEADER_BOARD] = "unused",
	[LNOTIFY_NEW_REWARD] = "你收到了一件新的奖励。",
	[LNOTIFY_GAIN_CP] = "你获得了%d点社区贡献值。",
	[LNOTIFY_GAIN_CURRENCY] = "你得到了%d点市场资金。",
	[LNOTIFY_RECEIVED_COSMIC_MAIL] = "你收到了一封星际邮件。",
	[LNOTIFY_MF_STATE_CHANGED] = "你的阅历状态改变为：%s",
	[LNOTIFY_MF_SEAL_FAILED_NO_MF_LEVEL] = "你还未记录任何阅历信息，不能将阅历状态改变为封笔。",
	[LNOTIFY_GUILD_LEVEL_UP] = "你的公会等级提高到了%d。",
	[LNOTIFY_GUILD_LEVEL_DOWN] = "你的公会等级降低到了%d。",
	[LNOTIFY_NOT_ENOUGH_XP_TO_ACTIVATE_GUILD_BONUS] = "你的公会影响力不足以启动这项阵营增益。（需要最少%d影响力，公会当前拥有%d影响力）",
	[LNOTIFY_NOT_MEET_GUILD_LEVEL_REQ_TO_ACTIVATE_BONUS] = "你的公会等级未达到使用这项阵营增益的最低要求。",
	[LNOTIFY_NOT_ENOUGH_MEMORY_SHARD] = "你没有足够的记忆碎片。",
	[LNOTIFY_NOT_ENOUGH_MEMORY_POWER] = "你的记忆无法以与这道裂隙产生共鸣。";
	[LNOTIFY_RECEIVED_NEW_ACTIVATION_KEY] = "你收到了%d段新的神秘代码。";
	[LNOTIFY_NOT_ENOUGH_CP] = "你没有足够的贡献值。";
	[LNOTIFY_REQUIRE_RESTING] = "需要处于休息状态才能执行这项动作。";
	[LNOTIFY_NOT_ENOUGH_GOLD] = "你没有足够的金币。";
	[LNOTIFY_SOME_SPELLS_IN_COOLDOWN] = "仍有部分技能处于冷却状态。";
	[LNOTIFY_CLASSSKIL_RESET] = "技能精通已重置。";
	[LNOTIFY_SOCKET_MISMATCH] = "宝石与插槽的类型不符，无法进行镶嵌。";
	[LNOTIFY_ACHI13515_STARTED] = "正义杀戮：正在同步。——即使独身一人，也没有丝毫击退你深入这座危机四伏的矿井的决心。是什么令你如此坚定地要将那个盗匪头目就地正法？";
	[LNOTIFY_ACHI13515_STOPPED] = "正义杀戮：同步中断。——你记忆中的事件与此情此景并不相同。";
}

LEGACY_MESSAGE = 
{
	[0] = "%s已完全充能并转换为新形态：%s",
}