aaJLQiandao = {};
 aaJLQiandao.LevelViews = {};
 aaJLQiandao.levels = {};
 local titletop = -100;
local titleleft = 50;
 function aaJLQiandao:ClickDoneBtn(index,btn) 
 local level = aaJLQiandao.levels[index];
 aaData:sendAddonMsg(10006, level, "GUILD");
 end;
 function aaJLQiandao:ClickQDBtn() aaData:sendAddonMsg(10006, 999, "GUILD");
 end;
 aaJLQiandao.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaJLQiandao.mainView:SetFrameStrata("TOOLTIP");
 aaJLQiandao.mainView:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-qdjl"});
 aaJLQiandao.mainView:SetWidth(512);
 aaJLQiandao.mainView:SetHeight(512);
 aaJLQiandao.mainView:SetPoint("CENTER",0,0);
 aaJLQiandao.mainView:SetMovable(1);
 aaJLQiandao.mainView:EnableMouse();
 aaJLQiandao.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaJLQiandao.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaJLQiandao.mainView:Hide();
 end;
 local titlewidth1 = 80;
local titlewidth2 = 262;
local titlewidth3 = 80;
local titletop = -120;
local titletop1 = titletop-30;
 aaJLQiandao.title = aaJLQiandao.mainView:CreateFontString("aaJLQiandao.title", "OVERLAY", "GameFontNormal") do 
 aaJLQiandao.title:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLQiandao.title:SetPoint("TOPLEFT", aaJLQiandao.mainView, "TOPLEFT", 45,titletop);
 aaJLQiandao.title:SetWidth(titlewidth1+titlewidth2+titlewidth3);
 aaJLQiandao.title:SetHeight(20);
 aaJLQiandao.title:SetSpacing(5);
 aaJLQiandao.title:SetText(aaData.color_green.."asdada阿斯达撒大所大所大所大所多");
 end;
 aaJLQiandao.leveltitle = aaJLQiandao.mainView:CreateFontString("aaJLQiandao.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLQiandao.leveltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLQiandao.leveltitle:SetPoint("TOPLEFT", aaJLQiandao.mainView, "TOPLEFT", 45,titletop1);
 aaJLQiandao.leveltitle:SetWidth(titlewidth1);
 aaJLQiandao.leveltitle:SetHeight(20);
 aaJLQiandao.leveltitle:SetSpacing(5);
 aaJLQiandao.leveltitle:SetText(aaData.color_green.."签到天数");
 end;
 aaJLQiandao.jltitle = aaJLQiandao.mainView:CreateFontString("aaJLQiandao.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLQiandao.jltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLQiandao.jltitle:SetPoint("TOPLEFT", aaJLQiandao.mainView, "TOPLEFT", 45+titlewidth1,titletop1);
 aaJLQiandao.jltitle:SetWidth(titlewidth2);
 aaJLQiandao.jltitle:SetHeight(20);
 aaJLQiandao.jltitle:SetSpacing(5);
 aaJLQiandao.jltitle:SetText(aaData.color_green.."奖励");
 end;
 aaJLQiandao.zttitle = aaJLQiandao.mainView:CreateFontString("aaJLQiandao.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLQiandao.zttitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLQiandao.zttitle:SetPoint("TOPLEFT", aaJLQiandao.mainView, "TOPLEFT", 45+titlewidth1+titlewidth2,titletop1);
 aaJLQiandao.zttitle:SetWidth(titlewidth3);
 aaJLQiandao.zttitle:SetHeight(20);
 aaJLQiandao.zttitle:SetSpacing(5);
 aaJLQiandao.zttitle:SetText(aaData.color_green.."状态");
 end;
 aaJLQiandao.btn = CreateFrame('Button', nil, aaJLQiandao.mainView, 'UIPanelButtonTemplate') do 
 aaJLQiandao.btn:SetPoint('TOPLEFT', aaJLQiandao.mainView, 'TOPLEFT', (512-80)/2, titletop1-300);
 aaJLQiandao.btn:SetSize(80, 40);
 aaJLQiandao.btn:Disable();
 aaJLQiandao.btn:SetText("点击签到");
 aaJLQiandao.btn:SetScript('OnClick', function() aaJLQiandao:ClickQDBtn();
 end);
 end;
 function aaJLQiandao:CreateCell(index,x,y) 
 local menuView = CreateFrame("Frame",nil,aaJLQiandao.mainView) do 
 menuView:SetFrameStrata("TOOLTIP");
 menuView:SetWidth(titlewidth1+titlewidth2+titlewidth3);
 menuView:SetHeight(30);
 menuView:SetPoint("TOPLEFT",aaJLQiandao.mainView,'TOPLEFT',x,y);
 menuView:Hide();
 end;
 menuView.levelText = menuView:CreateFontString("menuView.levelText", "OVERLAY", "GameFontNormal") do 
 menuView.levelText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.levelText:SetPoint("TOPLEFT", menuView, "TOPLEFT", 0,0);
 menuView.levelText:SetWidth(titlewidth1);
 menuView.levelText:SetHeight(30);
 menuView.levelText:SetSpacing(5);
 menuView.levelText:SetText(aaData.color_blue.."Lv888");
 end;
 menuView.itemText = menuView:CreateFontString("menuView.itemText", "OVERLAY", "GameFontNormal") do 
 menuView.itemText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.itemText:SetPoint("TOPLEFT", menuView, "TOPLEFT", titlewidth1,0);
 menuView.itemText:SetWidth(titlewidth2);
 menuView.itemText:SetHeight(30);
 menuView.itemText:SetSpacing(5);
 menuView.itemText:SetText(aaData.color_blue.."Lv888");
 end;
 local btnwidth = 70;
 local btnheight = 30;
 menuView.levelBtn = CreateFrame('Button', nil, menuView, 'UIPanelButtonTemplate') do 
 menuView.levelBtn:SetPoint('TOPLEFT', menuView, 'TOPLEFT', titlewidth1+titlewidth2+(titlewidth3-btnwidth)/2, 0);
 menuView.levelBtn:SetSize(btnwidth, btnheight);
 menuView.levelBtn:Disable();
 menuView.levelBtn:SetText("已领取");
 menuView.levelBtn:SetScript('OnClick', function() aaJLQiandao:ClickDoneBtn(index,menuView.levelBtn);
 end);
 end;
 return menuView;
 end;
 local top = -175;
 local topspace = -35;
 aaJLQiandao.LevelViews[1] = aaJLQiandao:CreateCell(1,45,top+topspace*0);
 aaJLQiandao.LevelViews[2] = aaJLQiandao:CreateCell(2,45,top+topspace*1);
 aaJLQiandao.LevelViews[3] = aaJLQiandao:CreateCell(3,45,top+topspace*2);
 aaJLQiandao.LevelViews[4] = aaJLQiandao:CreateCell(4,45,top+topspace*3);
 aaJLQiandao.LevelViews[5] = aaJLQiandao:CreateCell(5,45,top+topspace*4);
 aaJLQiandao.LevelViews[6] = aaJLQiandao:CreateCell(6,45,top+topspace*5);
 aaJLQiandao.LevelViews[7] = aaJLQiandao:CreateCell(7,45,top+topspace*6);
 aaJLQiandao.LevelViews[8] = aaJLQiandao:CreateCell(8,45,top+topspace*7);
 aaJLQiandao.LevelViews[9] = aaJLQiandao:CreateCell(9,45,top+topspace*8);
 function aaJLQiandao:reload() 
 local index = 1;
 aaJLQiandao.levels = {};
 for k,v in pairs(aa_jlqiandao) do 
 aaJLQiandao.levels[index] = k;
 index = index + 1;
 end;
 table.sort(aaJLQiandao.levels);
 index = 1;
 for i = 1,#aaJLQiandao.levels do 
 local k = aaJLQiandao.levels[i];
 local v = aa_jlqiandao[k];
 if k>0 and v~=nil and v~="" and v~={} then aaJLQiandao.LevelViews[index]:Show();
 local isOk = v[1]+0;
 local jfall = v[2]+0;
 local level = k+0;
 local isOkqd = v[3];
 local reward = v[4];
 aaJLQiandao.title:SetText("已签到天数："..aaData.color_blue..jfall.."天|r");
 aaJLQiandao.LevelViews[index].levelText:SetText(level.."天");
 aaJLQiandao.LevelViews[index].itemText:SetText(reward);
 aaJLQiandao.LevelViews[index]:Show();
 if isOkqd == 0 then aaJLQiandao.btn:SetText("点击签到");
 aaJLQiandao.btn:Enable();
 else aaJLQiandao.btn:SetText("已签到");
 aaJLQiandao.btn:Disable();
 end;
 if isOk == 0 then aaJLQiandao.LevelViews[index].levelBtn:SetText("领取");
 aaJLQiandao.LevelViews[index].levelBtn:Enable();
 elseif isOk == 1 then aaJLQiandao.LevelViews[index].levelBtn:SetText("已领取");
 aaJLQiandao.LevelViews[index].levelBtn:Disable();
 elseif isOk == 2 then aaJLQiandao.LevelViews[index].levelBtn:SetText("未达到");
 aaJLQiandao.LevelViews[index].levelBtn:Disable();
 end;
 index = index + 1;
 else aaJLQiandao.LevelViews[index]:Hide();
 end;
 end;
 end;
 local CancelButton = CreateFrame('Button', nil, aaJLQiandao.mainView, 'UIPanelButtonTemplate') do 
 CancelButton:SetPoint('TOPRIGHT', aaJLQiandao.mainView, 'TOPRIGHT', -30, -20);
 CancelButton:SetSize(60, 30);
 CancelButton:SetText("关闭");
 CancelButton:SetScript('OnClick', function() aaJLQiandao:hide();
 end);
 end;
 function aaJLQiandao:show() aaJLQiandao.mainView:Show();
 aaJLQiandao:reload();
 end;
 function aaJLQiandao:hide() aaJLQiandao.mainView:Hide();
 aaJLQiandao:reload();
 end;