local targetguid

local DD_UPLIMIT_DATA ={}

local UplimitItemStr

local UplimitDescriptionStr

local UplimtMySelfVal

local playertex,targettex

local ptext,ttext

local UplimtTargeVal

-- local tarX = 110;
-- local tarY = -55;
-- local tarSizeX = 50;
-- local tarSizeY = 50;
-- local plaX = 138;
-- local plaY = 0;
local tarX = 0;
local tarY = 0;
local tarSizeX = 0;
local tarSizeY = 0;
local plaX = 0;
local plaY = 0;

function DD_CreateUpLimitInfoButton()

	local Button = CreateFrame("Button", "DD_UpLimitInfoButton", UIParent, nil)

	local BgFile = "Interface\\ICONS\\info1"

	Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});

	-- Button:SetSize(35, 35)
	Button:SetSize(tarSizeX, tarSizeY)
	-- Button:SetPoint("BOTTOM", TargetFrame,"TOP",85, -82)	--TargetFrame
	-- Button:SetPoint("BOTTOM", TargetFrame,"TOP",110, -55) --TargetFrame
	-- Button:SetPoint("BOTTOM", TargetFrame,"TOP",tarX, tarY)
	Button:SetPoint("TOP",tarX, tarY)
	Button:EnableMouse(true)

	local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
			
	ButtonFontStr:SetFont("Interface\\Fonts\\FZZY.TTF", 30)	--需要设置字体

	ButtonFontStr:SetAllPoints(Button)

	ButtonFontStr:SetText("")
	Button:Hide()
	return Button

end


function DD_CreateTargetUPlimitInfo()

local ttex = PlayerFrame:CreateFontString("DD_ttex")

	-- dyu:SetFontObject(GameFontNormalLarge)
	ttex:SetFont("Interface\\Fonts\\FZZY.TTF", 13)

	ttex:SetPoint("TOPLEFT",138,0)
	
	local str = string.format(UplimitDescriptionStr,UplimtTargeVal)
	
	ttex:SetText(targettex..str)

	ttex:SetTextColor(1.0,1.0,0)

	ttex:SetShadowColor(1.0,0.65,0,0.5)
	
	return ttex
	
end


function DD_CreatePlayerUPlimitInfo()

	local ptex = PlayerFrame:CreateFontString("DD_ptex")

	-- dyu:SetFontObject(GameFontNormalLarge)
	ptex:SetFont("Interface\\Fonts\\FZZY.TTF", 13)

	-- ptex:SetPoint("TOPLEFT",138,0)
	ptex:SetPoint("TOPLEFT",plaX,plaY)
	
	local str = string.format(UplimitDescriptionStr,UplimtMySelfVal)
	
	ptex:SetText(playertex..str)

	ptex:SetTextColor(1.0,1.0,0)

	ptex:SetShadowColor(1.0,0.65,0,0.5)
	
	return ptex
	-- ptex:Hide()
end


function DD_RECVUPLIMITDATA(self, event, h, msg, classtype, sender)	
	
	if event == "CHAT_MSG_ADDON" then
		-- print("msg = "..msg)
		--物品ID和值
		if h == "DD_UPLIMITDATA_ITEM"  then
			-- print("msg = "..msg)
			local list = Split(msg,";:;")	
			
			local i = tonumber(list[1])
			
			DD_UPLIMIT_DATA[i] = {}
			
			DD_UPLIMIT_DATA[i].val = list[2]
		end
		
		--物品信息提示窗口描述语句,需带%s
		if h == "DD_UPLIMITITEM_TEXT"  then
			UplimitItemStr = msg
		end
		
		if h == "DD_UPLIMITDESCRIPTION_TEXT"  then
			UplimitDescriptionStr = msg
		end
		
		if h == "DD_UPLIMITMYSELF_VAL"  then
			UplimtMySelfVal = msg
		end
		
		if h == "DD_UPLIMITDESCRIPTION_PLAYERTEX" then
			playertex = msg
			if ptext ~= nil then
				if ptext:GetName() then 
					local name = ptext:GetName()
					setglobal(name,nil)
				 end	
				ptext:Hide()
				ptext = nil
				ptext = {}
			end			
			ptext = DD_CreatePlayerUPlimitInfo()
			ptext:Show()
		end
		
		if h == "DD_UPLIMITDESCRIPTION_TARGETVAL" then
			UplimtTargeVal = msg
		end
		
		
		if h == "DD_UPLIMITDESCRIPTION_TARGETTEX" then
			targettex = msg
			if ttext ~= nil then
				if ttext:GetName() then 
					local name = ttext:GetName()
					setglobal(name,nil)
				 end	
				ttext:Hide()
				ttext = nil
				ttext = {}
			end		
			--ttext = DD_CreateTargetUPlimitInfo()
			ttext =	DD_CreateUpLimitInfoButton()
			
			local str = string.format(UplimitDescriptionStr,UplimtTargeVal)
			local text = targettex..str
			ttext:SetScript("OnEnter", function() GameTooltip:SetOwner(ttext, "ANCHOR_RIGHT"); GameTooltip:SetText(str); GameTooltip:Show() end)
			ttext:SetScript("OnLeave", function() GameTooltip:SetOwner(ttext, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
		end
	
		if h == "DD_UPVAL_tarX" then
			tarX = tonumber(msg)
			-- print("tarX = "..tarX)
		end			
		if h == "DD_UPVAL_tarY" then
			tarY = tonumber(msg)
			-- print("tarY = "..tarY)
		end
		if h == "DD_UPVAL_tarSizeX" then
			tarSizeX = tonumber(msg)
			-- print("tarSizeX = "..tarSizeX)
		end
		if h == "DD_UPVAL_tarSizeY" then
			tarSizeY = tonumber(msg)
			-- print("tarSizeY = "..tarSizeY)
		end
		if h == "DD_UPVAL_plaX" then
			plaX = tonumber(msg)
			-- print("plaX = "..plaX)
		end
		if h == "DD_UPVAL_plaY" then
			plaY = tonumber(msg)
			-- print("plaY = "..plaY)
		end
		
	end
end


-- TargetFrameTextureFrame:HookScript("OnShow",function() local nn = UnitGUID("target")

-- print(nn)

	-- if targetguid ~= nn then
		-- targetguid = nn
		-- if UnitIsPlayer("target") then
		-- local m = tonumber(nn)
		-- SendAddonMessage("DD_UPLIMIT_TARGETDATA",tostring(m),"GUILD", UnitName("player"));
		-- else
		-- print("不是玩家")
			-- return;
		-- end
	-- else
		-- if UnitIsPlayer("target") then
			-- ttext:Show()
		-- end
	-- end

-- end)


-- TargetFrameTextureFrame:HookScript("OnUpdate",function() local nn = UnitGUID("target")
-- print(nn)

	-- if targetguid ~= nn then
		-- targetguid = nn
		-- if UnitIsPlayer("target") then
			-- local m = tonumber(nn)
			-- SendAddonMessage("DD_UPLIMIT_TARGETDATA",tostring(m),"GUILD", UnitName("player"));
		-- else
		-- print("不是玩家")
			-- return;
		-- end
	-- else
		-- if UnitIsPlayer("target") then
			-- ttext:Show()
		-- end
	-- end

-- end)


TargetFrameTextureFrame:HookScript("OnHide",function() 
ttext:Hide()
end)



function Split(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

local DD_UPLIMIT_RECV_FRAME = CreateFrame("Frame")
DD_UPLIMIT_RECV_FRAME:RegisterEvent("CHAT_MSG_ADDON")
DD_UPLIMIT_RECV_FRAME:SetScript("OnEvent", DD_RECVUPLIMITDATA)


local DD_UPLIMIT_RELOAD_FRAME = CreateFrame("Frame")
DD_UPLIMIT_RELOAD_FRAME:RegisterEvent("PLAYER_LOGIN")
DD_UPLIMIT_RELOAD_FRAME:SetScript("OnEvent", 
function(self, event, ...) SendAddonMessage("DD_UPLIMIT_DATA","All","GUILD", UnitName("player"));  end)
--local B = DD_CreateUpLimitInfoButton();

local DD_UPLIMIT_FRAME = CreateFrame("Frame")
DD_UPLIMIT_FRAME:HookScript("OnUpdate",function() local nn = UnitGUID("target")
-- print(nn)

	if targetguid ~= nn then
		targetguid = nn
		if UnitIsPlayer("target") then
			local m = tonumber(nn)
			SendAddonMessage("DD_UPLIMIT_TARGETDATA",tostring(m),"GUILD", UnitName("player"));
		else
		--print("不是玩家")
			return;
		end
	else
		if UnitIsPlayer("target") then
			ttext:Show()
			else
			ttext:Hide()
		end
	end

end)

function adds(self)

local _, itemLink = self:GetItem()

local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")
	
	local num = tonumber(itemId)
	if DD_UPLIMIT_DATA[num] ~= nil then
		if DD_UPLIMIT_DATA[num].val ~= nil then
		local text = string.format(UplimitItemStr,DD_UPLIMIT_DATA[num].val)
			self:AddLine(text)
		end
	end
end




-- GameTooltip:HookScript("OnShow", adds);
-- ItemRefTooltip:HookScript("OnShow", adds);
ItemRefTooltip:HookScript("OnTooltipSetItem",adds)
GameTooltip:HookScript("OnTooltipSetItem",adds)
-- ItemRefShoppingTooltipl:HookScript("OnTooltipSetItem",adds)
-- ItemRefShoppingTooltip2:HookScript("OnTooltipSetItem",adds)
-- ItemRefShoppingTooltip3:HookScript("OnTooltipSetItem",adds)
-- ShoppingTooltipl:HookScript("OnTooltipSetItem",adds)
-- ShoppingTooltip2:HookScript("OnTooltipSetItem",adds)
-- ShoppingTooltip3:HookScript("OnTooltipSetItem",adds)