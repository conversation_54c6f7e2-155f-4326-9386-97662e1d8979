/*
SQLyog Ultimate
MySQL - 5.7.35-log 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

create table `store_frames_ui` (
	`Name` varchar (765),
	`Size` int (11),
	`Coords_X` int (11),
	`Coords_Y` int (11),
	`Bg_File` varchar (765),
	`ParentFrame` varchar (765),
	`Frame_Title` varchar (765)
); 
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('TRANSMOG_FRAME','1100','0','30','StoreFrame_Main','UIParent','Transmogs');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('MOUNTS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Mounts');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('BUNDLES_FRAME','1100','0','30','StoreFrame_Main','UIParent','Bundles');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('SERVICES_FRAME','1100','0','30','StoreFrame_Main','UIParent','Services');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('AXE_ONEHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','One-Hand Axes');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('AXE_TWOHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','Two-Hand Axes');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('SWORD_ONEHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','One-Hand Swords');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('SWORD_TWOHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','Two-Hand Swords');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('MACE_ONEHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','One-Hand Maces');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('MACE_TWOHAND_FRAME','1100','0','30','StoreFrame_Main','UIParent','Two-Hand Maces');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('STAFF_FRAME','1100','0','30','StoreFrame_Main','UIParent','Staves');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('DAGGERS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Daggers');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('WARGLAIVE_FRAME','1100','0','30','StoreFrame_Main','UIParent','Warglaives');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('FIST_WEAPON_FRAME','1100','0','30','StoreFrame_Main','UIParent','Fists Weapons');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('SHIELD_FRAME','1100','0','30','StoreFrame_Main','UIParent','Shields');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('CLOTH_FRAME','1100','0','30','StoreFrame_Main','UIParent','Cloth Equipments');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('LEATHER_FRAME','1100','0','30','StoreFrame_Main','UIParent','Leather Equipments');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('PLATE_FRAME','1100','0','30','StoreFrame_Main','UIParent','Plate Equipments');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('MAIL_FRAME','1100','0','30','StoreFrame_Main','UIParent','Mail Equipments');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('VIP_FRAME','1100','0','30','StoreFrame_Main','UIParent','Vip Subscription');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('POLEARMS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Polearms');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('LIBRAMS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Librams');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('RELICS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Relics');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('IDOLS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Idols');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('TOTEMS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Totems');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('WANDS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Wands');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('THROWNS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Throwns');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('CROSSBOWS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Crossbows');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('BOWS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Bows');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('GUNS_FRAME','1100','0','30','StoreFrame_Main','UIParent','Guns');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('TRANSMOG_2','1100','0','30','StoreFrame_Main','UIParent','Transmogs');
insert into `store_frames_ui` (`Name`, `Size`, `Coords_X`, `Coords_Y`, `Bg_File`, `ParentFrame`, `Frame_Title`) values('TRANSMOG_3','1100','0','30','StoreFrame_Main','UIParent','Transmogs');
