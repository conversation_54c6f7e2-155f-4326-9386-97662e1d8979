<!-- 此xml为滚动窗口实验xml,已经测试成功-->
<!--
<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\..\FrameXML\UI.xsd">
<ScrollFrame name="scrolltest">
	<Size x="150" y="150"/>
	<Anchors>
		<Anchor point="CENTER"/>
	</Anchors>
	<Layers>
		<Layer level="BACKGROUND">
			<Texture setAllPoints="true">
				<Color r="0.0" g="0.0" b="0.0"/>
			</Texture>
		</Layer>
	</Layers>
	<ScrollChild>
	<Frame>
		<Size x="250" y="250"/>
		<Layers>
			<Layer level="ARTWORK">
				<Texture file="Interface\ICONS\jf1">
					<Size x="100" y="100"/>
					<Anchors>
						<Anchor point="CENTER"/>
					</Anchors>
				</Texture>
			</Layer>
		</Layers>
	</Frame>
	</ScrollChild>
	<Frames>
		<Slider name = "slidertesth" orientation="HORIZONTAL" minValue="0" maxValue="100" defaultValue="0" valueStep="1">
			<Anchors>
				<Anchor point="TOP" relativePoint="BOTTOM" relativeTo="scrolltest"/>
			</Anchors>
			<Size x="150" y="25"/>
			<ThumbTexture name="$parentThumbTexture" file="Interface\Buttons\UI-ScrollBar-Knob">
				<Size x="25" y="25"/>
			</ThumbTexture>
			<Scripts>
			<OnValueChanged>
				scrolltest:SetHorizontalScroll(1 * self:GetValue())
			</OnValueChanged>
		</Scripts>
		</Slider>
		<Slider name = "slidertestv" orientation="VERTICAL" minValue="0" maxValue="100" defaultValue="0" valueStep="1">
			<Anchors>
				<Anchor point="LEFT" relativePoint="RIGHT" relativeTo="scrolltest"/>
			</Anchors>
			<Size x="25" y="150"/>
			<ThumbTexture name="$parentThumbTexture" file="Interface\Buttons\UI-ScrollBar-Knob">
				<Size x="25" y="25"/>
			</ThumbTexture>
			<Scripts>
			<OnValueChanged>
				scrolltest:SetVerticalScroll(self:GetValue())
			</OnValueChanged>
			</Scripts>
		</Slider>
	</Frames>
</ScrollFrame>
</Ui>
-->