local SignInRewSubFrames = {}

local SignInRewShowButtons = {}
local SignInRewShowButtonStrs = {}
local SignInRewShowButtonCountStrs = {}

local SignInContinuousRewButtons = {}
local SignInContinuousRewButtonStrs = {}
local SignInContinuousRewButtonCountStrs = {}

local SignInTotalRewButtons = {}
local SignInTotalRewButtonStrs = {}
local SignInTotalRewButtonCountStrs = {}

local SignInVipExRewButtons = {}
local SignInVipExRewButtonStrs = {}
local SignInVipExRewButtonCountStrs = {}

local SignInIcon ={
EXP	  	= "Interface\\ICONS\\xp",
TOKEN	= "Interface\\ICONS\\jf",
GOLD	= "Interface\\ICONS\\jinbi",
LMHR	= "Interface\\ICONS\\lmry",
BLHR	= "Interface\\ICONS\\blry",
AREAN 	= "Interface\\ICONS\\jjc",
STAT	= "",
}

RewShow_Month, RewShow_Year, RewShow_NumDays, RewShow_FirstWeekday = CalendarGetMonth(NowClick)

--背景图窗口
local SignInRewBackDropFrame = CreateFrame("Frame", "SignInRewBackDropFrame", UIParent)
SignInRewBackDropFrame:SetSize(760, 640)
SignInRewBackDropFrame:RegisterForDrag("LeftButton")
SignInRewBackDropFrame:SetPoint("CENTER")
SignInRewBackDropFrame:SetBackdrop(
{
    bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
    edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
    edgeSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})
SignInRewBackDropFrame:EnableMouse(true)
SignInRewBackDropFrame:Hide()

--滚动窗口
local SignInRewScrollFrame = CreateFrame("ScrollFrame","SignInRewScrollFrame",SignInRewBackDropFrame)
SignInRewScrollFrame:SetSize(760, 490)
SignInRewScrollFrame:SetPoint("TOP",0,-10)
SignInRewScrollFrame:Show()
SignInRewScrollFrame:EnableMouse(true)
SignInRewScrollFrame:EnableMouseWheel(true)

--滚动子窗口
local SignInRewScrollChildFrame = CreateFrame("Frame","SignInRewScrollChildFrame",SignInRewScrollFrame) 
SignInRewScrollChildFrame:SetSize(760, 640)
-- SignInRewScrollChildFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 4, right = 4, top = 4, bottom = 4 }});
SignInRewScrollChildFrame:SetPoint("CENTER",0,0)
SignInRewScrollChildFrame:Show()
SignInRewScrollFrame:SetScrollChild(SignInRewScrollChildFrame)


local SignInRewSlider = CreateFrame("Slider","SignInRewSlider",SignInRewScrollFrame)
SignInRewSlider:SetOrientation("VERTICAL")
SignInRewSlider:SetMinMaxValues(0,100)
SignInRewSlider:SetValue(0)
SignInRewSlider:SetValueStep(1.0)
SignInRewSlider:SetPoint("LEFT",SignInRewScrollFrame,"RIGHT",0,0)
SignInRewSlider:SetSize(32,32)

-- 奖励窗体标题模块
local SignInRewTitleFrame = CreateFrame("Frame", "SignInRewTitleFrame", SignInRewBackDropFrame, nil)
SignInRewTitleFrame:SetSize(150, 30)
SignInRewTitleFrame:SetPoint("TOP", 0, 28)
SignInRewTitleFrame:EnableMouse(true)
SignInRewTitleFrame:SetBackdrop(
{
    bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
    edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
    tile = true,
    edgeSize = 16,
    tileSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})

--奖励窗体标题模块文字描述
local SignInRewTitleText = SignInRewTitleFrame:CreateFontString("SignInRewTitleText")
SignInRewTitleText:SetFont("Fonts\\ZYHei.ttf", 18)
SignInRewTitleText:SetPoint("CENTER", 0, 0)
SignInRewTitleText:SetText("|cffFFC125签到奖励|r")

-- 奖励窗体关闭按钮
local SignInRewCloseButton = CreateFrame("Button", "SignInRewCloseButton", SignInRewBackDropFrame, "UIPanelCloseButton")
SignInRewCloseButton:SetPoint("TOPRIGHT", 15, 15)
SignInRewCloseButton:EnableMouse(true)
SignInRewCloseButton:SetSize(36, 36)

-- 奖励窗体关闭按钮
local SignInRewBlackButton = CreateFrame("Button", "SignInRewBlackButton", SignInRewBackDropFrame)
SignInRewBlackButton:SetPoint("TOPRIGHT", -5, 15)
SignInRewBlackButton:EnableMouse(true)
SignInRewBlackButton:SetSize(36, 36)
SignInRewBlackButton:SetNormalTexture("Interface\\Buttons\\UI-Panel-BiggerButton-Up")
SignInRewBlackButton:SetPushedTexture("Interface\\Buttons\\UI-Panel-BiggerButton-Down")
SignInRewBlackButton:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight")

SignInRewBlackButton:SetScript("OnClick",
function(self)
	SignInFrame:Show()
    SignInRewBackDropFrame:Hide()
end)

SignInPrepareScript(SignInRewBlackButton,"返回签到页面",nil,nil)

local function GetShowInfo(list)
	local des = list[1]
	local englishFaction, localizedFaction = UnitFactionGroup("player")
	if des == "ITEM" then
		local itemid = tonumber(list[2])
		local count = ("|cFFFF6600"..list[3].."|r")
		local link = list[4]
		local icon = ("|T"..GetItemIcon(itemid)..":36|t")
		return count,icon,link
	elseif des == "EXP" then
		local count = tonumber(list[2])
		local icon = ("|T"..SignInIcon["EXP"]..":36|t")
		return count,icon
	elseif des == "GOLD" then
		local count = tonumber(list[2])
		local icon = ("|T"..SignInIcon["GOLD"]..":36|t")
		return count,icon
	elseif des == "TOKEN" then
		local count = tonumber(list[2])
		local icon = ("|T"..SignInIcon["TOKEN"]..":36|t")
		return count,icon
	elseif des == "HR" then
		local count = tonumber(list[2])
		local icon = ""
		if englishFaction == "Alliance" then
			icon = ("|T"..SignInIcon["LMHR"]..":36|t")
		else
			icon = ("|T"..SignInIcon["BLHR"]..":36|t")
		end
		return count,icon
	elseif des == "AREAN" then
		local count = tonumber(list[2])
		local icon = ("|T"..SignInIcon["AREAN"]..":36|t")
		return count,icon
	elseif des == "STAT" then
		local count = tonumber(list[2])
		local icon = ("|T"..SignInIcon["STAT"]..":36|t")
		return count,icon
	end
end

local function GetShowDes(list)
	local des = list[1]
	
	if des == "ITEM" then
		return nil
	elseif des == "EXP" then
		return "经验值 * "..list[2]
	elseif des == "GOLD" then
		return "金币 * "..list[2]
	elseif des == "TOKEN" then
		return "积分 * "..list[2]
	elseif des == "HR" then
		return "荣誉点 * "..list[2]
	elseif des == "AREAN" then
		return "竞技点 * "..list[2]
	elseif des == "STAT" then
		return "斗气值 * "..list[2]
	end
end

local function CreateSignInRewSubFrames(i)
local sf = CreateFrame("Frame", "SignInRewSubFrame"..i, SignInRewScrollChildFrame)
sf:SetSize(740, 40)
sf:SetPoint("TOP", SignInRewScrollChildFrame, 0, -(sf:GetHeight() * (i - 1) + 10))
sf:SetBackdrop(
{
	bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
	edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
	edgeSize = 12,
	insets = { left = 4, right = 4, top = 4, bottom = 4 }
})
sf:SetBackdropColor(0, 0, 0, 0)
local sfstr = sf:CreateFontString("$parent".."Str"..i)
sfstr:SetFont("Fonts\\ZYHei.ttf", 15)
sfstr:SetPoint("LEFT", sf, 10, 0)
sfstr:SetText("|cFF00FF00"..i.."日|r")
return sf
end

local function CreateSignInShowButtons(j,i,f)
	if f == nil then
		f = SignInRewSubFrames[j]
	end
local b = CreateFrame("Button", "SignInShowButton["..j.."]"..i, f, nil)
b:SetSize(40, 40)
b:SetPoint("LEFT", f, 100 + (b:GetWidth() * (i - 1)), 0)
b:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
b:SetBackdrop( { bgFile = "Interface\\BUTTONS\\UI-EmptySlot", })
bstr = b:CreateFontString("$parent".."Str"..i)
bstr:SetFont("Fonts\\ZYHei.ttf", 12)
bstr:SetAllPoints(b)
b:SetFontString(bstr)
bcstr = b:CreateFontString("$parent".."CStr"..i)
bcstr:SetFont("Fonts\\ZYHei.ttf", 10, "OUTLINE")
bcstr:SetPoint("CENTER", b, 0, -8)
return b,bstr,bcstr
end

local function CreateSignInOtherFrame(name,x,y)
	local scf = CreateFrame("Frame", name, SignInRewBackDropFrame)
	scf:SetSize(740, 40)
	scf:SetPoint("BOTTOM", SignInRewBackDropFrame, x, y)
	scf:SetBackdrop(
	{
		bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
		edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
		edgeSize = 12,
		insets = { left = 4, right = 4, top = 4, bottom = 4 }
	})
	scf:SetBackdropColor(0, 0, 0, 0)
	scf:SetFrameStrata("HIGH")
	local scfstr = scf:CreateFontString("$parent".."Str")
	scfstr:SetFont("Fonts\\ZYHei.ttf", 15)
	scfstr:SetPoint("LEFT", scf, 10, 0)
	return scf,scfstr
end

local function CreateSignInOtherShowButton(f,i)
local b = CreateFrame("Button", "$parent".."Button"..i, f, nil)
b:SetSize(40, 40)
b:SetPoint("LEFT", f, 100 + (b:GetWidth() * (i - 1)), 0)
b:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
b:SetBackdrop( { bgFile = "Interface\\BUTTONS\\UI-EmptySlot", })
bstr = b:CreateFontString("$parent".."Str"..i)
bstr:SetFont("Fonts\\ZYHei.ttf", 12)
bstr:SetAllPoints(b)
b:SetFontString(bstr)
bcstr = b:CreateFontString("$parent".."CStr"..i)
bcstr:SetFont("Fonts\\ZYHei.ttf", 10, "OUTLINE")
bcstr:SetPoint("CENTER", b, 0, -8)
return b,bstr,bcstr
end

for i = 1,RewShow_NumDays do
	SignInRewSubFrames[i] = CreateSignInRewSubFrames(i)
	SignInRewShowButtons[i] = {}
	SignInRewShowButtonStrs[i] = {}
	SignInRewShowButtonCountStrs[i] = {}
	for j = 1,16 do
	SignInRewShowButtons[i][j],SignInRewShowButtonStrs[i][j],SignInRewShowButtonCountStrs[i][j]= CreateSignInShowButtons(i,j);
	end
end

SignInRewScrollChildFrame:SetHeight(40*RewShow_NumDays+10)

local SignInContinuousRewFrame,SignInContinuousRewStr = CreateSignInOtherFrame("SignInContinuousRewFrame",0,58)
SignInContinuousRewStr:SetText("|cFF00FF00连签999日|r")

local SignInTotalRewFrame,SignInTotalRewStr = CreateSignInOtherFrame("SignInTotalRewFrame",0,18)
SignInTotalRewStr:SetText("|cFF00FF00累签666日|r")

local SignInVipExRewFrame,SignInVipExRewStr = CreateSignInOtherFrame("SignInVipExRewFrame",0,98)
SignInVipExRewStr:SetText("|cFF00FF00会员额外奖励|r")

for i = 1,16 do
SignInContinuousRewButtons[i],SignInContinuousRewButtonStrs[i],SignInContinuousRewButtonCountStrs[i] = CreateSignInOtherShowButton(SignInContinuousRewFrame,i)
SignInTotalRewButtons[i],SignInTotalRewButtonStrs[i],SignInTotalRewButtonCountStrs[i] = CreateSignInOtherShowButton(SignInTotalRewFrame,i)
SignInVipExRewButtons[i],SignInVipExRewButtonStrs[i],SignInVipExRewButtonCountStrs[i] = CreateSignInOtherShowButton(SignInVipExRewFrame,i)
end

function ChangeOtherButtonInfo()
	for k,v in pairs(SignInTotalRewDate) do 
		if SignInTotalRewDate[k] ~= nil then
			if PlayerSignInDate.TotalDay ~= nil then
				if k > PlayerSignInDate.TotalDay then
					for key,val in pairs(SignInTotalRewDate[k]) do
						local str = val
						local i,j = k,key
						local list = SignInSplit(str,"&")
						local count,icon,link = GetShowInfo(list)
						local text = GetShowDes(list)
						SignInTotalRewStr:SetText("|cFF00FF00累签"..i.."日|r") 
						SignInTotalRewButtons[j]:SetText(icon)
						SignInTotalRewButtonCountStrs[j]:SetText("|cFFFF6600"..count.."|r")
						SignInPrepareScript(SignInTotalRewButtons[j],text,nil,link)
					end	
					break
				end	
			end
		end
	end

	for k,v in pairs(SignInContinuousRewDate) do 
		if SignInContinuousRewDate[k] ~= nil then
			if PlayerSignInDate.ContDay ~= nil then
				if k > PlayerSignInDate.ContDay then
					for key,val in pairs(SignInContinuousRewDate[k]) do
						local str = val
						local i,j = k,key
						local list = SignInSplit(str,"&")
						local count,icon,link = GetShowInfo(list)
						local text = GetShowDes(list)
						SignInContinuousRewStr:SetText("|cFF00FF00连签"..i.."日|r") 
						SignInContinuousRewButtons[j]:SetText(icon)
						SignInContinuousRewButtonCountStrs[j]:SetText("|cFFFF6600"..count.."|r")
						SignInPrepareScript(SignInContinuousRewButtons[j],text,nil,link)
					end
					break
				end
			end
		end
	end
	
	for k,v in pairs(SignInVipExRewDate) do 
		if SignInVipExRewDate[k] ~= nil then
			local viplv = PlayerSignInDate.VIPVal
			if viplv == 0 then
				viplv = 1
			end
			for key,val in pairs(SignInVipExRewDate[k]) do
				local str = val
				local i,j = k,key
				local list = SignInSplit(str,"&")
				local count,icon,link = GetShowInfo(list)
				local text = GetShowDes(list)
				SignInVipExRewStr:SetText("|cFF00FF00会员VIP"..viplv.."奖励|r") 
				SignInVipExRewButtons[j]:SetText(icon)
				SignInVipExRewButtonCountStrs[j]:SetText("|cFFFF6600"..count.."|r")
				SignInPrepareScript(SignInVipExRewButtons[j],text,nil,link)
			end
		end
	end
end

function ChangeDayButtonInfo()
	for k,v in pairs(SignInRewShowDate) do 
		if SignInRewShowDate[k] ~= nil then
			for key,val in pairs(SignInRewShowDate[k]) do
				local str = val
				local i,j = k,key
				local list = SignInSplit(str,"&")
				local count,icon,link = GetShowInfo(list)
				local text = GetShowDes(list)
				SignInRewShowButtons[i][j]:SetText(icon)
				SignInRewShowButtonCountStrs[i][j]:SetText("|cFFFF6600"..count.."|r")
				SignInPrepareScript(SignInRewShowButtons[i][j],text,nil,link)
			end
		end
	end
end

--滑块注册滚动窗口移动
SignInRewSlider:SetScript("OnValueChanged",
function(self,offset) 
SignInRewScrollFrame:SetVerticalScroll(math.ceil((SignInRewScrollChildFrame:GetHeight()-SignInRewScrollFrame:GetHeight())/100 * self:GetValue()))
end)

--滚动窗口注册鼠标中键关联滑块
SignInRewScrollFrame:SetScript("OnMouseWheel", 
function(self,val)  
	local s1 = SignInRewSlider	
	local minv,maxv = s1:GetMinMaxValues()
	local cv = s1:GetValue()
	local nv = cv - ( val * 5 )
	nv = max(nv, minv)
	nv = min(nv, maxv)
	if ( nv ~= cv ) then
		s1:SetValue(nv);
	end		
end)