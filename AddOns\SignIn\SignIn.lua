local SignInDayButtons = {}
local SignInDayStrings = {}

local SignInWeekday = {}	--星期1-7按钮边框
local SignInWeekdayStr = {}	--星期1-7文字描述

local nian = tonumber(date("%Y")); yue = tonumber(date("%m")); ri = tonumber(date("%d"));

local Clicks = 0
local _, MaxMonth, _, MaxYear = CalendarGetMaxDate()
local _, MinMonth, _, MinYear = CalendarGetMinDate()

local function SignInSetTexture(Button, File)
    local ButtonTexture = Button:CreateTexture("ButtonTexture")
    ButtonTexture:SetAllPoints(Button)
    ButtonTexture:SetTexture(File)
	Button:SetNormalTexture(ButtonTexture)
end

local function IsInSignIn(d, n)
    for i=SignNowDay,n,1 do
        if (d == i) then
		    return true
		end
	end
	return false
end

local function SignInSetBackdrop(Button, BgFile, EdgeFile)
    Button:SetBackdrop(
                {
                    bgFile = BgFile,
					edgeFile = EdgeFile,
                    edgeSize = 16,
                    insets = { left = 4, right = 4, top = 4, bottom = 4 }
                })
end

function SignInPrepareScript(object, text, script, itemlink)
	  if text then	
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:SetFrameStrata("TOOLTIP");GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:SetFrameStrata("TOOLTIP");GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
	  end
	  	  
	  if type(script) == "function" then	
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end

-- 菜单按钮
local SignInMenuButton = CreateFrame("Button", "SignInMenuButton", UIParent)
SignInMenuButton:SetSize(64, 64)
SignInMenuButton:SetPoint("TOPLEFT", 90, -115)
SignInMenuButton:SetMovable(true)
SignInMenuButton:EnableMouse(true)
SignInMenuButton:RegisterForDrag("LeftButton")
SignInMenuButton:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
SignInMenuButton:SetBackdrop({bgFile = "Interface\\Icons\\Inv_Misc_Tournaments_banner_Nightelf"})
SignInMenuButton:SetScript("OnDragStart", SignInMenuButton.StartMoving)
SignInMenuButton:SetScript("OnDragStop", SignInMenuButton.StopMovingOrSizing)
SignInMenuButton:SetScript("OnMouseUp", 
function(self)
    if (SignInFrame:IsShown()) then
        SignInFrame:Hide()
    else
        SignInFrame:Show()
		-- SpFrame:Hide()
    end
end)

SignInMenuButton:SetScript("OnEnter", 
    function(self)
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
        GameTooltip.default = 1
        GameTooltip:SetText("|cFFFF0033签到奖励|r\n|cFFFFCC66按住拖动图标|r\n|cFF99FF33单击打开功能|r\n|cFF00CCFF再次单击关闭|r\n|cFF00CCFF按 Esc 键退出|r")
        GameTooltip:Show()
    end)

SignInMenuButton:SetScript("OnLeave", 
    function(self)
        GameTooltip:Hide()
    end)

SignInMenuButton:Show()

--主窗口
local SignInFrame = CreateFrame("Frame", "SignInFrame", UIParent)
SignInFrame:SetSize(480, 560)
SignInFrame:RegisterForDrag("LeftButton")
SignInFrame:SetPoint("CENTER")
SignInFrame:SetToplevel(true)
SignInFrame:SetClampedToScreen(true)
SignInFrame:SetMovable(true)
SignInFrame:EnableMouse(true)
SignInFrame:SetScript("OnDragStart", SignInFrame.StartMoving)
SignInFrame:SetScript("OnDragStop", SignInFrame.StopMovingOrSizing)
SignInFrame:SetBackdrop(
{
    bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
    edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
    edgeSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})
SignInFrame:Hide()

-- 关闭按钮
local SignInCloseButton = CreateFrame("Button", "SignInCloseButton", SignInFrame, "UIPanelCloseButton")
SignInCloseButton:SetPoint("TOPRIGHT", 15, 15)
SignInCloseButton:EnableMouse(true)
SignInCloseButton:SetSize(36, 36)

-- 标题模块
local SignInTitleFrame = CreateFrame("Frame", "SignInTitleFrame", SignInFrame, nil)
SignInTitleFrame:SetSize(150, 30)
SignInTitleFrame:SetPoint("TOP", 0, 25)
SignInTitleFrame:EnableMouse(true)
SignInTitleFrame:SetBackdrop(
{
    bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
    edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
    tile = true,
    edgeSize = 16,
    tileSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})

--标题模块文字描述
local SignInTitleText = SignInTitleFrame:CreateFontString("SignInTitleText")
SignInTitleText:SetFont("Fonts\\ZYHei.ttf", 18)
SignInTitleText:SetPoint("CENTER", 0, 0)
SignInTitleText:SetText("|cffFFC125每天签到|r")

--玩家签到信息文字描述
local PlayerSignInText = SignInTitleFrame:CreateFontString("PlayerSignInText")
PlayerSignInText:SetFont("Fonts\\ZYHei.ttf", 18)
PlayerSignInText:SetPoint("CENTER", 0, -500)
PlayerSignInText:SetText("|cffFFC125玩家当前连签天数:|r")

-- 月份选项左
local SignInMonthButtonLeft = CreateFrame("Button", "SignInMonthButtonLeft", SignInFrame)
SignInMonthButtonLeft:SetSize(30, 30)
SignInMonthButtonLeft:SetPoint("TOPLEFT", 160, -20)
SignInMonthButtonLeft:EnableMouse(true)
SignInMonthButtonLeft:SetNormalTexture("Interface\\Buttons\\UI-SpellbookIcon-PrevPage-Up")
SignInMonthButtonLeft:SetHighlightTexture("Interface\\Buttons\\UI-Common-MouseHilight")
SignInMonthButtonLeft:SetPushedTexture("Interface\\Buttons\\UI-SpellbookIcon-PrevPage-Down")
SignInMonthButtonLeft:SetDisabledTexture("Interface\\Buttons\\UI-SpellbookIcon-PrevPage-Disabled")

-- 月份选项右
local SignInMonthButtonRight = CreateFrame("Button", "SignInMonthButtonRight", SignInFrame)
SignInMonthButtonRight:SetSize(30, 30)
SignInMonthButtonRight:SetPoint("TOPLEFT", 290, -20)
SignInMonthButtonRight:EnableMouse(true)
SignInMonthButtonRight:SetNormalTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Up")
SignInMonthButtonRight:SetHighlightTexture("Interface\\Buttons\\UI-Common-MouseHilight")
SignInMonthButtonRight:SetPushedTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Down")
SignInMonthButtonRight:SetDisabledTexture("Interface\\Buttons\\UI-SpellbookIcon-NextPage-Disabled")

-- 中间月份
local SignInMonthButtonCenter = CreateFrame("Button", "SignInMonthButtonCenter", SignInFrame)
SignInMonthButtonCenter:SetSize(94, 28)
SignInMonthButtonCenter:SetPoint("TOP", 0, -20)
SignInMonthButtonCenter:EnableMouse(true)
SignInMonthButtonCenter:SetBackdrop(
{
    bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
    edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
    edgeSize = 16,
    insets = { left = 4, right = 4, top = 4, bottom = 4 }
})

-- 月份说明
local SignInMonthButtonCenterStr = SignInMonthButtonCenter:CreateFontString("SignInMonthButtonCenterStr")
SignInMonthButtonCenterStr:SetFont("Fonts\\ZYHei.ttf", 15)
SignInMonthButtonCenterStr:SetPoint("CENTER")

-- 查看签到奖励按钮
local SignInViewRewInfoButton = CreateFrame("Button", "SignInViewRewInfoButton", SignInFrame, "UIPanelButtonTemplate")
SignInViewRewInfoButton:SetSize(120, 40)
SignInViewRewInfoButton:SetPoint("CENTER", 0, -250)
SignInViewRewInfoButton:EnableMouse(true)

-- 查看签到奖励按钮文字
local SignInViewRewInfoButtonStr = SignInViewRewInfoButton:CreateFontString("$parent".."str")
SignInViewRewInfoButtonStr:SetFont("Fonts\\ZYHei.ttf", 12)
SignInViewRewInfoButton:SetFontString(SignInViewRewInfoButtonStr)
SignInViewRewInfoButton:SetText("查看签到奖励")

-- 获取VIP额外奖励按钮
-- local SignInGetDayVipExRewButton = CreateFrame("Button", "SignInGetDayVipExRewButton", SignInFrame, "UIPanelButtonTemplate")
-- SignInGetDayVipExRewButton:SetSize(120, 40)
-- SignInGetDayVipExRewButton:SetPoint("CENTER", -160, -200)
-- SignInGetDayVipExRewButton:EnableMouse(true)

-- 获取VIP额外奖励按钮文字
-- local SignInGetDayVipExRewButtonStr = SignInGetDayVipExRewButton:CreateFontString("$parent".."str")
-- SignInGetDayVipExRewButtonStr:SetFont("Fonts\\ZYHei.ttf", 12)
-- SignInGetDayVipExRewButton:SetFontString(SignInGetDayVipExRewButtonStr)
-- SignInGetDayVipExRewButton:SetText("领取会员额外奖励")

-- 领取连签奖励按钮
-- local SignInGetContinuousRewButton = CreateFrame("Button", "SignInGetContinuousRewButton", SignInFrame, "UIPanelButtonTemplate")
-- SignInGetContinuousRewButton:SetSize(120, 40)
-- SignInGetContinuousRewButton:SetPoint("CENTER", 0, -200)
-- SignInGetContinuousRewButton:EnableMouse(true)

-- 领取连签奖励按钮文字
-- local SignInGetContinuousRewButtonStr = SignInGetContinuousRewButton:CreateFontString("$parent".."str")
-- SignInGetContinuousRewButtonStr:SetFont("Fonts\\ZYHei.ttf", 12)
-- SignInGetContinuousRewButton:SetFontString(SignInGetContinuousRewButtonStr)
-- SignInGetContinuousRewButton:SetText("领取连续签到奖励")

-- 领取累积奖励按钮
-- local SignInGetTotalRewButton = CreateFrame("Button", "SignInGetTotalRewButton", SignInFrame, "UIPanelButtonTemplate")
-- SignInGetTotalRewButton:SetSize(120, 40)
-- SignInGetTotalRewButton:SetPoint("CENTER", 160, -200)
-- SignInGetTotalRewButton:EnableMouse(true)

-- 领取累积奖励按钮文字
-- local SignInGetTotalRewButtonStr = SignInGetTotalRewButton:CreateFontString("$parent".."str")
-- SignInGetTotalRewButtonStr:SetFont("Fonts\\ZYHei.ttf", 12)
-- SignInGetTotalRewButton:SetFontString(SignInGetTotalRewButtonStr)
-- SignInGetTotalRewButton:SetText("领取累积签到奖")


--创建签到一周星期1-7窗口显示
local function CreateSignInWeekdayButton(i)
local b = CreateFrame("Button", "SignInWeekday"..i, SignInFrame)
b:SetSize(60, 30)
b:SetPoint("TOPLEFT", (i - 1) * 60 + 30, -60)
b:SetBackdrop(
    {
        bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
        edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
        edgeSize = 16,
        insets = { left = 4, right = 4, top = 4, bottom = 4 }
    })
b:SetHighlightTexture("Interface\\Buttons\\UI-QuickslotRed")
local str = b:CreateFontString("SignInWeekdayStr"..i)
str:SetFont("Fonts\\ZYHei.ttf", 15)
str:SetPoint("CENTER")
str:SetText("|cFFFF9900周"..i.."|r")
return b,str
end

--生成星期1-7的窗口
for i =1,7 do
SignInWeekday[i],SignInWeekdayStr[i] = CreateSignInWeekdayButton(i)
end

for i=1,42,1 do
    SignInDayButtons[i] = CreateFrame("Button", "SignInDayButton"..i, SignInFrame)

	SignInDayButtons[i]:SetSize(60, 60)
	if (i < 8) then
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 1) * 60 + 30, -90)
	elseif (i > 7 and i < 15) then
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 7 - 1) * 60 + 30, -150)
	elseif (i > 14 and i < 22) then
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 14 - 1) * 60 + 30, -210)
    elseif (i > 21 and i < 29) then
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 21 - 1) * 60 + 30, -270)
	elseif (i > 28 and i < 36) then
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 28 - 1) * 60 + 30, -330)
    else
	    SignInDayButtons[i]:SetPoint("TOPLEFT", (i - 35 - 1) * 60 + 30, -390)
	end
	
	SignInSetBackdrop(SignInDayButtons[i], "Interface\\DialogFrame\\UI-DialogBox-Background", "Interface\\Tooltips\\UI-Tooltip-Border")
	SignInDayButtons[i]:SetHighlightTexture("Interface\\Buttons\\UI-Quickslot-Depress")
	
	SignInDayButtons[i]:SetScript("OnClick",
    function(self)
	    UpDateSignDay(player,i)
    end)
	
	SignInDayButtons[i]:SetScript("OnEnter", 
    function(self)
	    for k=1,7,1 do
		    if ((i - k) % 7 == 0) then
				SignInWeekday[k]:LockHighlight()
			end
		end
    end)

    SignInDayButtons[i]:SetScript("OnLeave", 
    function(self)
	    for p=1,7,1 do
		    if ((i - p) % 7 == 0) then
		        SignInWeekday[p]:UnlockHighlight()
			end
		end
    end)
	
	SignInDayStrings[i] = SignInDayButtons[i]:CreateFontString("SignInDayString"..i)
	SignInDayStrings[i]:SetPoint("CENTER")
	SignInDayStrings[i]:SetFont("Fonts\\ZYHei.ttf", 24)
end

local function SignInUpdateDayEvent(NowClick)
-- Number(可选，默认值=0) -与当前选定日历月的偏移量(以月为单位)，正数表示未来月份,负数代表过去月份
-- month number 月份(1-12)
-- year number 年份(2004+)
-- numDays number 一个月中的天数(28~31)
-- firstWeekday number 每月开始的周日(1=周日,2=周一,...,7=周六)

    local PreMonth, PreYear, PreNumDays, PreFirstWeekday = CalendarGetMonth(NowClick - 1)		--上个月
    local Month, Year, NumDays, FirstWeekday = CalendarGetMonth(NowClick)						-- 本月
	local NextMonth, NextYear, NextNumDays, NextFirstWeekday = CalendarGetMonth(NowClick + 1) 	--下个月
	local PreFirstDay = PreNumDays - (FirstWeekday - 1)
	local NextFirstDay = NumDays + FirstWeekday - 1
	local FirstDay = FirstWeekday - 1

	SignInMonthButtonCenterStr:SetText("|cFFFFFF00"..Year.."年-"..Month.."月|r")

	if (NowClick < 0) then
	    if (SignInMonthButtonLeft:IsEnabled() == 0) then
            SignInMonthButtonLeft:Enable()
	    end
	elseif(NowClick > 0) then
	    if (SignInMonthButtonRight:IsEnabled() == 0) then
            SignInMonthButtonRight:Enable()
	    end
	else
	    SignInMonthButtonLeft:Enable()
	    SignInMonthButtonRight:Enable()
	end
	
	if (Year <= MinYear and Month == MinMonth) then
	    SignInMonthButtonLeft:Disable()
	end
	
	if (Year >= MaxYear and Month == MaxMonth) then
	    SignInMonthButtonRight:Disable()
	end

	for i=1,42,1 do
		SignInDayStrings[i]:SetVertexColor(204/255, 204/255, 204/255)
		SignInDayStrings[i]:SetAlpha(0.5)
		SignInSetTexture(SignInDayButtons[i], "Interface\\Icons\\Zzz_Tou")
		if (i < FirstWeekday) then
		    SignInDayStrings[i]:SetText(tostring(i + PreFirstDay))
		elseif (i > NextFirstDay) then
		    SignInDayStrings[i]:SetText(tostring(i - NextFirstDay))
		else
			SignInDayStrings[i]:SetVertexColor(255/255, 255/255, 0)
			SignInDayStrings[i]:SetAlpha(1)
			SignInDayStrings[i]:SetText(tostring(i - FirstDay))
			if (tonumber(SignInDayStrings[i]:GetText()) == tonumber(date("%d")) and NowClick == 0) then
			    SignInSetBackdrop(SignInDayButtons[i], "Interface\\Icons\\Zzz_NowDay", "Interface\\Tooltips\\UI-Tooltip-Border")
			else
			    SignInSetBackdrop(SignInDayButtons[i], "Interface\\DialogFrame\\UI-DialogBox-Background", "Interface\\Tooltips\\UI-Tooltip-Border")
			end

			local UiDay = tonumber(SignInDayStrings[i]:GetText())
			
			
			if Year == nian and Month == yue then
				if (UiDay == tonumber(date("%d"))) then
					SignInSetTexture(SignInDayButtons[i], "Interface\\Icons\\Zzz_SignNow")
					-- SignInPrepareScript(SignInDayButtons[i],"|T"..GetItemIcon(769)..":40|t * 100")
				elseif (UiDay > tonumber(date("%d"))) then
					SignInPrepareScript(SignInDayButtons[i],"|cFFFF0033还未到达|r\n|cFFFFCC66不可签到|r")
				elseif (UiDay < tonumber(date("%d"))) then
					SignInSetTexture(SignInDayButtons[i], "Interface\\Icons\\Zzz_Repair")
					SignInPrepareScript(SignInDayButtons[i],"|cFFFF0033错过日期|r\n|cFFFFCC66点击补签|r")
				end	
			else
				SignInPrepareScript(SignInDayButtons[i],"")
			end
			
			if (NowClick == 0) then
			    for j=1,31,1 do
			        if PlayerSignInDate[j] == 1 then
				        SignInSetTexture(SignInDayButtons[j + 1 + FirstWeekday - 1 - 1], "Interface\\RaidFrame\\ReadyCheck-Ready")
			        end
			    end
			end
		end
	end
end

function UpDateSignDay(player,Num)
		local UiDay = tonumber(SignInDayStrings[Num]:GetText())

		local msg = nian.."#"..yue.."#"..UiDay.."#"..Num
		
		if UiDay == ri then
			SendAddonMessage("CSSI_SIGNIN_DAY",msg,"GUILD", UnitName("player"))
		elseif UiDay < ri then
			SendAddonMessage("CSSI_SIGNIN_REPAIR",msg,"GUILD", UnitName("player"))
			SignInFrame:Hide()
		end
end

function SignInReady(Num)
SignInSetTexture(SignInDayButtons[Num], "Interface\\RaidFrame\\ReadyCheck-Ready")
end

function SetPlayerSignInText()
	local cday = 0
	local tday = 0
	if PlayerSignInDate.ContDay ~= nil then
		cday = PlayerSignInDate.ContDay
	end

	if PlayerSignInDate.TotalDay ~= nil then
		tday = PlayerSignInDate.TotalDay
	end

PlayerSignInText:SetText("|cffFFC125玩家当前连签天数:"..cday..",累计签到天数："..tday.."|r")
end

function SignInMenuONOFF()
	if SignInMenuButton:IsShown() then
		SignInMenuButton:Hide()
	else
		SignInMenuButton:Show()
	end
	
	if SignInFrame:IsShown() then
		SignInFrame:Hide()
	end
end

SignInFrame:SetScript("OnShow", 
function(self)
    Clicks = 0
	SignInUpdateDayEvent(Clicks)
	SetPlayerSignInText()
end)

SignInMonthButtonLeft:SetScript("OnClick",
function(self)
	Clicks = Clicks - 1
	SignInUpdateDayEvent(Clicks)
end)

SignInMonthButtonRight:SetScript("OnClick",
function(self)
    Clicks = Clicks + 1
	SignInUpdateDayEvent(Clicks)
end)

SignInViewRewInfoButton:SetScript("OnClick",
function(self)
ChangeOtherButtonInfo()
ChangeDayButtonInfo()
SignInFrame:Hide()
SignInRewBackDropFrame:Show()
end)