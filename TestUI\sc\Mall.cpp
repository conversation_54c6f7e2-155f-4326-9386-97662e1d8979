﻿#pragma execution_character_set("UTF-8")

#include "Mall.h"
#include "Object.h"
#include "../../GCAddon/GCAddon.h"
#include "../../VIP/VIP.h"
#include "../../String/String.h"
#include "../../CommonFunc/CommonFunc.h"

void Mall::Load(bool online)
{
	if (online)
	{
		LoadUIMallData();
		SessionMap const& smap = sWorld->GetAllSessions();
		for (SessionMap::const_iterator iter = smap.begin(); iter != smap.end(); ++iter)
			if (Player* player = iter->second->GetPlayer())
			{
				sMall->SendMallUIData(player);
				sMall->SendMallClass(player);
				sGCAddon->SendPacketTo(player, "MALL_DATA_RESET", "OK");
			}
	}
	else
	{
		LoadUIMallData();
	}
}

void Mall::LoadUIMallData()
{
    _MallDataMap.clear();
    _TemporaryPClassVec.clear();
    _MallClass.clear();
    _TemporaryClassMap.clear();
    _ItemInHomePosVec.clear();
    _PclassInHomePosVec.clear();
    _ReceiveRequestMap.clear();
	_SellItemLimit.clear();
    
    QueryResult result = WorldDatabase.PQuery("SELECT 序号,物品编号,物品主类,物品子类,需求VIP,消耗物品ID,消耗物品数量,消耗积分数量,消耗金币数量,消耗经验值,消耗荣誉值,消耗竞技点,主页展示,促销类型,促销百分比,UNIX_TIMESTAMP(限时秒杀开始时间),UNIX_TIMESTAMP(限时秒杀结束时间) FROM 商城");
    if (!result)
        return;

    uint32 count = 0;
    do
    {
        uint32 num = 0;

        Field * field = result->Fetch();
        MallData _MallData;

        uint32 SerialNumber = field[0].GetUInt32();                     
        _MallData.ItemId = field[1].GetUInt32();                        
        _MallData.P_Class = field[2].GetString();                       
        _MallData.S_Class = field[3].GetString();                       
        _MallData.Req_VipLevel = field[4].GetUInt32();                  
        Tokenizer tokens1(field[5].GetString(), ',');                   
        Tokenizer tokens2(field[6].GetString(), ',');                   
        _MallData.Req_ItemId.resize(MAX_MALL_SELL_REQITEM_VAL);
        _MallData.Req_ItemVal.resize(MAX_MALL_SELL_REQITEM_VAL);

       


        uint32 i = 0;
        if (field[5].GetString() == "0" || field[5].GetString().empty())
        {
            for (uint8 j = 0; j < MAX_MALL_SELL_REQITEM_VAL; j++)
            {
                _MallData.Req_ItemId[j] = 0;
                _MallData.Req_ItemVal[j] = 0;
            }
        }
        else
        {
            for (Tokenizer::const_iterator itr1 = tokens1.begin(), itr2 = tokens2.begin(); itr1 != tokens1.end(), itr2 != tokens2.end(); ++itr1, ++itr2)
            {
                ItemTemplate const * pProto = sObjectMgr->GetItemTemplate(uint32(atol(*itr1)));
                if (!pProto && uint32(atol(*itr1)) != 0)
                {
                    sLog->outErrorDb("ItemTemplate表 里不存在有 (Entry: %u) 的物品，商城表：序号 %u 的 第 %u 组 消耗物品编号 和 消耗物品数量 将默认为 0。", uint32(atol(*itr1)), SerialNumber, i + 1);
                    
                    _MallData.Req_ItemId[i] = 0;
                    _MallData.Req_ItemVal[i] = 0;
                    ++i;
                }
                else
                {
                    _MallData.Req_ItemId[i] = uint32(atol(*itr1));
                    _MallData.Req_ItemVal[i] = uint32(atol(*itr2));
                    ++i;
                    ++num;
                }
            }
        }
        _MallData.Req_Jf = field[7].GetUInt32();                        
        _MallData.Req_Money = field[8].GetUInt32();                     
        _MallData.Req_Exp = field[9].GetUInt32();                       
        _MallData.Req_Honor_value = field[10].GetUInt32();               
        _MallData.Req_Arena_Point = field[11].GetUInt32();              
        _MallData.IsHomeShow = field[12].GetString();                   
        _MallData.Promotion_Type = field[13].GetString();               
        _MallData.Discount = field[14].GetUInt32();                     
        _MallData.s_time = field[15].GetUInt64();                       
        _MallData.e_time = field[16].GetUInt64();                       
        _MallData.Ppos = GetItemPosOfPclass(_MallData.P_Class);
        _MallData.Spos = GetItemPosOfSclass(_MallData.P_Class, _MallData.S_Class);

        if (_MallData.Req_Money != 0)
            ++num;
        if (_MallData.Req_Exp != 0)
            ++num;
        if (_MallData.Req_Honor_value != 0)
            ++num;
        if (_MallData.Req_Arena_Point != 0)
            ++num;

    

        if (_MallData.IsHomeShow == "是")
        {
            _MallData.Hpos = GetHomeItemPos(_MallData.P_Class);     
            _PclassInHomePosVec.push_back(_MallData.P_Class);       
        }    
        else
        {
            _MallData.Hpos = 0;
        }

        
        // if (!field[17].GetString().empty())
        // {
            // _MallData.tex = field[13].GetString();
            // sLog->outString("_MallData.tex = %s", _MallData.tex.c_str());
        // }

        _MallData.ElementNum = num;

        std::string msg = _MallData.P_Class + Splice + _MallData.S_Class;
        
        _TemporaryClassMap.push_back(msg);
    
        _MallDataMap[SerialNumber] = _MallData;

        ++count;

    } while (result->NextRow());

    sLog->outString("======加载商城表(MallTemplate) %u 条数据======", count);
}

std::string Mall::GetItemLink(uint32 entry)
{
    ItemTemplate const * temp = sObjectMgr->GetItemTemplate(entry);
    if (!temp)
        return "";
    std::string name = temp->Name1;
    std::ostringstream oss;
    oss << "|c" << std::hex << ItemQualityColors[temp->Quality] << std::dec <<
        "|Hitem:" << entry << ":0:0:0:0:0:0:0:0:0|h[" << name << "]|h|r";
    return oss.str();
}


uint32 Mall::GetItemPosOfPclass(std::string pclass)
{
    _TemporaryPClassVec.push_back(pclass);
    uint32 pos = 0;
    for (auto itr = _TemporaryPClassVec.begin(); itr != _TemporaryPClassVec.end(); ++itr)
    {
        if (pclass == *itr)
            ++pos;
        else
            continue;
    }
    return pos;
}


uint32 Mall::GetItemPosOfSclass(std::string pclass, std::string sclass)
{
    MallClass mc;
    mc.P_Class = pclass;
    mc.S_Class = sclass;
    _MallClass.push_back(mc);
    uint32 pos = 0;
    for (auto itr = _MallClass.begin(); itr != _MallClass.end(); ++itr)
    {
        if (pclass == itr->P_Class && sclass == itr->S_Class)
            ++pos;
        else
            continue;
    }
    return pos;
}

uint32 Mall::GetItemInThePClassWithCount(std::string pclass)
{
    uint32 pos = 0;
    
    for (auto itr = _ItemInHomePosVec.begin(); itr != _ItemInHomePosVec.end(); ++itr)
    {
        if (pclass == *itr)
            ++pos;
        else
            continue;  
    }

    return pos;
}


void Mall::SendHomePclassPos(Player * player)
{
    uint32 pos = 0;

    
    std::set<std::string> st(_PclassInHomePosVec.begin(), _PclassInHomePosVec.end());

    _PclassInHomePosVec.assign(st.begin(), st.end());

    
    for (auto itr = _PclassInHomePosVec.begin(); itr != _PclassInHomePosVec.end(); ++itr)
    {
        std::ostringstream str;
        uint32 num = GetItemInThePClassWithCount(*itr);
        ++pos;
        str << *itr << Splice << pos << Splice << num;
        //player->SendAddonMessage("MALL_UI_FOURTH_GROUP_DATA", str.str(), 7, player);
		sGCAddon->SendPacketTo(player, "MALL_UI_FOURTH_GROUP_DATA", str.str());
        //sLog->outString("MALL_UI_FOURTH_GROUP_DATA = %s",str.str().c_str());
    }

}


uint32 Mall::GetHomeItemPos(std::string pclass)
{
    _ItemInHomePosVec.push_back(pclass);

    uint32 pos = 0;
    for (auto itr = _ItemInHomePosVec.begin(); itr != _ItemInHomePosVec.end(); ++itr)
    {
        if (pclass == *itr)
            ++pos;
        else
            continue;
    }

    return pos;
}

void Mall::SendMallClass(Player * player)
{

    std::sort(_TemporaryClassMap.begin(), _TemporaryClassMap.end());

    _TemporaryClassMap.erase(std::unique(_TemporaryClassMap.begin(), _TemporaryClassMap.end()), _TemporaryClassMap.end()); 

    for (auto itr = _TemporaryClassMap.begin(); itr != _TemporaryClassMap.end(); ++itr)
    {		
		sGCAddon->SendPacketTo(player, "MALL_UI_THIRD_GROUP_DATA", *itr);
        string str = *itr;
    }

	sGCAddon->SendPacketTo(player, "MALL_ALL_DATA", "OK");
}

void Mall::SendMallUIData(Player * player)
{

    for (auto itr = _MallDataMap.begin(); itr != _MallDataMap.end(); ++itr)
    {	
		ItemTemplate const* pProto = sObjectMgr->GetItemTemplate(itr->first);
		if (!pProto)
			continue;

        std::ostringstream str, msg;

		
		str << itr->first << Splice << GetItemLink(itr->second.ItemId) << Splice << itr->second.P_Class;
		
		str << Splice << itr->second.S_Class << Splice << itr->second.Req_VipLevel << Splice << itr->second.Req_Money;
		
		str << Splice << itr->second.Req_Exp << Splice << itr->second.Req_Honor_value << Splice << itr->second.Req_Arena_Point;
		
		str << Splice << itr->second.IsHomeShow << Splice << itr->second.Promotion_Type << Splice << itr->second.Ppos;
		
		str << Splice << itr->second.Spos << Splice << itr->second.Hpos << Splice << itr->second.ElementNum;
		
		str << Splice << itr->second.Discount << Splice << itr->second.s_time << Splice << itr->second.e_time;
		
		str << Splice << itr->second.Req_Jf;

		sGCAddon->SendPacketTo(player, "MALL_UI_FIRST_GROUP_DATA", str.str());

        for (uint32 i = 0; i < MAX_MALL_SELL_REQITEM_VAL; ++i)
        {
            if (GetItemLink(itr->second.Req_ItemId[i]).empty() || GetItemLink(itr->second.Req_ItemId[i]) == "")
                continue;
            else
                if (itr->second.Req_ItemVal[i] == 0)
                    continue;
                else    
                    msg << itr->first <<Splice << i+1 << Splice << GetItemLink(itr->second.Req_ItemId[i]) << Splice << itr->second.Req_ItemVal[i];
    
			sGCAddon->SendPacketTo(player, "MALL_UI_SECOND_GROUP_DATA", msg.str());

            msg.str("");      
        }
    }
    
    SendMallClass(player);
	sGCAddon->SendPacketTo(player, "MALL_ALL_DATA", "OK");
    SendHomePclassPos(player);
}

void Mall::SendMallTestData(Player * player)
{
    std::ostringstream str;

    str << 300 <<Splice<< 500;

	sGCAddon->SendPacketTo(player, "MALL_UI_TEST_DATA", str.str());

	sGCAddon->SendPacketTo(player, "MALL_ALL_DATA", "OK");
}


bool Mall::IsIfLimit(Player * player)
{
    auto itr = _ReceiveRequestMap.find(player);

	if (itr == _ReceiveRequestMap.end())    
	{
		return true;
	}
    else
    {
        if (itr->second > 100)       
        {
            sLog->outString("玩家：%s,在请求服务器交互超过限制!!!",player->GetName().c_str());
            return false;
        }
    }
        
    return true;
}


void Mall::EraseLimit(Player * player)
{
    _ReceiveRequestMap.erase(player);
}


void Mall::SetLimit(Player * player)
{
    auto itr = _ReceiveRequestMap.find(player);

    if (itr == _ReceiveRequestMap.end())
    {
        _ReceiveRequestMap[player] = 1;
    }
    else
    {
        itr->second += 1;
    }

}


bool Mall::check(Player * player, uint32 id)
{
	auto itr = _MallDataMap.find(id);

	if (itr == _MallDataMap.end())
		return false;

	if (player->vipLevel < itr->second.Req_VipLevel)
	{
		ChatHandler(player->GetSession()).PSendSysMessage("当前%s，需达到%s", sVIP->GetVIPName(player->vipLevel).c_str(), sVIP->GetVIPName(itr->second.Req_VipLevel).c_str());
		return false;
	}

	if (!player->HasEnoughMoney(itr->second.Req_Money * GOLD))
	{
		ChatHandler(player->GetSession()).PSendSysMessage(sString->Format(sString->GetText(STR_REQ_GOLD), player->GetMoney(), itr->second.Req_Money));
		return false;
	}

	if (player->GetUInt32Value(PLAYER_XP) < itr->second.Req_Exp)
	{
		ChatHandler(player->GetSession()).PSendSysMessage(sString->Format(sString->GetText(STR_REQ_XP), player->GetUInt32Value(PLAYER_XP), itr->second.Req_Exp));
		return false;
	}

	if (itr->second.Req_Honor_value > 0 && player->GetHonorPoints() < itr->second.Req_Honor_value)
	{
		ChatHandler(player->GetSession()).PSendSysMessage(sString->Format(sString->GetText(STR_REQ_HONOR), player->GetHonorPoints(), itr->second.Req_Honor_value));
		return false;
	}

	if (itr->second.Req_Arena_Point > 0 && player->GetArenaPoints() < itr->second.Req_Arena_Point)
	{
		ChatHandler(player->GetSession()).PSendSysMessage(sString->Format(sString->GetText(STR_REQ_ARENA), player->GetArenaPoints(), itr->second.Req_Arena_Point));
		return false;
	}

	ItemTemplate const* pProto[MAX_MALL_SELL_REQITEM_VAL];
	for (size_t i = 0; i < MAX_MALL_SELL_REQITEM_VAL; i++)
		pProto[i] = sObjectMgr->GetItemTemplate(itr->second.Req_ItemId[i]);

	for (size_t i = 0; i < MAX_MALL_SELL_REQITEM_VAL; i++)
	{
		if (pProto[i] && itr->second.Req_ItemId[i] > 0)
		{
			if (player->GetItemCount(itr->second.Req_ItemId[i]) < itr->second.Req_ItemVal[i])
			{
				ChatHandler(player->GetSession()).PSendSysMessage(sString->Format(sString->GetText(STR_REQ_ITEM), sCF->GetItemLink(itr->second.Req_ItemId[i]).c_str(), itr->second.Req_ItemVal[i] - player->GetItemCount(itr->second.Req_ItemId[i])));
				return false;
			}
		}
	}

	return true;
}

void Mall::DesItemSellReq(Player * player, uint32 id)
{
	if (!check(player, id))
		return;

	auto itr = _MallDataMap.find(id);

	if (itr->second.Req_Money > 0)
	{
		player->ModifyMoney(-int32(itr->second.Req_Money));
	}

	if (itr->second.Req_Exp > 0)
	{
		player->SetUInt32Value(PLAYER_XP, player->GetUInt32Value(PLAYER_XP) - itr->second.Req_Exp);
	}

	if (itr->second.Req_Jf > 0)
	{
		sCF->UpdateTokenAmount(player, itr->second.Req_Jf, false, "[商城]购买物品");
	}

	if (itr->second.Req_Honor_value > 0)
	{
		player->ModifyHonorPoints(-int32(itr->second.Req_Honor_value));
	}

	if (itr->second.Req_Arena_Point)
	{
		player->ModifyArenaPoints(-int32(itr->second.Req_Arena_Point));
	}

	ItemTemplate const* pProto[MAX_MALL_SELL_REQITEM_VAL];
	for (size_t i = 0; i < MAX_MALL_SELL_REQITEM_VAL; i++)
		pProto[i] = sObjectMgr->GetItemTemplate(itr->second.Req_ItemId[i]);

	for (size_t i = 0; i < MAX_MALL_SELL_REQITEM_VAL; i++)
		if (pProto[i] && itr->second.Req_ItemVal[i] > 0)
			player->DestroyItemCount(itr->second.Req_ItemId[i], itr->second.Req_ItemVal[i], true, false);
	
	AddMallItem(player, id);
}

void Mall::SetSellLimit(Player * player, bool b)
{
	_SellItemLimit[player] = b;
}

void Mall::AddMallItem(Player * player, uint32 id)
{
	auto itr = _MallDataMap.find(id);

	player->AddItem(itr->second.ItemId, 1);
}

bool Mall::IsSellLimit(Player * player)
{
	bool b = _SellItemLimit[player]  == false ? _SellItemLimit[player] : true; //true 有限制

	SetSellLimit(player, true);

	return b;
}


class MALL_WORLDSCRIPT : public WorldScript
{
public:
    MALL_WORLDSCRIPT() : WorldScript("MALL_WORLDSCRIPT") {}
    void LoadCustomData()
    {
        sMall->LoadUIMallData();
    }
};

class MALL_PLAYERSCRIPT : public PlayerScript
{
public:
    MALL_PLAYERSCRIPT() : PlayerScript("MALL_PLAYERSCRIPT") {}
   
	void OnLogin(Player * player ,bool first)
    {
        if (!sMall->IsIfLimit(player))  
            sMall->EraseLimit(player);

		sMall->SetSellLimit(player, false);	
    }

};

void AddSC_MallScripts()
{
    new MALL_WORLDSCRIPT();
    new MALL_PLAYERSCRIPT();
}
