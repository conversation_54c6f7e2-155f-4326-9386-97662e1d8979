/**
    This plugin can be used for common player customizations
 */

#include "ScriptMgr.h"
#include "Player.h"
#include "Config.h"
#include "Chat.h"
#include "Laboratory.h"

#pragma execution_character_set("UTF-8")


void Laboratory::LoadUIInteractionData()
{
    _UIvec.clear();

    QueryResult result = WorldDatabase.PQuery("SELECT * FROM LaboratoryUI");

    if (!result)
        return;

    uint32 count = 0;

    do
    {
        Field * fields = result->Fetch();

        string str = fields[0].GetString();

        _UIvec.push_back(str);

        ++count;

    } while (result->NextRow());

    sLog->outString("======加载实验室-界面交互表(LaboratoryUI) %u 条数据======",count);
}

void Laboratory::SendUIInteractionData(Player * player)
{
    uint32 count = 0;
    for (auto itr = _UIvec.begin(); itr != _UIvec.end(); ++itr)
    {

        string str = *itr;

        player->SendAddonMessage("实验室",str,7, player);

        ++count;
    }
    sLog->outString("======发送给客户端<<<<<<<<<<<<实验室-界面交互表(LaboratoryUI) %u 条数据======,", count);
}


class LABORATORY_WORLDSCRIPT : public WorldScript
{
public:
    LABORATORY_WORLDSCRIPT() : WorldScript("LABORATORY_WORLDSCRIPT") {}

    void LoadCustomData()
    {
        sLaboratory->LoadUIInteractionData();
    }
};


class LABORATORY_PLAYERSCRIPT : public PlayerScript
{
public:
    LABORATORY_PLAYERSCRIPT() : PlayerScript("LABORATORY_PLAYERSCRIPT") {}

    void OnLogin(Player* player)
    {
        sLaboratory->SendUIInteractionData(player);
    }

    void OnPlayerChatAddon(std::string header, std::string msg, uint8 chn, Player* player)
    {
        if (chn == 4) //CHAT_MSG_GUILD = 4
        {
            if (header == "UI" && msg == "All")
            {
                sLaboratory->SendUIInteractionData(player);
            }
        }
    }
};

class LABORATORY_COMMANDSCRIPT : public CommandScript
{
public:
    LABORATORY_COMMANDSCRIPT() : CommandScript("LABORATORY_COMMANDSCRIPT") { }

    static bool HandleReLoadUICommand(ChatHandler* handler, const char* args)
    {
        /*
        if (!*args)	这是命令后面跟着的参数 例如 breload dd_itemup 3<- 这个就是args
        return false;

        std::string temp = args;

        if (!args || temp.find_first_not_of(' ') == std::string::npos)
        return false;
        */
        if (!handler->GetSession()->GetPlayer()->CanSpeak())
            return false;

        Player* player = handler->GetSession()->GetPlayer();
        if (!player)
            return false;

        
        sLaboratory->LoadUIInteractionData();
        sLaboratory->SendUIInteractionData(player);

        handler->PSendSysMessage("实验室-界面交互表 已经重新加载成功！！");
        handler->PSendSysMessage("实验室-界面交互表数据 已经发送给客户端！！");
        return true;
    }

    std::vector<ChatCommand> GetCommands() const override
    {
        static std::vector<ChatCommand> ddallcommandTable =
        {
            { "ui", SEC_ADMINISTRATOR, true, &HandleReLoadUICommand, "" },	//实验室UI

        };

        static std::vector<ChatCommand> commandTable =
        {
            { "breload", SEC_ADMINISTRATOR, true, nullptr, "", ddallcommandTable }
        };

        return commandTable;
    }
};


void AddLaboratoryScripts()
{
    new LABORATORY_WORLDSCRIPT();
    new LABORATORY_PLAYERSCRIPT();
    new LABORATORY_COMMANDSCRIPT();
}

