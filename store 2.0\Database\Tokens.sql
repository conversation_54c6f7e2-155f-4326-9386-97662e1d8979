/*
SQLyog Ultimate
MySQL - 5.7.35-log 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

create table `item_template` (
	`entry` mediumint (8),
	`class` tinyint (3),
	`subclass` tinyint (3),
	`SoundOverrideSubclass` tinyint (3),
	`name` varchar (765),
	`displayid` mediumint (8),
	`Quality` tinyint (3),
	`Flags` int (10),
	`FlagsExtra` int (10),
	`BuyCount` tinyint (3),
	`BuyPrice` bigint (20),
	`SellPrice` int (10),
	`InventoryType` tinyint (3),
	`AllowableClass` int (11),
	`AllowableRace` int (11),
	`ItemLevel` smallint (5),
	`RequiredLevel` tinyint (3),
	`RequiredSkill` smallint (5),
	`RequiredSkillRank` smallint (5),
	`requiredspell` mediumint (8),
	`requiredhonorrank` mediumint (8),
	`RequiredCityRank` mediumint (8),
	`RequiredReputationFaction` smallint (5),
	`RequiredReputationRank` smallint (5),
	`maxcount` int (11),
	`stackable` int (11),
	`ContainerSlots` tinyint (3),
	`StatsCount` tinyint (3),
	`stat_type1` tinyint (3),
	`stat_value1` bigint (6),
	`stat_type2` tinyint (3),
	`stat_value2` bigint (6),
	`stat_type3` tinyint (3),
	`stat_value3` bigint (6),
	`stat_type4` tinyint (3),
	`stat_value4` bigint (6),
	`stat_type5` tinyint (3),
	`stat_value5` bigint (6),
	`stat_type6` tinyint (3),
	`stat_value6` bigint (6),
	`stat_type7` tinyint (3),
	`stat_value7` bigint (6),
	`stat_type8` tinyint (3),
	`stat_value8` bigint (6),
	`stat_type9` tinyint (3),
	`stat_value9` bigint (6),
	`stat_type10` tinyint (3),
	`stat_value10` bigint (6),
	`ScalingStatDistribution` bigint (6),
	`ScalingStatValue` int (10),
	`dmg_min1` float ,
	`dmg_max1` float ,
	`dmg_type1` tinyint (3),
	`dmg_min2` float ,
	`dmg_max2` float ,
	`dmg_type2` tinyint (3),
	`armor` smallint (5),
	`holy_res` tinyint (3),
	`fire_res` tinyint (3),
	`nature_res` tinyint (3),
	`frost_res` tinyint (3),
	`shadow_res` tinyint (3),
	`arcane_res` tinyint (3),
	`delay` smallint (5),
	`ammo_type` tinyint (3),
	`RangedModRange` float ,
	`spellid_1` mediumint (8),
	`spelltrigger_1` tinyint (3),
	`spellcharges_1` smallint (6),
	`spellppmRate_1` float ,
	`spellcooldown_1` int (11),
	`spellcategory_1` smallint (5),
	`spellcategorycooldown_1` int (11),
	`spellid_2` mediumint (8),
	`spelltrigger_2` tinyint (3),
	`spellcharges_2` smallint (6),
	`spellppmRate_2` float ,
	`spellcooldown_2` int (11),
	`spellcategory_2` smallint (5),
	`spellcategorycooldown_2` int (11),
	`spellid_3` mediumint (8),
	`spelltrigger_3` tinyint (3),
	`spellcharges_3` smallint (6),
	`spellppmRate_3` float ,
	`spellcooldown_3` int (11),
	`spellcategory_3` smallint (5),
	`spellcategorycooldown_3` int (11),
	`spellid_4` mediumint (8),
	`spelltrigger_4` tinyint (3),
	`spellcharges_4` smallint (6),
	`spellppmRate_4` float ,
	`spellcooldown_4` int (11),
	`spellcategory_4` smallint (5),
	`spellcategorycooldown_4` int (11),
	`spellid_5` mediumint (8),
	`spelltrigger_5` tinyint (3),
	`spellcharges_5` smallint (6),
	`spellppmRate_5` float ,
	`spellcooldown_5` int (11),
	`spellcategory_5` smallint (5),
	`spellcategorycooldown_5` int (11),
	`bonding` tinyint (3),
	`description` varchar (765),
	`PageText` mediumint (8),
	`LanguageID` tinyint (3),
	`PageMaterial` tinyint (3),
	`startquest` mediumint (8),
	`lockid` mediumint (8),
	`Material` tinyint (4),
	`sheath` tinyint (3),
	`RandomProperty` mediumint (8),
	`RandomSuffix` mediumint (8),
	`block` mediumint (8),
	`itemset` mediumint (8),
	`MaxDurability` smallint (5),
	`area` mediumint (8),
	`Map` smallint (6),
	`BagFamily` mediumint (8),
	`TotemCategory` mediumint (8),
	`socketColor_1` tinyint (4),
	`socketContent_1` mediumint (8),
	`socketColor_2` tinyint (4),
	`socketContent_2` mediumint (8),
	`socketColor_3` tinyint (4),
	`socketContent_3` mediumint (8),
	`socketBonus` mediumint (8),
	`GemProperties` mediumint (8),
	`RequiredDisenchantSkill` smallint (6),
	`ArmorDamageModifier` float ,
	`duration` int (10),
	`ItemLimitCategory` smallint (6),
	`HolidayId` int (11),
	`ScriptName` varchar (192),
	`DisenchantID` mediumint (8),
	`FoodType` tinyint (3),
	`minMoneyLoot` int (10),
	`maxMoneyLoot` int (10),
	`flagsCustom` int (10),
	`VerifiedBuild` smallint (5)
); 
insert into `item_template` (`entry`, `class`, `subclass`, `SoundOverrideSubclass`, `name`, `displayid`, `Quality`, `Flags`, `FlagsExtra`, `BuyCount`, `BuyPrice`, `SellPrice`, `InventoryType`, `AllowableClass`, `AllowableRace`, `ItemLevel`, `RequiredLevel`, `RequiredSkill`, `RequiredSkillRank`, `requiredspell`, `requiredhonorrank`, `RequiredCityRank`, `RequiredReputationFaction`, `RequiredReputationRank`, `maxcount`, `stackable`, `ContainerSlots`, `StatsCount`, `stat_type1`, `stat_value1`, `stat_type2`, `stat_value2`, `stat_type3`, `stat_value3`, `stat_type4`, `stat_value4`, `stat_type5`, `stat_value5`, `stat_type6`, `stat_value6`, `stat_type7`, `stat_value7`, `stat_type8`, `stat_value8`, `stat_type9`, `stat_value9`, `stat_type10`, `stat_value10`, `ScalingStatDistribution`, `ScalingStatValue`, `dmg_min1`, `dmg_max1`, `dmg_type1`, `dmg_min2`, `dmg_max2`, `dmg_type2`, `armor`, `holy_res`, `fire_res`, `nature_res`, `frost_res`, `shadow_res`, `arcane_res`, `delay`, `ammo_type`, `RangedModRange`, `spellid_1`, `spelltrigger_1`, `spellcharges_1`, `spellppmRate_1`, `spellcooldown_1`, `spellcategory_1`, `spellcategorycooldown_1`, `spellid_2`, `spelltrigger_2`, `spellcharges_2`, `spellppmRate_2`, `spellcooldown_2`, `spellcategory_2`, `spellcategorycooldown_2`, `spellid_3`, `spelltrigger_3`, `spellcharges_3`, `spellppmRate_3`, `spellcooldown_3`, `spellcategory_3`, `spellcategorycooldown_3`, `spellid_4`, `spelltrigger_4`, `spellcharges_4`, `spellppmRate_4`, `spellcooldown_4`, `spellcategory_4`, `spellcategorycooldown_4`, `spellid_5`, `spelltrigger_5`, `spellcharges_5`, `spellppmRate_5`, `spellcooldown_5`, `spellcategory_5`, `spellcategorycooldown_5`, `bonding`, `description`, `PageText`, `LanguageID`, `PageMaterial`, `startquest`, `lockid`, `Material`, `sheath`, `RandomProperty`, `RandomSuffix`, `block`, `itemset`, `MaxDurability`, `area`, `Map`, `BagFamily`, `TotemCategory`, `socketColor_1`, `socketContent_1`, `socketColor_2`, `socketContent_2`, `socketColor_3`, `socketContent_3`, `socketBonus`, `GemProperties`, `RequiredDisenchantSkill`, `ArmorDamageModifier`, `duration`, `ItemLimitCategory`, `HolidayId`, `ScriptName`, `DisenchantID`, `FoodType`, `minMoneyLoot`, `maxMoneyLoot`, `flagsCustom`, `VerifiedBuild`) values('600003','0','0','-1','Donation Token','55773','6','134217760','0','1','0','0','0','-1','-1','0','0','0','0','0','0','0','0','0','0','1000','0','10','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','1','The Donation Token is a premium currency used for various items such as |cff00ddddgear, transmog, mounts, pets or to renew your vip subscription|r.','0','0','0','0','0','2','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','-1','0','0','0','0','','0','0','0','0','0','12340');
insert into `item_template` (`entry`, `class`, `subclass`, `SoundOverrideSubclass`, `name`, `displayid`, `Quality`, `Flags`, `FlagsExtra`, `BuyCount`, `BuyPrice`, `SellPrice`, `InventoryType`, `AllowableClass`, `AllowableRace`, `ItemLevel`, `RequiredLevel`, `RequiredSkill`, `RequiredSkillRank`, `requiredspell`, `requiredhonorrank`, `RequiredCityRank`, `RequiredReputationFaction`, `RequiredReputationRank`, `maxcount`, `stackable`, `ContainerSlots`, `StatsCount`, `stat_type1`, `stat_value1`, `stat_type2`, `stat_value2`, `stat_type3`, `stat_value3`, `stat_type4`, `stat_value4`, `stat_type5`, `stat_value5`, `stat_type6`, `stat_value6`, `stat_type7`, `stat_value7`, `stat_type8`, `stat_value8`, `stat_type9`, `stat_value9`, `stat_type10`, `stat_value10`, `ScalingStatDistribution`, `ScalingStatValue`, `dmg_min1`, `dmg_max1`, `dmg_type1`, `dmg_min2`, `dmg_max2`, `dmg_type2`, `armor`, `holy_res`, `fire_res`, `nature_res`, `frost_res`, `shadow_res`, `arcane_res`, `delay`, `ammo_type`, `RangedModRange`, `spellid_1`, `spelltrigger_1`, `spellcharges_1`, `spellppmRate_1`, `spellcooldown_1`, `spellcategory_1`, `spellcategorycooldown_1`, `spellid_2`, `spelltrigger_2`, `spellcharges_2`, `spellppmRate_2`, `spellcooldown_2`, `spellcategory_2`, `spellcategorycooldown_2`, `spellid_3`, `spelltrigger_3`, `spellcharges_3`, `spellppmRate_3`, `spellcooldown_3`, `spellcategory_3`, `spellcategorycooldown_3`, `spellid_4`, `spelltrigger_4`, `spellcharges_4`, `spellppmRate_4`, `spellcooldown_4`, `spellcategory_4`, `spellcategorycooldown_4`, `spellid_5`, `spelltrigger_5`, `spellcharges_5`, `spellppmRate_5`, `spellcooldown_5`, `spellcategory_5`, `spellcategorycooldown_5`, `bonding`, `description`, `PageText`, `LanguageID`, `PageMaterial`, `startquest`, `lockid`, `Material`, `sheath`, `RandomProperty`, `RandomSuffix`, `block`, `itemset`, `MaxDurability`, `area`, `Map`, `BagFamily`, `TotemCategory`, `socketColor_1`, `socketContent_1`, `socketColor_2`, `socketContent_2`, `socketColor_3`, `socketContent_3`, `socketBonus`, `GemProperties`, `RequiredDisenchantSkill`, `ArmorDamageModifier`, `duration`, `ItemLimitCategory`, `HolidayId`, `ScriptName`, `DisenchantID`, `FoodType`, `minMoneyLoot`, `maxMoneyLoot`, `flagsCustom`, `VerifiedBuild`) values('600004','0','0','-1','Vote Token','55773','6','134217760','0','1','0','0','0','-1','-1','0','0','0','0','0','0','0','0','0','0','1000','0','10','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','0','0','0','0','-1','0','0','1','The Vote Token is a premium currency used for various items such as |cff00ddddgear, transmog, mounts, pets or to renew your vip subscription|r.','0','0','0','0','0','2','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','0','-1','0','0','0','0','','0','0','0','0','0','12340');
