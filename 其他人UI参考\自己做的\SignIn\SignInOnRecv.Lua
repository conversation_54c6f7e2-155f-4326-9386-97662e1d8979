SignInRewShowDate = {}
SignInContinuousRewDate = {}
SignInTotalRewDate = {}
SignInVipExRewDate = {}
SignInRepairReqDate = {}

PlayerSignInDate = {}

function SignInSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

local function SignInEvent(self, event, h, msg, classtype, sender)
	if event == "CHAT_MSG_ADDON" then
	
		if h == "SMSG_SI_DAY_DATE" then	
			local list = SignInSplit(msg,"#")
			local day = tonumber(list[1])
			if day <= RewShow_NumDays then
				if SignInRewShowDate[day] == nil then
					SignInRewShowDate[day] = {}
				end
				local val = tonumber(list[2])
				local str = list[3]
				SignInRewShowDate[day][val] = str
			end
		end
		
		if h == "SMSG_SI_CONTINUOUSDAY_DATE" then
			local list = SignInSplit(msg,"#")
			local day = tonumber(list[1])
			local val = tonumber(list[2])
			local str = list[3]
			if SignInContinuousRewDate[day] == nil then
				SignInContinuousRewDate[day] = {}
			end
			SignInContinuousRewDate[day][val] = str
		end
		
		if h == "SSMSG_SI_TOTALDAY_DATE" then
			local list = SignInSplit(msg,"#")
			local day = tonumber(list[1])
			local val = tonumber(list[2])
			local str = list[3]
			if SignInTotalRewDate[day] == nil then
				SignInTotalRewDate[day] = {}
			end
			SignInTotalRewDate[day][val] = str
		end
		
		if h == "SMSG_SI_VIPEX_DATE" then
		print("msg = "..msg)
			local list = SignInSplit(msg,"#")
			local day = tonumber(list[1])
			local val = tonumber(list[2])
			local str = list[3]
			if SignInVipExRewDate[day] == nil then
				SignInVipExRewDate[day] = {}
			end
			SignInVipExRewDate[day][val] = str
		end
		
		if h == "SMSG_SI_PLAYERDAY_DATE" then
			print("SMSG_SI_PLAYERDAY_DATE = "..msg)
			local list = SignInSplit(msg,"#")
			local day = tonumber(list[1])
			local IsOk = tonumber(list[2])
			PlayerSignInDate[day] = IsOk
		end
		
		if h == "SMSG_SI_PLAYECONTDAY_DATE" then
			local ContDay = tonumber(msg)
			PlayerSignInDate.ContDay = ContDay
		end
		
		if h == "SMSG_SI_PLAYETOTALDAY_DATE" then
			local TotalDay = tonumber(msg)
			PlayerSignInDate.TotalDay = TotalDay
		end
		
		if h == "SMSG_SI_PLAYEVIPDAY_DATE" then
			-- print("msg = "..msg)
			local VIPVal = tonumber(msg)
			PlayerSignInDate.VIPVal = VIPVal
		end
		
		if h == "SMSG_SI_PLAYEREPAIRDAY_DATE" then
			local RepairDayCount = tonumber(msg)
			PlayerSignInDate.RepairDayCount = RepairDayCount
		end
		
		if h == "SMSG_SI_READY_DAY" then
			local day = tonumber(msg)
			SignInReady(day)
		end
		
		if h == "SMSG_SI_CHANGE_TEXT" then
			if msg == "PLAYER" then
			SetPlayerSignInText()
			end
		end
	end
end

local function SignInSendReq()
SendAddonMessage("CSSI","REQALLDATA","GUILD", UnitName("player"))
end


local SignInReceiveMsg = CreateFrame("Frame")
SignInReceiveMsg:RegisterEvent("CHAT_MSG_ADDON")
SignInReceiveMsg:SetScript("OnEvent", SignInEvent)

local SignInLoad = CreateFrame("Frame")
SignInLoad:RegisterEvent("PLAYER_LOGIN")
SignInLoad:SetScript("OnEvent", SignInSendReq)