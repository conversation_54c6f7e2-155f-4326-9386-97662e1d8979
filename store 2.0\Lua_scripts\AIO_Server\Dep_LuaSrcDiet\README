
  LuaSrcDiet
  Compresses Lua source code by removing unnecessary characters.

  Copyright (c) 2005-2008 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  The COPYRIGHT file describes the conditions
  under which this software may be distributed.

  http://luaforge.net/projects/luasrcdiet/
  http://luasrcdiet.luaforge.net/

--

For the older unmaintained version of LuaSrcDiet for Lua 5.0.2 sources,
please see the 5.0/README file.

--

PREVIEW NOTES

See also: http://luasrcdiet.luaforge.net/

The 0.11.0 release of LuaSrcDiet has a local variable name optimizer.
Local variable names are renamed into the shortest possible names. In
addition, variable names are reused whenever possible, reducing the
number of unique variable names. Several hundred local variable names
can be reduced into 53 or less unique names, which allows all locals
to be single-character in length.

The local variable name optimizer uses a full parser of Lua 5.1 source
code, thus it can rename all local variables, including function
parameters. It should handle the implicit "self" parameter gracefully.
The optimizer needs more testing, but is already able to optimize the
LuaSrcDiet sources itself and generate correct Lua output.

String and number token optimizations are also performed, apart from the
usual whitespace, line ending and comment removal. Numbers can switch
between different formats. Strings can be simplified and can switch
delimiters between " or ' characters.

Most options can also be enabled or disabled separately, for maximum
flexibility. If you need to keep a copyright message in the optimized
output, the --keep option can keep block comments that contain a certain
string.

For samples, see the sample/ directory. Performance statistics can be
found in the sample/statistics.txt file. Preliminary test samples for
strings and numbers can also be found in the sample/ directory.

Priority for future work:
(a) automatic tests for lexer/parser optimizations
(b) integrity checking, token stream check or binary chunk check

--

INTRODUCTION

...

WARNING! Locals optimization does NOT have support for 'arg' vararg
functions (LUA_COMPAT_VARARG).

--

WHAT'S NEW

Major changes for version 0.11.2 (see the ChangeLog as well):
* improved local variable name allocation, more efficient now
* added experimental --plugin option with an example plugin script
* added a SLOC plugin to count SLOC for Lua 5.1 source files
* added a HTML plugin to see globals and locals marked

Major changes for version 0.11.1 (see the ChangeLog as well):
* --detail option for more string, number and local variable info
* fixed a local rename bug that generates names that are keywords
* added explanatory notes on local variable optimization
* added --opt-entropy option for locals to reduce symbol entropy

Major changes for version 0.11.0 (see the ChangeLog as well):
* Local variable name optimization.
* Many options and sample output added.

Major changes for version 0.10.2 (see the ChangeLog as well):
* Aggressive optimizations for string and number tokens.
* Minor bug fixes.

Major changes for version 0.10.1 (see the ChangeLog as well):
* Totally rewritten for Lua 5.1.x.

--

USAGE OPTIONS

...

Example of summary data display:

Statistics for: LuaSrcDiet.lua -> sample/LuaSrcDiet.lua

*** local variable optimization summary ***
----------------------------------------------------------
Variable          Unique   Decl.   Token    Size   Average
Types              Names   Count   Count   Bytes     Bytes
----------------------------------------------------------
Global                10       0      19      95      5.00
----------------------------------------------------------
Local (in)            88     153     683    3340      4.89
TOTAL (in)            98     153     702    3435      4.89
----------------------------------------------------------
Local (out)           32     153     683     683      1.00
TOTAL (out)           42     153     702     778      1.11
----------------------------------------------------------

*** lexer-based optimizations summary ***
--------------------------------------------------------------------
Lexical            Input   Input     Input  Output  Output    Output
Elements           Count   Bytes   Average   Count   Bytes   Average
--------------------------------------------------------------------
TK_KEYWORD           374    1531      4.09     374    1531      4.09
TK_NAME              795    3963      4.98     795    1306      1.64
TK_NUMBER             54      59      1.09      54      59      1.09
TK_STRING            152    1725     11.35     152    1717     11.30
TK_LSTRING             7    1976    282.29       7    1976    282.29
TK_OP                997    1092      1.10     997    1092      1.10
TK_EOS                 1       0      0.00       1       0      0.00
--------------------------------------------------------------------
TK_COMMENT           140    6884     49.17       1      18     18.00
TK_LCOMMENT            7    1723    246.14       0       0      0.00
TK_EOL               543     543      1.00     197     197      1.00
TK_SPACE            1270    2465      1.94     263     263      1.00
--------------------------------------------------------------------
Total Elements      4340   21961      5.06    2841    8159      2.87
--------------------------------------------------------------------
Total Tokens        2380   10346      4.35    2380    7681      3.23
--------------------------------------------------------------------

--

USING LUASRCDIET

...

Please see the command line help or see sample/Makefile for examples.

This is experimental software and nothing has been done yet on a proper
installation scheme for use with normal work. A thousand apologies...

--

CODE SIZE REDUCTION

...

--

OTHER OPTIONS

...

--

BEHAVIOUR NOTES

* embedded line endings in strings and long strings always
  normalized to LF
* will not optimize trailing spaces in long strings, only warns
* scientific notation generated in number optimzation is not in
  canonical form, this may or may not be a bad thing, so feedback
  is welcome

--

ACKNOWLEDGEMENTS

Thanks to the LuaForge people for hosting this.
Developed on SciTE http://www.scintilla.org/. Two thumbs up.

--

FEEDBACK

Feedback and contributions are welcome. Your name will be acknowledged,
as long as you are willing to comply with COPYRIGHT. If your material is
self-contained, you can retain a copyright notice for those material in
your own name, as long as you use the same Lua 5/MIT-style copyright.

My alternative e-mail address is: keinhong AT gmail DOT com

Enjoy!!

Kein-Hong Man (esq.)
Kuala Lumpur
Malaysia 20080603
