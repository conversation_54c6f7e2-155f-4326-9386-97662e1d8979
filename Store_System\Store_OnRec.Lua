local Service_Buttons = {}
local DropDown_Buttons = {}
local Navigation_Buttons = {}
local Page_Buttons = {}
local Frame_Names = {}
local NavFrames = {}
local Account_Rank_Check;

local function OnRecvNavFrames(msg)
    table.insert(NavFrames, msg)
end

local function OnRecvFrameNames(msg)    
    local Name, Size, Coords_X, Coords_Y, Bg_File, ParentFrame, Frame_Title = strsplit(",", msg)
    table.insert(Frame_Names, {Name, tonumber(Size), tonumber(Coords_X),tonumber(Coords_Y), Bg_File, ParentFrame, Frame_Title})
end

local function OnRecvPageButtons(msg)
    local Page_ID, Hide_Page, Show_Page, Page_Number, Page_Total, Previous_Button, Previous_Frame = strsplit(",", msg)
    table.insert(
        Page_Buttons,
        {Page_ID, Hide_Page, Show_Page, tonumber(Page_Number), tonumber(Page_Total), tonumber(<PERSON>_Button), Previous_Frame}
    )
end

local function OnRecvNavigationButtons(msg)
    local ID, Name, Texture, Coord_Y, FrameShow, Account_Rank = strsplit(",", msg)
    table.insert(Navigation_Buttons, {tonumber(ID), Name, Texture, tonumber(Coord_Y), FrameShow, tonumber(Account_Rank)})
end

local function OnRecvDropDownButtons(msg)
    local DropDown_Name, ParentFrame = strsplit(",", msg)
    table.insert(DropDown_Buttons, {DropDown_Name, ParentFrame})
end

local function OnRecvServiceButtons(msg)
    local Box_Name,
        Box_Coord_Y,
        Box_Coord_X,
        InsideBox_Name,
        Tooltip_Name,
        Tooltip_Type,
        Tooltip_Text,
        ParentFrame,
        ICON,
        Service_Price,
        Service_Function,
        HyperLink_ID,
        Service_ID,
        Model_Type,
        Creature_DisplayID,
        Currency_Type,
        Discount_Price,
        Discount_Percent,
        Discount_Color,
        Flags,
        Confirmation_ID = strsplit(",", msg);
    table.insert(
        Service_Buttons,
        {
            Box_Name,
            tonumber(Box_Coord_Y),
            tonumber(Box_Coord_X),
            InsideBox_Name,
            Tooltip_Name,
            Tooltip_Type,
            Tooltip_Text,
            ParentFrame,
            ICON,
            tonumber(Service_Price),
            Service_Function,
            tonumber(HyperLink_ID),
            tonumber(Service_ID),
            tonumber(Model_Type),
            tonumber(Creature_DisplayID),
            Currency_Type,
            tonumber(Discount_Price),
            tonumber(Discount_Percent),
            Discount_Color,
            tonumber(Flags),
            tonumber(Confirmation_ID)
        }
    )
end

function StoreOnDataRecv(self, event, opcode, msg, type, sender)
    if event == "CHAT_MSG_ADDON" then
        if opcode == "STORE_NavFrames_Data" then
            OnRecvNavFrames(msg);
        elseif opcode == "STORE_Frame_Names_Data" then
            OnRecvFrameNames(msg);
        elseif opcode == "STORE_Page_Buttons_Data" then
            OnRecvPageButtons(msg);
        elseif opcode == "STORE_Navigation_Buttons_Data" then
            OnRecvNavigationButtons(msg);
        elseif opcode == "STORE_DropDown_Buttons_Data" then
            OnRecvDropDownButtons(msg);
        elseif opcode == "STORE_Service_Buttons_Data" then
            OnRecvServiceButtons(msg);
        elseif opcode == "STORE_Account_Rank_Data" then
            Account_Rank_Check = tonumber(msg);
        end
    end
end

function GetStoreServiceButtons()
    return Service_Buttons
end

function GetStoreDropDownButtons()
    return DropDown_Buttons
end

function GetStoreNavigationButtons()
    return Navigation_Buttons
end

function GetStorePageButtons()
    return Page_Buttons
end

function GetStoreFrameNames()
    return Frame_Names
end

function GetStoreNavFrames()
    return NavFrames
end

function GetAccountRank()
    return Account_Rank_Check;
end

local STORE_RECV_FRAME = CreateFrame("Frame")
STORE_RECV_FRAME:RegisterEvent("CHAT_MSG_ADDON")
STORE_RECV_FRAME:SetScript("OnEvent", StoreOnDataRecv)

local STORE_LOAD_FRAME = CreateFrame("Frame")
STORE_LOAD_FRAME:RegisterEvent("PLAYER_LOGIN")
STORE_LOAD_FRAME:SetScript(
    "OnEvent",
    function(self, event, ...)
        SendAddonMessage("STORE_REQDATA", "", "GUILD")
    end
)