local AtlasDataTabs = 
{
	["_draft-border-h"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.00537109375, 0.0146484375, 0.0107421875, 0.029296875, false, false },
	["draft-border-tl"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.0166015625, 0.0263671875, 0.0107421875, 0.029296875, false, false },
	["!draft-border-v"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.0283203125, 0.0380859375, 0.0107421875, 0.029296875, false, false },
	["draft-cost-rune"] = { "Interface\\Draft\\DraftAtlas", 55, 53, 0.0048828125, 0.03173828, 0.033203125, 0.0849609375, false, false },
	["draft-name-highlight"] = { "Interface\\Draft\\DraftAtlas", 258, 64, 0.0400390625, 0.166015625, 0.009765625, 0.072265625, false, false },
	["draft-ring"] = { "Interface\\Draft\\DraftAtlas", 90, 90, 0.0048828125, 0.0439453125, 0.0869140625, 0.1650390625, false, false },
	["draft-rarity-1-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.53955078125, 0.669921875, 0.61328125, 0.990234375, false, false },
	["draft-rarity-2-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.0048828125, 0.135253906, 0.61328125, 0.990234375, false, false },
	["draft-rarity-3-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.13818359375, 0.2685546875, 0.61328125, 0.990234375, false, false },
	["draft-rarity-4-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.27294921875, 0.4033203125, 0.61328125, 0.990234375, false, false },
	["draft-rarity-5-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.40625, 0.53662109375, 0.61328125, 0.990234375, false, false },
	["draft-filigree"] = { "Interface\\Draft\\DraftAtlas", 50, 71, 0.07373046875, 0.09814453125, 0.0849609375, 0.154296875, false, false },
	["draft-card-back"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.73095703125, 0.9951171875, 0.232421875, 0.990234375, false, false },
	["draft-card-back-glow"] = { "Interface\\Draft\\DraftAtlas", 288, 401, 0.0048828125, 0.1455078125, 0.2109375, 0.6025390625, false, false },
}

function GhostGetSpellDescription(spellId)
	if not SpellDescriptionTooltip then
		CreateFrame("GameTooltip","SpellDescriptionTooltip",UIParent,"GameTooltipTemplate");
		SpellDescriptionTooltip:SetOwner(UIParent,"ANCHOR_NONE");
	end
	SpellDescriptionTooltip:ClearLines();
	SpellDescriptionTooltip:SetHyperlink("spell:"..spellId);
	local des = _G["SpellDescriptionTooltipTextLeft"..SpellDescriptionTooltip:NumLines()]:GetText();
	des = "|cFF00FF00"..des.."|r";	
	return des;
end

function Ghost_SendData( opcode, msg )
	local len = strlen(opcode); if len > 16 then print("opcode out len") return end;
	SendAddonMessage(opcode, msg, "GUILD")	--opcode 不能超过16个字符 即16个A
end

function GhostSubString(self, str)
	local num = 24
	local sNum = num or 1
    local tab = {}
    for uchar in string.gmatch(str, "[%z\1-\127\194-\244][\128-\191]*") do 
        tab[#tab+1] = uchar
    end
    local resultStr = ""
	local x = 1	
    for i=1,#tab do
		x = x + 1
		if x == num then
			resultStr = resultStr..tab[i]
			self:AddDoubleLine("|cFF00FF00"..resultStr.."|r")
            self.count = self.count + 1
			x = 1
			resultStr = ""
		else
			resultStr = resultStr..tab[i]
		end
    end
    self:AddDoubleLine("|cFF00FF00 "..resultStr.."|r")
    self.count = self.count + 1
end

function GhostGetSpellLink(Entry)
    return GetSpellLink(Entry);
end

function HexStrToDec(hexStr)  
    return tonumber(hexStr, 16)  
end 

function MixinAndLoadScripts(target, ...)
    for i = 1, select("#", ...) do
        local mixin = select(i, ...)
        if type(mixin) == "string" then
            mixin = _G[mixin]
        end

        assert(type(mixin) == "table", "Mixins must be a table or a string pointing to a global table. Got Name: "..(tostring(select(i, ...)) or "nil") .. " Type: " .. type(mixin))

        local hasScript = target.HasScript ~= nil
        for k, v in pairs(mixin) do
            target[k] = v
            if hasScript and target:HasScript(k) then
                target:SetScript(k, v)
            end
        end

        if mixin.OnLoad then
            mixin.OnLoad(target)
        end
    end

    return target
end

function MixinAndLoad(target, ...)
    -- 遍历所有提供的混入表
    for i = 1, select("#", ...) do
        -- 获取当前混入表
        local mixin = select(i, ...)
        -- 如果混入表是一个字符串，则尝试从全局变量中获取对应的表
        if type(mixin) == "string" then
            mixin = _G[mixin]
        end

        -- 确保混入表是一个表类型，如果不是，则抛出错误
        assert(type(mixin) == "table", "Mixins must be a table or a string pointing to a global table. Got Name: "..(tostring(select(i, ...)) or "nil") .. " Type: " .. type(mixin))

        -- 将混入表的属性和方法复制到目标对象
        for k, v in pairs(mixin) do
            target[k] = v
        end

        -- 如果混入表包含OnLoad方法，则调用它，传入目标对象作为参数
        if mixin.OnLoad then
            mixin.OnLoad(target)
        end
    end

    -- 返回被混入和初始化后的目标对象
    return target
end


--- 将一个数值限制在指定的最小值和最大值之间。
-- 此函数确保返回的值不会小于最小值也不会大于最大值。
-- 如果未提供最小值和最大值，则默认将值限制在0到1之间。
--
-- @param value 数值，需要被限制在最小值和最大值之间。
-- @param minValue 最小值，如果未提供，则默认为0。
-- @param maxValue 最大值，如果未提供，则默认为1。
-- @return 限制后的数值。
function clamp(value, minValue, maxValue)
    -- 如果未提供最小值，则将最小值设为0，最大值设为1。
    if not minValue then
        minValue = 0
        maxValue = 1
    end
    -- 确保返回的值不会小于最小值也不会大于最大值。
    return min(max(minValue, value), max(minValue, maxValue))
end


-- 定义一个线性插值函数
-- 该函数用于计算两个数值之间的线性插值结果
-- 参数:
--   startValue: 插值的起始值
--   endValue: 插值的结束值
--   a: 插值的因子，表示插值的程度，应在0到1之间
-- 返回值:
--   返回插值的结果如果起始值和结束值相等，则直接返回该值
function lerp(startValue, endValue, a)
    -- 当起始值与结束值相等时，直接返回起始值
    -- 这是因为在此情况下，无论插值因子a为何值，结果都不会改变
    if startValue == endValue then
        return startValue
    end

    -- 将插值因子a限制在0到1的范围内
    -- 这是为了确保插值结果位于起始值和结束值之间
    a = clamp(a, 0, 1)

    -- 计算并返回插值结果
    -- 该公式根据插值因子a，将结果从起始值平滑过渡到结束值
    return (startValue + (endValue - startValue) * a)
end

-- 定义EaseIn函数，用于实现逐渐进入的动画效果。
-- 该函数通过改变时间变量t的指数来调整动画的速度。
-- 参数t是动画的当前时间，通常在0到1之间，表示动画的进度。
-- 参数exp是指数，决定了动画速度变化的速率，默认值为2。
-- 返回值是调整后的动画进度。
function EaseIn(t, exp)
    exp = exp or 2 -- 如果未提供exp参数，则默认为2。
    return t ^ exp -- 返回t的exp次幂，实现逐渐进入的效果。
end

-- 定义EaseOut函数，用于实现逐渐退出的动画效果。
-- 类似于EaseIn，但动画效果是先快后慢。
-- 参数t和exp的意义与EaseIn中相同。
-- 返回值是调整后的动画进度。
function EaseOut(t, exp)
    exp = exp or 2 -- 如果未提供exp参数，则默认为2。
    return 1 - ((1 - t) ^ exp) -- 计算逐渐退出的动画效果。
end

-- 定义EaseInOut函数，结合EaseIn和EaseOut，实现先逐渐进入再逐渐退出的动画效果。
-- 该函数通过线性插值(lerp)结合EaseIn和EaseOut的结果，平滑地从逐渐进入过渡到逐渐退出。
-- 参数t和exp的意义与前两个函数中相同。
-- 返回值是调整后的动画进度，平滑地从0过渡到1。
function EaseInOut(t, exp)
    -- 使用lerp函数，根据t的值选择性地靠近EaseIn或EaseOut的结果。
    -- 这里没有直接提供lerp函数的实现，假设它已经定义在其他地方。
    return lerp(EaseIn(t, exp), EaseOut(t, exp), t)
end

function SetParentArray(frame, key, pos)
    if not frame or not frame.GetParent then return end
    
    local parent = frame:GetParent()
    if not parent then return end

    if not parent[key] then
        parent[key] = {}
    end

    if pos then
        tinsert(parent[key], pos, frame)
    else
        tinsert(parent[key], frame)
    end
    
    return parent[key]
end

function ClearAndSetPoint(target, ...)
    target:ClearAllPoints()
    target:SetPoint(...)
end

function SetTexCoordSize(target,str)
    local tab = AtlasDataTabs[str]
    target:SetTexture(tab[1])
    target:SetSize(tab[2],tab[3])
    target:SetTexCoord(tab[4],tab[5],tab[6],tab[7])
end

function Frozen_Split(S, R)
	local List = {};
	string.gsub(S, '[^' .. R .. ']+', function(F) table.insert(List, F) end);
	return List;
end

--使得1000-> 1,000
function Formatnumberthousands(num)
	local function checknumber(value)
		return tonumber(value) or 0
	end

	local formatted = tostring(checknumber(num))
	local k
	while true do
		formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
		if k == 0 then
			break
		end
	end
	return formatted
end

function Ghosthongse(s)
    local s = s.." %s";
    s = string.format(s,"[开始加属性]")
    return s 
end

function GhostStatShow(self, guid)
	for i = 1, self:NumLines() do
		local s = _G[self:GetName() .. "TextLeft" .. i]:GetText()
		local t, m
		if s then
			if string.find(s, "^%+%d.+%s爆击躲闪（远程）")  then Ghosthongse(s) 
			elseif string.find(s, "([%s%S]*)点护甲") then Ghosthongse(s) 
			elseif string.find(s, "^%+%d.+%s每5秒的生命值恢复")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s法力值")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s生命值")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s敏捷")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s力量")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s智力")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s精神")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s耐力")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s防御")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s躲闪")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s招架")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s格挡")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s命中（近战）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s命中（远程）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s命中（法术）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s爆击（近战）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s爆击（远程）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s爆击（法术）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s急速（近战）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s急速（远程）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s急速（法术）")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s命中")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s爆击")  and not string.find(s, "躲闪") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%sPvP韧性")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s急速")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s精准")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s远程攻击强度")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s攻击强度")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s法力回复")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s护甲穿透")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s法术强度")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s法术穿透")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s精通")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%sPvP强度")  then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s火焰抗性") and string.find(s, "火焰抗性") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s冰霜抗性") and string.find(s, "冰霜抗性") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s神圣抗性") and string.find(s, "神圣抗性") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s暗影抗性") and string.find(s, "暗影抗性") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s自然抗性") and string.find(s, "自然抗性") then Ghosthongse(s)
			elseif string.find(s, "^%+%d.+%s奥术抗性") and string.find(s, "奥术抗性") then Ghosthongse(s)
			elseif string.find(s, "（每秒伤害([%s%S]*)）") then
                Ghosthongse(s)
				-- m, _, _ = GetItemGUIDDMG(guid)
				-- if m then
				-- 	s = string.format("（每秒伤害%s）", Formatnumberthousands(string.format("%.2f", m)))
				-- end
			elseif string.find(s, "(点[神圣|火焰|冰霜|自然|暗影|奥术]*)伤害") and string.find(s, "-") then
				local shtype = "伤害";
				if string.find(s, "神圣伤害") then
					shtype = "神圣伤害"
				elseif string.find(s, "火焰伤害") then
					shtype = "火焰伤害"
				elseif string.find(s, "冰霜伤害") then
					shtype = "冰霜伤害"
				elseif string.find(s, "自然伤害") then
					shtype = "自然伤害"
				elseif string.find(s, "暗影伤害") then
					shtype = "暗影伤害"
				elseif string.find(s, "奥术伤害") then
					shtype = "奥术伤害"
				end
				-- local _, mind, maxd = GetItemGUIDDMG(guid)
				-- if mind then
				-- 	s = string.format("%s - %s点%s", Formatnumberthousands(mind), Formatnumberthousands(maxd), shtype)
				-- end
			end
			-- local R, G, B = _G[self:GetName() .. "TextLeft" .. i]:GetTextColor();
			-- print("i : " .. i .. "| s : " .. s .. "|")
			_G[self:GetName() .. "TextLeft" .. i]:SetText(s);
			-- _G[self:GetName() .. "TextLeft" .. i]:SetTextColor(R, G, B, 1)
		end
	end
end

function GhostGetItemLink(Entry)
	local itemName, itemLink, itemRarity, itemLevel, itemMinLevel, itemType, itemSubType, itemStackCount, itemEquipLoc, itemTexture, itemSellPrice = GetItemInfo(Entry);
	if not itemLink then
		GameTooltip:SetHyperlink("item:" .. Entry .. ":0:0:0:0:0:0:0");
	end

	return itemLink;
end

function GhostGetItemGuidIdByLink(ItemLink)
	local itemSplits={strsplit(":", ItemLink)};
	local guid=tonumber(itemSplits[9]) or 0;
	return guid;
end

function GhostGetItemIdByLink(ItemLink)
	local itemSplits={strsplit(":", ItemLink)};
	local itemid = tonumber(itemSplits[2]) or 0;
	return itemid;
end

function GhostPopback(str)
    if #str > 0 then
        return str:sub(1, -2)  -- 截取从第一个字符到倒数第二个字符
    else
        return str  -- 如果字符串为空，直接返回
    end
end

-- 使用gsub移除末尾的\n\r
function Ghostpop_back(str)
    return str:gsub("\n\r$", "")  
end

--返回给定数值除以10000并向下取整的结果
function GhostGetGoldNum(val)
	return floor(val / 10000)
end