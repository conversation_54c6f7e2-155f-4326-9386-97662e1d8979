local KuangTi = CreateFrame("Frame", "KuangTi", UIParent);
KuangTi:SetSize(832, 512);
KuangTi:RegisterForDrag("LeftButton");
KuangTi:SetPoint("CENTER");
KuangTi:SetToplevel(true);
KuangTi:SetClampedToScreen(true);

KuangTi.PortraitTex = KuangTi:CreateTexture("$parentPortraitTex","BACKGROUND")
KuangTi.PortraitTex:SetPoint("TOPLEFT",8,-7)
KuangTi.PortraitTex:SetSize(58, 58)
SetPortraitTexture(KuangTi.PortraitTex, "player")

KuangTi.TopLeft = KuangTi:CreateTexture("$parentTopLeft","ARTWORK")
KuangTi.TopLeft:SetPoint("TOPLEFT")
KuangTi.TopLeft:SetSize(256, 256)
KuangTi.TopLeft:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-TopLeft");

KuangTi.Top = KuangTi:CreateTexture("$parentTop","ARTWORK")
KuangTi.Top:SetPoint("TOPLEFT",256,0)
KuangTi.Top:SetSize(320, 256)
KuangTi.Top:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-Top");

KuangTi.TopRight = KuangTi:CreateTexture("$parentTopRight","ARTWORK")
KuangTi.TopRight:SetPoint("TOPLEFT","$parentTop","TOPRIGHT",0,0)
KuangTi.TopRight:SetSize(256, 256)
KuangTi.TopRight:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-TopRight");

KuangTi.BotLeft = KuangTi:CreateTexture("$parentBotLeft","ARTWORK")
KuangTi.BotLeft:SetPoint("TOPLEFT",0,-256)
KuangTi.BotLeft:SetSize(256, 256+50)
KuangTi.BotLeft:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-BotLeft");

KuangTi.Bot = KuangTi:CreateTexture("$parentBot","ARTWORK",nil,1)
KuangTi.Bot:SetPoint("TOPLEFT",19.5,-256)
KuangTi.Bot:SetSize(320+256-19, 256+50)
KuangTi.Bot:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-Bot");

KuangTi.BotRight = KuangTi:CreateTexture("$parentBotRight","ARTWORK")
KuangTi.BotRight:SetPoint("TOPLEFT","$parentBot","TOPRIGHT",0,0)
KuangTi.BotRight:SetSize(256, 256+50)
KuangTi.BotRight:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-BotRight");

KuangTi.TitleText = KuangTi:CreateFontString("TitleText");
KuangTi.TitleText:SetFont(Frozen_GameFont, 18);
KuangTi.TitleText:SetPoint("TOP", 0, -22);
KuangTi.TitleText:SetText("|cFF00FF00斗气属性|r");

KuangTi:Hide()

-- AuctionFrameBotRight:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-BotRight");