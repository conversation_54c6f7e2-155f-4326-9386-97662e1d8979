--此为创建滚动窗口模板

local SignInDayButton = {}

local SignInRewTex = {}

--背景图窗口
local SignInBackDropFrame = CreateFrame("Frame","SignInBackDropFrame",UIParent)
SignInBackDropFrame:SetSize(1024, 512)
SignInBackDropFrame:SetBackdrop({bgFile = "Interface\\icons\\background_01"})
-- SignInBackDropFrame:SetBackdropColor(0,0,0,0.5)
SignInBackDropFrame:SetPoint("CENTER",0,0)
SignInBackDropFrame:Hide()

--滚动窗口
local SignInScrollFrame = CreateFrame("ScrollFrame","SignInScrollFrame",SignInBackDropFrame)
-- SignInScrollFrame:SetFrameStrata("TOOLTIP")
SignInScrollFrame:SetSize(1024, 412)
SignInScrollFrame:SetPoint("CENTER",0,0)
SignInScrollFrame:Show()
SignInScrollFrame:EnableMouse(true)
SignInScrollFrame:EnableMouseWheel(true)

--滚动子窗口
local SignInScrollChildFrame = CreateFrame("Frame","SignInScrollChildFrame",SignInScrollFrame) 
-- SignInScrollChildFrame:SetFrameStrata("TOOLTIP")
SignInScrollChildFrame:SetSize(1024, 3000)
SignInScrollChildFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
SignInScrollChildFrame:SetPoint("CENTER",0,0)
SignInScrollChildFrame:SetBackdropColor(0,0,0,0.5)
SignInScrollChildFrame:Show()
SignInScrollFrame:SetScrollChild(SignInScrollChildFrame)

--滑块
local SignInSlider = CreateFrame("Slider","SignInSlider",SignInScrollFrame)
SignInSlider:SetOrientation("VERTICAL")
SignInSlider:SetMinMaxValues(0,100)
SignInSlider:SetValue(0)
SignInSlider:SetValueStep(1.0)
SignInSlider:SetPoint("LEFT",SignInScrollFrame,"RIGHT",0,0)
SignInSlider:SetSize(32,32)
SignInSlider:EnableMouse(true)
local SignInSliderTex = SignInSlider:CreateTexture("SignInSliderTex","HIGHLIGHT",nil)	--滑块图标
SignInSliderTex:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
SignInSliderTex:SetSize(32, 32)
local Slidertexs = SignInSlider:SetThumbTexture(SignInSliderTex)

--批量创建滚动子窗口内连续按钮
local function CreateSignInDayButton(i)
local b = CreateFrame("Button", "SignIn"..i.."Button", SignInScrollChildFrame, "UIPanelButtonTemplate")
b:SetSize(150, 30)
b:SetPoint("TOPLEFT", 15, -20 -(38 * (i-1)))	--竖向间隔 * 数量 + 40 = SignInScrollChildFrame:SetHeight(val)的val?? 也许是b:GetHeight() * 数量 + 间隔
b:EnableMouse(true)
local bstr = b:CreateFontString("$parenStr")
bstr:SetFont("Fonts\\ZYHei.ttf", 15)
b:SetFontString(bstr)
b:SetText("连签第"..i.."天")
b:Show()
return b
end

--批量创建滚动子窗口内连续贴图
local function CreateSignInRewTex(F,i)
local tex = F:CreateTexture("$paren".."Tex","ARTWORK")
tex:SetTexture("Interface\\Buttons\\UI-EmptySlot")
tex:SetSize(32, 32)
tex:SetPoint("TOPLEFT",180,-20-(38*(i-1)))
return tex
end


for i = 1, 78 do
SignInDayButton[i] = CreateSignInDayButton(i)
SignInRewTex[i] = CreateSignInRewTex(SignInScrollChildFrame,i)
end

--滑块注册滚动窗口移动
SignInSlider:SetScript("OnValueChanged",
function(self,offset) 
--子窗口的大小改变后 这里也需要改变 这里是设置滑动条偏移值
SignInScrollFrame:SetVerticalScroll(math.ceil((SignInScrollChildFrame:GetHeight()-SignInScrollFrame:GetHeight())/100 * self:GetValue()))
end)

--滚动窗口注册鼠标中键关联滑块
SignInScrollFrame:SetScript("OnMouseWheel", 
function(self,val)  
	--往上滑 val是正数	方向：上，左
	--往下滑 val是负数	方向：下，右
	local s1 = SignInSlider	-- 滚动条
	--获取最小最大值
	local minv,maxv = s1:GetMinMaxValues()
	--获取滚动条当前值
	local cv = s1:GetValue()
	--计算新数值
	local nv = cv - ( val * 5 )
	--新数值等于 nv 和 minv中最大的那个
	nv = max(nv, minv)
	--新数值等于 nv 和 maxv中最小的那个
	nv = min(nv, maxv)
	--如果新值 不等于 旧值 
	--设置滚动条的值为新值  -->设置滚动到哪个点(值)
	if ( nv ~= cv ) then
		s1:SetValue(nv);
	end		
end)