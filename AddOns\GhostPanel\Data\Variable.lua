Legacy = {
	Var = {
		Ptr = true,
		Nav = {
			Selected = {
				Page = 0,
				ClassSkill = 1,
				Transmog = -1,
				TransmogSlot = 0,
				SpellMod = 0,
			},
			Page = {
				Legacy = 0,
				TransmogCollection = 0,
				Market = {
					Item = 0,
					Spell = 0,
					Buff = 0,
				},
				Reputation = {
					Item = 0,
					Spell = 0,
					Supply = 0,
				},
				Reward = 
				{
					Item = 0,
					Key = 0,
				},
				ClassSkill = {
					Skill = 0,
					Memory = 0,
					Spell = 0,
				},
			},
		},
	},
	UI = {
		MainFrame = nil,
		UIFrame = nil,
		PrevFrame = nil,
		CurFrame = nil,
		SpellModFrame = nil,
		SpellMod = {},
		Home = {
			Background = nil,
			CharName = nil,
			Page = {},
			Nav = {},
		},
		Specialty = {
			Stat = nil,
			Armforce = nil,
			Instinct = nil,
			Willpower = nil,
			Sanity = nil,
			Pysche = nil,
			Trial = nil,
		},
		ClassSkill = {
			Stat = {
				Skill = nil,
				Memory = nil,
				Spell = nil,
			},
			Skill = {},
			Memory = {},
			Spell = {},
			Nav = {
				Skill = {},
				Memory = {},
				Spell = {},
			},
		},
		Rune = {},
		Guild = {
			RankIndicator = {},
			RankProgress = {},
			Bonus = {},
			BonusSelectionFrame = nil,
			BonusSelection = {},
		},
		Market = {
			Item = {},
			Spell = {},
			Buff = {},
			Nav = {
				Item = {},
				Spell = {},
				Buff = {},
			},
		},
		Transmog = {
			Frame = nil,
			SlotFrame = nil,
			CollFrame = nil,
			EquipSlot = {},
			Slot = {},
			Coll = {},
			Nav = {
				Coll = {},
			},
		},
		Reputation = {
			Item = {},
			Spell = {},
			Supply = {},
			Nav = {
				Item = {},
				Spell = {},
				Supply = {},
			},
		},
		Reward = {
			Key = {},
			Item = {},
			Nav = {
				Key = {},
				Item = {},
			},
		},
		Legacy = {
			Item = {},
			Nav = {},
		},
	},
	Data = {
		Env = {
			GoldRatio = 0,
			Legacy = {},
			Market = {
				Item = {},
				Spell = {},
				Buff = {},
			},
			Reputation = {
				Supply = {},
				Item = {},
				Spell = {},
			},
			ClassSkillBonus = {
				[1] = {},
				[2] = {},
				[3] = {},
				[4] = {},
				[5] = {},
				[6] = {},
				[7] = {},
				[8] = {},
				[9] = {},
				[10] = {},
				[11] = {},
				[12] = {},
				[13] = {},
				[14] = {},
				[15] = {},
				[16] = {},
				[17] = {},
				[18] = {},
				[19] = {},
				[20] = {},
				[21] = {},
				[22] = {},
				[23] = {},
				[24] = {},
				[25] = {},
				[26] = {},
				[27] = {},
				[28] = {},
				[29] = {},
				[30] = {},
			},
		},
		Account = {
			Currency = 0,
			Reputation = 0,
			Rank = 0,
		},
		Guild = {
			Rank = 0,
			XP = 0,
			XPToNextLevel = 0,
			Buff = {},
		},
		Character = {
			Specialty = {
				Point = 0,
				Available = 0,
				Stat = {
					[LEGACY_SPECIALTY_ARMFORCE] = 0,
					[LEGACY_SPECIALTY_INSTINCT] = 0,
					[LEGACY_SPECIALTY_WILLPOWER] = 0,
					[LEGACY_SPECIALTY_SANITY] = 0,
					[LEGACY_SPECIALTY_PYSCHE] = 0,
					[LEGACY_SPECIALTY_TRIAL] = 0,
				},
			},
			ClassSkill = {
				Point = 0,
				Available = 0,
				Ranking = {},
			},
			Memory = {
				Point = 0,
				Available = 0,
				Cost = {
					Mod = 0,
					Slot = 0,
					Spell = 0,
				},
			},
			Chrono = {
				Cap = 0,
				Available = 0,
				ResetCost = {
					ClassSkill = 0,
					Spell = 0,
				}
			},
			Spell = {
				Memorized = {},
				Activated = {},
				Mod = {},
				MaxSlot = 0,
			},
			Rune = {},
			Market = {
				Item = {},
				Spell = {},
				Buff = {},
			},
			Reputation = {
				Spell = {},
			},
			Reward = {
				Count = 0,
				Item = {},
				Key = {},
			},
		},
		Transmog = {
			EquipSlot = {},
			Slot = {},
			Collection = {},
		},
	},
};

FRESH_RUN = true;


