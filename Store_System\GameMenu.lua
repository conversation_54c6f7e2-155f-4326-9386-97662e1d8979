local StoreGameMenu = CreateFrame("Button", "StoreGameMenu", GameMenuFrame)
StoreGameMenu:SetPoint("CENTER", GameMenuFrame, 0, 95)
StoreGameMenu:SetSize(188, 30)
StoreGameMenu:SetNormalTexture("Interface/Store_System/UI/Buttons/GameMenu/GameMenuButton")
StoreGameMenu:SetHighlightTexture("Interface/Store_System/UI/Buttons/GameMenu/GameMenuButtonHighLight")
StoreGameMenu:SetPushedTexture("Interface/Store_System/UI/Buttons/GameMenu/GameMenuButtonPushed")

StoreGameMenu:SetScript(
      "OnClick",
      function()
            HideUIPanel(GameMenuFrame)
            StoreFramesUI()
            StoreShowStore()
            StoreNavigationUI()
            StorePageButton()
            StoreDropDownButton()
            StoreServicesUI()
      end
)

StoreGameMenuName = StoreGameMenu:CreateFontString("StoreGameMenuName", StoreGameMenu)
StoreGameMenuName:SetFont("Fonts\\FRIZQT__.TTF", 13, "OUTLINE")
StoreGameMenuName:SetShadowOffset(1, -1)
StoreGameMenuName:SetPoint("CENTER", StoreGameMenu, "CENTER", 0, 2)
StoreGameMenuName:SetText("|cffdbe005Store")
