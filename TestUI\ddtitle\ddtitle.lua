
function NameEvent(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" then
	
		if h == "DDTITLE" and msg == "SHOW" then
			
			SetCVar("UnitNameOwn", 0)
			SetCVar("UnitNameOwn", 1)
			SetCVar("UnitNameFriendlyPlayerName", 0)
			SetCVar("UnitNameFriendlyPlayerName", 1)
			SetCVar("UnitNameEnemyPlayerName", 0)
			SetCVar("UnitNameEnemyPlayerName", 1)
		
		end
	end
end

local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("CHAT_MSG_ADDON")
MsgReceivers:SetScript("OnEvent", NameEvent)