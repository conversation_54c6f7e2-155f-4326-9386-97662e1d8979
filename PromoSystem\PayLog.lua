PayLog = {}
PayLog.width=800;
PayLog.height=400;
PayLog.paihang={};

local totalPayPage = 1
local nowPayPage = 1
local PayData = {}
local TotalPay = 0
local synchrotimer = 20;

local gradientColors = {
    {1, 0, 0},     -- 红色
    {1, 0.5, 0},   -- 橙色
    {1, 1, 0},     -- 黄色
    {0, 1, 0},     -- 绿色
    {0, 0.7, 1},   -- 蓝色
    {0.5, 0, 1},   -- 紫色
	{1,1,1},	   -- 白色
}

local GetSpellIcon = function(index) 
	local _, _, icon, _, _, _ = GetSpellInfo(index);
	return icon;
end;

local function countTableElements(tbl)
    local count = 0
    for _, _ in pairs(tbl) do
        count = count + 1
    end
    return count
end

function PayLog:CreateCell(index,x,y,colorstr,iconstr,namestr,dmgstr,rewardstr)

	local menuView=CreateFrame("Frame",nil,PayLog.mainView) 
	menuView:SetFrameStrata("TOOLTIP");
	menuView:SetWidth(PayLog.width);
	menuView:SetHeight(24);
	menuView:SetPoint("TOPLEFT",PayLog.mainView,'TOPLEFT',x,y);
	menuView:Show();
	
	
	menuView.keytext=menuView:CreateFontString("menuView.keytext","OVERLAY","GameFontNormal") 
	menuView.keytext:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.keytext:SetPoint("TOPLEFT",menuView,"TOPLEFT",0,-2);
	menuView.keytext:SetWidth(30);
	menuView.keytext:SetHeight(20);
	menuView.keytext:SetText("1");
	menuView.keytext:SetTextColor(unpack(gradientColors[3]))
	menuView.keytext:SetShadowOffset(1, -2)
	menuView.keytext:SetShadowColor(0, 0, 0)
	
	
	menuView.iconView=CreateFrame('Button',nil,menuView,'')
	menuView.iconView:SetPoint('TOPLEFT',menuView,'TOPLEFT',20+10,-4);
	menuView.iconView:SetButtonState('NORMAL');
	menuView.iconView:IsEnabled();
	menuView.iconView:SetNormalTexture(iconstr);
	menuView.iconView:SetDisabledTexture(iconstr);
	menuView.iconView:SetHighlightTexture(iconstr);
	menuView.iconView:SetPushedTexture(iconstr);
	menuView.iconView:SetWidth(24);
	menuView.iconView:SetHeight(24);
	
	
	menuView.datatext=menuView:CreateFontString("menuView.datatext","OVERLAY","GameFontNormal")
	menuView.datatext:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.datatext:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2+170,-2);
	menuView.datatext:SetWidth(PayLog.width/3);
	menuView.datatext:SetHeight(20);
	menuView.datatext:SetText("2023-06-12 08:08:08");
	menuView.datatext:SetTextColor(unpack(gradientColors[3]))
	menuView.datatext:SetShadowOffset(1, -2)
	menuView.datatext:SetShadowColor(0, 0, 0)
	
	
	menuView.payvalstr=menuView:CreateFontString("menuView.payvalstr","OVERLAY","GameFontNormal")
	menuView.payvalstr:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.payvalstr:SetPoint("TOPRIGHT",menuView,"TOPRIGHT",0,-2);
	menuView.payvalstr:SetWidth(PayLog.width/3);
	menuView.payvalstr:SetHeight(20);
	menuView.payvalstr:SetText("999");
	menuView.payvalstr:SetTextColor(unpack(gradientColors[3]))
	menuView.payvalstr:SetShadowOffset(1, -2)
	menuView.payvalstr:SetShadowColor(0, 0, 0)

	return menuView;
end;


PayLog.mainView=CreateFrame("Frame",nil,UIParent)
PayLog.mainView:SetFrameStrata("TOOLTIP");
PayLog.mainView:SetBackdrop({bgFile="Interface\\Tooltips\\UI-Tooltip-Background",edgeFile="Interface\\Tooltips\\UI-Tooltip-Border",edgeSize="16",tile=true});
PayLog.mainView:SetBackdropColor(0,0,0,0.5);
PayLog.mainView:SetWidth(PayLog.width);
PayLog.mainView:SetHeight(PayLog.height);
PayLog.mainView:SetPoint("CENTER",0,0);
PayLog.mainView:SetMovable(1);
PayLog.mainView:EnableMouse();
PayLog.mainView:Hide();

PayLog.CloseButton = CreateFrame('Button', nil, PayLog.mainView, 'UIPanelButtonTemplate') 
PayLog.CloseButton:SetPoint('BOTTOMRIGHT', PayLog.mainView, 'TOPRIGHT', 0, -30);
PayLog.CloseButton:SetSize(80, 30);
PayLog.CloseButton:SetText("关闭");
PayLog.CloseButton:SetScript('OnClick', function() PayLog.mainView:Hide(); end);

PayLog.title = PayLog.mainView:CreateFontString("title", "OVERLAY", "GameFontNormal") 
-- PayLog.title:SetFont("Fonts\\ZYKai_C.ttf", 35);
PayLog.title:SetFont("Fonts\\ZYKai_C.ttf", 40, "OUTLINE")
PayLog.title:SetText("充值记录明细");
PayLog.title:SetPoint("TOPLEFT", PayLog.mainView, "TOPLEFT", 310, -10);
PayLog.title:SetHeight(20);

PayLog.lineFrame = CreateFrame("Frame",nil,PayLog.mainView) 
PayLog.lineFrame:SetFrameStrata("TOOLTIP");
PayLog.lineFrame:SetBackdrop ({bgFile="Interface\\TutorialFrame\\TutorialFrameBackground"});
PayLog.lineFrame:SetBackdropColor(1, 1, 1, 1);
PayLog.lineFrame:SetWidth(PayLog.width-8);
PayLog.lineFrame:SetHeight(2);
PayLog.lineFrame:SetPoint("TOPLEFT",4,-10-20-10-20-15);

PayLog.totalpaystr = PayLog.mainView:CreateFontString("totalpaystr", "OVERLAY", "GameFontNormal") 
PayLog.totalpaystr:SetText("累计充值：999999999");
PayLog.totalpaystr:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PayLog.totalpaystr:SetPoint("TOPLEFT", PayLog.mainView, "TOPLEFT", 10, -10-20-10-10);
PayLog.totalpaystr:SetHeight(20);

PayLog.key = PayLog.mainView:CreateFontString("PayLog.key", "OVERLAY", "GameFontNormal") 
PayLog.key:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PayLog.key:SetPoint("TOPLEFT", PayLog.mainView, "TOPLEFT", -80,-10-20-10-20-25);
PayLog.key:SetWidth(PayLog.width/3);
PayLog.key:SetHeight(20);
PayLog.key:SetSpacing(5);
PayLog.key:SetText("|cFFFF0033序号");

PayLog.datatimer = PayLog.mainView:CreateFontString("PayLog.datatimer", "OVERLAY", "GameFontNormal") 
PayLog.datatimer:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PayLog.datatimer:SetPoint("TOPLEFT", PayLog.mainView, "TOPLEFT", 250,-10-20-10-20-25);
PayLog.datatimer:SetWidth(PayLog.width/3);
PayLog.datatimer:SetHeight(20);
PayLog.datatimer:SetSpacing(5);
PayLog.datatimer:SetText("|cFFFF0033日期时间");

PayLog.getPromo = PayLog.mainView:CreateFontString("PayLog.getPromo", "OVERLAY", "GameFontNormal") 
PayLog.getPromo:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PayLog.getPromo:SetPoint("TOPRIGHT", PayLog.mainView, "TOPRIGHT", 40,-10-20-10-20-25);
PayLog.getPromo:SetWidth(PayLog.width/3);
PayLog.getPromo:SetHeight(20);
PayLog.getPromo:SetSpacing(5);
PayLog.getPromo:SetText("|cFFFF0033充值数值");

PayLog.paihang[1] = PayLog:CreateCell(1,37,-10-20-10-20-10-20-15,nil,GetSpellIcon(0),"","","");
PayLog.paihang[2] = PayLog:CreateCell(2,37,-10-20-10-20-10-20-15-24*1,nil,GetSpellIcon(0),"","","");
PayLog.paihang[3] = PayLog:CreateCell(3,37,-10-20-10-20-10-20-15-24*2,nil,GetSpellIcon(0),"","","");
PayLog.paihang[4] = PayLog:CreateCell(4,37,-10-20-10-20-10-20-15-24*3,nil,GetSpellIcon(0),"","","");
PayLog.paihang[5] = PayLog:CreateCell(5,37,-10-20-10-20-10-20-15-24*4,nil,GetSpellIcon(0),"","","");
PayLog.paihang[6] = PayLog:CreateCell(6,37,-10-20-10-20-10-20-15-24*5,nil,GetSpellIcon(0),"","","");
PayLog.paihang[7] = PayLog:CreateCell(7,37,-10-20-10-20-10-20-15-24*6,nil,GetSpellIcon(0),"","","");
PayLog.paihang[8] = PayLog:CreateCell(8,37,-10-20-10-20-10-20-15-24*7,nil,GetSpellIcon(0),"","","");
PayLog.paihang[9] = PayLog:CreateCell(9,37,-10-20-10-20-10-20-15-24*8,nil,GetSpellIcon(0),"","","");
PayLog.paihang[10] = PayLog:CreateCell(10,37,-10-20-10-20-10-20-15-24*9,nil,GetSpellIcon(0),"","","");

PayLog.UpPage = CreateFrame('Button', nil, PayLog.mainView, 'UIPanelButtonTemplate') 
PayLog.UpPage:SetPoint('CENTER', PayLog.mainView, 'BOTTOM', -150, 30);
PayLog.UpPage:SetSize(80, 30);
PayLog.UpPage:SetText("上一页");
PayLog.UpPage:SetScript('OnClick', function() 
	if nowPayPage > 1 then nowPayPage = nowPayPage - 1 end 
end);

PayLog.NextPage = CreateFrame('Button', nil, PayLog.mainView, 'UIPanelButtonTemplate') 
PayLog.NextPage:SetPoint('CENTER', PayLog.mainView, 'BOTTOM', 150, 30);
PayLog.NextPage:SetSize(80, 30);
PayLog.NextPage:SetText("下一页");
PayLog.NextPage:SetScript('OnClick', function() 
	if nowPayPage < totalPayPage then nowPayPage = nowPayPage + 1 end 
end);

PayLog.PageStr = PayLog.mainView:CreateFontString("PayLog.PageStr", "OVERLAY", "GameFontNormal") 
PayLog.PageStr:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PayLog.PageStr:SetPoint("CENTER", PayLog.mainView, "BOTTOM", 0,33);
PayLog.PageStr:SetWidth(PayLog.width/3);
PayLog.PageStr:SetHeight(20);
PayLog.PageStr:SetSpacing(5);
PayLog.PageStr:SetText("第99页 / 第99页");

-- PayLog.mainView:Show();

function PayDataOnDataRecv(self, event, opcode, msg, type, sender)
	
	if event == "CHAT_MSG_ADDON" then

		if opcode == "PAY_DATA" then
			-- 序号,充值记录时间,充值数值
			local num,datas,payval = strsplit("#", msg)
            -- print("msg = "..msg)
			PayData[tonumber(num)] = nil
			PayData[tonumber(num)] = {}
			PayData[tonumber(num)].datas = datas
			PayData[tonumber(num)].payval = payval
		end

		if opcode == "PAY_DATA_TOTALPAY" then
			-- 充值总点数
			TotalPay = tonumber(msg)
            -- print("msg = "..msg)
		end

		if opcode == "PAY_DATA_LOG_OPEN" then
			local totals = countTableElements(PayData)
			totalPayPage = math.ceil(totals/10)
			if totalPayPage == 0 then totalPayPage = 1 end
			nowPayPage = 1
			PayLog:reload()
			PayLog.mainView:Show();
		end

		if opcode == "PAY_DATA_LOG_CLOSE" then
			local totals = countTableElements(PayData)
			totalPayPage = math.ceil(totals/10)
			nowPayPage = 1
			PayLog.mainView:Hide();
		end
	end
end


function PayLog:reload() 
		PayLog.totalpaystr:SetText("累计充值："..TotalPay);
		local count = 0;
		for k, v in pairs(PayData) do
			if ((nowPayPage-1) * 10) < k and k <= (nowPayPage * 10)  then
				local _num = k - (nowPayPage-1) * 10
				PayLog.paihang[_num].keytext:SetText(tostring(k)); 		--序号
				PayLog.paihang[_num].datatext:SetText(v.datas); 		--日期时间
				PayLog.paihang[_num].payvalstr:SetText(v.payval);		--充值点数
				count = count + 1
			end
		end

		if(count < 10) then
			local max = 10
			for i = count +1,max do
				PayLog.paihang[i].keytext:SetText(""); 		--序号
				PayLog.paihang[i].datatext:SetText(""); 	--日期时间
				PayLog.paihang[i].payvalstr:SetText("");	--充值点数
			end
		end
end

local PAL_RECV_FRAME = CreateFrame("Frame")
PAL_RECV_FRAME:RegisterEvent("CHAT_MSG_ADDON")
PAL_RECV_FRAME:SetScript("OnEvent", PayDataOnDataRecv)

local PayLogOnUpdate = CreateFrame("Frame")
PayLogOnUpdate:SetScript("OnUpdate", function() 
	if PayLog.mainView:IsShown() then 
		PayLog.PageStr:SetText("第"..nowPayPage.."页 / 第"..totalPayPage.."页");
		if synchrotimer <= 0 then
			PayLog:reload() 
			synchrotimer = 20
		else
			synchrotimer = synchrotimer - 1
		end
	end 
end)