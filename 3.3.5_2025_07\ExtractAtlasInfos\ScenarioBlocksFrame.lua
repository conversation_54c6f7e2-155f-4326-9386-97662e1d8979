-- ScenarioBlocksFrame.lua
-- 将temp1.xml中的所有XML内容转换为符合World of Warcraft 3.3.5 API规范的Lua代码

-- 调试模式控制开关
local DEBUG_ENABLED = true
local OBJECTIVES_COUNT = 6
local OBJECTIVES_TABLE = {} -- 演示用目标数据
local ICON_NAME_TABLE = { "Objective-Nub","Tracker-Check","Objective-Fail" } -- 演示用图标数据
local StageBlockData = {}
local ChallengeModeData = {}
local ProvingGroundsData = {}
local ChallengeModCountdownData = {}
-- 全局测试框架变量
local TheScenarioFrame = nil
local TheTrackerHeaderFrame = nil

-- 统一调试函数
local function DebugPrint(message)
    if DEBUG_ENABLED then
        local currentTime = time()
        local gameTime = GetTime()
        local timeTable = date("*t", currentTime)
        local hours = timeTable.hour
        local minutes = timeTable.min
        local seconds = timeTable.sec
        local milliseconds = math.floor((gameTime - math.floor(gameTime)) * 1000)
        local timestamp = string.format("|cff888888[%02d:%02d:%02d.%03d]|r",
                                      hours, minutes, seconds, milliseconds)
        print(timestamp .. " " .. message)
    end
end

local function InitObjectivesTable(text, textIndex, iconIndex)
    if iconIndex > #ICON_NAME_TABLE then
        DebugPrint("|cffff0000ScenarioBlocks|r: InitObjectivesTable - iconnum超出范围: " .. iconIndex)
        return
    end 
    local icon = ICON_NAME_TABLE[iconIndex]
    OBJECTIVES_TABLE[textIndex] = {text = text, icon = icon}
end

local function GetObjectivesLineIndex(name)
    -- 遍历ICON_NAME_TABLE
    for index, iconName in ipairs(ICON_NAME_TABLE) do
        if iconName == name then
            return index
        end
    end
    return nil
end

local function GetObjectivesLineIconTexCoordAndSize(index)
    if ICON_NAME_TABLE[index] then
        local atlasInfo = GetAtlasTextureInfo(ICON_NAME_TABLE[index])
        if atlasInfo then
            return atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom, atlasInfo.width, atlasInfo.height
        end
    end
    return nil
end

ScenarioBlocksFrameManager = {}

-- 创建Atlas纹理的辅助函数 - 参考ChallengesKeystoneFrameUI.lua
function ScenarioBlocksFrameManager:CreateAtlasTexture(parent, atlasName, layer, sublevel, useAtlasSize)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)

    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)

        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功加载Atlas纹理 '" .. atlasName .. "'")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法找到Atlas纹理: " .. atlasName)
        -- 设置一个默认纹理或颜色作为后备
        --texture:SetColorTexture(1, 1, 1, 0.5) -- 白色半透明作为占位符
    end

    return texture
end

-- 主函数：创建ScenarioBlocksFrame及其所有子框架（使用固定高度Frame替代ScrollFrame）
-- @param parentFrame Frame 父框架（可以是ObjectiveTrackerBlocksFrame或其他）
-- @param frameName string 框架名称（可选）
-- @param blockTypes table 要创建的子框架类型数组（可选），可选值：{"ObjectiveBlock", "StageBlock", "ChallengeModeBlock", "ProvingGroundsBlock"}
-- @return Frame 创建的主框架对象
--
-- 使用示例：
-- 1. 只创建主容器框架：CreateScenarioBlocksFrame(parent, "MyFrame")
-- 2. 创建指定类型的子框架：CreateScenarioBlocksFrame(parent, "MyFrame", "StageBlock")
-- 3. 创建挑战模式子框架：CreateScenarioBlocksFrame(parent, "MyFrame", "ChallengeModeBlock")
function ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建ScenarioBlocksFrame（固定高度Frame方案）")

    local name = frameName or "ScenarioBlocksFrame"

    -- 创建主容器框架 - 使用普通Frame替代ScrollFrame
    local mainFrame = CreateFrame("Frame", name, parentFrame)
    if parentFrame ~= UIParent then
        mainFrame:SetPoint("BOTTOMLEFT", parentFrame, "BOTTOMLEFT", 0, 0)
    end

    mainFrame:SetSize(261, 87) -- 固定尺寸
    mainFrame:Hide()
    parentFrame.MainFrame = mainFrame

    DebugPrint("|cff00ff00ScenarioBlocks|r: 主容器框架已创建")

    -- 参数验证和处理
    local blockTypeToCreate = nil
    if blockTypes then
        if type(blockTypes) == "string" then
            -- 验证字符串参数是否有效
            local validTypes = {
                ["ObjectiveBlock"] = true,
                ["StageBlock"] = true,
                ["ChallengeModeBlock"] = true,
                ["ProvingGroundsBlock"] = true
            }

            if validTypes[blockTypes] then
                blockTypeToCreate = blockTypes
                DebugPrint("|cff00ff00ScenarioBlocks|r: 将创建指定的子框架类型: " .. blockTypes)
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 无效的子框架类型: " .. blockTypes)
                return mainFrame
            end
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - blockTypes参数必须是字符串类型")
            return mainFrame
        end
    else
        DebugPrint("|cff00ff00ScenarioBlocks|r: 未指定子框架类型，只创建主容器框架")
    end

    -- 根据指定类型创建对应的子框架（所有子框架都直接使用mainFrame作为父框架）
    if blockTypeToCreate == "ObjectiveBlock" then
        self:CreateScenarioObjectiveBlock(mainFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ObjectiveBlock")

    elseif blockTypeToCreate == "StageBlock" then
        self:CreateScenarioStageBlock(mainFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建StageBlock")

    elseif blockTypeToCreate == "ChallengeModeBlock" then
        self:CreateScenarioChallengeModeBlock(mainFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ChallengeModeBlock")

    elseif blockTypeToCreate == "ProvingGroundsBlock" then
        self:CreateScenarioProvingGroundsBlock(mainFrame)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 已创建ProvingGroundsBlock")
    end

    -- 设置脚本事件
    -- self:SetupScenarioBlocksFrameScripts(mainFrame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: ScenarioBlocksFrame创建完成（固定高度Frame方案）")
    return mainFrame
end

-- 中文子框架类型名称转换为英文blockType的辅助函数
-- @param chineseName string 中文的子框架类型名称
-- @return string|nil 对应的英文blockType字符串，匹配失败时返回nil
--
-- 支持的中文名称映射：
-- "目标块" 或 "目标框架" → "ObjectiveBlock"
-- "阶段块" 或 "阶段框架" → "StageBlock"
-- "挑战模式块" 或 "挑战模式框架" → "ChallengeModeBlock"
-- "试炼场块" 或 "试炼场框架" → "ProvingGroundsBlock"
--
-- 使用示例：
-- local blockType = ScenarioBlocksFrameManager:GetBlockTypeFromChineseName("阶段块")
-- -- 返回 "StageBlock"
function ScenarioBlocksFrameManager:GetBlockTypeFromChineseName(chineseName)
    if not chineseName or type(chineseName) ~= "string" then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetBlockTypeFromChineseName 参数无效: " .. tostring(chineseName))
        return nil
    end

    -- 转换为小写以支持大小写不敏感匹配
    local lowerName = string.lower(chineseName)

    -- 中英文映射表
    local chineseToEnglishMap = {
        -- 目标相关
        ["目标块"] = "ObjectiveBlock",
        ["目标框架"] = "ObjectiveBlock",
        ["目标追踪块"] = "ObjectiveBlock",
        ["目标追踪框架"] = "ObjectiveBlock",

        -- 阶段相关
        ["阶段块"] = "StageBlock",
        ["阶段框架"] = "StageBlock",
        ["关卡块"] = "StageBlock",
        ["关卡框架"] = "StageBlock",
        ["场景阶段块"] = "StageBlock",
        ["场景阶段框架"] = "StageBlock",

        -- 挑战模式相关
        ["挑战模式块"] = "ChallengeModeBlock",
        ["挑战模式框架"] = "ChallengeModeBlock",
        ["挑战块"] = "ChallengeModeBlock",
        ["挑战框架"] = "ChallengeModeBlock",
        ["史诗钥石块"] = "ChallengeModeBlock",
        ["史诗钥石框架"] = "ChallengeModeBlock",
        ["大秘境块"] = "ChallengeModeBlock",
        ["大秘境框架"] = "ChallengeModeBlock",

        -- 试炼场相关
        ["试炼场块"] = "ProvingGroundsBlock",
        ["试炼场框架"] = "ProvingGroundsBlock",
        ["试炼块"] = "ProvingGroundsBlock",
        ["试炼框架"] = "ProvingGroundsBlock",
        ["竞技场块"] = "ProvingGroundsBlock",
        ["竞技场框架"] = "ProvingGroundsBlock"
    }

    -- 查找匹配的英文类型
    local englishType = chineseToEnglishMap[lowerName]

    if englishType then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 中文名称 '" .. chineseName .. "' 转换为英文类型: " .. englishType)
        return englishType
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 未找到匹配的中文名称: " .. chineseName)
        return nil
    end
end

-- 转换中文子框架类型名称为英文blockType的辅助函数（支持单个名称或数组）
-- @param chineseNames string|table 中文子框架类型名称（字符串）或名称数组
-- @return string|nil 转换后的英文blockType字符串，如果输入无效或转换失败则返回nil
--
-- 使用示例：
-- local blockType = ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes("阶段块")
-- -- 返回 "StageBlock"
-- local blockType = ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes({"阶段块"})
-- -- 返回 "StageBlock"（取第一个有效的）
function ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes(chineseNames)
    -- 处理字符串参数
    if type(chineseNames) == "string" then
        return self:GetBlockTypeFromChineseName(chineseNames)
    end

    -- 处理数组参数（为了向后兼容）
    if not chineseNames or type(chineseNames) ~= "table" or #chineseNames == 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes 参数无效")
        return nil
    end

    -- 返回第一个有效的转换结果
    for _, chineseName in ipairs(chineseNames) do
        local englishType = self:GetBlockTypeFromChineseName(chineseName)
        if englishType then
            DebugPrint("|cff00ff00ScenarioBlocks|r: 成功转换中文名称: " .. chineseName .. " → " .. englishType)
            return englishType
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 跳过无效的中文名称: " .. tostring(chineseName))
        end
    end

    DebugPrint("|cffff0000ScenarioBlocks|r: 没有成功转换任何中文名称")
    return nil
end

-- 创建ScenarioObjectiveBlock框架
function ScenarioBlocksFrameManager:CreateScenarioObjectiveBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioObjectiveBlock")

    local frame = CreateFrame("Frame", "ScenarioObjectiveBlock", parent)
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:SetSize(192, 10) -- 对应XML中的Size x="192" y="10"
    frame:Show() -- 对应XML中的hidden="true"

    parent.ScenarioObjectiveBlock = frame
    return frame
end

-- 创建ScenarioStageBlock框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioStageBlock")

    local frame = CreateFrame("Frame", "ScenarioStageBlock", parent)
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:SetSize(201, 83) -- 对应XML中的Size x="201" y="83"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateScenarioStageBlockLayers(frame)
    --self:CreateScenarioStageBlockFrames(frame)
    --self:SetupScenarioStageBlockScripts(frame)

    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)

    parent.ScenarioStageBlock = frame
    return frame
end

-- 创建ScenarioStageBlock的层级结构
function ScenarioBlocksFrameManager:CreateScenarioStageBlockLayers(frame)
    -- BACKGROUND层
    local normalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    normalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    frame.NormalBG = normalBG -- 对应XML中的parentKey="NormalBG"

    -- BORDER层
    local finalBG = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    finalBG:SetPoint("TOPLEFT", frame, "TOPLEFT", 4, -4)
    frame.FinalBG = finalBG -- 对应XML中的parentKey="FinalBG"

    local glowTexture = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BORDER", 0, true)
    glowTexture:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
    glowTexture:SetAlpha(0) -- 对应XML中的alpha="0"
    glowTexture:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    frame.GlowTexture = glowTexture -- 对应XML中的parentKey="GlowTexture"

    -- 创建GlowTexture的动画
    self:CreateGlowTextureAnimation(glowTexture)

    -- ARTWORK层 - 字体字符串
    self:CreateScenarioStageBlockFontStrings(frame)
end

-- 创建ScenarioStageBlock的字体字符串
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFontStrings(frame)
    -- Stage字体字符串
    local stage = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    stage:SetSize(172, 18) -- 对应XML中的Size x="172" y="18"
    stage:SetPoint("TOPLEFT", frame, "TOPLEFT", 15, -10)
    stage:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    stage:SetText("阶段: ") -- 对应XML中的text="STAGE"
    stage:SetWordWrap(true) -- 对应XML中的wordwrap="true"
    stage:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color r="1" g="0.914" b="0.682"
    stage:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    stage:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.Stage = stage -- 对应XML中的parentKey="Stage"

    -- CompleteLabel字体字符串
    local completeLabel = frame:CreateFontString(nil, "ARTWORK", "QuestTitleFont")
    completeLabel:SetPoint("LEFT", frame, "LEFT", 15, 3)
    completeLabel:SetText("") -- 对应XML中的text="STAGE_COMPLETE"
    completeLabel:Show() -- 对应XML中的hidden="true"
    completeLabel:SetTextColor(1, 0.914, 0.682) -- 对应XML中的Color
    completeLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    completeLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.CompleteLabel = completeLabel -- 对应XML中的parentKey="CompleteLabel"

    -- Name字体字符串
    local name = frame:CreateFontString(nil, "ARTWORK", "GameFontNormal")
    name:SetSize(172, 28) -- 对应XML中的Size x="172" y="28"
    name:SetPoint("TOPLEFT", stage, "BOTTOMLEFT", 0, -4) -- 对应XML中的相对定位
    name:SetText("天灾入侵") -- 对应XML中的text="阶段名称"
    name:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    name:SetJustifyV("TOP") -- 对应XML中的justifyV="TOP"
    name:SetSpacing(2) -- 对应XML中的spacing="2"
    name:SetTextColor(1, 0.831, 0.380) -- 对应XML中的Color r="1" g="0.831" b="0.380"
    frame.Name = name -- 对应XML中的parentKey="Name"
end

-- 创建GlowTexture的动画组
function ScenarioBlocksFrameManager:CreateGlowTextureAnimation(glowTexture)
    local animGroup = glowTexture:CreateAnimationGroup()
    glowTexture.AlphaAnim = animGroup -- 对应XML中的parentKey="AlphaAnim"

    -- 第一个Alpha动画 - 从0变为1，使用SetChange(1)
    local alpha1 = animGroup:CreateAnimation("Alpha")
    alpha1:SetChange(1) -- 对应XML中的fromAlpha="0" toAlpha="1"，变化量为1
    alpha1:SetDuration(0.266) -- 对应XML中的duration="0.266"
    alpha1:SetOrder(1) -- 对应XML中的order="1"

    -- 第二个Alpha动画 - 从1变为0，使用SetChange(-1)
    local alpha2 = animGroup:CreateAnimation("Alpha")
    alpha2:SetEndDelay(0.2) -- 对应XML中的endDelay="0.2"
    alpha2:SetChange(-1) -- 对应XML中的fromAlpha="1" toAlpha="0"，变化量为-1
    alpha2:SetDuration(0.333) -- 对应XML中的duration="0.333"
    alpha2:SetOrder(2) -- 对应XML中的order="2"
end

-- 创建ScenarioStageBlock的子框架
function ScenarioBlocksFrameManager:CreateScenarioStageBlockFrames(frame)

    -- 创建RewardButton
    local rewardButton = CreateFrame("Button", nil, frame)
    rewardButton:SetSize(48, 48) -- 对应XML中的Size x="48" y="48"
    rewardButton:SetPoint("BOTTOMRIGHT", frame, "BOTTOMRIGHT", 50, -3)
    rewardButton:Hide() -- 对应XML中的hidden="true"
    frame.RewardButton = rewardButton -- 对应XML中的parentKey="RewardButton"

    -- 创建RewardButton的纹理层
    self:CreateRewardButtonLayers(rewardButton)
    self:SetupRewardButtonScripts(rewardButton)
end

-- 创建RewardButton的纹理层
function ScenarioBlocksFrameManager:CreateRewardButtonLayers(button)
    -- OVERLAY层 textureSubLevel="1" - RewardRing
    local rewardRing = self:CreateAtlasTexture(button, "legioninvasion-scenario-rewardring", "OVERLAY", 1, true)
    rewardRing:SetPoint("CENTER", button, "CENTER") -- 对应XML中的相对定位
    button.RewardRing = rewardRing -- 对应XML中的parentKey="RewardRing"

    -- OVERLAY层 textureSubLevel="0" - RewardIcon
    local rewardIcon = button:CreateTexture(nil, "OVERLAY", nil, 0)
    rewardIcon:SetSize(29, 29) -- 对应XML中的Size x="29" y="29"
    rewardIcon:SetPoint("CENTER", rewardRing, "CENTER") -- 对应XML中的相对定位
    button.RewardIcon = rewardIcon -- 对应XML中的parentKey="RewardIcon"
end

-- 设置RewardButton的脚本事件
function ScenarioBlocksFrameManager:SetupRewardButtonScripts(button)
    -- OnEnter事件
    button:SetScript("OnEnter", function(self)
        if ScenarioRewardButton_OnEnter then
            ScenarioRewardButton_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnEnter函数未定义")
        end
    end)

    -- OnLeave事件
    button:SetScript("OnLeave", function(self)
        if ScenarioRewardButton_OnLeave then
            ScenarioRewardButton_OnLeave(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioRewardButton_OnLeave函数未定义")
        end
    end)
end

-- 设置ScenarioStageBlock的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioStageBlockScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad脚本
        if self.Stage then
            -- 设置字体对象尝试列表
            -- 注意：在WoW 3.3.5中可能需要使用不同的方法
            if self.Stage.SetFontObjectsToTry then
                self.Stage:SetFontObjectsToTry("QuestTitleFont", "Fancy16Font", "SystemFont_Med1")
            else
                -- 备用方法：直接设置字体对象
                self.Stage:SetFontObject("QuestTitleFont")
            end
        end
    end)

    -- OnEnter事件
    frame:SetScript("OnEnter", function(self)
        if ScenarioObjectiveStageBlock_OnEnter then
            ScenarioObjectiveStageBlock_OnEnter(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioObjectiveStageBlock_OnEnter函数未定义")
        end
    end)

    -- OnLeave事件
    frame:SetScript("OnLeave", function(self)
        GameTooltip:Hide() -- 对应XML中的OnLeave脚本
    end)
end

-- 创建ScenarioChallengeModeBlock框架
function ScenarioBlocksFrameManager:CreateScenarioChallengeModeBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock")

    local frame = CreateFrame("Frame", "ScenarioChallengeModeBlock", parent)
    frame:SetPoint("LEFT", 0, 0)
    frame:SetSize(261, 87) -- 对应XML中的Size x="251" y="87"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateChallengeModeBlockLayers(frame)
    self:CreateChallengeModeBlockFrames(frame)

    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)
    
    parent.ScenarioChallengeModeBlock = frame
    return frame
end

-- 创建ScenarioChallengeModeBlock的层级结构
function ScenarioBlocksFrameManager:CreateChallengeModeBlockLayers(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock的层级结构")
    -- BACKGROUND层
    local timerBGBack = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG-back", "BACKGROUND", 0, true)
    timerBGBack:SetPoint("BOTTOM", frame, 0, 13)
    frame.TimerBGBack = timerBGBack -- 对应XML中的parentKey="TimerBGBack"

    -- BACKGROUND层 textureSubLevel="1"
    local timerBG = self:CreateAtlasTexture(frame, "ChallengeMode-TimerBG", "BACKGROUND", 1, true)
    timerBG:SetPoint("BOTTOM", frame, 0, 13)
    frame.TimerBG = timerBG -- 对应XML中的parentKey="TimerBG"

    -- OVERLAY层
    local overlayTexture = self:CreateAtlasTexture(frame, "ChallengeMode-Timer", "OVERLAY", 0, false)
    overlayTexture:SetAllPoints(frame) -- 对应XML中的setAllPoints="true"
    frame.OverlayTexture = overlayTexture -- 对应XML中的parentKey="OverlayTexture"


    -- OVERLAY层字体字符串
    local level = frame:CreateFontString(nil, "OVERLAY", "GameFontNormalMed2")
    level:SetPoint("TOPLEFT", frame, "TOPLEFT", 28, -18)
    level:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    level:SetText("LEVEL 10") -- 对应XML中的text="LEVEL"
    frame.Level = level -- 对应XML中的parentKey="Level"

    -- 创建法术图标按钮
    self:CreateChallengeModeSpellIconButtons(frame, level)

    local timeLeft = frame:CreateFontString(nil, "OVERLAY", "GameFontHighlightHuge")
    timeLeft:SetPoint("TOPLEFT", level, "BOTTOMLEFT", 0, -8) -- 对应XML中的相对定位
    timeLeft:SetJustifyH("LEFT") -- 对应XML中的justifyH="LEFT"
    timeLeft:SetText("40:00") -- 对应XML中的text="TIME LEFT"
    frame.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"

    -- 初始化倒计时功能
    self:InitializeCountdownTimer(frame)
end

-- 创建法术图标按钮
-- @param frame Frame 挑战模式框架
-- @param levelFontString FontString level字体字符串，用于定位参考（当前未使用，保留用于未来扩展）
function ScenarioBlocksFrameManager:CreateChallengeModeSpellIconButtons(frame, levelFontString)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建法术图标按钮")

    -- 法术ID
    local spellIds = {2825, 32182, 62392}

    -- 创建按钮容器数组
    frame.SpellIconButtons = {}

    for i = 1, 3 do
        local spellId = spellIds[i]

        -- 创建按钮
        local button = CreateFrame("Button", nil, frame)
        button:SetSize(20, 20)

        -- 设置按钮位置（右对齐布局）
        if i == 1 then
            button:SetPoint("TOPRIGHT", frame, "TOPRIGHT", -25, -15)
        else
            local prevButton = frame.SpellIconButtons[i - 1]
            button:SetPoint("TOPRIGHT", prevButton, "TOPLEFT", -4, 0)
        end

        -- 获取法术图标
        local spellName, _, icon = GetSpellInfo(spellId)
        if icon then
            local iconTexture = button:CreateTexture(nil, "ARTWORK")
            iconTexture:SetAllPoints(button)
            iconTexture:SetTexture(icon)
            button.Icon = iconTexture
        end
        
        -- 设置脚本事件
        button:SetScript("OnEnter", function(self)
            if self.spellId then
                local name = GetSpellInfo(self.spellId)
                local description = GhostGetSpellDescription(self.spellId)
                GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                GameTooltip:SetText(name, 1, 1, 1, 1, true)
                GameTooltip:AddLine(description, nil, nil, nil, true)
                GameTooltip:Show()
            end
        end)
        
        button:SetScript("OnLeave", function(self)
            GameTooltip:Hide()
        end)

        -- 保存按钮到数组
        frame.SpellIconButtons[i] = button
        button.spellId = spellId
        button:Show()

        DebugPrint("|cff00ff00ScenarioBlocks|r: 法术图标按钮" .. i .. "创建完成")
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 所有法术图标按钮创建完成")
end

-- 初始化挑战模式倒计时器
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:InitializeCountdownTimer(frame)
    if not frame.TimeLeft then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法初始化倒计时，TimeLeft字体字符串不存在")
        return
    end

    -- 倒计时状态数据
    frame.countdownData = {
        totalSeconds = 40 * 60,  -- 默认40分钟（2400秒）
        remainingSeconds = 40 * 60, -- 剩余时间
        isRunning = false,
        isPaused = false,
        lastUpdateTime = 0,
        warningThreshold = 1 * 60,  -- 5分钟警告阈值
        originalColor = {1, 1, 1},  -- 原始白色
        warningColor = {1, 0.2, 0.2}  -- 警告红色
    }

    if ChallengeModCountdownData.totalSeconds then
        frame.countdownData.totalSeconds = ChallengeModCountdownData.totalSeconds
        frame.countdownData.remainingSeconds = ChallengeModCountdownData.remainingSeconds
        frame.countdownData.warningThreshold = ChallengeModCountdownData.warningThreshold
    end

    -- 创建更新定时器框架
    frame.countdownTimer = CreateFrame("Frame")
    frame.countdownTimer.parentFrame = frame

    -- 设置OnUpdate事件处理器
    frame.countdownTimer:SetScript("OnUpdate", function(self, elapsed)
        ScenarioBlocksFrameManager:UpdateCountdownTimer(self.parentFrame, elapsed)
    end)
    -- self:StartCountdown(frame, 1 * 60)
    self:StartCountdown(frame, nil)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时器已初始化，默认时间: " ..
              ScenarioBlocksFrameManager:FormatTime(frame.countdownData.totalSeconds))
end

-- 更新倒计时显示
-- @param frame Frame 挑战模式框架
-- @param elapsed number 自上次更新以来的时间（秒）
function ScenarioBlocksFrameManager:UpdateCountdownTimer(frame, elapsed)
    local data = frame.countdownData
    if not data or not data.isRunning or data.isPaused then
        -- DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时未运行或已暂停，跳过更新")
        return
    end

    -- 累积时间
    data.lastUpdateTime = data.lastUpdateTime + elapsed

    -- 每秒更新一次
    if data.lastUpdateTime >= 1.0 then
        data.remainingSeconds = data.remainingSeconds - math.floor(data.lastUpdateTime)
        data.lastUpdateTime = data.lastUpdateTime - math.floor(data.lastUpdateTime)

        -- 确保不会变成负数
        if data.remainingSeconds < 0 then
            data.remainingSeconds = 0
            data.isRunning = false
            DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时结束")
        end
        
        -- 更新显示
        self:UpdateCountdownDisplay(frame)

        -- 更新进度条
        self:UpdateCountdownProgressBar(frame)

        -- 检查是否需要颜色警告
        self:CheckCountdownWarning(frame)
    end
end

-- 更新倒计时显示文本
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:UpdateCountdownDisplay(frame)
    if not frame.TimeLeft or not frame.countdownData then
        return
    end

    local timeText = self:FormatTime(frame.countdownData.remainingSeconds)
    frame.TimeLeft:SetText(timeText)

    if timeText == "00:00" then
        for i = 1, #frame.CheckLines do
            self:UpdateObjectiveLinesStats(i, 3)
        end
    end
end

-- 检查倒计时警告状态
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:CheckCountdownWarning(frame)
    local data = frame.countdownData
    if not data or not frame.TimeLeft then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 时间文本或倒计时数据无效")
        return
    end

    -- 如果剩余时间少于警告阈值，改变颜色为红色
    if data.remainingSeconds <= data.warningThreshold and data.remainingSeconds > 0 then
        frame.TimeLeft:SetTextColor(data.warningColor[1], data.warningColor[2], data.warningColor[3])
    else
        -- frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])
    end
end

-- 更新倒计时进度条
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:UpdateCountdownProgressBar(frame)
    local data = frame.countdownData
    local statusBar = frame.StatusBar

    if not data or not statusBar or not statusBar.countdownLinked then
        return
    end

    -- 计算进度百分比
    local progressPercent = 0
    if data.totalSeconds > 0 then
        progressPercent = data.remainingSeconds / data.totalSeconds
    end

    -- 确保百分比在0-1范围内
    progressPercent = math.max(0, math.min(1, progressPercent))

    -- 计算进度条值（0-1000）
    local progressValue = progressPercent * 1000
    statusBar:SetValue(progressValue)
    
    -- 根据剩余时间百分比调整进度条颜色
    if progressPercent <= statusBar.warningThreshold then
        -- 时间不足时显示警告色（红色）
        statusBar:SetStatusBarColor(statusBar.warningColor[1], statusBar.warningColor[2], statusBar.warningColor[3])
    else
        -- 正常时间显示原始色（蓝色）
        statusBar:SetStatusBarColor(1, 1, 1)
    end

    -- DebugPrint("|cff00ff00ScenarioBlocks|r: 进度条已更新 - 百分比: " ..
    --           string.format("%.1f%%", progressPercent * 100) ..
    --           ", 值: " .. math.floor(progressValue))
end

-- 格式化时间为MM:SS格式
-- @param seconds number 总秒数
-- @return string 格式化的时间字符串
function ScenarioBlocksFrameManager:FormatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local secs = seconds % 60
    return string.format("%02d:%02d", minutes, secs)
end

-- 启动倒计时
-- @param frame Frame 挑战模式框架
-- @param initialSeconds number 初始秒数（可选，默认使用当前设置）
function ScenarioBlocksFrameManager:StartCountdown(frame, initialSeconds)
    if not frame.countdownData then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法启动倒计时，倒计时数据未初始化")
        return
    end

    local data = frame.countdownData

    if initialSeconds then
        data.totalSeconds = initialSeconds
        data.remainingSeconds = initialSeconds
    end

    data.isRunning = true
    data.isPaused = false
    data.lastUpdateTime = 0

    -- 重置颜色
    frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])

    -- 更新初始显示
    self:UpdateCountdownDisplay(frame)

    -- 更新进度条到满值
    self:UpdateCountdownProgressBar(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已启动，时间: " .. self:FormatTime(data.remainingSeconds))
end

-- 暂停倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:PauseCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isPaused = true
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已暂停")
end

-- 恢复倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:ResumeCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isPaused = false
    frame.countdownData.lastUpdateTime = 0
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已恢复")
end

-- 停止倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:StopCountdown(frame)
    if not frame.countdownData then
        return
    end

    frame.countdownData.isRunning = false
    frame.countdownData.isPaused = false
    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已停止")
end

-- 重置倒计时
-- @param frame Frame 挑战模式框架
function ScenarioBlocksFrameManager:ResetCountdown(frame)
    if not frame.countdownData then
        return
    end

    local data = frame.countdownData
    data.remainingSeconds = data.totalSeconds
    data.isRunning = false
    data.isPaused = false
    data.lastUpdateTime = 0
    
    -- 重置颜色和显示
    frame.TimeLeft:SetTextColor(data.originalColor[1], data.originalColor[2], data.originalColor[3])
    self:UpdateCountdownDisplay(frame)

    -- 重置进度条到满值
    self:UpdateCountdownProgressBar(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 倒计时已重置到: " .. self:FormatTime(data.remainingSeconds))
end

-- 创建ScenarioChallengeModeBlock的子框架
function ScenarioBlocksFrameManager:CreateChallengeModeBlockFrames(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioChallengeModeBlock的子框架")
    -- 创建StartedDepleted框架
    self:CreateStartedDepletedFrame(frame)

    -- 创建TimesUpLootStatus框架
    self:CreateTimesUpLootStatusFrame(frame)

    -- 创建DeathCount框架
    self:CreateDeathCountFrame(frame)

    -- 创建StatusBar
    self:CreateChallengeModeStatusBar(frame)

    -- 创建词缀框架（继承ScenarioChallengeModeAffixTemplate）
    -- 注意：这里需要根据实际需求创建词缀框架
    -- local affixFrame = CreateFrame("Frame", nil, frame, "ScenarioChallengeModeAffixTemplate")
    -- affixFrame:Hide() -- 对应XML中的hidden="true"
end

-- 创建StartedDepleted框架
function ScenarioBlocksFrameManager:CreateStartedDepletedFrame(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建StartedDepleted框架")
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.Level, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.StartedDepleted = frame -- 对应XML中的parentKey="StartedDepleted"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame)

    -- ARTWORK层 textureSubLevel="1"
    local redlineTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    redlineTexture:SetPoint("CENTER", frame)

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
        GameTooltip:SetText("枯竭的钥石", 1, 1, 1) -- 对应XML中的文本
        GameTooltip:AddLine("你在完成此地下城后将无法获得战利品宝箱。但在限定时间内完成地下城能够为钥石充能并将其升级。", nil, nil, nil, true) -- 对应XML中的文本
        GameTooltip:Show()
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建TimesUpLootStatus框架
function ScenarioBlocksFrameManager:CreateTimesUpLootStatusFrame(parent)
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(19, 20) -- 对应XML中的Size x="19" y="20"
    frame:SetPoint("LEFT", parent.TimeLeft, "RIGHT", 4, 0) -- 对应XML中的相对定位
    frame:Hide() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.TimesUpLootStatus = frame -- 对应XML中的parentKey="TimesUpLootStatus"

    -- ARTWORK层
    local chestTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-chest", "ARTWORK", 0, true)
    chestTexture:SetPoint("CENTER", frame, "CENTER")

    -- ARTWORK层 textureSubLevel="1"
    local noLootTexture = self:CreateAtlasTexture(frame, "ChallengeMode-icon-redline", "ARTWORK", 1, true)
    noLootTexture:SetPoint("CENTER", frame, "CENTER")
    frame.NoLoot = noLootTexture -- 对应XML中的parentKey="NoLoot"

    -- 设置脚本事件
    frame:SetScript("OnEnter", function(self)
        ScenarioBlocksFrameManager:Scenario_ChallengeMode_TimesUpLootStatus_OnEnter(self)
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建DeathCount框架
function ScenarioBlocksFrameManager:CreateDeathCountFrame(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建DeathCount框架")
    local frame = CreateFrame("Frame", nil, parent)
    frame:SetSize(20, 20) -- 对应XML中的Size x="20" y="16"
    frame:SetPoint("TOPLEFT", parent, "BOTTOMRIGHT", -47, 43) -- 对应XML中的相对定位
    frame:Show() -- 对应XML中的hidden="true"
    frame:EnableMouse(true) -- 对应XML中的enableMouse="true"
    parent.DeathCount = frame -- 对应XML中的parentKey="DeathCount"

    -- 注意：XML中有mixin="ScenarioChallengeDeathCountMixin"，这里需要手动实现相关功能

    -- ARTWORK层
    local icon = self:CreateAtlasTexture(frame, "poi-graveyard-neutral", "ARTWORK", 0, true)
    icon:SetPoint("LEFT", frame, "LEFT")
    frame.Icon = icon -- 对应XML中的parentKey="Icon"

    local count = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightSmall2")
    count:SetPoint("LEFT", icon, "RIGHT", 1, 0) -- 对应XML中的相对定位
    count:SetText("0") -- 对应XML中的text="0"
    frame.Count = count -- 对应XML中的parentKey="Count"

    -- 设置脚本事件（对应XML中的mixin方法）
    frame:SetScript("OnLoad", function(self)
        -- 对应XML中的OnLoad method="OnLoad"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnLoad")
    end)

    frame:SetScript("OnEvent", function(self, event, ...)
        -- 对应XML中的OnEvent method="OnEvent"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEvent: " .. event)
    end)

    frame:SetScript("OnEnter", function(self)
        -- 对应XML中的OnEnter method="OnEnter"
        DebugPrint("|cff00ff00ScenarioBlocks|r: DeathCount OnEnter")
    end)

    frame:SetScript("OnLeave", function(self)
        if GameTooltip_Hide then
            GameTooltip_Hide()
        else
            GameTooltip:Hide()
        end
    end)
end

-- 创建挑战模式状态进度条（与倒计时联动）
function ScenarioBlocksFrameManager:CreateChallengeModeStatusBar(parent)
    local statusBar = CreateFrame("StatusBar", "ScenarioChallengeModeStatusBar", parent)
    statusBar:SetSize(207, 10) -- 对应XML中的Size x="207" y="13"
    statusBar:SetPoint("BOTTOM", parent, 0, 13)
    statusBar:SetFrameLevel(parent:GetFrameLevel())
    parent.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 设置状态栏纹理
    local barTexture = self:CreateAtlasTexture(statusBar, "ChallengeMode-TimerFill", "ARTWORK", 0, false)
    statusBar:SetStatusBarTexture(barTexture) -- 对应XML中的BarTexture atlas="ChallengeMode-TimerFill"

    -- 设置进度条数值范围（0-1000便于精确计算）
    statusBar:SetMinMaxValues(0, 1000)
    statusBar:SetValue(1000) -- 初始满值

    -- 初始化进度条联动数据
    statusBar.countdownLinked = true -- 标记为与倒计时联动
    -- statusBar.originalColor = {0, 0.33, 0.61} -- 原始蓝色
    statusBar.warningColor = {0.8, 0.2, 0.2}  -- 警告红色
    statusBar.warningThreshold = 0.2 -- 20%时显示警告色

    -- 设置初始颜色
    -- statusBar:SetStatusBarColor(statusBar.originalColor[1], statusBar.originalColor[2], statusBar.originalColor[3])

    DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战模式进度条已创建并配置倒计时联动")
end

-- 创建Glow动画效果
-- 对应XML中的Glow动画：Scale + Alpha动画组合
function ScenarioBlocksFrameManager:CreateGlowAnimation(frame)
    if not frame.Glow then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - Glow纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.Glow:CreateAnimationGroup()
    frame.Glow.Anim = animGroup

    -- Scale动画：startDelay="0.067" scaleX="3" scaleY="1" duration="0.433" order="1"
    local scaleAnim = animGroup:CreateAnimation("Scale")
    scaleAnim:SetStartDelay(0.067)
    scaleAnim:SetScale(3, 1)
    scaleAnim:SetDuration(0.433)
    scaleAnim:SetOrder(1)
    scaleAnim:SetOrigin("LEFT", 0, 0) -- 对应XML中的Origin point="LEFT"
    frame.Glow.ScaleGlow = scaleAnim

    -- Alpha动画1：startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.1" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetStartDelay(0.067)
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.1)
    alphaAnim1:SetOrder(1)

    -- Alpha动画2：startDelay="0.467" fromAlpha="1" toAlpha="0" duration="0.267" order="1"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetStartDelay(0.467)
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetDuration(0.267)
    alphaAnim2:SetOrder(1)

    DebugPrint("|cff00ff00ScenarioBlocks|r: Glow动画创建完成")
end

-- 创建CheckFlash动画效果
-- 对应XML中的CheckFlash动画：Alpha + Scale动画组合
function ScenarioBlocksFrameManager:CreateCheckFlashAnimation(frame)
    if not frame.CheckFlash then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - CheckFlash纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.CheckFlash:CreateAnimationGroup()
    frame.CheckFlash.Anim = animGroup

    -- Alpha动画1：fromAlpha="0" toAlpha="1" duration="0.067" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.067)
    alphaAnim1:SetOrder(1)

    -- Scale动画：scaleX="1.25" scaleY="1.25" duration="0.2" order="2"
    local scaleAnim = animGroup:CreateAnimation("Scale")
    scaleAnim:SetScale(1.25, 1.25)
    scaleAnim:SetDuration(0.2)
    scaleAnim:SetOrder(2)

    -- Alpha动画2：fromAlpha="1" toAlpha="0" startDelay="0.167" duration="0.23" order="2"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetStartDelay(0.167)
    alphaAnim2:SetDuration(0.23)
    alphaAnim2:SetOrder(2)

    DebugPrint("|cff00ff00ScenarioBlocks|r: CheckFlash动画创建完成")
end

-- 创建Sheen动画效果
-- 对应XML中的Sheen动画：Translation + Alpha动画组合
function ScenarioBlocksFrameManager:CreateSheenAnimation(frame)
    if not frame.Sheen then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - Sheen纹理不存在")
        return
    end

    -- 创建动画组
    local animGroup = frame.Sheen:CreateAnimationGroup()
    frame.Sheen.Anim = animGroup

    -- Translation动画：startDelay="0.067" offsetX="155" offsetY="0" duration="0.467" order="1"
    local translateAnim = animGroup:CreateAnimation("Translation")
    translateAnim:SetStartDelay(0.067)
    translateAnim:SetOffset(155, 0)
    translateAnim:SetDuration(0.467)
    translateAnim:SetOrder(1)

    -- Alpha动画1：startDelay="0.067" fromAlpha="0" toAlpha="1" duration="0.133" order="1"
    -- 从0变为1，变化量为+1
    local alphaAnim1 = animGroup:CreateAnimation("Alpha")
    alphaAnim1:SetStartDelay(0.067)
    alphaAnim1:SetChange(1) -- 从当前alpha值(0)增加1，变为1
    alphaAnim1:SetDuration(0.133)
    alphaAnim1:SetOrder(1)

    -- Alpha动画2：startDelay="0.2" fromAlpha="1" toAlpha="0" duration="0.133" order="1" smoothing="IN"
    -- 从1变为0，变化量为-1
    local alphaAnim2 = animGroup:CreateAnimation("Alpha")
    alphaAnim2:SetStartDelay(0.2)
    alphaAnim2:SetChange(-1) -- 从当前alpha值(1)减少1，变为0
    alphaAnim2:SetDuration(0.133)
    alphaAnim2:SetOrder(1)
    -- 注意：WoW 3.3.5可能不支持smoothing属性，这里省略

    DebugPrint("|cff00ff00ScenarioBlocks|r: Sheen动画创建完成")
end

-- 创建 ObjectiveTrackerCheckLine（从 ObjectiveTrackerCheckLineTemplate XML 转换）
function ScenarioBlocksFrameManager:CreateObjectiveTrackerCheckLine(parent, count)
    -- 参数验证：如果count为nil或小于1，则默认创建1个框架
    local frameCount = count and count > 0 and count or 1
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建 " .. frameCount .. " 个ObjectiveTrackerCheckLine框架")

    local frames = {} -- 存储所有创建的框架
    local previousFrame = nil -- 用于垂直布局的前一个框架引用

    for i = 1, frameCount do
        local frameName = "ObjectiveTrackerCheckLine" .. i
        DebugPrint("|cff00ff00ScenarioBlocks|r: 创建框架 " .. frameName)

        local frame = CreateFrame("Frame", frameName, parent)
        frame:SetSize(232, 16)

        -- 垂直布局：第一个框架锚定到父框架顶部，后续框架锚定到前一个框架底部
        if i == 1 then
            frame:SetPoint("BOTTOMLEFT", parent, "BOTTOMLEFT", 0, -20)
        else
            frame:SetPoint("TOPLEFT", previousFrame, "BOTTOMLEFT", 0, -3)
        end
        frame:Show()

        -- ARTWORK: Text (inherits ObjectiveFont)
        local text = frame:CreateFontString(frameName .. "Text", "ARTWORK", "GameFontHighlight")
        text:SetPoint("TOPLEFT", frame, "TOPLEFT", 20, 0)
        text:SetText("目标 " .. i)
        frame.Text = text

        -- ARTWORK: IconAnchor + Icon
        local iconAnchor = frame:CreateTexture(nil, "ARTWORK")
        iconAnchor:SetSize(16, 16)
        iconAnchor:SetPoint("TOPLEFT", frame, "TOPLEFT", 1, 2)
        frame.IconAnchor = iconAnchor

        -- 使用Atlas纹理创建图标
        -- local icon = self:CreateAtlasTexture(frame, "Objective-Nub", "ARTWORK", 0, true)
        local icon = frame:CreateTexture(nil, "ARTWORK")
        icon:SetSize(16, 16)
        icon:SetPoint("CENTER", iconAnchor, "CENTER")
        frame.Icon = icon

        -- ARTWORK: Glow
        local glow = frame:CreateTexture(nil, "ARTWORK")
        glow:SetTexture("Interface\\Scenarios\\Objective-Lineglow")
        glow:SetAlpha(0)
        glow:SetBlendMode("ADD")
        glow:SetSize(80, 0)
        glow:SetPoint("LEFT", text, "LEFT", -2, 0)
        glow:SetPoint("TOP", frame, "TOP", 0, 0)
        glow:SetPoint("BOTTOM", frame, "BOTTOM", 0, -4)
        frame.Glow = glow

        -- OVERLAY: CheckFlash
        local checkFlash = frame:CreateTexture(nil, "OVERLAY")
        checkFlash:SetTexture("Interface\\Scenarios\\ScenarioIcon-Check")
        checkFlash:SetAlpha(0)
        checkFlash:SetBlendMode("ADD")
        checkFlash:SetSize(16, 16)
        checkFlash:SetPoint("CENTER", icon, "CENTER")
        checkFlash:Hide()
        frame.CheckFlash = checkFlash

        -- OVERLAY: Sheen
        local sheen = frame:CreateTexture(nil, "OVERLAY")
        sheen:SetTexture("Interface\\Scenarios\\Objective-Sheen")
        sheen:SetAlpha(0)
        sheen:SetSize(32, 0)
        sheen:SetPoint("LEFT", glow, "LEFT")
        sheen:SetPoint("TOP", frame, "TOP", 0, 2)
        sheen:SetPoint("BOTTOM", frame, "BOTTOM", 0, -14)
        frame.Sheen = sheen

        -- 创建动画效果
        self:CreateGlowAnimation(frame)
        self:CreateCheckFlashAnimation(frame)
        self:CreateSheenAnimation(frame)

        -- Scripts
        frame:SetScript("OnLoad", function(self)
            local width = _G.OBJECTIVE_TRACKER_TEXT_WIDTH or 232
            if self.Text then self.Text:SetWidth(width) end
        end)
        frame:SetScript("OnHide", function(self)
            if ObjectiveTrackerCheckLine_OnHide then
                ObjectiveTrackerCheckLine_OnHide(self)
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: ObjectiveTrackerCheckLine_OnHide 未定义")
            end
        end)

        frame:SetScript("OnShow", function(self)
            ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(self)
        end)

        --self:PlayAllCheckLineAnimations(frame)

        -- 将框架添加到返回数组中
        table.insert(frames, frame)
        previousFrame = frame

        DebugPrint("|cff00ff00ScenarioBlocks|r: 框架 " .. frameName .. " 创建完成")
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 所有 " .. frameCount .. " 个ObjectiveTrackerCheckLine框架创建完成")

    -- 创建BonusTrackerProgressBar
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建BonusTrackerProgressBar")
    local bonusProgressBar = self:CreateBonusTrackerProgressBar(parent, "BonusTrackerProgressBar_" .. math.random(1000, 9999))

    if bonusProgressBar and frameCount > 0 then
        -- 将BonusTrackerProgressBar定位在最后一个ObjectiveTrackerCheckLine框架的底部
        local lastFrame = frames[frameCount]
        bonusProgressBar:SetPoint("TOPLEFT", lastFrame, "BOTTOMLEFT", 0, -8)
        -- bonusProgressBar:Hide() -- 默认隐藏

        -- 保存到父框架的属性中
        parent.BonusProgressBar = bonusProgressBar

        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar已创建并定位在最后一个CheckLine下方")
    elseif bonusProgressBar then
        -- 如果没有CheckLine框架，则定位在父框架底部
        bonusProgressBar:SetPoint("TOPLEFT", parent, "TOPLEFT", 0, -20)
        bonusProgressBar:Hide() -- 默认隐藏

        -- 保存到父框架的属性中
        parent.BonusProgressBar = bonusProgressBar

        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar已创建并定位在父框架顶部")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: BonusTrackerProgressBar创建失败")
    end

    return frames
end

-- 便捷函数：批量设置ObjectiveTrackerCheckLine的文本和图标
-- @param frames table 由CreateObjectiveTrackerCheckLine返回的框架数组
-- @param objectives table 目标数据数组，格式：{{text="目标1", icon="Atlas名称"}, ...}
function ScenarioBlocksFrameManager:SetObjectiveTrackerCheckLineData(frames, objectives)
    if not frames or not objectives then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetObjectiveTrackerCheckLineData 参数无效")
        return
    end

    for i, frame in ipairs(frames) do
        if objectives[i] then
            local objective = objectives[i]

            -- 设置文本
            if frame.Text and objective.text then
                frame.Text:SetText(objective.text)
            end

            -- 设置图标（如果提供了Atlas名称）
            if frame.Icon and objective.icon then
                -- 使用CreateAtlasTexture重新设置图标
                local newIcon = self:CreateAtlasTexture(frame, objective.icon, "ARTWORK", 0, true)
                newIcon:SetPoint("CENTER", frame.IconAnchor, "CENTER")
                frame.Icon = newIcon
                frame.IconIndex = GetObjectivesLineIndex(objective.icon)
            end

            DebugPrint("|cff00ff00ScenarioBlocks|r: 设置框架 " .. i .. " 数据: " .. (objective.text or "无文本"))
        end
    end
end

-- 创建演示用的目标追踪检查线框架
-- @param parentFrame Frame 父框架，用于承载ObjectiveTrackerCheckLine框架
-- @param count number 要创建的框架数量（可选，默认为10）
-- @param customObjectives table 自定义目标数据数组（可选），格式：{{text="文本", icon="图标名称"}, ...}
-- @return table 返回创建的checkLines框架数组
--
-- 使用示例：
-- 1. 使用默认数据：self:CreateDemoObjectiveLines(frame, 5)
-- 2. 使用自定义数据：self:CreateDemoObjectiveLines(frame, 3, {{text="自定义目标", icon="Objective-Nub"}})
function ScenarioBlocksFrameManager:CreateDemoObjectiveLines(parentFrame, count, customObjectives)
    local frameCount = count or 10
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建演示用目标追踪线，数量: " .. frameCount)

    -- 创建ObjectiveTrackerCheckLine框架
    local checkLines = self:CreateObjectiveTrackerCheckLine(parentFrame, frameCount)
    parentFrame.CheckLines = checkLines -- 保存框架数组引用

    -- 根据参数选择使用自定义数据还是默认演示数据
    local objectives
    if customObjectives and type(customObjectives) == "table" and #customObjectives > 0 then
        -- 使用传入的自定义目标数据
        objectives = customObjectives
        DebugPrint("|cff00ff00ScenarioBlocks|r: 使用自定义目标数据，数量: " .. #objectives)
    else
        -- 使用默认演示数据
        objectives = {
            {text = "击败10个敌人",   icon = "Objective-Nub"},
            {text = "收集5个物品",    icon = "Objective-Nub"},
            {text = "到达指定地点",   icon = "Objective-Nub"},
            {text = "击败9个敌人",    icon = "Objective-Nub"},
            {text = "收集9个物品",    icon = "Objective-Nub"},
            {text = "9到达指定地点",  icon = "Objective-Nub"},
            {text = "击败8个敌人",    icon = "Objective-Nub"},
            {text = "收集8个物品",    icon = "Objective-Nub"},
            {text = "8到达指定地点",  icon = "Objective-Nub"},
            {text = "击败10个敌人",   icon = "Objective-Nub"},
            {text = "收集10个物品",   icon = "Objective-Nub"},
            {text = "10到达指定地点", icon = "Objective-Nub"},
        }
        DebugPrint("|cff00ff00ScenarioBlocks|r: 使用默认演示数据")
    end

    -- 批量设置目标数据
    self:SetObjectiveTrackerCheckLineData(checkLines, objectives)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 演示用目标追踪线创建完成")
    return checkLines
end

-- 动态调整目标追踪线框架数量
-- @param parentFrame Frame 父框架对象
-- @param targetCount number 目标Line框架数量
-- @return boolean 操作是否成功
function ScenarioBlocksFrameManager:AdjustObjectiveLineCount(parentFrame, targetCount)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始调整目标追踪线框架数量")

    -- 参数验证
    if not parentFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: AdjustObjectiveLineCount - 父框架不能为空")
        return false
    end

    if not targetCount or type(targetCount) ~= "number" or targetCount < 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: AdjustObjectiveLineCount - 无效的目标数量: " .. tostring(targetCount))
        return false
    end

    -- 确保目标数量为整数
    targetCount = math.floor(targetCount)

    -- 获取当前CheckLines数组
    local currentCheckLines = parentFrame.CheckLines or {}
    local currentCount = #currentCheckLines

    DebugPrint("|cff00ff00ScenarioBlocks|r: 当前框架数量: " .. currentCount .. ", 目标数量: " .. targetCount)

    if currentCount == targetCount then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 框架数量已符合要求，无需调整")
        return true
    elseif currentCount < targetCount then
        -- 需要增加框架
        local addCount = targetCount - currentCount
        DebugPrint("|cff00ff00ScenarioBlocks|r: 需要增加 " .. addCount .. " 个框架")

        -- 创建新的框架
        for i = currentCount + 1, targetCount do
            local frameName = "ObjectiveTrackerCheckLine" .. i
            DebugPrint("|cff00ff00ScenarioBlocks|r: 创建新框架 " .. frameName)

            local frame = CreateFrame("Frame", frameName, parentFrame)
            frame:SetSize(232, 16)

            -- 垂直布局：锚定到前一个框架底部
            if i == 1 then
                frame:SetPoint("BOTTOMLEFT", parentFrame, "BOTTOMLEFT", 0, -20)
            else
                frame:SetPoint("TOPLEFT", currentCheckLines[i-1], "BOTTOMLEFT", 0, -3)
            end
            frame:Show()

            -- 创建文本组件
            local text = frame:CreateFontString(frameName .. "Text", "ARTWORK", "GameFontHighlight")
            text:SetPoint("TOPLEFT", frame, "TOPLEFT", 20, 0)
            text:SetText("目标 " .. i)
            frame.Text = text

            -- 创建图标锚点和图标
            local iconAnchor = frame:CreateTexture(nil, "ARTWORK")
            iconAnchor:SetSize(16, 16)
            iconAnchor:SetPoint("TOPLEFT", frame, "TOPLEFT", 0, 0)
            frame.IconAnchor = iconAnchor

            local icon = self:CreateAtlasTexture(frame, "Objective-Nub", "ARTWORK", 0, true)
            icon:SetPoint("CENTER", iconAnchor, "CENTER")
            frame.Icon = icon
            frame.IconIndex = 1

            -- ARTWORK: Glow
            local glow = frame:CreateTexture(nil, "ARTWORK")
            glow:SetTexture("Interface\\Scenarios\\Objective-Lineglow")
            glow:SetAlpha(0)
            glow:SetBlendMode("ADD")
            glow:SetSize(80, 0)
            glow:SetPoint("LEFT", text, "LEFT", -2, 0)
            glow:SetPoint("TOP", frame, "TOP", 0, 0)
            glow:SetPoint("BOTTOM", frame, "BOTTOM", 0, -4)
            frame.Glow = glow

            -- OVERLAY: CheckFlash
            local checkFlash = frame:CreateTexture(nil, "OVERLAY")
            checkFlash:SetTexture("Interface\\Scenarios\\ScenarioIcon-Check")
            checkFlash:SetAlpha(0)
            checkFlash:SetBlendMode("ADD")
            checkFlash:SetSize(16, 16)
            checkFlash:SetPoint("CENTER", icon, "CENTER")
            checkFlash:Hide()
            frame.CheckFlash = checkFlash

            -- OVERLAY: Sheen
            local sheen = frame:CreateTexture(nil, "OVERLAY")
            sheen:SetTexture("Interface\\Scenarios\\Objective-Sheen")
            sheen:SetAlpha(0)
            sheen:SetSize(32, 0)
            sheen:SetPoint("LEFT", glow, "LEFT")
            sheen:SetPoint("TOP", frame, "TOP", 0, 2)
            sheen:SetPoint("BOTTOM", frame, "BOTTOM", 0, -14)
            frame.Sheen = sheen

            -- 创建Sheen动画
            self:CreateGlowAnimation(frame)
            self:CreateCheckFlashAnimation(frame)
            self:CreateSheenAnimation(frame)

            -- -- 设置脚本事件
            -- frame:SetScript("OnLoad", function(self)
            --     local width = _G.OBJECTIVE_TRACKER_TEXT_WIDTH or 232
            --     if self.Text then self.Text:SetWidth(width) end
            -- end)
            -- frame:SetScript("OnHide", function(self)
            --     if ObjectiveTrackerCheckLine_OnHide then
            --         ObjectiveTrackerCheckLine_OnHide(self)
            --     else
            --         DebugPrint("|cffff0000ScenarioBlocks|r: ObjectiveTrackerCheckLine_OnHide 未定义")
            --     end
            -- end)
            
            frame:SetScript("OnShow", function(self)
                ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(self)
            end)

            --self:PlayAllCheckLineAnimations(frame)
            -- 添加到CheckLines数组
            table.insert(currentCheckLines, frame)
        end

        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功增加 " .. addCount .. " 个框架")

    else
        -- 需要减少框架
        local removeCount = currentCount - targetCount
        DebugPrint("|cff00ff00ScenarioBlocks|r: 需要隐藏 " .. removeCount .. " 个框架")

        -- 隐藏多余的框架（从后往前）
        for i = currentCount, targetCount + 1, -1 do
            if currentCheckLines[i] then
                currentCheckLines[i]:Hide()
                DebugPrint("|cff00ff00ScenarioBlocks|r: 隐藏框架 " .. i)
            end
        end

        DebugPrint("|cff00ff00ScenarioBlocks|r: 成功隐藏 " .. removeCount .. " 个框架")
    end

    -- 更新父框架的CheckLines引用
    parentFrame.CheckLines = currentCheckLines

    -- 调整bonusProgressBar的位置
    self:AdjustBonusProgressBarPosition(parentFrame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: 目标追踪线框架数量调整完成，当前总数: " .. #currentCheckLines)
    return true
end

-- 调整BonusProgressBar的位置以适应当前的Line框架布局
-- @param parentFrame Frame 父框架对象
function ScenarioBlocksFrameManager:AdjustBonusProgressBarPosition(parentFrame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始调整BonusProgressBar位置")

    -- 检查父框架是否存在
    if not parentFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: AdjustBonusProgressBarPosition - 父框架不存在")
        return
    end

    -- 检查是否存在BonusProgressBar
    local bonusProgressBar = parentFrame.BonusProgressBar
    if not bonusProgressBar then
        DebugPrint("|cff00ff00ScenarioBlocks|r: AdjustBonusProgressBarPosition - BonusProgressBar不存在，无需调整")
        return
    end

    -- 获取当前的CheckLines数组
    local checkLines = parentFrame.CheckLines or {}
    local visibleCheckLines = {}

    -- 找出所有可见的CheckLine框架
    for _, checkLine in ipairs(checkLines) do
        if checkLine and checkLine:IsShown() then
            table.insert(visibleCheckLines, checkLine)
        end
    end

    local visibleCount = #visibleCheckLines
    DebugPrint("|cff00ff00ScenarioBlocks|r: 找到 " .. visibleCount .. " 个可见的CheckLine框架")

    -- 清除现有的锚点
    bonusProgressBar:ClearAllPoints()

    if visibleCount > 0 then
        -- 如果有可见的CheckLine框架，将BonusProgressBar定位在最后一个可见框架的底部
        local lastVisibleFrame = visibleCheckLines[visibleCount]
        bonusProgressBar:SetPoint("TOPLEFT", lastVisibleFrame, "BOTTOMLEFT", 0, -8)
        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusProgressBar已重新定位到第" .. visibleCount .. "个CheckLine框架下方")
    else
        -- 如果没有可见的CheckLine框架，将BonusProgressBar定位在父框架顶部
        bonusProgressBar:SetPoint("TOPLEFT", parentFrame, "TOPLEFT", 0, -20)
        DebugPrint("|cff00ff00ScenarioBlocks|r: BonusProgressBar已重新定位到父框架顶部")
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusProgressBar位置调整完成")
end

-- 根据当前激活的场景模式类型，智能管理子框架的显示状态
-- @param activeMode number 当前激活的模式类型（1=阶段模式，2=试炼模式，3=挑战模式）
function ScenarioBlocksFrameManager:ManageScenarioBlockVisibility(activeMode)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始管理场景块框架显示状态，激活模式: " .. tostring(activeMode))

    -- 参数验证
    if not activeMode or type(activeMode) ~= "number" then
        DebugPrint("|cffff0000ScenarioBlocks|r: ManageScenarioBlockVisibility - 无效的模式参数: " .. tostring(activeMode))
        return false
    end

    -- 验证模式范围
    if activeMode < 1 or activeMode > 3 then
        DebugPrint("|cffff0000ScenarioBlocks|r: ManageScenarioBlockVisibility - 模式参数超出范围(1-3): " .. activeMode)
        return false
    end

    -- 验证TheScenarioFrame是否存在
    if not TheScenarioFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: ManageScenarioBlockVisibility - TheScenarioFrame不存在")
        return false
    end

    -- 获取各个子框架的引用
    local stageBlock = TheScenarioFrame.ScenarioStageBlock
    local provingBlock = TheScenarioFrame.ScenarioProvingGroundsBlock
    local challengeBlock = TheScenarioFrame.ScenarioChallengeModeBlock

    -- 根据激活模式管理框架显示状态
    if activeMode == 1 then
        -- 阶段模式：显示阶段框架，隐藏其他框架
        DebugPrint("|cff00ff00ScenarioBlocks|r: 激活阶段模式")

        -- -- 显示阶段框架
        -- if stageBlock then
        --     stageBlock:Show()
        --     DebugPrint("|cff00ff00ScenarioBlocks|r: 阶段框架已显示")
        -- else
        --     DebugPrint("|cffff0000ScenarioBlocks|r: 阶段框架不存在")
        -- end

        -- 隐藏试炼框架
        if provingBlock then
            provingBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 试炼框架已隐藏")
        end

        -- 隐藏挑战框架
        if challengeBlock then
            challengeBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战框架已隐藏")
        end

    elseif activeMode == 2 then
        -- 试炼模式：显示试炼框架，隐藏其他框架
        DebugPrint("|cff00ff00ScenarioBlocks|r: 激活试炼模式")

        -- 隐藏阶段框架
        if stageBlock then
            stageBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 阶段框架已隐藏")
        end

        -- -- 显示试炼框架
        -- if provingBlock then
        --     provingBlock:Show()
        --     DebugPrint("|cff00ff00ScenarioBlocks|r: 试炼框架已显示")
        -- else
        --     DebugPrint("|cffff0000ScenarioBlocks|r: 试炼框架不存在")
        -- end

        -- 隐藏挑战框架
        if challengeBlock then
            challengeBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战框架已隐藏")
        end

    elseif activeMode == 3 then
        -- 挑战模式：显示挑战框架，隐藏其他框架
        DebugPrint("|cff00ff00ScenarioBlocks|r: 激活挑战模式")

        -- 隐藏阶段框架
        if stageBlock then
            stageBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 阶段框架已隐藏")
        end

        -- 隐藏试炼框架
        if provingBlock then
            provingBlock:Hide()
            DebugPrint("|cff00ff00ScenarioBlocks|r: 试炼框架已隐藏")
        end

        -- -- 显示挑战框架
        -- if challengeBlock then
        --     challengeBlock:Show()
        --     DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战框架已显示")
        -- else
        --     DebugPrint("|cffff0000ScenarioBlocks|r: 挑战框架不存在")
        -- end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 场景块框架显示状态管理完成")
    return true
end

-- 显示或隐藏场景测试框架
-- @param headerText string 头部框架显示的文本内容（可选，默认为"天灾入侵"）
-- @param blockType string 要创建的子框架类型（可选，默认为"ChallengeModeBlock"）
-- @return boolean 操作是否成功
function ScenarioBlocksFrameManager:ShowOrHideScenarioTestFrames(headerText, blockType)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始显示或隐藏场景测试框架")

    -- 设置默认参数
    local displayText = headerText or "天灾入侵"
    local frameType = blockType or "ChallengeModeBlock"

    -- 动态计算位置偏移
    local BarRightWidth = _G["MultiBarRight"]:IsShown() and -50 or 0
    local BarLeftWidth = _G["MultiBarLeft"]:IsShown() and -50 or 0

    DebugPrint("|cff00ff00ScenarioBlocks|r: 位置偏移计算 - BarRightWidth: " .. BarRightWidth .. ", BarLeftWidth: " .. BarLeftWidth)

    -- 获取或创建 TheTrackerHeaderFrame
    if not TheTrackerHeaderFrame then
        TheTrackerHeaderFrame = GetTheTrackerHeaderFrame()
    end

    if not TheTrackerHeaderFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 请先使用 '/objheader show' 显示测试框架")
        return false
    end

    -- 设置头部框架文本和位置
    TheTrackerHeaderFrame.Text:SetText(displayText)
    TheTrackerHeaderFrame:SetPoint("RIGHT", UIParent, "RIGHT", (BarRightWidth + BarLeftWidth), 150)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 头部框架文本已设置为: " .. displayText)

    -- 创建或获取 TheScenarioFrame
    if not TheScenarioFrame then
        DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建测试框架...")
        -- 创建包含指定类型块的测试框架
        -- 支持类型: "ObjectiveBlock" "StageBlock" "ProvingGroundsBlock" "ChallengeModeBlock"
        TheScenarioFrame = CreateScenarioBlocksFrame(TheTrackerHeaderFrame, "TestScenarioBlocksFrame", frameType)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已创建，类型: " .. frameType)
    end

    if not TheScenarioFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架创建失败")
        return false
    end

    local IsHide = true
    if TheScenarioFrame then
        if frameType == "StageBlock" then
            if not TheScenarioFrame.ScenarioStageBlock then
                self:CreateScenarioStageBlock(TheScenarioFrame)
                IsHide = false
            end
            self:ManageScenarioBlockVisibility(1)
        elseif frameType == "ProvingGroundsBlock" then
            if not TheScenarioFrame.ScenarioProvingGroundsBlock then
                self:CreateScenarioProvingGroundsBlock(TheScenarioFrame)
                IsHide = false
            end
            self:ManageScenarioBlockVisibility(2)
        elseif frameType == "ChallengeModeBlock" then
            if not TheScenarioFrame.ScenarioChallengeModeBlock then
                self:CreateScenarioChallengeModeBlock(TheScenarioFrame)
                IsHide = false
            end
            self:ManageScenarioBlockVisibility(3)
        end
    end

    -- 设置场景框架位置
    TheScenarioFrame:SetPoint("BOTTOMLEFT", TheTrackerHeaderFrame, "BOTTOMLEFT", -15, -85)

    -- 切换显示/隐藏状态
    if TheTrackerHeaderFrame:IsShown() and TheScenarioFrame:IsShown() then
        if IsHide then
            TheTrackerHeaderFrame:Hide()
            TheScenarioFrame:Hide()
        end
        -- -- 如果当前都显示，则隐藏
        -- TheTrackerHeaderFrame:Hide()
        -- TheScenarioFrame:Hide()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已隐藏")
    else
        -- 如果当前隐藏，则显示
        TheTrackerHeaderFrame:Show()
        TheScenarioFrame:Show()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已显示")

        -- 测试功能调用（注释状态保持不变）
        if TheScenarioFrame.ScenarioChallengeModeBlock then
            -- StartChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock, 1) -- 可用
            -- self:AdjustObjectiveLineCount(TheScenarioFrame.ScenarioChallengeModeBlock, 7) -- 可用
            -- self:UpdateObjectiveLinesText(7, "测试目标1") --可用
            -- self:UpdateObjectiveLinesStats(1,2) -- 可用
            -- self:UpdateScenarioChallengeSpellIconButtons(676,nil,nil) -- 可用
            DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战模式块可用，测试功能已准备")
        end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 场景测试框架操作完成")
    return true
end

-- 更新演示目标追踪线的图标和进度条
function ScenarioBlocksFrameManager:UpdateObjectiveLinesStats(index, iconindex)
    local frame = nil

    if TheScenarioFrame then
        if TheScenarioFrame.ScenarioChallengeModeBlock then
            frame = TheScenarioFrame.ScenarioChallengeModeBlock
        elseif TheScenarioFrame.ScenarioStageBlock then
            frame = TheScenarioFrame.ScenarioStageBlock
        elseif TheScenarioFrame.ScenarioProvingGroundsBlock then
            frame = TheScenarioFrame.ScenarioProvingGroundsBlock
        end
    end

    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - 框架不存在")
        return
    end

    if index == 0 or index > #frame.CheckLines then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - index超出范围: " .. index)
        return
    end

    if iconindex == 0 or iconindex > #ICON_NAME_TABLE then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - iconindex超出范围: " .. iconindex)
        return
    end

    local mframe = frame.CheckLines[index]
    mframe.IconIndex = iconindex
    local left, right, top, bottom, width, height = GetObjectivesLineIconTexCoordAndSize(iconindex)
    if not left then
        local icon = ICON_NAME_TABLE[iconindex]
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - 获取图标坐标失败: " .. icon)
        return
    end
    mframe.Icon:SetTexCoord(left, right, top, bottom)
    mframe.Icon:SetSize(width, height)


    -- 获取进度条对象和标签对象
    local bonusProgressBar = frame.BonusProgressBar
    if not bonusProgressBar or not bonusProgressBar.Bar then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - BonusProgressBar不存在")
        return
    end

    local statusBar = bonusProgressBar.Bar
    local label = statusBar.Label

    if not statusBar or not label then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesStats - StatusBar或Label不存在")
        return
    end

    -- 计算IconIndex == 2的比重
    local totalCheckLines = #frame.CheckLines
    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateObjectiveLinesStats - 总目标数: " .. totalCheckLines)
    local completedCount = 0

    -- 遍历所有CheckLines，统计IconIndex == 2的数量
    for i = 1, totalCheckLines do
        local checkLine = frame.CheckLines[i]
        if checkLine and checkLine.IconIndex == 2 then
            completedCount = completedCount + 1
        end
    end

    -- 计算进度百分比
    local progressPercent = 0
    if totalCheckLines > 0 then
        progressPercent = (completedCount / totalCheckLines) * 100
    end

    -- 更新进度条数值
    statusBar:SetValue(progressPercent)

    -- 更新标签文本
    label:SetText(string.format("%.0f%%", progressPercent))

    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateObjectiveLinesStats - 进度更新: " .. completedCount .. "/" .. totalCheckLines .. " (" .. string.format("%.0f%%", progressPercent) .. ")")
end

-- 更新演示目标追踪线的文本内容
-- @param index number 目标行索引（1-based）
-- @param text string 新的文本内容
-- @return boolean 操作是否成功
function ScenarioBlocksFrameManager:UpdateObjectiveLinesText(index, text)
    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateObjectiveLinesText - 开始更新目标行文本")

    -- 参数验证 - 索引
    if not index or type(index) ~= "number" then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - 无效的索引参数: " .. tostring(index))
        return false
    end

    -- 参数验证 - 文本
    if not text or type(text) ~= "string" or text == "" then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - 无效的文本参数: " .. tostring(text))
        return false
    end

    -- 获取场景框架（参考UpdateObjectiveLinesStats的逻辑）
    local frame = nil

    if TheScenarioFrame then
        if TheScenarioFrame.ScenarioChallengeModeBlock then
            frame = TheScenarioFrame.ScenarioChallengeModeBlock
        elseif TheScenarioFrame.ScenarioStageBlock then
            frame = TheScenarioFrame.ScenarioStageBlock
        elseif TheScenarioFrame.ScenarioProvingGroundsBlock then
            frame = TheScenarioFrame.ScenarioProvingGroundsBlock
        end
    end

    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - 框架不存在")
        return false
    end

    -- 验证CheckLines是否存在
    if not frame.CheckLines then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - CheckLines不存在")
        return false
    end

    -- 验证索引范围
    if index <= 0 or index > #frame.CheckLines then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - index超出范围: " .. index .. " (有效范围: 1-" .. #frame.CheckLines .. ")")
        return false
    end

    -- 获取目标行框架
    local checkLine = frame.CheckLines[index]
    if not checkLine then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - CheckLine[" .. index .. "]不存在")
        return false
    end

    -- 验证Text对象是否存在
    if not checkLine.Text then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateObjectiveLinesText - CheckLine[" .. index .. "].Text不存在")
        return false
    end

    -- 更新文本内容
    checkLine.Text:SetText(text)

    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateObjectiveLinesText - 文本更新成功: CheckLine[" .. index .. "] = \"" .. text .. "\"")

    return true
end

-- 动态更新挑战模式框架中的法术图标按钮
-- @param spellId1 number 第一个按钮的法术ID
-- @param spellId2 number 第二个按钮的法术ID
-- @param spellId3 number 第三个按钮的法术ID
function ScenarioBlocksFrameManager:UpdateScenarioChallengeSpellIconButtons(spellId1, spellId2, spellId3)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 开始更新法术图标按钮")

    -- 获取挑战模式框架（参考UpdateObjectiveLinesStats的逻辑）
    local frame = nil

    if TheScenarioFrame then
        if TheScenarioFrame.ScenarioChallengeModeBlock then
            frame = TheScenarioFrame.ScenarioChallengeModeBlock
        end
    end

    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateScenarioChallengeSpellIconButtons - 框架不存在")
        return
    end

    if not frame.SpellIconButtons then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateScenarioChallengeSpellIconButtons - SpellIconButtons不存在")
        return
    end

    -- 法术ID数组
    local spellIds = {spellId1, spellId2, spellId3}

    -- 更新每个按钮
    for i = 1, 3 do
        local button = frame.SpellIconButtons[i]
        local spellId = spellIds[i]

        if button and spellId then
            -- 获取新的法术图标
            local spellName, _, icon = GetSpellInfo(spellId)

            if icon then
                -- 更新按钮图标纹理
                if button.Icon then
                    button.Icon:SetTexture(icon)
                else
                    -- 如果图标纹理不存在，创建新的
                    local iconTexture = button:CreateTexture(nil, "ARTWORK")
                    iconTexture:SetAllPoints(button)
                    iconTexture:SetTexture(icon)
                    button.Icon = iconTexture
                end

                -- 更新按钮的法术ID属性
                button.spellId = spellId

                DebugPrint("|cff00ff00ScenarioBlocks|r: 按钮" .. i .. "更新为法术: " .. (spellName or "未知法术") .. " (ID:" .. spellId .. ")")
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: 无法获取法术ID " .. spellId .. " 的图标")
            end
        else
            if not button then
                DebugPrint("|cffff0000ScenarioBlocks|r: 按钮" .. i .. "不存在")
            end
            if not spellId then
                DebugPrint("|cffff0000ScenarioBlocks|r: 按钮" .. i .. "的法术ID为空")
            end
        end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 法术图标按钮更新完成")
end

-- 创建ScenarioProvingGroundsBlock框架
function ScenarioBlocksFrameManager:CreateScenarioProvingGroundsBlock(parent)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock")

    local frame = CreateFrame("Frame", "ScenarioProvingGroundsBlock", parent)
    frame:SetSize(201, 77) -- 对应XML中的Size x="201" y="77"
    frame:SetPoint("LEFT", parent, "LEFT", 0, 0)
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建层级结构
    self:CreateProvingGroundsBlockLayers(frame)
    self:CreateProvingGroundsBlockFrames(frame)

    -- 创建演示用的目标追踪检查线框架
    self:CreateDemoObjectiveLines(frame, OBJECTIVES_COUNT, OBJECTIVES_TABLE)

    parent.ScenarioProvingGroundsBlock = frame
    return frame
end

-- 创建ScenarioProvingGroundsBlock的层级结构
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockLayers(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的层级结构")
    -- BACKGROUND层
    local bg = self:CreateAtlasTexture(frame, "ScenarioTrackerToast", "BACKGROUND", 0, true)
    bg:SetPoint("TOPLEFT", frame, 0, 0)
    frame.BG = bg -- 对应XML中的parentKey="BG"

    -- BORDER层
    local goldCurlies = self:CreateAtlasTexture(frame, "ScenarioTrackerToast-FinalFiligree", "BORDER", 0, true)
    goldCurlies:SetPoint("TOPLEFT", frame, 4, -4)
    frame.GoldCurlies = goldCurlies -- 对应XML中的parentKey="GoldCurlies"

    -- ARTWORK层
    self:CreateProvingGroundsArtworkLayer(frame)
end

-- 创建ScenarioProvingGroundsBlock的ARTWORK层
function ScenarioBlocksFrameManager:CreateProvingGroundsArtworkLayer(frame)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的ARTWORK层")
    -- MedalIcon纹理
    local medalIcon = frame:CreateTexture(nil, "ARTWORK")
    medalIcon:SetSize(52, 52) -- 对应XML中的Size x="52" y="52"
    medalIcon:SetPoint("LEFT", frame, "LEFT", 5, -1)
    medalIcon:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    medalIcon:SetTexture("Interface\\Challenges\\challenges-plat") -- 对应XML中的file属性
    frame.MedalIcon = medalIcon -- 对应XML中的parentKey="MedalIcon"

    -- WaveLabel字体字符串
    local waveLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    waveLabel:SetPoint("TOPLEFT", medalIcon, "TOPRIGHT", 1, -4)
    waveLabel:SetText("波次：") -- 对应XML中的text属性
    waveLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color r="1.0" g="0.82" b="0"
    waveLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    waveLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.WaveLabel = waveLabel -- 对应XML中的parentKey="WaveLabel"

    -- Wave字体字符串
    local wave = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    wave:SetPoint("BOTTOMLEFT", waveLabel, "BOTTOMRIGHT", 4, -1)
    wave:SetText("90") -- 对应XML中的text="0"
    frame.Wave = wave -- 对应XML中的parentKey="Wave"

    -- ScoreLabel字体字符串
    local scoreLabel = frame:CreateFontString(nil, "ARTWORK", "QuestFont_Large")
    scoreLabel:SetPoint("TOPLEFT", waveLabel, "BOTTOMLEFT", 0, -3)
    scoreLabel:SetText("分数：") -- 对应XML中的text属性
    scoreLabel:Show() -- 对应XML中的hidden="true"
    scoreLabel:SetTextColor(1.0, 0.82, 0) -- 对应XML中的Color
    scoreLabel:SetShadowColor(0, 0, 0) -- 对应XML中的Shadow Color
    scoreLabel:SetShadowOffset(1, -1) -- 对应XML中的Shadow Offset
    frame.ScoreLabel = scoreLabel -- 对应XML中的parentKey="ScoreLabel"

    -- Score字体字符串
    local score = frame:CreateFontString(nil, "ARTWORK", "GameFontHighlightLarge")
    score:SetPoint("BOTTOMLEFT", scoreLabel, "BOTTOMRIGHT", 4, -1)
    score:SetText("999") -- 对应XML中的text="0"
    score:Show() -- 对应XML中的hidden="true"
    frame.Score = score -- 对应XML中的parentKey="Score"
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的ARTWORK层完成")
end

-- 创建ScenarioProvingGroundsBlock的子框架
function ScenarioBlocksFrameManager:CreateProvingGroundsBlockFrames(frame)
    -- 创建StatusBar
    local statusBar = CreateFrame("StatusBar", nil, frame)
    statusBar:SetPoint("BOTTOM", frame, 40, 10)
    statusBar:SetSize(177, 10) -- 对应XML中的Size x="177" y="15"
    -- statusBar:SetParentLevel(true) -- 对应XML中的useParentLevel="true"，在3.3.5中可能不支持
    frame.StatusBar = statusBar -- 对应XML中的parentKey="StatusBar"

    -- 创建StatusBar的层级
    self:CreateProvingGroundsStatusBarLayers(statusBar)

    -- 设置状态栏纹理和颜色
    statusBar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar") -- 对应XML中的BarTexture file
    statusBar:SetStatusBarColor(0, 0.33, 0.61) -- 对应XML中的BarColor r="0" g="0.33" b="0.61"
    statusBar:SetMinMaxValues(0, 1000)
    statusBar:SetValue(300)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建ScenarioProvingGroundsBlock的子框架完成！！！")
end

-- 创建试炼场状态栏的层级
function ScenarioBlocksFrameManager:CreateProvingGroundsStatusBarLayers(statusBar)
    -- OVERLAY层
    local borderTexture = self:CreateAtlasTexture(statusBar, "challenges-timerborder", "OVERLAY", 0, false)
    borderTexture:SetSize(184, 20) -- 对应XML中的Size x="184" y="25"
    borderTexture:SetPoint("CENTER", statusBar, "CENTER", 0, 0)

    local timeLeft = statusBar:CreateFontString(nil, "OVERLAY", "GameFontHighlight")
    timeLeft:SetJustifyH("CENTER") -- 对应XML中的justifyH="CENTER"
    statusBar.TimeLeft = timeLeft -- 对应XML中的parentKey="TimeLeft"
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建试炼场状态栏的层级完成")
end

-- 设置ScenarioBlocksFrame的脚本事件
function ScenarioBlocksFrameManager:SetupScenarioBlocksFrameScripts(frame)
    -- OnLoad事件
    frame:SetScript("OnLoad", function(self)
        if ScenarioBlocksFrame_OnLoad then
            ScenarioBlocksFrame_OnLoad(self)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnLoad函数未定义")
        end
    end)

    -- OnEvent事件
    frame:SetScript("OnEvent", function(self, event, ...)
        if ScenarioBlocksFrame_OnEvent then
            ScenarioBlocksFrame_OnEvent(self, event, ...)
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: ScenarioBlocksFrame_OnEvent函数未定义")
        end
    end)
end

-- 挑战模式时间结束提示
function ScenarioBlocksFrameManager:Scenario_ChallengeMode_TimesUpLootStatus_OnEnter(frame)
	local block = frame:GetParent();

	GameTooltip:SetOwner(frame, "ANCHOR_RIGHT");
	GameTooltip:SetText("时间结束", 1, 1, 1);
	local line;
	if (block.wasDepleted) then
		if (UnitIsGroupLeader("player")) then
			line = "你的钥石无法升级，且你在完成此地下城后将无法获得战利品宝箱。你可以右键点击头像并选择“重置史诗地下城”来重新开始挑战。";
		else
			line = "你的钥石无法升级，且你在完成此地下城后将无法获得战利品宝箱。小队队长可以右键点击头像并选择“重置史诗地下城”来重新开始挑战。";
		end
	else
		line = "你的钥石无法升级。但你完成此地下城后仍可获得战利品宝箱。";
	end
	GameTooltip:AddLine(line, nil, nil, nil, true);
	GameTooltip:Show();
end

-- 设置试炼场的波次、分数和进度条
function ScenarioBlocksFrameManager:SetScenarioProvingWaveAndScoreAndStatusBar(frame, wavestr, scorestr, statusbarvalue)
    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetScenarioProvingWaveAndScore - 框架不存在")
        return
    end

    if not frame.Wave or not frame.Score or not frame.StatusBar then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetScenarioProvingWaveAndScore - 波次或分数标签不存在")
        return
    end

    frame.Wave:SetText(wavestr)
    frame.Score:SetText(scorestr)
    frame.StatusBar:SetValue(statusbarvalue)
end

-- 公共API函数：更新挑战模式倒计时数据
function UpDateChallengeModeCountdownData(totalSeconds, remainingSeconds, warningThreshold)
    ChallengeModCountdownData.totalSeconds = totalSeconds
    ChallengeModCountdownData.remainingSeconds = remainingSeconds
    ChallengeModCountdownData.warningThreshold = warningThreshold
end

-- 公共API函数：更新试炼场显示信息
-- @param currwaveNumber number 当前波次（数字）
-- @param currentScore number 当前得分（数字）
-- @param progressValue number 进度条数值（0-100的百分比）
-- @return boolean 操作是否成功
function UpdateProvingGroundsDisplay(currwaveNumber, totalWaveNumber,  currentScore, progressValue)
    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateProvingGroundsDisplay - 开始更新试炼场显示")

    -- 参数验证
    if not currwaveNumber then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 无效的波次参数: " .. tostring(currwaveNumber))
        return false
    end

    if not totalWaveNumber then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 无效的总波次参数: " .. tostring(totalWaveNumber))
        return false
    end

    if not currentScore then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 无效的得分参数: " .. tostring(currentScore))
        return false
    end

    if not progressValue then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 无效的进度条参数: " .. tostring(progressValue))
        return false
    end

    -- 验证进度条数值范围
    if progressValue < 0 or progressValue > 100 then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 进度条数值超出范围(0-100): " .. progressValue)
        return false
    end

    -- 获取当前活动的场景框架
    local frame = nil
    if TheScenarioFrame then
        if TheScenarioFrame.ScenarioProvingGroundsBlock then
            frame = TheScenarioFrame.ScenarioProvingGroundsBlock
        end
    end

    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: UpdateProvingGroundsDisplay - 未找到有效的场景框架")
        return false
    end

    local waveText = currwaveNumber .. "/" .. totalWaveNumber
    local scoreText = currentScore

    -- 调用内部方法更新显示
    ScenarioBlocksFrameManager:SetScenarioProvingWaveAndScoreAndStatusBar(frame, waveText, scoreText, progressValue)

    DebugPrint("|cff00ff00ScenarioBlocks|r: UpdateProvingGroundsDisplay - 更新完成: " .. waveText .. ", " .. scoreText .. ", 进度: " .. progressValue .. "%")

    return true
end

-- 便捷函数：创建ScenarioBlocksFrame实例
-- @param parentFrame Frame 父框架
-- @param frameName string 框架名称（可选）
-- @param blockTypes string 要创建的子框架类型（可选），可选值："ObjectiveBlock", "StageBlock", "ChallengeModeBlock", "ProvingGroundsBlock"
-- @return Frame 创建的ScenarioBlocksFrame
function CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
    if not parentFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 父框架不能为空")
        return nil
    end
    return ScenarioBlocksFrameManager:CreateScenarioBlocksFrame(parentFrame, frameName, blockTypes)
end

-- 全局API：中文子框架类型名称转换为英文blockType
-- @param chineseName string 中文的子框架类型名称
-- @return string|nil 对应的英文blockType字符串，匹配失败时返回nil
--
-- 支持的中文名称映射：
-- "目标块" 或 "目标框架" → "ObjectiveBlock"
-- "阶段块" 或 "阶段框架" → "StageBlock"
-- "挑战模式块" 或 "挑战模式框架" → "ChallengeModeBlock"
-- "试炼场块" 或 "试炼场框架" → "ProvingGroundsBlock"
--
-- 使用示例：
-- local blockType = GetScenarioBlockTypeFromChineseName("阶段块")
-- if blockType then
--     print("转换结果: " .. blockType) -- 输出: "StageBlock"
-- end
function GetScenarioBlockTypeFromChineseName(chineseName)
    -- 参数验证
    if not chineseName then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数不能为空")
        return nil
    end

    if type(chineseName) ~= "string" then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数必须是字符串，当前类型: " .. type(chineseName))
        return nil
    end

    if string.len(chineseName) == 0 then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetScenarioBlockTypeFromChineseName - 参数不能为空字符串")
        return nil
    end

    -- 调用内部实现
    return ScenarioBlocksFrameManager:GetBlockTypeFromChineseName(chineseName)
end

-- 全局API：转换中文子框架类型名称为英文blockType
-- @param chineseNames string|table 中文子框架类型名称（字符串）或名称数组（向后兼容）
-- @return string|nil 转换后的英文blockType字符串，如果输入无效或转换失败则返回nil
--
-- 使用示例：
-- local blockType = ConvertChineseNamesToBlockTypes("阶段块")
-- if blockType then
--     -- blockType = "StageBlock"
--     CreateScenarioBlocksFrame(parent, "MyFrame", blockType)
-- end
--
-- 向后兼容示例（取第一个有效的）：
-- local blockType = ConvertChineseNamesToBlockTypes({"阶段块", "试炼场框架"})
-- if blockType then
--     -- blockType = "StageBlock"（第一个有效的）
--     CreateScenarioBlocksFrame(parent, "MyFrame", blockType)
-- end
function ConvertChineseNamesToBlockTypes(chineseNames)
    -- 参数验证
    if not chineseNames then
        DebugPrint("|cffff0000ScenarioBlocks|r: ConvertChineseNamesToBlockTypes - 参数不能为空")
        return nil
    end

    -- 调用内部实现
    return ScenarioBlocksFrameManager:ConvertChineseNamesToBlockTypes(chineseNames)
end

-- 全局API：启动挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @param minutes number 倒计时分钟数（可选，默认40分钟）
-- @return boolean 是否成功启动
--
-- 使用示例：
-- local success = StartChallengeModeCountdown(challengeFrame, 30) -- 启动30分钟倒计时
-- if success then
--     print("倒计时已启动")
-- end
function StartChallengeModeCountdown(challengeModeFrame, minutes)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: StartChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    local initialSeconds = nil
    if minutes and type(minutes) == "number" and minutes > 0 then
        initialSeconds = minutes * 60
    end

    ScenarioBlocksFrameManager:StartCountdown(challengeModeFrame, initialSeconds)
    return true
end

-- 全局API：暂停挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功暂停
function PauseChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: PauseChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:PauseCountdown(challengeModeFrame)
    return true
end

-- 全局API：恢复挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功恢复
function ResumeChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: ResumeChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:ResumeCountdown(challengeModeFrame)
    return true
end

-- 全局API：停止挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功停止
function StopChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: StopChallengeModeCountdown - 框架参数不能为空")
        return false
    end

    ScenarioBlocksFrameManager:StopCountdown(challengeModeFrame)
    return true
end

-- 全局API：重置挑战模式倒计时
-- @param challengeModeFrame Frame 挑战模式框架
-- @return boolean 是否成功重置
function ResetChallengeModeCountdown(challengeModeFrame)
    if not challengeModeFrame then
        DebugPrint("|cffff0000ScenarioBlocks|r: ResetChallengeModeCountdown - 框架参数不能为空")
        return false
    end
    DebugPrint("|cff00ff00ScenarioBlocks|r: ResetChallengeModeCountdown - 重置倒计时")
    ScenarioBlocksFrameManager:ResetCountdown(challengeModeFrame)
    return true
end

-- 全局API：获取挑战模式倒计时剩余时间
-- @param challengeModeFrame Frame 挑战模式框架
-- @return number|nil 剩余秒数，失败时返回nil
function GetChallengeModeCountdownTime(challengeModeFrame)
    if not challengeModeFrame or not challengeModeFrame.countdownData then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetChallengeModeCountdownTime - 框架或倒计时数据无效")
        return nil
    end

    return challengeModeFrame.countdownData.remainingSeconds
end

-- 全局API：设置挑战模式进度条联动状态
-- @param challengeModeFrame Frame 挑战模式框架
-- @param enabled boolean 是否启用联动
-- @return boolean 是否成功设置
function SetChallengeModeProgressBarLinked(challengeModeFrame, enabled)
    if not challengeModeFrame or not challengeModeFrame.StatusBar then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetChallengeModeProgressBarLinked - 框架或进度条无效")
        return false
    end

    challengeModeFrame.StatusBar.countdownLinked = enabled
    DebugPrint("|cff00ff00ScenarioBlocks|r: 进度条联动状态已设置为: " .. tostring(enabled))

    -- 如果启用联动，立即更新一次进度条
    if enabled and challengeModeFrame.countdownData then
        ScenarioBlocksFrameManager:UpdateCountdownProgressBar(challengeModeFrame)
    end

    return true
end

-- 全局API：获取挑战模式进度条当前值
-- @param challengeModeFrame Frame 挑战模式框架
-- @return number|nil 进度条当前值（0-1000），失败时返回nil
function GetChallengeModeProgressBarValue(challengeModeFrame)
    if not challengeModeFrame or not challengeModeFrame.StatusBar then
        DebugPrint("|cffff0000ScenarioBlocks|r: GetChallengeModeProgressBarValue - 框架或进度条无效")
        return nil
    end

    return challengeModeFrame.StatusBar:GetValue()
end

-- 全局API：设置TheScenarioFrame的值
function SetScenarioBlocksFrameManagerTheScenarioFrame(frame)
    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: SetScenarioBlocksFrameManagerTheScenarioFrame - 框架参数不能为空")
        return
    end

    TheScenarioFrame = frame
end

-- 注册斜杠命令的函数
function ScenarioBlocksFrameManager:RegisterSlashCommands()
    -- 注册 /scenario 命令
    SLASH_SCENARIOTEST1 = "/scenario"
    SLASH_SCENARIOTEST2 = "/testscenario"

    SlashCmdList["SCENARIOTEST"] = function(msg)
        DebugPrint("ScenarioBlocks: 收到命令参数: '" .. (msg or "nil") .. "'")
        local cmd = string.lower(msg or "")

        if cmd == "show" or cmd == "" then
            -- UpDateChallengeModeCountdownData(2*60, 2*60, 30)
            local BarRightWidth = _G["MultiBarRight"]:IsShown() and -50 or 0
            local BarLeftWidth = _G["MultiBarLeft"]:IsShown() and -50 or 0

            if not TheTrackerHeaderFrame then
                TheTrackerHeaderFrame = GetTheTrackerHeaderFrame()
            end
            
            if not TheTrackerHeaderFrame then
                DebugPrint("|cffff0000ScenarioBlocks|r: 请先使用 '/objheader show' 显示测试框架")
                return
            end

            TheTrackerHeaderFrame.Text:SetText("天灾入侵")
            TheTrackerHeaderFrame:SetPoint("RIGHT", UIParent, "RIGHT", (BarRightWidth + BarLeftWidth), 150)

            -- 创建或显示测试框架
            if not TheScenarioFrame then
                DebugPrint("|cff00ff00ScenarioBlocks|r: 开始创建测试框架...")
                -- 创建包含挑战模式块的测试框架，以便测试倒计时功能
                -- "ObjectiveBlock" "StageBlock" "ProvingGroundsBlock" "ChallengeModeBlock" 
                TheScenarioFrame = CreateScenarioBlocksFrame(TheTrackerHeaderFrame, "TestScenarioBlocksFrame", "ChallengeModeBlock")
            end

            if not TheScenarioFrame then
                DebugPrint("|cffff0000ScenarioBlocks|r: 请先使用 '/objheader show' 显示测试框架")
                return
            end
            TheScenarioFrame:SetPoint("BOTTOMLEFT", TheTrackerHeaderFrame, "BOTTOMLEFT", -15, -90)
            if TheTrackerHeaderFrame:IsShown() and TheScenarioFrame:IsShown() then
                TheTrackerHeaderFrame:Hide()
                TheScenarioFrame:Hide()
            else
                TheTrackerHeaderFrame:Show()
                TheScenarioFrame:Show()
                if TheScenarioFrame.ScenarioChallengeModeBlock then
                    -- StartChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock, 1) -- 可用
                    -- self:AdjustObjectiveLineCount(TheScenarioFrame.ScenarioChallengeModeBlock, 7) -- 可用
                    -- self:UpdateObjectiveLinesText(7, "测试目标1") --可用
                    -- self:UpdateObjectiveLinesStats(1,2) -- 可用
                    -- self:UpdateScenarioChallengeSpellIconButtons(676,nil,nil) -- 可用
                end
            end

            DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已显示")

        elseif cmd == "hide" then
            -- 隐藏测试框架
            if TheScenarioFrame then
                TheScenarioFrame:Hide()
                DebugPrint("|cff00ff00ScenarioBlocks|r: 测试框架已隐藏")
            else
                DebugPrint("|cffff0000ScenarioBlocks|r: 测试框架不存在")
            end

        elseif cmd == "debug on" then
            -- 开启调试模式
            DEBUG_ENABLED = true
            print("|cff00ff00ScenarioBlocks|r: 调试模式已开启")

        elseif cmd == "debug off" then
            -- 关闭调试模式
            DEBUG_ENABLED = false
            print("|cff00ff00ScenarioBlocks|r: 调试模式已关闭")
        
        elseif cmd == "setcount" then
            OBJECTIVES_COUNT = 10
        elseif cmd == "timer start" then
            -- 启动倒计时测试
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local challengeFrame = TheScenarioFrame.ScenarioChallengeModeBlock
                StartChallengeModeCountdown(challengeFrame, 5) -- 测试用5分钟
                print("|cff00ff00ScenarioBlocks|r: 倒计时已启动（5分钟测试）")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer pause" then
            -- 暂停倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                PauseChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已暂停")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer resume" then
            -- 恢复倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                ResumeChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已恢复")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer stop" then
            -- 停止倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                StopChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已停止")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer reset" then
            -- 重置倒计时
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                ResetChallengeModeCountdown(TheScenarioFrame.ScenarioChallengeModeBlock)
                print("|cff00ff00ScenarioBlocks|r: 倒计时已重置")
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "timer status" then
            -- 查看倒计时状态
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local remainingTime = GetChallengeModeCountdownTime(TheScenarioFrame.ScenarioChallengeModeBlock)
                local progressValue = GetChallengeModeProgressBarValue(TheScenarioFrame.ScenarioChallengeModeBlock)
                if remainingTime and progressValue then
                    local timeText = ScenarioBlocksFrameManager:FormatTime(remainingTime)
                    local progressPercent = (progressValue / 1000) * 100
                    print("|cff00ff00ScenarioBlocks|r: 倒计时剩余时间: " .. timeText)
                    print("|cff00ff00ScenarioBlocks|r: 进度条进度: " .. string.format("%.1f%%", progressPercent))
                else
                    print("|cffff0000ScenarioBlocks|r: 无法获取倒计时状态")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "progress toggle" then
            -- 切换进度条联动状态
            if TheScenarioFrame and TheScenarioFrame.ScenarioChallengeModeBlock then
                local statusBar = TheScenarioFrame.ScenarioChallengeModeBlock.StatusBar
                if statusBar then
                    local newState = not statusBar.countdownLinked
                    SetChallengeModeProgressBarLinked(TheScenarioFrame.ScenarioChallengeModeBlock, newState)
                    print("|cff00ff00ScenarioBlocks|r: 进度条联动已" .. (newState and "启用" or "禁用"))
                else
                    print("|cffff0000ScenarioBlocks|r: 进度条不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim glow" then
            -- 测试Glow动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayGlowAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放Glow动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim check" then
            -- 测试CheckFlash动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayCheckFlashAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放CheckFlash动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim sheen" then
            -- 测试Sheen动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlaySheenAnimation(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放Sheen动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif cmd == "anim all" then
            -- 测试所有动画
            if TheScenarioFrame and TheScenarioFrame.CheckLines then
                local checkLine = TheScenarioFrame.CheckLines[1] -- 使用第一个CheckLine
                if checkLine then
                    ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(checkLine)
                    print("|cff00ff00ScenarioBlocks|r: 播放所有CheckLine动画")
                else
                    print("|cffff0000ScenarioBlocks|r: CheckLine不存在")
                end
            else
                print("|cffff0000ScenarioBlocks|r: 请先显示测试框架")
            end

        elseif string.match(cmd, "^update%s+") then
            -- 测试UpdateObjectiveLinesStats功能
            -- 命令格式: /scenario update <index> <iconindex>
            local params = string.match(cmd, "^update%s+(.+)")
            if params then
                local index, iconindex = string.match(params, "^(%d+)%s+(%d+)$")
                if index and iconindex then
                    index = tonumber(index)
                    iconindex = tonumber(iconindex)

                    -- 参数验证
                    if index < 1 or index > OBJECTIVES_COUNT then
                        print("|cffff0000ScenarioBlocks|r: index参数错误，应为1-" .. OBJECTIVES_COUNT .. "，当前值: " .. index)
                        return
                    end

                    if iconindex < 1 or iconindex > #ICON_NAME_TABLE then
                        print("|cffff0000ScenarioBlocks|r: iconindex参数错误，应为1-" .. #ICON_NAME_TABLE .. "，当前值: " .. iconindex)
                        print("|cff00ff00ScenarioBlocks|r: 可用图标索引: 1=" .. ICON_NAME_TABLE[1] .. ", 2=" .. ICON_NAME_TABLE[2] .. ", 3=" .. ICON_NAME_TABLE[3])
                        return
                    end

                    -- 检查测试框架是否存在
                    if not TheScenarioFrame then
                        print("|cffff0000ScenarioBlocks|r: 请先使用 '/scenario show' 显示测试框架")
                        return
                    end

                    -- 调用UpdateObjectiveLinesStats方法
                    ScenarioBlocksFrameManager:UpdateObjectiveLinesStats(index, iconindex)

                    -- 显示操作结果反馈
                    local iconName = ICON_NAME_TABLE[iconindex]
                    print("|cff00ff00ScenarioBlocks|r: 已更新CheckLine[" .. index .. "]的图标为: " .. iconName .. " (索引:" .. iconindex .. ")")
                    print("|cff00ff00ScenarioBlocks|r: 进度条和标签已同步更新")
                else
                    print("|cffff0000ScenarioBlocks|r: update命令参数格式错误")
                    print("|cff00ff00ScenarioBlocks|r: 正确格式: /scenario update <index> <iconindex>")
                    print("|cff00ff00ScenarioBlocks|r: 示例: /scenario update 1 2")
                    print("|cff00ff00ScenarioBlocks|r: index范围: 1-" .. OBJECTIVES_COUNT .. ", iconindex范围: 1-" .. #ICON_NAME_TABLE)
                end
            else
                print("|cffff0000ScenarioBlocks|r: update命令缺少参数")
                print("|cff00ff00ScenarioBlocks|r: 正确格式: /scenario update <index> <iconindex>")
            end

        elseif string.match(cmd, "^testframes") then
            -- 测试ShowOrHideScenarioTestFrames功能
            -- 命令格式: /scenario testframes [headerIndex] [blockTypeIndex]
            local params = string.match(cmd, "^testframes%s*(.*)")

            -- 头部文本映射表
            local headerTexts = {
                [1] = "天灾入侵",
                [2] = "试炼场测试",
                [3] = "挑战模式",
                [4] = "阶段任务",
                [5] = "自定义场景"
            }

            -- 框架类型映射表
            local blockTypes = {
                [1] = "ChallengeModeBlock",
                [2] = "ProvingGroundsBlock",
                [3] = "StageBlock",
                [4] = "ObjectiveBlock"
            }

            local headerText = nil
            local blockType = nil

            if params and params ~= "" then
                -- 解析参数
                local headerIndex, blockTypeIndex = string.match(params, "^(%d+)%s*(%d*)$")

                if headerIndex then
                    headerIndex = tonumber(headerIndex)

                    -- 验证头部文本索引
                    if headerIndex >= 1 and headerIndex <= #headerTexts then
                        headerText = headerTexts[headerIndex]
                    else
                        print("|cffff0000ScenarioBlocks|r: 头部文本索引错误，应为1-" .. #headerTexts .. "，当前值: " .. headerIndex)
                        print("|cff00ff00ScenarioBlocks|r: 可用选项:")
                        for i, text in ipairs(headerTexts) do
                            print("  " .. i .. " = " .. text)
                        end
                        return
                    end

                    -- 解析框架类型索引（可选）
                    if blockTypeIndex and blockTypeIndex ~= "" then
                        blockTypeIndex = tonumber(blockTypeIndex)

                        if blockTypeIndex >= 1 and blockTypeIndex <= #blockTypes then
                            blockType = blockTypes[blockTypeIndex]
                        else
                            print("|cffff0000ScenarioBlocks|r: 框架类型索引错误，应为1-" .. #blockTypes .. "，当前值: " .. blockTypeIndex)
                            print("|cff00ff00ScenarioBlocks|r: 可用选项:")
                            for i, type in ipairs(blockTypes) do
                                print("  " .. i .. " = " .. type)
                            end
                            return
                        end
                    end
                else
                    print("|cffff0000ScenarioBlocks|r: testframes命令参数格式错误")
                    print("|cff00ff00ScenarioBlocks|r: 正确格式: /scenario testframes [headerIndex] [blockTypeIndex]")
                    print("|cff00ff00ScenarioBlocks|r: 示例: /scenario testframes 2 3")
                    return
                end
            end

            -- 调用测试函数
            local success = ScenarioBlocksFrameManager:ShowOrHideScenarioTestFrames(headerText, blockType)

            if success then
                local usedHeader = headerText or "天灾入侵(默认)"
                local usedBlock = blockType or "ChallengeModeBlock(默认)"
                print("|cff00ff00ScenarioBlocks|r: 测试框架操作完成")
                print("  头部文本: " .. usedHeader)
                print("  框架类型: " .. usedBlock)
            else
                print("|cffff0000ScenarioBlocks|r: 测试框架操作失败")
            end

        else
            -- 显示帮助信息
            print("|cff00ff00ScenarioBlocks 测试命令:|r")
            print("  /scenario show - 显示测试框架")
            print("  /scenario hide - 隐藏测试框架")
            print("  /scenario debug on - 开启调试模式")
            print("  /scenario debug off - 关闭调试模式")
            print("  |cffFFD700倒计时测试命令:|r")
            print("  /scenario timer start - 启动倒计时（5分钟测试）")
            print("  /scenario timer pause - 暂停倒计时")
            print("  /scenario timer resume - 恢复倒计时")
            print("  /scenario timer stop - 停止倒计时")
            print("  /scenario timer reset - 重置倒计时")
            print("  /scenario timer status - 查看倒计时和进度条状态")
            print("  |cff00FFFF进度条测试命令:|r")
            print("  /scenario progress toggle - 切换进度条联动状态")
            print("  |cffFF69B4目标更新测试命令:|r")
            print("  /scenario update <index> <iconindex> - 更新CheckLine图标和进度条")
            print("    index: CheckLine索引 (1-" .. OBJECTIVES_COUNT .. ")")
            print("    iconindex: 图标索引 (1=" .. ICON_NAME_TABLE[1] .. ", 2=" .. ICON_NAME_TABLE[2] .. ", 3=" .. ICON_NAME_TABLE[3] .. ")")
            print("    示例: /scenario update 1 2 (将第1个目标设为已完成)")
            print("  |cffADFF2F框架测试命令:|r")
            print("  /scenario testframes [headerIndex] [blockTypeIndex] - 测试场景框架显示")
            print("    headerIndex: 头部文本 (1=天灾入侵, 2=试炼场测试, 3=挑战模式, 4=阶段任务, 5=自定义场景)")
            print("    blockTypeIndex: 框架类型 (1=ChallengeModeBlock, 2=ProvingGroundsBlock, 3=StageBlock, 4=ObjectiveBlock)")
            print("    示例: /scenario testframes 2 3 (试炼场测试 + 阶段框架)")
        end
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 斜杠命令已注册 - /scenario, /testscenario")
end

-- 解析来自C++端的场景模式数据消息
-- @param message string 消息字符串，格式: mode#data1#data2#...（使用#分隔符）
-- @param sender string 发送者
-- @return table|nil 解析后的数据表，解析失败时返回nil
function ScenarioBlocksFrameManager:ParseScenarioModeData(message, sender)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 原始消息: " .. (message or "空"))

    -- 参数验证
    if not message or type(message) ~= "string" or message == "" then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 消息为空或无效")
        return nil
    end

    -- 使用 "#" 分隔符分割消息
    local parts = {}
    for part in string.gmatch(message, "([^#]+)") do
        table.insert(parts, part)
    end

    -- 验证消息至少包含模式标识
    if #parts < 1 then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 消息格式无效，缺少模式标识")
        return nil
    end

    -- 解析模式类型
    local mode = tonumber(parts[1])
    if not mode then
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 无效的模式标识: " .. (parts[1] or "nil"))
        return nil
    end

    -- 初始化数据表
    local data = {
        modeType = mode
    }

    -- 根据模式类型进行不同的数据处理
    if mode == 1 then
        -- 阶段模式 (STAGE)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 解析阶段模式数据")
        data.modeType = "STAGE"

        -- TODO: 在这里添加阶段模式特定的数据解析逻辑
        -- 例如：阶段编号、阶段名称、目标描述等

    elseif mode == 2 then
        -- 试炼模式 (PROVING)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 解析试炼模式数据")
        data.modeType = "PROVING"

        -- TODO: 在这里添加试炼模式特定的数据解析逻辑
        -- 例如：当前波次#总波次#当前得分#文本描述1#文本描述2#....#文本描述n


    elseif mode == 3 then
        -- 挑战模式 (CHALLENGE)
        DebugPrint("|cff00ff00ScenarioBlocks|r: 解析挑战模式数据")
        data.modeType = "CHALLENGE"

        -- TODO: 在这里添加挑战模式特定的数据解析逻辑
        -- 例如：等级、时间限制、词缀等

    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 错误 - 未知的模式类型: " .. mode)
        return nil
    end

    -- 输出解析结果用于调试
    DebugPrint("|cff00ff00ScenarioBlocks|r: 场景模式数据解析成功:")
    DebugPrint("  模式类型: " .. data.modeType .. " (ID: " .. data.mode .. ")")
    DebugPrint("  消息字段数量: " .. #parts)

    return data
end

-- 处理来自C++端的Addon消息
function ScenarioBlocksFrameManager:OnChatMsgAddon(prefix, message, _, sender)
    if prefix == "SBFM_STAGE" then     -- 阶段
        -- 处理阶段模式消息（模式1）
        local data = self:ParseScenarioModeData("1#" .. (message or ""), sender)
        if data then
            DebugPrint("|cff00ff00ScenarioBlocks|r: 阶段模式数据处理完成")
            -- 管理框架显示状态
            self:ManageScenarioBlockVisibility(1)
            -- TODO: 在这里添加具体的阶段模式业务逻辑
            self:ShowOrHideScenarioTestFrames("阶段模式", "StageBlock")
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 阶段模式数据解析失败")
        end

    elseif prefix == "SBFM_PROVING" then -- 试炼
        -- 处理试炼模式消息（模式2）
        local data = self:ParseScenarioModeData("2#" .. (message or ""), sender)
        if data then
            DebugPrint("|cff00ff00ScenarioBlocks|r: 试炼模式数据处理完成")
            -- 管理框架显示状态
            self:ManageScenarioBlockVisibility(2)
            self:ShowOrHideScenarioTestFrames("试炼模式", "ProvingGroundsBlock")
            -- TODO: 在这里添加具体的试炼模式业务逻辑
            -- UpdateProvingGroundsDisplay(1,2,3,30);
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 试炼模式数据解析失败")
        end

    elseif prefix == "SBFM_CHALLENGE" then -- 挑战
        -- 处理挑战模式消息（模式3）
        local data = self:ParseScenarioModeData("3#" .. (message or ""), sender)
        if data then
            DebugPrint("|cff00ff00ScenarioBlocks|r: 挑战模式数据处理完成")
            -- 管理框架显示状态
            self:ManageScenarioBlockVisibility(3)
            -- TODO: 在这里添加具体的挑战模式业务逻辑
            self:ShowOrHideScenarioTestFrames("挑战模式", "ChallengeModeBlock")
        else
            DebugPrint("|cffff0000ScenarioBlocks|r: 挑战模式数据解析失败")
        end
    end
end

-- 事件处理函数
function ScenarioBlocksFrameManager:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        DebugPrint("ScenarioBlocks addonName: " .. addonName)
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            DebugPrint("|cff00ff00ScenarioBlocks|r: 插件初始化完成")
            -- 注册斜杠命令
            self:RegisterSlashCommands()
        end
    elseif event == "CHAT_MSG_ADDON" then
        self:OnChatMsgAddon(...)
    end
end

-- ========================================
-- BonusTrackerProgressBar模板实现
-- ========================================

-- 创建BonusTrackerProgressBar（从BonusTrackerProgressBarTemplate XML转换）
-- @param parent Frame 父框架
-- @param name string 框架名称（可选）
-- @return Frame 创建的进度条框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBar(parent, name)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar")

    -- 创建主框架 - 对应XML中的Frame
    local frameName = name or ("BonusTrackerProgressBar" .. math.random(1000, 9999))
    local frame = CreateFrame("Frame", frameName, parent)
    frame:SetSize(192, 38) -- 对应XML中的Size x="192" y="38"
    frame:SetPoint("CENTER") -- 对应XML中的Anchor point="CENTER"
    frame:Show() -- 对应XML中的hidden="true"

    -- 创建StatusBar子框架 - 对应XML中的StatusBar parentKey="Bar"
    local statusBar = CreateFrame("StatusBar", nil, frame)
    statusBar:SetSize(191, 17) -- 对应XML中的Size x="191" y="17"
    statusBar:SetPoint("LEFT", frame, "LEFT", 10, 0) -- 对应XML中的Anchor point="LEFT" x="10" y="0"
    statusBar:SetMinMaxValues(0, 100) -- 对应XML中的minValue="0" maxValue="100"
    statusBar:SetValue(0) -- 对应XML中的defaultValue="0"
    frame.Bar = statusBar -- 对应XML中的parentKey="Bar"

    -- local sbarFrame = self:CreateAtlasTexture(frame, "bonusobjectives-bar-frame", "OVERLAY", 0, true)
    -- sbarFrame:SetPoint("LEFT", frame, "LEFT", -8, -1) -- 对应XML中的Anchor point="LEFT" x="-8" y="-1"

    -- 设置StatusBar的纹理和颜色
    statusBar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar") -- 对应XML中的BarTexture
    statusBar:SetStatusBarColor(0.26, 0.42, 1) -- 对应XML中的BarColor r="0.26" g="0.42" b="1"

    -- 创建StatusBar的层级结构
    self:CreateBonusTrackerProgressBarLayers(statusBar)

    -- 创建StatusBar的动画
    self:CreateBonusTrackerProgressBarAnimations(statusBar)

    self:PlayBonusTrackerProgressBarAnimation(frame)

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar创建完成")
    return frame
end

-- 创建BonusTrackerProgressBar的层级结构
-- @param statusBar StatusBar StatusBar框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBarLayers(statusBar)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar层级结构")

    -- BACKGROUND层 textureSubLevel="-1"
    -- BarBG纹理
    local barBG = statusBar:CreateTexture(nil, "BACKGROUND", nil, -1)
    -- barBG:SetColorTexture(0.04, 0.07, 0.18) -- 对应XML中的Color r="0.04" g="0.07" b="0.18"
    barBG:SetAllPoints(statusBar)
    statusBar.BarBG = barBG -- 对应XML中的parentKey="BarBG"

    -- Icon纹理
    local icon = statusBar:CreateTexture(nil, "BACKGROUND", nil, -1)
    icon:SetSize(32, 32) -- 对应XML中的Size x="32" y="32"
    icon:SetPoint("RIGHT", statusBar, "RIGHT", 33, 2) -- 对应XML中的Anchor point="RIGHT" x="33" y="2"
    statusBar.Icon = icon -- 对应XML中的parentKey="Icon"

    -- ARTWORK层 level="ARTWORK"
    -- BarFrame纹理
    local barFrame = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 0, true)
    barFrame:SetPoint("LEFT", statusBar, "LEFT", -8, -1) -- 对应XML中的Anchor point="LEFT" x="-8" y="-1"
    statusBar.BarFrame = barFrame -- 对应XML中的parentKey="BarFrame"

    -- IconBG纹理
    local iconBG = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-ring", "ARTWORK", 0, true)
    iconBG:SetPoint("RIGHT", barFrame, "RIGHT", 0, 0) -- 对应XML中的Anchor point="RIGHT" relativeKey="$parent.BarFrame"
    statusBar.IconBG = iconBG -- 对应XML中的parentKey="IconBG"

    -- Label字体字符串
    local label = statusBar:CreateFontString(nil, "ARTWORK", "GameFontHighlightMedium")
    label:SetJustifyH("CENTER") -- 对应XML中的justifyH="CENTER"
    label:SetPoint("CENTER", statusBar, "CENTER", -1, -1) -- 对应XML中的Anchor point="CENTER" x="-1" y="-1"
    label:SetText("0%") -- 对应XML中的text="Label"
    statusBar.Label = label -- 对应XML中的parentKey="Label"

    -- ARTWORK层 textureSubLevel="1"
    -- BarFrame2纹理
    local barFrame2 = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 1, true)
    barFrame2:SetPoint("CENTER", barFrame, "CENTER") -- 对应XML中的Anchor point="CENTER" relativeKey="$parent.BarFrame"
    barFrame2:SetAlpha(0) -- 对应XML中的alpha="0"
    barFrame2:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarFrame2 = barFrame2 -- 对应XML中的parentKey="BarFrame2"

    -- ARTWORK层 textureSubLevel="2"
    -- BarFrame3纹理
    local barFrame3 = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-frame", "ARTWORK", 2, true)
    barFrame3:SetPoint("CENTER", barFrame, "CENTER") -- 对应XML中的Anchor point="CENTER" relativeKey="$parent.BarFrame"
    barFrame3:SetAlpha(0) -- 对应XML中的alpha="0"
    barFrame3:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarFrame3 = barFrame3 -- 对应XML中的parentKey="BarFrame3"

    -- OVERLAY层
    -- BarGlow纹理
    local barGlow = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-glow", "OVERLAY", 0, true)
    barGlow:SetPoint("LEFT", statusBar, "LEFT", -8, -1) -- 对应XML中的Anchor point="LEFT" x="-8" y="-1"
    barGlow:SetAlpha(0) -- 对应XML中的alpha="0"
    barGlow:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.BarGlow = barGlow -- 对应XML中的parentKey="BarGlow"

    -- Sheen纹理
    local sheen = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-sheen", "OVERLAY", 0, false)
    sheen:SetSize(97, 22) -- 对应XML中的Size x="97" y="22"
    sheen:SetPoint("LEFT", barFrame, "LEFT", -60, 0) -- 对应XML中的Anchor point="LEFT" relativeKey="$parent.BarFrame" x="-60" y="0"
    sheen:SetAlpha(0) -- 对应XML中的alpha="0"
    sheen:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.Sheen = sheen -- 对应XML中的parentKey="Sheen"

    -- OVERLAY层 textureSubLevel="1"
    -- Starburst纹理
    local starburst = self:CreateAtlasTexture(statusBar, "bonusobjectives-bar-starburst", "OVERLAY", 1, true)
    starburst:SetPoint("TOPRIGHT", barFrame, "TOPRIGHT", 1, 6) -- 对应XML中的Anchor point="TOPRIGHT" relativeKey="$parent.BarFrame" x="1" y="6"
    starburst:SetAlpha(0) -- 对应XML中的alpha="0"
    starburst:SetBlendMode("ADD") -- 对应XML中的alphaMode="ADD"
    statusBar.Starburst = starburst -- 对应XML中的parentKey="Starburst"

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar层级结构创建完成")
end

-- 创建BonusTrackerProgressBar的动画组
-- @param statusBar StatusBar StatusBar框架
function ScenarioBlocksFrameManager:CreateBonusTrackerProgressBarAnimations(statusBar)
    DebugPrint("|cff00ff00ScenarioBlocks|r: 创建BonusTrackerProgressBar动画")

    -- 创建主StatusBar的AnimIn动画组 - 对应XML中的AnimationGroup parentKey="AnimIn"
    local animIn = statusBar:CreateAnimationGroup()
    statusBar.AnimIn = animIn -- 对应XML中的parentKey="AnimIn"

    -- 主框架Alpha动画：duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local mainAlpha = animIn:CreateAnimation("Alpha")
    mainAlpha:SetDuration(0.1)
    mainAlpha:SetOrder(1)
    mainAlpha:SetChange(1) -- 从0变为1，变化量为+1

    statusBar.AnimIn:SetScript("OnFinished", function(self)
        statusBar:SetAlpha(1)
    end)

    -- 创建BarGlow的动画组
    local barGlowAnim = statusBar.BarGlow:CreateAnimationGroup()
    statusBar.BarGlow.Anim = barGlowAnim

    -- BarGlow Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="0.5"
    local barGlowAlpha1 = barGlowAnim:CreateAnimation("Alpha")
    barGlowAlpha1:SetStartDelay(1.34)
    barGlowAlpha1:SetDuration(0.53)
    barGlowAlpha1:SetOrder(1)
    barGlowAlpha1:SetChange(0.5) -- 从0变为0.5，变化量为+0.5

    -- BarGlow Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="0.5" toAlpha="0"
    local barGlowAlpha2 = barGlowAnim:CreateAnimation("Alpha")
    barGlowAlpha2:SetStartDelay(1.87)
    barGlowAlpha2:SetDuration(0.53)
    barGlowAlpha2:SetOrder(1)
    barGlowAlpha2:SetChange(-0.5) -- 从0.5变为0，变化量为-0.5

    -- 创建Starburst的动画组
    local starburstAnim = statusBar.Starburst:CreateAnimationGroup()
    statusBar.Starburst.Anim = starburstAnim

    -- Starburst Scale动画1：startDelay="1" duration="0.1" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"
    local starburstScale1 = starburstAnim:CreateAnimation("Scale")
    starburstScale1:SetStartDelay(1)
    starburstScale1:SetDuration(0.1)
    starburstScale1:SetOrder(1)
    starburstScale1:SetScale(0.5, 0.5) -- 缩放到0.5倍

    -- Starburst Scale动画2：startDelay="1.34" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="2" toScaleY="2"
    local starburstScale2 = starburstAnim:CreateAnimation("Scale")
    starburstScale2:SetStartDelay(1.34)
    starburstScale2:SetDuration(0.5)
    starburstScale2:SetOrder(1)
    starburstScale2:SetScale(2, 2) -- 缩放到2倍

    -- Starburst Scale动画3：startDelay="1.84" duration="0.5" order="1" fromScaleX="1" fromScaleY="1" toScaleX="0.5" toScaleY="0.5"
    local starburstScale3 = starburstAnim:CreateAnimation("Scale")
    starburstScale3:SetStartDelay(1.84)
    starburstScale3:SetDuration(0.5)
    starburstScale3:SetOrder(1)
    starburstScale3:SetScale(0.5, 0.5) -- 缩放到0.5倍

    -- Starburst Alpha动画1：startDelay="1.34" duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local starburstAlpha1 = starburstAnim:CreateAnimation("Alpha")
    starburstAlpha1:SetStartDelay(1.34)
    starburstAlpha1:SetDuration(0.1)
    starburstAlpha1:SetOrder(1)
    starburstAlpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- Starburst Alpha动画2：startDelay="1.44" duration="0.9" order="1" fromAlpha="1" toAlpha="0"
    local starburstAlpha2 = starburstAnim:CreateAnimation("Alpha")
    starburstAlpha2:SetStartDelay(1.44)
    starburstAlpha2:SetDuration(0.9)
    starburstAlpha2:SetOrder(1)
    starburstAlpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- Starburst Rotation动画1：startDelay="1" duration="0.1" order="1" degrees="-41"
    local starburstRotation1 = starburstAnim:CreateAnimation("Rotation")
    starburstRotation1:SetStartDelay(1)
    starburstRotation1:SetDuration(0.1)
    starburstRotation1:SetOrder(1)
    starburstRotation1:SetDegrees(-41)

    -- Starburst Rotation动画2：startDelay="1.2" duration="1.41" order="1" degrees="-35"
    local starburstRotation2 = starburstAnim:CreateAnimation("Rotation")
    starburstRotation2:SetStartDelay(1.2)
    starburstRotation2:SetDuration(1.41)
    starburstRotation2:SetOrder(1)
    starburstRotation2:SetDegrees(-35)

    -- 创建BarFrame2的动画组
    local barFrame2Anim = statusBar.BarFrame2:CreateAnimationGroup()
    statusBar.BarFrame2.Anim = barFrame2Anim

    -- BarFrame2 Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="1"
    local barFrame2Alpha1 = barFrame2Anim:CreateAnimation("Alpha")
    barFrame2Alpha1:SetStartDelay(1.34)
    barFrame2Alpha1:SetDuration(0.53)
    barFrame2Alpha1:SetOrder(1)
    barFrame2Alpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- BarFrame2 Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="1" toAlpha="0"
    local barFrame2Alpha2 = barFrame2Anim:CreateAnimation("Alpha")
    barFrame2Alpha2:SetStartDelay(1.87)
    barFrame2Alpha2:SetDuration(0.53)
    barFrame2Alpha2:SetOrder(1)
    barFrame2Alpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- 创建BarFrame3的动画组
    local barFrame3Anim = statusBar.BarFrame3:CreateAnimationGroup()
    statusBar.BarFrame3.Anim = barFrame3Anim

    -- BarFrame3 Alpha动画1：startDelay="1.34" duration="0.53" order="1" fromAlpha="0" toAlpha="1"
    local barFrame3Alpha1 = barFrame3Anim:CreateAnimation("Alpha")
    barFrame3Alpha1:SetStartDelay(1.34)
    barFrame3Alpha1:SetDuration(0.53)
    barFrame3Alpha1:SetOrder(1)
    barFrame3Alpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- BarFrame3 Alpha动画2：startDelay="1.87" duration="0.53" order="1" fromAlpha="1" toAlpha="0"
    local barFrame3Alpha2 = barFrame3Anim:CreateAnimation("Alpha")
    barFrame3Alpha2:SetStartDelay(1.87)
    barFrame3Alpha2:SetDuration(0.53)
    barFrame3Alpha2:SetOrder(1)
    barFrame3Alpha2:SetChange(-1) -- 从1变为0，变化量为-1

    -- 创建Sheen的动画组
    local sheenAnim = statusBar.Sheen:CreateAnimationGroup()
    statusBar.Sheen.Anim = sheenAnim

    -- Sheen Translation动画：startDelay="1.06" duration="0.48" order="1" offsetX="68" offsetY="0"
    local sheenTranslation = sheenAnim:CreateAnimation("Translation")
    sheenTranslation:SetStartDelay(1.06)
    sheenTranslation:SetDuration(0.48)
    sheenTranslation:SetOrder(1)
    sheenTranslation:SetOffset(68, 0)

    -- Sheen Alpha动画1：startDelay="1.09" duration="0.1" order="1" fromAlpha="0" toAlpha="1"
    local sheenAlpha1 = sheenAnim:CreateAnimation("Alpha")
    sheenAlpha1:SetStartDelay(1.09)
    sheenAlpha1:SetDuration(0.1)
    sheenAlpha1:SetOrder(1)
    sheenAlpha1:SetChange(1) -- 从0变为1，变化量为+1

    -- Sheen Alpha动画2：startDelay="1.34" duration="0.05" order="1" fromAlpha="1" toAlpha="0"
    local sheenAlpha2 = sheenAnim:CreateAnimation("Alpha")
    sheenAlpha2:SetStartDelay(1.34)
    sheenAlpha2:SetDuration(0.05)
    sheenAlpha2:SetOrder(1)
    sheenAlpha2:SetChange(-1) -- 从1变为0，变化量为-1

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar动画创建完成")
end

-- 播放BonusTrackerProgressBar的所有动画
-- @param frame Frame BonusTrackerProgressBar框架
function ScenarioBlocksFrameManager:PlayBonusTrackerProgressBarAnimation(frame)
    if not frame or not frame.Bar then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放BonusTrackerProgressBar动画 - 框架不存在")
        return
    end

    local statusBar = frame.Bar
    DebugPrint("|cff00ff00ScenarioBlocks|r: 播放BonusTrackerProgressBar所有动画")

    -- 播放主StatusBar的AnimIn动画
    if statusBar.AnimIn then
        statusBar.AnimIn:Play()
    end

    -- 播放BarGlow动画
    if statusBar.BarGlow and statusBar.BarGlow.Anim then
        statusBar.BarGlow.Anim:Play()
    end

    -- 播放Starburst动画
    if statusBar.Starburst and statusBar.Starburst.Anim then
        statusBar.Starburst.Anim:Play()
    end

    -- 播放BarFrame2动画
    if statusBar.BarFrame2 and statusBar.BarFrame2.Anim then
        statusBar.BarFrame2.Anim:Play()
    end

    -- 播放BarFrame3动画
    if statusBar.BarFrame3 and statusBar.BarFrame3.Anim then
        statusBar.BarFrame3.Anim:Play()
    end

    -- 播放Sheen动画
    if statusBar.Sheen and statusBar.Sheen.Anim then
        statusBar.Sheen.Anim:Play()
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: BonusTrackerProgressBar所有动画已启动")
end

-- 创建框架用于事件处理
local eventFrame = CreateFrame("Frame")
eventFrame:SetScript("OnEvent", function(_, event, ...)
    ScenarioBlocksFrameManager:OnEvent(event, ...)
end)

-- 注册事件
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("CHAT_MSG_ADDON")

-- ========================================
-- 动画播放便捷函数
-- ========================================

-- 播放ObjectiveTrackerCheckLine的Glow动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayGlowAnimation(frame)
    if frame and frame.Glow and frame.Glow.Anim then
        frame.Glow.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放Glow动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放Glow动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的CheckFlash动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayCheckFlashAnimation(frame)
    if frame and frame.CheckFlash and frame.CheckFlash.Anim then
        frame.CheckFlash:Show() -- 显示CheckFlash纹理
        frame.CheckFlash.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放CheckFlash动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放CheckFlash动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的Sheen动画
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlaySheenAnimation(frame)
    if frame and frame.Sheen and frame.Sheen.Anim then
        frame.Sheen.Anim:Play()
        DebugPrint("|cff00ff00ScenarioBlocks|r: 播放Sheen动画")
    else
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放Sheen动画 - 框架或动画不存在")
    end
end

-- 播放ObjectiveTrackerCheckLine的所有动画（完整的目标完成效果）
-- @param frame Frame ObjectiveTrackerCheckLine框架
function ScenarioBlocksFrameManager:PlayAllCheckLineAnimations(frame)
    if not frame then
        DebugPrint("|cffff0000ScenarioBlocks|r: 无法播放动画 - 框架不存在")
        return
    end

    DebugPrint("|cff00ff00ScenarioBlocks|r: 播放所有CheckLine动画效果")

    -- 播放Glow动画
    self:PlayGlowAnimation(frame)

    -- 播放CheckFlash动画
    self:PlayCheckFlashAnimation(frame)

    -- 播放Sheen动画
    self:PlaySheenAnimation(frame)
end