--- 将一个数值限制在指定的最小值和最大值之间。
-- 此函数确保返回的值不会小于最小值也不会大于最大值。
-- 如果未提供最小值和最大值，则默认将值限制在0到1之间。
--
-- @param value 数值，需要被限制在最小值和最大值之间。
-- @param minValue 最小值，如果未提供，则默认为0。
-- @param maxValue 最大值，如果未提供，则默认为1。
-- @return 限制后的数值。
function clamp(value, minValue, maxValue)
    -- 如果未提供最小值，则将最小值设为0，最大值设为1。
    if not minValue then
        minValue = 0
        maxValue = 1
    end
    -- 确保返回的值不会小于最小值也不会大于最大值。
    return min(max(minValue, value), max(minValue, maxValue))
end


-- 定义一个线性插值函数
-- 该函数用于计算两个数值之间的线性插值结果
-- 参数:
--   startValue: 插值的起始值
--   endValue: 插值的结束值
--   a: 插值的因子，表示插值的程度，应在0到1之间
-- 返回值:
--   返回插值的结果如果起始值和结束值相等，则直接返回该值
function lerp(startValue, endValue, a)
    -- 当起始值与结束值相等时，直接返回起始值
    -- 这是因为在此情况下，无论插值因子a为何值，结果都不会改变
    if startValue == endValue then
        return startValue
    end

    -- 将插值因子a限制在0到1的范围内
    -- 这是为了确保插值结果位于起始值和结束值之间
    a = clamp(a, 0, 1)

    -- 计算并返回插值结果
    -- 该公式根据插值因子a，将结果从起始值平滑过渡到结束值
    return (startValue + (endValue - startValue) * a)
end

-- 定义EaseIn函数，用于实现逐渐进入的动画效果。
-- 该函数通过改变时间变量t的指数来调整动画的速度。
-- 参数t是动画的当前时间，通常在0到1之间，表示动画的进度。
-- 参数exp是指数，决定了动画速度变化的速率，默认值为2。
-- 返回值是调整后的动画进度。
function EaseIn(t, exp)
    exp = exp or 2 -- 如果未提供exp参数，则默认为2。
    return t ^ exp -- 返回t的exp次幂，实现逐渐进入的效果。
end

-- 定义EaseOut函数，用于实现逐渐退出的动画效果。
-- 类似于EaseIn，但动画效果是先快后慢。
-- 参数t和exp的意义与EaseIn中相同。
-- 返回值是调整后的动画进度。
function EaseOut(t, exp)
    exp = exp or 2 -- 如果未提供exp参数，则默认为2。
    return 1 - ((1 - t) ^ exp) -- 计算逐渐退出的动画效果。
end

-- 定义EaseInOut函数，结合EaseIn和EaseOut，实现先逐渐进入再逐渐退出的动画效果。
-- 该函数通过线性插值(lerp)结合EaseIn和EaseOut的结果，平滑地从逐渐进入过渡到逐渐退出。
-- 参数t和exp的意义与前两个函数中相同。
-- 返回值是调整后的动画进度，平滑地从0过渡到1。
function EaseInOut(t, exp)
    -- 使用lerp函数，根据t的值选择性地靠近EaseIn或EaseOut的结果。
    -- 这里没有直接提供lerp函数的实现，假设它已经定义在其他地方。
    return lerp(EaseIn(t, exp), EaseOut(t, exp), t)
end

function SetParentArray(frame, key, pos)
    if not frame or not frame.GetParent then return end
    
    local parent = frame:GetParent()
    if not parent then return end

    if not parent[key] then
        parent[key] = {}
    end

    if pos then
        tinsert(parent[key], pos, frame)
    else
        tinsert(parent[key], frame)
    end
    
    return parent[key]
end

function ClearAndSetPoint(target, ...)
    target:ClearAllPoints()
    target:SetPoint(...)
end

local AtlasDataTabs = 
{
	["_draft-border-h"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.00537109375, 0.0146484375, 0.0107421875, 0.029296875, false, false },
	["draft-border-tl"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.0166015625, 0.0263671875, 0.0107421875, 0.029296875, false, false },
	["!draft-border-v"] = { "Interface\\Draft\\DraftAtlas", 20, 20, 0.0283203125, 0.0380859375, 0.0107421875, 0.029296875, false, false },
	["draft-cost-rune"] = { "Interface\\Draft\\DraftAtlas", 55, 53, 0.0048828125, 0.03173828, 0.033203125, 0.0849609375, false, false },
	["draft-name-highlight"] = { "Interface\\Draft\\DraftAtlas", 258, 64, 0.0400390625, 0.166015625, 0.009765625, 0.072265625, false, false },
	["draft-ring"] = { "Interface\\Draft\\DraftAtlas", 80, 80, 0.0048828125, 0.0439453125, 0.0869140625, 0.1650390625, false, false },
	["draft-rarity-1-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.53955078125, 0.669921875, 0.61328125, 0.990234375, false, false },
	["draft-rarity-2-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.0048828125, 0.135253906, 0.61328125, 0.990234375, false, false },
	["draft-rarity-3-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.13818359375, 0.2685546875, 0.61328125, 0.990234375, false, false },
	["draft-rarity-4-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.27294921875, 0.4033203125, 0.61328125, 0.990234375, false, false },
	["draft-rarity-5-highlight"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.40625, 0.53662109375, 0.61328125, 0.990234375, false, false },
	["draft-filigree"] = { "Interface\\Draft\\DraftAtlas", 50, 71, 0.07373046875, 0.09814453125, 0.0849609375, 0.154296875, false, false },
	["draft-card-back"] = { "Interface\\Draft\\DraftAtlas", 267, 386, 0.73095703125, 0.9951171875, 0.232421875, 0.990234375, false, false },
	["draft-card-back-glow"] = { "Interface\\Draft\\DraftAtlas", 288, 401, 0.0048828125, 0.1455078125, 0.2109375, 0.6025390625, false, false },
}

function SetTexCoordSize(target,str)
    local tab = AtlasDataTabs[str]
    target:SetTexture(tab[1])
    target:SetSize(tab[2],tab[3])
    target:SetTexCoord(tab[4],tab[5],tab[6],tab[7])
end

ITEM_QUALITY_COLORS = {}
-- idk why but the old UIParent version included -1, 8 and 9
ITEM_QUALITY_COLORS[-1] = CreateColor(1, 1, 1) -- invalid (White)
ITEM_QUALITY_COLORS[0] = CreateColor(0.62, 0.62, 0.62) -- Poor (Grey)
ITEM_QUALITY_COLORS[1] = CreateColor(1, 1, 1) -- Common (White)
ITEM_QUALITY_COLORS[2] = CreateColor(0.12, 1, 0) -- Uncommon (Green)
ITEM_QUALITY_COLORS[3] = CreateColor(0, 0.44, 0.87) -- Rare (Blue)
ITEM_QUALITY_COLORS[4] = CreateColor(0.64, 0.21, 0.93) -- Epic (Purple)
ITEM_QUALITY_COLORS[5] = CreateColor(1, 0.5, 0) -- Legendary (Orange)
ITEM_QUALITY_COLORS[6] = CreateColor(0.9, 0.8, 0.5) -- Artifact / Vanity (Light Gold)
ITEM_QUALITY_COLORS[7] = CreateColor(0.9, 0.8, 0.5) -- Artifact / Vanity (Light Gold)
ITEM_QUALITY_COLORS[8] = CreateColor(0, 0.8, 1) -- Heirloom (Light Blue)
ITEM_QUALITY_COLORS[9] = CreateColor(0, 0.8, 1) -- Heirloom (Light Blue)