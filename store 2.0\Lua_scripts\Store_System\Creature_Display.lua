local AIO = AIO or require("AIO")

if AIO.AddAddon() then
    return
end

function MountJournal_QueryCheck(entry)
SendAddonMessage("CMSG_QUERY_CREATURE_ID", entry, "WHISPER", UnitName("player"))
end

function MountJournal_OnEvent( self, event, arg1, ... )
    if event == "PLAYER_LOGIN" then
        MountJournal_CreateData()
		print("Login")
    elseif event == "CHAT_MSG_ADDON" and arg1 == "SMSG_REFRESH_MOUNT_SCENE" then
        MountJournal_UpdateDisplayScene()
		print("refresh")
    end
end

function MountJournal_UpdateDisplayScene()
    MountJournal.MountDisplay.ModelScene:Hide()
    MountJournal.MountDisplay.ModelScene:Show()
end