local c1 = 1

local b = {}

local PageShowInfo,SupermarketButton,SupermarketOffButton,SupermarketFrame,testbutton,s1,s2,s3,s4,s5,s6

local b1,b2,bb1,bb2,bb3,bb4,bb5,fs,fs1,fs2,fs3,fs4,fs5,fs6,fs7,fs8,fs9,fs10

local onoff = false

--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--无法实现"|T"..服务器发送过来路径..":20|t" 
--无法实现"|T"..服务器发送过来路径..":20|t" 
--无法实现"|T"..服务器发送过来路径..":20|t"
--服务器发来的文字显示是可以改变的详情看函数内的注解 
--服务器发来的文字显示是可以改变的详情看函数内的注解
--服务器发来的文字显示是可以改变的详情看函数内的注解

local T = 
{
	ParentFrameBackgroundLeft	= "Interface\\ICONS\\progress_frame_left",
	ParentFrameBackgroundRight	= "Interface\\ICONS\\progress_frame_right",
	ParentFrameTitle			= "Interface\\ICONS\\bt",
	SubFrameBackground			= "Interface\\ICONS\\yuanjiaolan",
	TabClassFrameBackground		= "Interface\\ICONS\\LineTab1",
	ItemClassButton				= "Interface\\ICONS\\_button_h",
	FontBackground				= "Interface\\ICONS\\languangditu",
	ButtonHighlighLight1		= "Interface\\BUTTONS\\CheckButtonHilight",
	ButtonHighlighLight2		= "Interface\\ICONS\\button_h2",
	ButtonHighlighLight3		= "Interface\\BUTTONS\\UI-Listbox-Highlight",
	Font						= "Interface\\Fonts\\FRIZQT_.TTF",
	SupermarketButton			= "Interface\\ICONS\\shangchengon",
	SupermarketOffButton		= "Interface\\ICONS\\shangchengoff",
	JiFen						= "Interface\\ICONS\\jf1",
	JinBi						= "Interface\\ICONS\\jinbi",
	Vip							= "Interface\\ICONS\\Vip",
	ItemExchange				= "Interface\\ICONS\\wpdh",
	AlliancePVP					= "Interface\\ICONS\\lmry",
	HordePVP					= "Interface\\ICONS\\blry",
	AlliancePVPPoint			= "Interface\\ICONS\\lmryd",
	HordePVPPoint				= "Interface\\ICONS\\blryd",
	ArenaPVP					= "Interface\\ICONS\\jjc",
	ArenaPVPPoint				= "Interface\\ICONS\\jjd",
	EXP							= "Interface\\ICONS\\bigxp",
	XP							= "Interface\\ICONS\\xp"
}


function CreateParentFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)
		
local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	
	f:SetSize(Length, Width)
	if InheritsFrame == nil then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 32, insets = { left = 10, right = 10, top = 10, bottom = 10 }});
	end
	f:SetPoint(Point,Ofsx,Ofsy)
	--只有主窗口才能移动
	if ParentFrame == UIParent then
		f:RegisterForDrag("LeftButton")
		f:SetToplevel(true)
		f:SetClampedToScreen(true)
		f:SetMovable(true)
		f:EnableMouse(true)
		f:SetScript("OnDragStart", f.StartMoving)
		f:SetScript("OnHide", f.StopMovingOrSizing)
		f:SetScript("OnDragStop", f.StopMovingOrSizing)
	end
	if ParentFrame == UIParent then
		f:Hide()
	end
	
	if ParentFrame == SupermarketFrame then
		f:SetBackdropColor(255/255,255/255,255/255,0.25)
		f:Hide()
	end
	
	return f
end


s1 = CreateParentFrame("Frame","s1",UIParent,nil,305,470,"CENTER",30,-15,T["SubFrameBackground"],"")

fs1 = s1:CreateFontString("fs1","ARTWORK","GameFontNormalSmall")
fs1:SetPoint("CENTER","b1","BOTTOM",0,-30)
fs1:SetSize(100,50)		---一定要设置大小x,y值 就能显示....了 也能显示换行
fs1:SetJustifyH("LEFT")
fs1:SetText("|cFFFF0000<真神> 黑龙军团的符文之剑")