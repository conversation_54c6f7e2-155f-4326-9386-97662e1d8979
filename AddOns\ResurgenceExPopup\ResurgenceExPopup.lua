local revivecount = 0

local reviveitemid,reviveitemlink,reviveitemval

local ReviveItemButton

--成功
-- local m = CreateFrame("PlayerModel")
-- m:SetPoint("CENTER")
-- m:SetSize(300, 300)
-- m:SetModel("Wings\\Wings2019-3.mdx")
-- m:SetModelScale(0.5)
-- m:SetModel("Creature\\Fz\\Fz.mdx")
-- Creature\spirithealer\SpiritHealer.mdx -- 天使mm
--local s = 2000;local a = 0;m:SetScript("OnUpdate", function(self, e)s = s + (e * 1000);self:SetSequenceTime(a, s);if s > 2000 then s = 0;a=106;DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)	--a 对应动作ID 对应dbc的动作id名字 对应模型查看器动作名字
-- local c = 0
-- local s = 0;local a = 0;m:SetScript("OnUpdate", function(self, e)
-- s = s + (e * 1000);self:SetFacing(a) ;
-- if s > 1000 then 
-- s = 0;
-- if c == 6 then 
-- c = 0;
-- end
-- if c <= 3 then 
-- c = c + 1
-- a = a + 1 
-- else 
-- a = a - 1 
-- end;
-- DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)
-- print("22222222")

local f = CreateFrame("Frame","f",UIParent)
f:SetFrameStrata("TOOLTIP")
f:SetSize(330, 300)
f:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
f:SetPoint("CENTER",0,0)
f:SetBackdropColor(0,0,0,0.5)
f:Hide()
-- local m = CreateFrame("PlayerModel","m",f)
-- m:SetPoint("CENTER",0,0)
-- m:SetCamera(0)
-- m:SetSize(500, 500)
-- m:SetModel("Wings\\Wings2019-2.mdx")	
-- m:SetModelScale(0.7)
-- m:SetPosition(-1.5,1,-1);--无法使武器立起来
-- local s = 2000;local a = 0;m:SetScript("OnUpdate", function(self, e)s = s + (e * 1000);self:SetSequenceTime(a, s);if s > 2000 then s = 0;a=106;DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)	--a 对应动作ID 对应dbc的动作id名字 对应模型查看器动作名字
-- local c = 0
-- local s = 0;local a = 0;m:SetScript("OnUpdate", function(self, e)
-- s = s + (e * 1000);self:SetFacing(a) ;
-- if s > 1000 then 
-- s = 0;
-- if c == 6 then 
-- c = 0;
-- end
-- if c <= 3 then 
-- c = c + 1
-- a = a + 1 
-- else 
-- a = a - 1 
-- end;
-- DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)

local fs = f:CreateFontString("$parent".."TitleText","ARTWORK","GameFontNormal")
fs:SetText("大天使的庇佑")
-- fs:SetFont("Fonts\\ZYHei.ttf", 15)
fs:SetPoint("TOP",0,-18)
f.FontString = fs
-- local t = f:CreateTexture("$parent".."Background","ARTWORK")
-- t:SetTexture("Interface\\icons\\大天使")
-- t:SetSize(150, 150)
-- t:SetPoint("CENTER",0,30)
-- f.texture = t

local ReviveRightButton = CreateFrame("Button", "ReviveRightButton", f, "UIPanelButtonTemplate")
ReviveRightButton:SetSize(150, 30)
ReviveRightButton:SetPoint("BOTTOMLEFT", 15, 20)
ReviveRightButton:EnableMouse(true)

local ReviveRightButtonStr = ReviveRightButton:CreateFontString("ReviveRightButtonStr")
ReviveRightButtonStr:SetFont("Fonts\\ZYHei.ttf", 15)
ReviveRightButton:SetFontString(ReviveRightButtonStr)
ReviveRightButton:SetText("免费复活(剩余0次)")
-- ReviveRightButton:Disable()

local ReviveLeftButton = CreateFrame("Button", "ReviveLeftButton", f, "UIPanelButtonTemplate")
ReviveLeftButton:SetSize(150, 30)
ReviveLeftButton:SetPoint("BOTTOMRIGHT", -15, 20)
ReviveLeftButton:EnableMouse(true)

local ReviveLeftButtonStr = ReviveLeftButton:CreateFontString("ReviveLeftButtonStr")
ReviveLeftButtonStr:SetFont("Fonts\\ZYHei.ttf", 15)
-- ReviveLeftButtonStr:SetPoint("LEFT",20,0)
ReviveLeftButton:SetFontString(ReviveLeftButtonStr)
ReviveLeftButton:SetText("复活")
-- ReviveLeftButton:Disable()


local ReviveCloseButton = CreateFrame("Button", "ReviveCloseButton", f, "UIPanelCloseButton")
ReviveCloseButton:SetPoint("TOPRIGHT", -10, -3)
ReviveCloseButton:EnableMouse(true)
ReviveCloseButton:SetSize(36, 36)

function CreateRexITEMButton(Parent,Name,Point,x,y,Tex,text,IsRed)
local Button = CreateFrame("Button",Name,Parent)
Button:SetSize(20,20)
Button:SetPoint(Point,x,y)
Button:SetFrameStrata("TOOLTIP")
local bt1 = Button:CreateTexture("$parent".."Portrait","ARTWORK")
bt1:SetSize(20, 20)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
Button.tex = bt1 
if text ~= nil and text ~= "" then
local StrLeft = Button:CreateFontString(Name.."StrLeft")
StrLeft:SetFont("Fonts\\ZYHei.ttf", 15)
StrLeft:SetPoint("LEFT",-110,0)
Button:SetFontString(StrLeft)
	if IsRed ~= nil then
		if IsRed == 1 then 
		Button:SetText("|CFF00FF00复活需求"..text.."个:")	--绿色
		ReviveLeftButton:Enable()
		else
		Button:SetText("|cFFFF0000复活需求"..text.."个:")	--红色
		ReviveLeftButton:Disable()
		end
	end
Button.str = StrLeft
end 
Button:Show()
return Button
end

local function RexSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

local function RExPrepareScript(object, text, script, itemlink)
	  if text then	
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:SetFrameStrata("TOOLTIP");GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:SetFrameStrata("TOOLTIP");GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
	  end
	  	  
	  if type(script) == "function" then	
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end

local function RexEvent(self, event, h, msg, classtype, sender)
	if event == "CHAT_MSG_ADDON" then
	
		if h == "SSREX_REQITEMDATA" then
			local list = RexSplit(msg,"#")
			reviveitemid = tonumber(list[1])
			reviveitemval = list[2]
			reviveitemlink = list[3]
			local IsRed = tonumber(list[4])
			-- print("reviveitemid = "..list[1])
			-- print("reviveitemval = "..list[2])
			-- print("reviveitemlink = "..list[3])
			if reviveitemid > 0 and reviveitemval ~= nil and reviveitemlink ~= nil then
				if ReviveItemButton ~= nil then
					ReviveItemButton.str:Hide()
					ReviveItemButton.tex:Hide()
					ReviveItemButton:Hide()
					_G[ReviveItemButton.str:GetName()] = nil
					_G[ReviveItemButton.tex:GetName()] = nil
					ReviveItemButton.str = nil
					ReviveItemButton.tex = nil					
					_G[ReviveItemButton:GetName()] = nil
					ReviveItemButton = nil
					ReviveItemButton = CreateRexITEMButton(f,"ReviveItemButton","BOTTOMLEFT",290,50,GetItemIcon(reviveitemid),reviveitemval,IsRed)
					RExPrepareScript(ReviveItemButton,nil,nil,reviveitemlink)
				else
					ReviveItemButton = CreateRexITEMButton(f,"ReviveItemButton","BOTTOMLEFT",290,50,GetItemIcon(reviveitemid),reviveitemval,IsRed)
					RExPrepareScript(ReviveItemButton,nil,nil,reviveitemlink)
				end
			end
		end
		
		if h == "SSREX_PLAYFREECOUNT" then
			if msg == "OFF" then
				ReviveRightButton:SetText("免费复活(剩余0次)")
				ReviveRightButton:Disable()
			else
				ReviveRightButton:SetText("免费复活(剩余"..msg.."次)")
				ReviveRightButton:Enable()
			end
		end
		
		if h == "SSREX_PLAYERHASAURA" then
			if msg == "YES" then
				if ReviveItemButton ~= nil then
					ReviveItemButton:Hide()
				end
			end
		end
		
		if h == "SSREX_FRAME" then
			if msg == "SHOW" then
				f:Show()
			else
				f:Hide()
			end
		end
	end
end

local function RexSendReq()
	SendAddonMessage("CSREX","FREECOUNT","GUILD", UnitName("player"))
	SendAddonMessage("CSREX","REQITEM","GUILD", UnitName("player"))
end

RExPrepareScript(ReviveLeftButton,nil,function() SendAddonMessage("CSREX","REQ","GUILD", UnitName("player")) f:Hide() end,nil)
RExPrepareScript(ReviveRightButton,nil,function() SendAddonMessage("CSREX","FREE","GUILD", UnitName("player")) f:Hide() end,nil)

local RexReceiveMsg = CreateFrame("Frame")
RexReceiveMsg:RegisterEvent("CHAT_MSG_ADDON")
RexReceiveMsg:SetScript("OnEvent", RexEvent)

local RexLoad = CreateFrame("Frame")
RexLoad:RegisterEvent("PLAYER_LOGIN")
RexLoad:SetScript("OnEvent", RexSendReq)

local RexPopShow = CreateFrame("Frame")
RexPopShow:RegisterEvent("PLAYER_DEAD")
RexPopShow:SetScript("OnEvent", function () SendAddonMessage("CSREX","DEAD","GUILD", UnitName("player")) end)

local RexPopHide = CreateFrame("Frame")
RexPopHide:RegisterEvent("PLAYER_ALIVE")
RexPopHide:SetScript("OnEvent", function () f:Hide() end)