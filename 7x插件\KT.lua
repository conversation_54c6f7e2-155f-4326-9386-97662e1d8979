local HasReqData = true

local ItemEffRefineFrame = CreateFrame("Frame", "<PERSON>angT<PERSON>", UIParent);
ItemEffRefineFrame:SetSize(832, 512);
ItemEffRefineFrame:RegisterForDrag("LeftButton");
ItemEffRefineFrame:SetPoint("CENTER");
ItemEffRefineFrame:SetToplevel(true);
ItemEffRefineFrame:SetClampedToScreen(true);
ItemEffRefineFrame:Hide()
tinsert(UISpecialFrames, ItemEffRefineFrame:GetName());

ItemEffRefineFrame.PortraitTex = ItemEffRefineFrame:CreateTexture("$parentPortraitTex","BACKGROUND")
ItemEffRefineFrame.PortraitTex:SetPoint("TOPLEFT",8,-7)
ItemEffRefineFrame.PortraitTex:SetSize(58, 58)
SetPortraitTexture(ItemEffRefineFrame.PortraitTex, "player")

ItemEffRefineFrame.TopLeft = ItemEffRefineFrame:CreateTexture("$parentTopLeft","ARTWORK")
ItemEffRefineFrame.TopLeft:SetPoint("TOPLEFT")
ItemEffRefineFrame.TopLeft:SetSize(256, 256)
ItemEffRefineFrame.TopLeft:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-TopLeft");

ItemEffRefineFrame.Top = ItemEffRefineFrame:CreateTexture("$parentTop","ARTWORK")
ItemEffRefineFrame.Top:SetPoint("TOPLEFT",256,0)
ItemEffRefineFrame.Top:SetSize(320, 256)
ItemEffRefineFrame.Top:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-Top");

ItemEffRefineFrame.TopRight = ItemEffRefineFrame:CreateTexture("$parentTopRight","ARTWORK")
ItemEffRefineFrame.TopRight:SetPoint("TOPLEFT","$parentTop","TOPRIGHT",0,0)
ItemEffRefineFrame.TopRight:SetSize(256, 256)
ItemEffRefineFrame.TopRight:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-TopRight");

ItemEffRefineFrame.BotLeft = ItemEffRefineFrame:CreateTexture("$parentBotLeft","ARTWORK")
ItemEffRefineFrame.BotLeft:SetPoint("TOPLEFT",0,-256)
ItemEffRefineFrame.BotLeft:SetSize(256, 256+50)
ItemEffRefineFrame.BotLeft:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-BotLeft");

ItemEffRefineFrame.Bot = ItemEffRefineFrame:CreateTexture("$parentBot","ARTWORK",nil,1)
ItemEffRefineFrame.Bot:SetPoint("TOPLEFT",19.5,-256)
ItemEffRefineFrame.Bot:SetSize(320+256-19, 256+50)
ItemEffRefineFrame.Bot:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Auction-Bot");

ItemEffRefineFrame.BotRight = ItemEffRefineFrame:CreateTexture("$parentBotRight","ARTWORK")
ItemEffRefineFrame.BotRight:SetPoint("TOPLEFT","$parentBot","TOPRIGHT",0,0)
ItemEffRefineFrame.BotRight:SetSize(256, 256+50)
ItemEffRefineFrame.BotRight:SetTexture("Interface\\AuctionFrame\\UI-AuctionFrame-Bid-BotRight");

ItemEffRefineFrame.TitleText = ItemEffRefineFrame:CreateFontString("TitleText");
ItemEffRefineFrame.TitleText:SetFont(Frozen_GameFont, 18);
ItemEffRefineFrame.TitleText:SetPoint("TOP", 0, -15);
ItemEffRefineFrame.TitleText:SetText("|cFFB4A064斗气属性|r");


local ItemEffRefineRollButton = CreateFrame("Button", "KuangTiRollButton", ItemEffRefineFrame, "UIPanelButtonTemplate");
ItemEffRefineRollButton:SetPoint("BOTTOM", 0, 45);
ItemEffRefineRollButton:SetSize(100, 25);
ItemEffRefineRollButton:SetText("洗练");


local ItemEffRefineFrameClose = CreateFrame("Button", "KuangTiClose", ItemEffRefineFrame, "UIPanelCloseButton");
ItemEffRefineFrameClose:SetPoint("TOPRIGHT", 3, -8);
ItemEffRefineFrameClose:SetSize(32, 32);
ItemEffRefineFrameClose:HookScript("OnClick", function(self)
    ItemEffRefineFrame:Hide()
end)

local ItemEffRefineFrameStringFrame = CreateFrame("Frame", "KuangTiStringFrame", ItemEffRefineFrame);
ItemEffRefineFrameStringFrame:SetSize(300, 300);
ItemEffRefineFrameStringFrame:SetPoint("CENTER",170,0);
-- ItemEffRefineFrameStringFrame:SetBackdrop({ bgFile = "Interface\\darkmoon\\bg" });

ItemEffRefineFrame.StrText = ItemEffRefineFrameStringFrame:CreateFontString("StrText");
ItemEffRefineFrame.StrText:SetFont(Frozen_GameFont, 18);
ItemEffRefineFrame.StrText:SetPoint("CENTER", 0, 0);
ItemEffRefineFrame.StrText:SetText("");

ItemEffRefineFrame.successRateStr = ItemEffRefineFrame:CreateFontString("successRateStr");
ItemEffRefineFrame.successRateStr:SetFont(Frozen_GameFont, 18);
ItemEffRefineFrame.successRateStr:SetPoint("TOPLEFT", 50, -100);
ItemEffRefineFrame.successRateStr:SetText("");

local ItemGuidEffButton = CreateFrame("Button", "ItemGuidEffButton", ItemEffRefineFrame);
ItemGuidEffButton:SetSize(100, 100)
ItemGuidEffButton:SetPoint("RIGHT", 0, 0);
ItemGuidEffButton.IsClick = false
local tex1 = ItemGuidEffButton:CreateTexture()
tex1:SetPoint("CENTER")
tex1:SetSize(78,77)
tex1:SetTexture("Interface\\Reforging\\Reforge-Texture");
tex1:SetTexCoord(0.81640625, 0.96875000, 0.00781250, 0.60937500);
local tex2 = ItemGuidEffButton:CreateTexture()
tex2:SetPoint("CENTER")
tex2:SetSize(67,67)
tex2:SetTexture("Interface\\Reforging\\Reforge-Texture");
tex2:SetTexCoord(0.00195313, 0.12890625, 0.00781250, 0.51562500);
tex2:SetBlendMode("ADD")
tex2:Hide()
ItemGuidEffButton.tex2 = tex2
local icon = ItemGuidEffButton:CreateTexture()
icon:SetPoint("CENTER")
icon:SetSize(58,58)
ItemGuidEffButton.itemIcon = icon

local function createCheckButton(parent, name, xOff, yOff, displayname)
	local checkButton = CreateFrame("CheckButton", name, parent, "ChatConfigCheckButtonTemplate");
	checkButton:SetPoint("TOPLEFT", xOff or 0, yOff or 0);
    checkButton.Text = _G[name.."Text"]
	checkButton.Text:SetText(displayname or "");
    checkButton.Text:SetWidth(320)
    checkButton.Text:SetPoint("LEFT",checkButton,"RIGHT", 50,2)
    local IconTex = checkButton:CreateTexture()
	IconTex:SetPoint("CENTER",35,0)
	IconTex:SetSize(50,50)
    checkButton.Icon = IconTex
	return checkButton;
end

local checkButton = {}
for i = 1, 5 do
    checkButton[i] = createCheckButton(ItemEffRefineFrame, "CheckButton"..i, 40, -80+(-60*i), nil);
    checkButton[i].IsChecked = 0
    checkButton[i]:HookScript("OnClick", function(self)
        self.IsChecked = 1
        if self:GetChecked() then self.IsChecked = 1 else self.IsChecked = 0 end
        KuangTiSetStrText()
    end)
    checkButton[i]:Hide()
end

ItemEffRefineRollButton:HookScript("OnClick", function(self, button)
    if ItemGuidEffButton.guid ~= nil or ItemGuidEffButton.guid > 0 or ItemGuidEffButton.ItemLink ~= nil then
        local guid = ItemGuidEffButton.guid
        local itemLink = ItemGuidEffButton.ItemLink
        local itemid = GhostGetItemIdByLink(itemLink)
        if itemid ~= nil and guid ~= nil and itemid > 0 and guid > 0 then
            local msg = tostring(guid).."#"..itemid;
            for i = 1, 5 do
               msg = msg.."#"..tostring(checkButton[i].IsChecked)
            end
            Ghost_SendData("C_ROLLSPELL", msg)
        end
    end
end)

--设置显示物品提示信息内容
local function SetItemGuidEffGameTooltip(self, button)
    if self.ItemLink then
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
        GameTooltip:SetHyperlink(self.ItemLink)
    end
end

--重置按钮物品法术描述
local function ResetItemGuidEffCheckButton()
    for i = 1, 5 do
        checkButton[i]:Show()
        checkButton[i].Text:SetText("");
        checkButton[i].Icon:SetTexture("");
        checkButton[i]:Hide()
    end
end

local function ResetItemGuidEffStrText()
    ItemEffRefineFrame.StrText:SetText("");
end

--清空按钮值，重置按钮物品图标
local function ResetItemGuidEffButtonValue(self, button)
    self.itemID = 0 
    self.ItemLink = nil
    self.itemIcon:SetTexture("")
    self.IsClick = false
    ItemEffRefineFrame.successRateStr:SetText("")
    ResetItemGuidEffCheckButton()
    ResetItemGuidEffStrText()
end

--根据guid显示物品的随机法术信息
local function ShowItemGuidEffCheckButtonTextAndIcon(guid)
    if DongBar[guid].spells.spellid ~= nil and next(DongBar[guid].spells.spellid) ~= nil then
        for key, value in pairs(DongBar[guid].spells.spellid) do
            checkButton[key]:Show()
            checkButton[key].Text:SetText(GetSpellDescription(tonumber(value)))
            checkButton[key].Icon:SetTexture(GetSpellTexture(tonumber(value)))
        end
    end
end

local function ItemGuidEffRollButton_OnClick(self, button)
    -- local hasItem  = CursorHasItem()
    -- if not hasItem then return end

    -- ClickAuctionSellItemButton(self, button);
    if HasReqData == false then
        SendSystemMessage("|cFFFF0000此物品无法洗练")
        return
    end

    if self.IsClick then
        if self.itemID > 0 then
            local infoType, itemID, itemLink = GetCursorInfo()
            if not itemID then 
                ResetItemGuidEffButtonValue(self, button)
                return
            end
            self.ItemLink = itemLink
            local guid = GhostGetItemGuidIdByLink(self.ItemLink)
            if guid < ITEMCUSENTRY then ResetItemGuidEffButtonValue(self,button) SendSystemMessage("|cFFFF0000此物品无法洗练") return end
            guid = guid - ITEMCUSENTRY
            self.guid = guid
            self.itemIcon:SetTexture(GetItemIcon(self.itemID))
            self.IsClick = true
            SetItemGuidEffGameTooltip(self, button)
            ResetItemGuidEffCheckButton()
            ShowItemGuidEffCheckButtonTextAndIcon(guid)
            --Ghost_SendData("C_SPELLREQDATA", tostring(self.itemID).."#"..tostring(guid))
        else
            ResetItemGuidEffButtonValue(self, button)
        end
	end

    if not self.IsClick then
		if self.itemID > 0 then
            local guid = GhostGetItemGuidIdByLink(self.ItemLink)
            if guid < ITEMCUSENTRY then ResetItemGuidEffButtonValue(self,button) SendSystemMessage("|cFFFF0000此物品无法洗练") return end
            guid = guid - ITEMCUSENTRY
            self.guid = guid
            self.itemIcon:SetTexture(GetItemIcon(self.itemID))
            self.IsClick = true
            SetItemGuidEffGameTooltip(self, button)
            ResetItemGuidEffCheckButton()
            ShowItemGuidEffCheckButtonTextAndIcon(guid)
            --Ghost_SendData("C_SPELLREQDATA", tostring(self.itemID).."#"..tostring(guid))
		end
	end
end

local function ItemGuidEffRollButton_OnEnter(self, button)
    self.tex2:Show()

    local infoType, itemID, itemLink = GetCursorInfo()

    if infoType == "item" then
        self.itemID = itemID
        if not self.IsClick then
            self.ItemLink = itemLink
        end
        local guid = GhostGetItemGuidIdByLink(itemLink)
        if guid > ITEMCUSENTRY then 
            guid = guid - ITEMCUSENTRY 
            Ghost_SendData("C_SPELLREQDATA", tostring(self.itemID).."#"..tostring(guid))
        end
    end

    SetItemGuidEffGameTooltip(self, button)
end

local function ItemGuidEffRollButton_OnLeave(self, button)
    self.tex2:Hide()
    if not self.IsClick then
		self.ItemLink = nil;
        self.guid = 0
	end
    self.itemID = 0
    GameTooltip_Hide();
end

ItemGuidEffButton:SetScript("OnClick", ItemGuidEffRollButton_OnClick);
ItemGuidEffButton:SetScript("OnEnter", ItemGuidEffRollButton_OnEnter);
ItemGuidEffButton:SetScript("OnLeave", ItemGuidEffRollButton_OnLeave);

--获取checkbutton打勾状态
local function GetCheckButtonChecked(slot)
    return checkButton[slot].IsChecked
end

-- 获取是否全部打勾
local function GetCheckButtonAllChecked()
    local isAll = true
    for i = 1, 5 do
        if checkButton[i].IsChecked == 0 then
            isAll = false
            break
        end
    end
    return isAll
end

--按照勾选状态累计式的统计显示需求
function KuangTiSetStrTexts()
    local itemCountSum = {}
    local IsHasAllReqId = false
    if GetCheckButtonAllChecked() then
        for slot, data in pairs(CurrItemEffRefine) do
            if slot == 6 and next(data.reqData.itemids) then
                for i, itemId in pairs(data.reqData.itemids) do
                    if itemId > 0 then
                        local count = data.reqData.itemcount[i]
                        if itemCountSum[itemId] ~= nil then
                            itemCountSum[itemId] = itemCountSum[itemId] + count
                        else
                            itemCountSum[itemId] = count
                        end
                        IsHasAllReqId = true
                    end
                end
            end
        end
        local msg = nil
        for id, sum in pairs(itemCountSum) do
            local str = GhostGetItemLink(id) .. " * "..sum
            if msg == nil then msg = str else msg = msg .. "\n" .. str end
        end
        ItemEffRefineFrame.StrText:SetText(msg);
    end

    if IsHasAllReqId then return end

    --可能还需要改
    for slot, data in pairs(CurrItemEffRefine) do
        if slot ~= 6 then  
            if GetCheckButtonChecked(slot) == 1 then
                for i, itemId in pairs(data.reqData.itemids) do
                    if itemId > 0 then
                        local count = data.reqData.itemcount[i]
                        if itemCountSum[itemId] ~= nil then
                            itemCountSum[itemId] = itemCountSum[itemId] + count
                        else
                            itemCountSum[itemId] = count
                        end
                    end
                end
            end
        end
    end

    local msg = nil
    for id, sum in pairs(itemCountSum) do
        local str = GhostGetItemLink(id) .. " * "..sum
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    ItemEffRefineFrame.StrText:SetText(msg);
end

--按照勾选数量显示需求
function KuangTiSetStrText()
    local slotcount = 0
    for i = 1, 5 do
        if checkButton[i].IsChecked == 1 then
            slotcount = slotcount + 1
        end
    end

    if slotcount == 0 then
        ItemEffRefineFrame.successRateStr:SetText("")
        ItemEffRefineFrame.StrText:SetText("");
        return
    end

    local itemCountSum = {}
    local otherVector = {successRate = 0, meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = ""}
    for slot, data in pairs(CurrItemEffRefine) do
        if slot == slotcount and data.guid == ItemGuidEffButton.guid then
            if next(data.reqData.itemids) then
                for i, itemId in pairs(data.reqData.itemids) do
                    if itemId > 0 then
                        otherVector.successRate = data.successRate
                        otherVector.meetLevel = data.reqData.meetLevel
                        otherVector.desXp = data.reqData.desXp
                        otherVector.desGoldCount = data.reqData.desGoldCount
                        otherVector.desTokenCount = data.reqData.desTokenCount
                        otherVector.tokenName = data.reqData.tokenName
                        local count = data.reqData.itemcount[i]
                        if itemCountSum[itemId] ~= nil then
                            itemCountSum[itemId] = itemCountSum[itemId] + count
                        else
                            itemCountSum[itemId] = count
                        end
                    end
                end
            end
        end
    end

    local msg = nil
    if otherVector.meetLevel > 0 then
        local str = "等级："..otherVector.meetLevel
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    if otherVector.desXp > 0 then
        local str = "经验："..otherVector.desXp
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    if otherVector.desGoldCount > 0 then
        local str = "金币："..GhostGetGoldNum(otherVector.desGoldCount)
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    if otherVector.desTokenCount > 0 then
        local str = otherVector.tokenName .. " * "..otherVector.desTokenCount
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    
    for id, sum in pairs(itemCountSum) do
        local str = GhostGetItemLink(id) .. " * "..sum
        if msg == nil then msg = str else msg = msg .. "\n" .. str end
    end
    
    if msg ~= nil then
        ItemEffRefineFrame.StrText:SetText(msg);
    end

    ItemEffRefineFrame.successRateStr:SetText("成功率 ："..tostring(otherVector.successRate))
end

function RefreshCheckButtonShowInfo(guid)
    if ItemEffRefineFrame:IsShown() then
        if ItemGuidEffButton.guid == guid then
            SetItemGuidEffGameTooltip(ItemGuidEffButton)
            ResetItemGuidEffCheckButton()
            ShowItemGuidEffCheckButtonTextAndIcon(guid)
        end
    end
end

function SetHasReqData(val)
    HasReqData = val
end

function IsIfHasReqData()
    SetHasReqData(false)
    ItemGuidEffButton.IsClick = false
    ItemGuidEffButton.ItemLink = nil
    ItemGuidEffButton.guid = 0
    ItemGuidEffButton.itemID = 0
    ItemGuidEffButton.itemIcon:SetTexture("")
    ResetItemGuidEffCheckButton()
    GameTooltip_Hide();
end

--还得写一个窗口hide了全面重置
SLASH_DKKT1 = '/dkkt';

local function handlers(msg, editBox)
	if ItemEffRefineFrame:IsShown() then
		ItemEffRefineFrame:Hide()
	else
		ItemEffRefineFrame:Show()
	end
end

SlashCmdList["DKKT"] = handlers;

ItemEffRefineFrame:HookScript("OnHide", function(self)
    ResetItemGuidEffButtonValue(ItemGuidEffButton);
end)