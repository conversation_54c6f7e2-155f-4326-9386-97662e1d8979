/*
SQLyog Ultimate
MySQL - 5.7.35-log 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

create table `store_dropbutton` (
	`DropDown_Name` varchar (300),
	`ParentFrame` varchar (300)
); 
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Cloth','CLOTH_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Leather','LEATHER_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Plate','PLATE_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mail','MAIL_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Axe1H','AXE_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Axe2H','AXE_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Sword1H','SWORD_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Sword2H','SWORD_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mace1H','MACE_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mace2H','MACE_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Daggers','DAGGERS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Staves','STAFF_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Warglaive','WARGLAIVE_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('FistWeapon','FIST_WEAPON_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Shield','SHIELD_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Polearm','POLEARMS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Cloth','CLOTH_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Leather','LEATHER_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Plate','PLATE_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mail','MAIL_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Axe1H','AXE_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Axe2H','AXE_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Sword1H','SWORD_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Sword2H','SWORD_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mace1H','MACE_ONEHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Mace2H','MACE_TWOHAND_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Daggers','DAGGERS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Staves','STAFF_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Warglaive','WARGLAIVE_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('FistWeapon','FIST_WEAPON_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Shield','SHIELD_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Polearm','POLEARMS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Bows','BOWS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Guns','GUNS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Crossbows','CROSSBOWS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Wand','WANDS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Thrown','THROWNS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Librams','LIBRAMS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Idol','IDOLS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Relics','RELICS_FRAME');
insert into `store_dropbutton` (`DropDown_Name`, `ParentFrame`) values('Totems','TOTEMS_FRAME');
