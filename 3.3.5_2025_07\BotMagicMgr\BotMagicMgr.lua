--[[
    佣兵法宝管理系统 (BotMagicMgr)
    作者: godPanel插件系统
    功能: 提供佣兵法宝的镶嵌和管理界面
    版本: 1.0
--]]

-- 全局变量定义
local BotMagicMgr = {}
BotMagicMgr.isVisible = false
BotMagicMgr.classButtons = {}
BotMagicMgr.gemSlots = {}
BotMagicMgr.selectedClassIndex = 1  -- 当前选中的职业索引，默认选择第一个职业（战士）
BotMagicMgr.classSlotData = {}      -- 二维数组：[职业索引][镶嵌孔索引] = {itemID, itemLink}

--缩放比例
local SCALE = 1.3378
--高度
local FRAME_HEIGHT = 512

-- 职业数据配置
local CLASS_DATA = {
    -- 左侧职业按钮
    {name = "战士", icon = "Icons\\classicon_warrior.BLP", side = "left", index = 1},
    {name = "圣骑士", icon = "Icons\\classicon_paladin.BLP", side = "left", index = 2},
    {name = "猎人", icon = "Icons\\classicon_hunter.BLP", side = "left", index = 3},
    {name = "潜行者", icon = "Icons\\classicon_rogue.BLP", side = "left", index = 4},
    {name = "牧师", icon = "Icons\\classicon_priest.BLP", side = "left", index = 5},
    -- 右侧职业按钮
    {name = "死亡骑士", icon = "Icons\\classicon_deathknight.BLP", side = "right", index = 1},
    {name = "萨满", icon = "Icons\\classicon_shaman.BLP", side = "right", index = 2},
    {name = "法师", icon = "Icons\\classicon_mage.BLP", side = "right", index = 3},
    {name = "术士", icon = "Icons\\classicon_warlock.BLP", side = "right", index = 4},
    {name = "德鲁伊", icon = "Icons\\classicon_druid.BLP", side = "right", index = 5}
}

-- 镶嵌孔位置配置（五芒星形排列）
local GEM_SLOT_POSITIONS = {
    {name = "上方孔", x = -115, y = -108},      -- 12点钟方向
    {name = "右上孔", x = -45, y = -155},     -- 约2点钟方向
    {name = "右下孔", x = -70, y = -240},     -- 约4点钟方向
    {name = "左下孔", x = -185, y = -155},    -- 约8点钟方向
    {name = "左上孔", x = -160, y = -240}     -- 约10点钟方向
}

--------------------------------------------------
-- 主框体创建
--------------------------------------------------
local BotMagicMgrFrame = CreateFrame('Frame', 'BotMagicMgrFrame', UIParent)
BotMagicMgrFrame:SetSize(FRAME_HEIGHT * SCALE, FRAME_HEIGHT) --是可行的 按比例在这里进行缩放
BotMagicMgrFrame:SetClampedToScreen(true)
BotMagicMgrFrame:SetPoint('CENTER', 0, 0)
BotMagicMgrFrame:SetBackdrop({bgFile = 'Interface\\AddOns\\godPanel\\Icons\\024.blp'}) 
BotMagicMgrFrame:Show() -- 始终可见，不隐藏
BotMagicMgrFrame:SetMovable(true) -- 设置可移动
BotMagicMgrFrame:EnableMouse(true) -- 启用鼠标
BotMagicMgrFrame:RegisterForDrag('LeftButton') -- 注册拖拽【左键】
BotMagicMgrFrame:SetScript('OnDragStart', BotMagicMgrFrame.StartMoving) -- 设置脚本 开始移动
BotMagicMgrFrame:SetScript('OnDragStop', BotMagicMgrFrame.StopMovingOrSizing) -- 设置脚本 停止移动
BotMagicMgrFrame:SetFrameLevel(100) -- 设置框体层级

-- 将框体添加到特殊框体列表，支持ESC键关闭
tinsert(UISpecialFrames, BotMagicMgrFrame:GetName())

--------------------------------------------------
-- 调试输出函数
--------------------------------------------------
local function DebugPrint(message)
    print("|cFF00FFFF[佣兵法宝管理]|r " .. tostring(message))
end

--------------------------------------------------
-- 初始化职业镶嵌孔数据
--------------------------------------------------
local function InitializeClassSlotData()
    DebugPrint("初始化职业镶嵌孔数据...")

    -- 为每个职业创建镶嵌孔数据数组
    for classIndex = 1, #CLASS_DATA do
        BotMagicMgr.classSlotData[classIndex] = {}
        -- 为每个镶嵌孔位置创建空数据
        for slotIndex = 1, #GEM_SLOT_POSITIONS do
            BotMagicMgr.classSlotData[classIndex][slotIndex] = {
                itemID = nil,
                itemLink = nil
            }
        end
        DebugPrint("初始化职业 " .. CLASS_DATA[classIndex].name .. " 的镶嵌孔数据")
    end

    DebugPrint("职业镶嵌孔数据初始化完成")
end

--------------------------------------------------
-- 保存当前镶嵌孔状态到职业数据
--------------------------------------------------
local function SaveCurrentSlotsToClassData(classIndex)
    if not classIndex or not BotMagicMgr.classSlotData[classIndex] then
        DebugPrint("|cFFFF0000错误: 无效的职业索引 " .. tostring(classIndex) .. "|r")
        return
    end

    DebugPrint("保存当前镶嵌孔状态到职业 " .. CLASS_DATA[classIndex].name)

    -- 遍历所有镶嵌孔，保存当前状态
    for slotIndex, slotButton in ipairs(BotMagicMgr.gemSlots) do
        if slotButton then
            BotMagicMgr.classSlotData[classIndex][slotIndex] = {
                itemID = slotButton.itemID,
                itemLink = slotButton.itemLink
            }
        end
    end
end

--------------------------------------------------
-- 从职业数据加载镶嵌孔状态到UI
--------------------------------------------------
local function LoadClassDataToSlots(classIndex)
    if not classIndex or not BotMagicMgr.classSlotData[classIndex] then
        DebugPrint("|cFFFF0000错误: 无效的职业索引 " .. tostring(classIndex) .. "|r")
        return
    end

    DebugPrint("加载职业 " .. CLASS_DATA[classIndex].name .. " 的镶嵌孔状态到UI")

    -- 遍历所有镶嵌孔，从职业数据加载状态
    for slotIndex, slotButton in ipairs(BotMagicMgr.gemSlots) do
        if slotButton and BotMagicMgr.classSlotData[classIndex][slotIndex] then
            local slotData = BotMagicMgr.classSlotData[classIndex][slotIndex]

            -- 设置按钮的物品信息
            slotButton.itemID = slotData.itemID
            slotButton.itemLink = slotData.itemLink

            -- 更新UI显示
            if slotData.itemID then
                local itemIcon = GetItemIcon(slotData.itemID)
                if itemIcon then
                    slotButton.itemTexture:SetTexture(itemIcon)
                    slotButton.itemTexture:Show()
                else
                    slotButton.itemTexture:Hide()
                end
            else
                slotButton.itemTexture:Hide()
            end
        end
    end
end

--------------------------------------------------
-- 更新职业按钮选中状态显示
--------------------------------------------------
local function UpdateClassButtonStates(selectedIndex)
    DebugPrint("更新职业按钮选中状态，选中职业: " .. CLASS_DATA[selectedIndex].name)

    -- 遍历所有职业按钮，更新选中状态
    for i, button in ipairs(BotMagicMgr.classButtons) do
        if button and button.texture then
            if i == selectedIndex then
                -- 选中状态：设置高亮效果
                button.texture:SetVertexColor(1, 1, 1, 1)  -- 正常颜色
                DebugPrint("职业按钮 " .. CLASS_DATA[i].name .. " 设置为选中状态")
            else
                -- 未选中状态：设置去饱和效果
                button.texture:SetVertexColor(0.5, 0.5, 0.5, 1)  -- 暗淡颜色
            end
        end
    end
end

--------------------------------------------------
-- 职业切换主控制函数
--------------------------------------------------
local function SwitchToClass(newClassIndex)
    if not newClassIndex or newClassIndex < 1 or newClassIndex > #CLASS_DATA then
        DebugPrint("|cFFFF0000错误: 无效的职业索引 " .. tostring(newClassIndex) .. "|r")
        return
    end

    if BotMagicMgr.selectedClassIndex == newClassIndex then
        DebugPrint("已经选中职业 " .. CLASS_DATA[newClassIndex].name .. "，无需切换")
        return
    end

    DebugPrint("开始切换职业: " .. CLASS_DATA[BotMagicMgr.selectedClassIndex].name .. " -> " .. CLASS_DATA[newClassIndex].name)

    -- 保存当前职业的镶嵌孔状态
    SaveCurrentSlotsToClassData(BotMagicMgr.selectedClassIndex)

    -- 更新选中的职业索引
    BotMagicMgr.selectedClassIndex = newClassIndex

    -- 加载新职业的镶嵌孔状态
    LoadClassDataToSlots(newClassIndex)

    -- 更新职业按钮的选中状态显示
    UpdateClassButtonStates(newClassIndex)

    DebugPrint("职业切换完成: " .. CLASS_DATA[newClassIndex].name)
end

--------------------------------------------------
-- 获取职业ID映射函数
--------------------------------------------------
local function GetClassIDByIndex(classIndex)
    -- 处理德鲁伊的特殊情况：索引10对应职业ID 11
    if classIndex == 10 then
        return 11
    end
    -- 其他职业索引直接对应职业ID
    return classIndex
end

--------------------------------------------------
-- 获取职业索引映射函数（反向映射）
--------------------------------------------------
local function GetClassIndexByID(classID)
    -- 处理德鲁伊的特殊情况：职业ID 11对应索引10
    if classID == 11 then
        return 10
    end
    -- 验证职业ID范围（1-9为正常职业）
    if classID >= 1 and classID <= 9 then
        return classID
    end
    -- 无效的职业ID
    return nil
end

--------------------------------------------------
-- 错误处理函数
--------------------------------------------------
local function SafeCall(func, ...)
    local success, result = pcall(func, ...)
    if not success then
        DebugPrint("|cFFFF0000错误: " .. tostring(result) .. "|r")
        return nil
    end
    return result
end

--------------------------------------------------
-- 职业按钮创建函数
--------------------------------------------------
local function CreateClassButton(classData, buttonIndex)
    local buttonName = "BotMagicMgr_ClassButton_" .. buttonIndex
    local button = CreateFrame('Button', buttonName, BotMagicMgrFrame)

    -- 设置按钮基本属性
    button:SetSize(38, 38)
    button:EnableMouse(true)
    button:SetFrameLevel(BotMagicMgrFrame:GetFrameLevel() + 1)

    -- 创建按钮图标纹理
    local texture = button:CreateTexture(nil, "ARTWORK")
    texture:SetAllPoints(button)
    texture:SetTexture("Interface\\AddOns\\godPanel\\" .. classData.icon)
    button.texture = texture

    -- 创建高光纹理
    local highlight = button:CreateTexture(nil, "HIGHLIGHT")
    highlight:SetAllPoints(button)
    highlight:SetTexture("Interface\\Buttons\\ButtonHilight-Square")
    highlight:SetBlendMode("ADD")

    -- 设置按钮位置
    local baseX, baseY
    if classData.side == "left" then
        baseX = 100
        baseY = 105
    else -- right
        baseX = 550
        baseY = 105
    end

    -- 计算具体位置（垂直向下排列，间距40像素）
    local offsetY = (classData.index - 1) * -65
    button:SetPoint("TOPLEFT", BotMagicMgrFrame, "TOPLEFT", baseX, -(baseY - offsetY))
    button.ClassID = buttonIndex -- 保存职业索引
    if button.ClassID == 10 then
        button.ClassID = 11
    end
    -- 设置按钮点击事件
    button:SetScript('OnClick', function(self)
        DebugPrint("点击了职业按钮: " .. classData.name .. " (职业ID: " .. self.ClassID .. ")")
        -- 执行职业切换逻辑
        SwitchToClass(buttonIndex)
    end)

    -- 设置鼠标悬停事件
    button:SetScript('OnEnter', function(self)
        GameTooltip:SetOwner(self, 'ANCHOR_RIGHT')
        GameTooltip:AddLine(classData.name, 1, 1, 1)
        GameTooltip:AddLine("点击选择此职业", 0.7, 0.7, 0.7)
        GameTooltip:Show()
    end)

    button:SetScript('OnLeave', function()
        GameTooltip:Hide()
    end)

    -- 存储按钮数据
    button.classData = classData

    return button
end

--------------------------------------------------
-- 创建所有职业按钮
--------------------------------------------------
local function CreateAllClassButtons()
    DebugPrint("开始创建职业按钮...")

    for i, classData in ipairs(CLASS_DATA) do
        local button = SafeCall(CreateClassButton, classData, i)
        if button then
            BotMagicMgr.classButtons[i] = button
            DebugPrint("创建职业按钮: " .. classData.name .. " (位置: " .. classData.side .. ")")
        else
            DebugPrint("|cFFFF0000创建职业按钮失败: " .. classData.name .. "|r")
        end
    end

    DebugPrint("职业按钮创建完成，共创建 " .. #BotMagicMgr.classButtons .. " 个按钮")
end

--------------------------------------------------
-- 镶嵌孔按钮创建函数
--------------------------------------------------
local function CreateGemSlotButton(slotData, slotIndex)
    local buttonName = "BotMagicMgr_GemSlot_" .. slotIndex
    local button = CreateFrame('Button', buttonName, BotMagicMgrFrame)

    -- 设置按钮基本属性
    button:SetSize(64, 64)
    button:EnableMouse(true)
    button:SetFrameLevel(BotMagicMgrFrame:GetFrameLevel() + 1)

    -- 创建背景纹理
    local bgTexture = button:CreateTexture(nil, "BACKGROUND")
    bgTexture:SetAllPoints(button)
    bgTexture:SetTexture("Interface\\AddOns\\godPanel\\Icons\\fuwen_bg.blp")
    button.bgTexture = bgTexture

    -- 创建物品图标纹理
    local itemTexture = button:CreateTexture(nil, "ARTWORK")
    itemTexture:SetSize(36, 36)
    itemTexture:SetPoint("CENTER", button, "CENTER", 0, 0)
    button.itemTexture = itemTexture

    -- 创建高光纹理
    local highlight = button:CreateTexture(nil, "HIGHLIGHT")
    highlight:SetAllPoints(button)
    highlight:SetTexture("Interface\\Buttons\\ButtonHilight-Square")
    highlight:SetBlendMode("ADD")

    -- 设置按钮位置（相对于框体中心的十字形排列）
    local centerX = 458  -- 框体中心X坐标
    local centerY = 448  -- 框体中心Y坐标
    button:SetPoint("CENTER", BotMagicMgrFrame, "BOTTOMLEFT", centerX + slotData.x, centerY + slotData.y)

    -- 存储镶嵌物品信息
    button.itemID = nil
    button.itemLink = nil
    button.slotId = slotIndex 

    return button
end

--------------------------------------------------
-- 处理完整职业法宝数据
--------------------------------------------------
function BotMagicMgr_HandleFullClassData(dataString)
    if not dataString or type(dataString) ~= "string" then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleFullClassData 参数无效|r")
        return
    end

    -- 使用#分隔符解析数据字符串
    local parts = {}
    for part in string.gmatch(dataString, "([^#]+)") do
        table.insert(parts, part)
    end

    -- 验证数据格式：应该有11个部分（1个职业ID + 10个镶嵌孔数据）
    if #parts ~= 11 then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleFullClassData 数据格式不正确，期望11个参数，实际" .. #parts .. "个|r")
        return
    end

    -- 解析职业ID并转换为职业索引
    local botClassID = tonumber(parts[1])
    if not botClassID then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleFullClassData 职业ID无效: " .. tostring(parts[1]) .. "|r")
        return
    end

    local classIndex = GetClassIndexByID(botClassID)
    if not classIndex then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleFullClassData 无效的职业ID: " .. botClassID .. "|r")
        return
    end

    DebugPrint("处理完整职业法宝数据: " .. CLASS_DATA[classIndex].name .. " (职业ID: " .. botClassID .. ")")

    -- 解析并存储5个镶嵌孔的数据
    for i = 1, 5 do
        local slotIdIndex = (i - 1) * 2 + 2  -- slot位置：2, 4, 6, 8, 10
        local itemIdIndex = slotIdIndex + 1   -- itemid位置：3, 5, 7, 9, 11

        local slotId = tonumber(parts[slotIdIndex])
        local itemId = tonumber(parts[itemIdIndex])

        if slotId and itemId then
            -- 存储数据到classSlotData
            if not BotMagicMgr.classSlotData[classIndex] then
                BotMagicMgr.classSlotData[classIndex] = {}
            end
            if not BotMagicMgr.classSlotData[classIndex][slotId] then
                BotMagicMgr.classSlotData[classIndex][slotId] = {}
            end

            BotMagicMgr.classSlotData[classIndex][slotId] = {
                itemID = itemId > 0 and itemId or nil,
                itemLink = itemId > 0 and GhostGetItemLink(itemId) or nil
            }

            DebugPrint("更新镶嵌孔数据: 职业" .. CLASS_DATA[classIndex].name .. " 第" .. slotId .. "号孔 物品ID:" .. (itemId > 0 and itemId or "无"))
        end
    end

    -- 如果当前选中的职业与接收到的数据职业相同，同步更新UI
    if BotMagicMgr.selectedClassIndex == classIndex then
        DebugPrint("同步更新UI显示: " .. CLASS_DATA[classIndex].name)
        LoadClassDataToSlots(classIndex)
    end
end

--------------------------------------------------
-- 处理单个镶嵌孔数据（镶嵌操作）
--------------------------------------------------
function BotMagicMgr_HandleSingleSlotData(dataString)
    if not dataString or type(dataString) ~= "string" then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSingleSlotData 参数无效|r")
        return
    end

    -- 使用#分隔符解析数据字符串
    local parts = {}
    for part in string.gmatch(dataString, "([^#]+)") do
        table.insert(parts, part)
    end

    -- 验证数据格式：应该有3个部分（职业ID + 镶嵌孔ID + 物品ID）
    if #parts ~= 3 then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSingleSlotData 数据格式不正确，期望3个参数，实际" .. #parts .. "个|r")
        return
    end

    -- 解析参数
    local botClassID = tonumber(parts[1])
    local slotId = tonumber(parts[2])
    local itemId = tonumber(parts[3])

    if not botClassID or not slotId or not itemId then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSingleSlotData 参数解析失败|r")
        return
    end

    -- 转换职业ID为职业索引
    local classIndex = GetClassIndexByID(botClassID)
    if not classIndex then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSingleSlotData 无效的职业ID: " .. botClassID .. "|r")
        return
    end

    -- 验证镶嵌孔ID范围
    if slotId < 1 or slotId > #GEM_SLOT_POSITIONS then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSingleSlotData 无效的镶嵌孔ID: " .. slotId .. "|r")
        return
    end

    DebugPrint("处理单个镶嵌孔数据: " .. CLASS_DATA[classIndex].name .. " (职业ID: " .. botClassID .. ") 第" .. slotId .. "号孔 物品ID:" .. itemId)

    -- 确保数据结构存在
    if not BotMagicMgr.classSlotData[classIndex] then
        BotMagicMgr.classSlotData[classIndex] = {}
    end
    if not BotMagicMgr.classSlotData[classIndex][slotId] then
        BotMagicMgr.classSlotData[classIndex][slotId] = {}
    end

    -- 存储数据到classSlotData
    BotMagicMgr.classSlotData[classIndex][slotId] = {
        itemID = itemId > 0 and itemId or nil,
        itemLink = itemId > 0 and GhostGetItemLink(itemId) or nil
    }

    -- 如果当前选中的职业与接收到的数据职业相同，同步更新UI
    if BotMagicMgr.selectedClassIndex == classIndex then
        DebugPrint("同步更新UI显示: " .. CLASS_DATA[classIndex].name .. " 第" .. slotId .. "号镶嵌孔")
        LoadClassDataToSlots(classIndex)
    end
end

--------------------------------------------------
-- 处理单个镶嵌孔清空数据（移除操作）
--------------------------------------------------
function BotMagicMgr_HandleSlotClearData(dataString)
    if not dataString or type(dataString) ~= "string" then
        DebugPrint("|cFFFF0000错误: HandleSlotClearData 参数无效|r")
        return
    end

    -- 使用#分隔符解析数据字符串
    local parts = {}
    for part in string.gmatch(dataString, "([^#]+)") do
        table.insert(parts, part)
    end

    -- 验证数据格式：应该有3个部分（职业ID + 镶嵌孔ID + 0）
    if #parts ~= 3 then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSlotClearData 数据格式不正确，期望3个参数，实际" .. #parts .. "个|r")
        return
    end

    -- 解析参数
    local botClassID = tonumber(parts[1])
    local slotId = tonumber(parts[2])
    local clearFlag = tonumber(parts[3])

    if not botClassID or not slotId or not clearFlag then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSlotClearData 参数解析失败|r")
        return
    end

    -- 验证清空标志
    if clearFlag ~= 0 then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSlotClearData 清空标志应为0，实际为: " .. clearFlag .. "|r")
        return
    end

    -- 转换职业ID为职业索引
    local classIndex = GetClassIndexByID(botClassID)
    if not classIndex then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSlotClearData 无效的职业ID: " .. botClassID .. "|r")
        return
    end

    -- 验证镶嵌孔ID范围
    if slotId < 1 or slotId > #GEM_SLOT_POSITIONS then
        DebugPrint("|cFFFF0000错误: BotMagicMgr_HandleSlotClearData 无效的镶嵌孔ID: " .. slotId .. "|r")
        return
    end

    DebugPrint("处理镶嵌孔清空数据: " .. CLASS_DATA[classIndex].name .. " (职业ID: " .. botClassID .. ") 第" .. slotId .. "号孔")

    -- 确保数据结构存在
    if not BotMagicMgr.classSlotData[classIndex] then
        BotMagicMgr.classSlotData[classIndex] = {}
    end
    if not BotMagicMgr.classSlotData[classIndex][slotId] then
        BotMagicMgr.classSlotData[classIndex][slotId] = {}
    end

    -- 清空数据
    BotMagicMgr.classSlotData[classIndex][slotId] = {
        itemID = nil,
        itemLink = nil
    }

    -- 如果当前选中的职业与接收到的数据职业相同，同步更新UI
    if BotMagicMgr.selectedClassIndex == classIndex then
        DebugPrint("同步更新UI显示: " .. CLASS_DATA[classIndex].name .. " 第" .. slotId .. "号镶嵌孔已清空")
        LoadClassDataToSlots(classIndex)
    end
end

--------------------------------------------------
-- 镶嵌孔拖拽处理函数
--------------------------------------------------
local function HandleGemSlotClick(button, mouseButton)
    local isInCombat = UnitAffectingCombat("player") -- 是否处于战斗状态
    if isInCombat then
        DebugPrint("处于战斗状态，无法创建镶嵌孔")
        return
    end

    if mouseButton == "LeftButton" then
        -- 输出详细的调试信息，包括职业、职业ID和镶嵌孔信息
        local currentClassName = CLASS_DATA[BotMagicMgr.selectedClassIndex].name
        local currentClassID = GetClassIDByIndex(BotMagicMgr.selectedClassIndex)
        DebugPrint("点击了 " .. currentClassName .. " (职业ID: " .. currentClassID .. ") 的第 " .. button.slotId .. " 号镶嵌孔")

        local infoType, itemID, itemLink = GetCursorInfo()

        if infoType == "item" then
            -- 有物品被拖拽到镶嵌孔
            DebugPrint("物品拖拽到镶嵌孔: " .. (itemLink or "未知物品"))
            
            -- 发送操作码
            Ghost_SendData("GC_C_BOTFABAO_XQ", currentClassID .. " " .. button.slotId .. " " .. itemID)
            -- 清除光标
            ClearCursor()

        else
            -- 没有拖拽物品，移除当前镶嵌的物品
            if button.itemID then
                -- 发送操作码
                Ghost_SendData("GC_C_BOTFABAO_XX", currentClassID .. " " .. button.slotId)

                DebugPrint("移除镶嵌物品: " .. (GetItemInfo(button.itemID) or "未知物品"))
            end
        end
    end
end

--------------------------------------------------
-- 创建所有镶嵌孔按钮
--------------------------------------------------
local function CreateAllGemSlots()
    DebugPrint("开始创建镶嵌孔...")

    for i, slotData in ipairs(GEM_SLOT_POSITIONS) do
        local button = SafeCall(CreateGemSlotButton, slotData, i)
        if button then
            -- 设置点击事件
            button:SetScript('OnClick', function(self, mouseButton)
                HandleGemSlotClick(self, mouseButton)
            end)

            -- 设置鼠标悬停事件
            button:SetScript('OnEnter', function(self)
                GameTooltip:SetOwner(self, 'ANCHOR_RIGHT')
                if self.itemID then
                    GameTooltip:SetHyperlink('item:' .. self.itemID)
                else
                    GameTooltip:AddLine(slotData.name, 1, 1, 1)
                    GameTooltip:AddLine("拖拽物品到此处进行镶嵌", 0.7, 0.7, 0.7)
                end
                GameTooltip:Show()
            end)

            button:SetScript('OnLeave', function()
                GameTooltip:Hide()
            end)

            BotMagicMgr.gemSlots[i] = button
            DebugPrint("创建镶嵌孔: " .. slotData.name)
        else
            DebugPrint("|cFFFF0000创建镶嵌孔失败: " .. slotData.name .. "|r")
        end
    end

    DebugPrint("镶嵌孔创建完成，共创建 " .. #BotMagicMgr.gemSlots .. " 个镶嵌孔")
end

--------------------------------------------------
-- 初始化所有UI组件
--------------------------------------------------
local function InitializeAllComponents()
    DebugPrint("开始初始化所有UI组件...")

    -- 初始化职业镶嵌孔数据
    SafeCall(InitializeClassSlotData)

    -- 创建职业按钮
    SafeCall(CreateAllClassButtons)

    -- 创建镶嵌孔
    SafeCall(CreateAllGemSlots)

    -- 设置默认选中的职业（战士）并更新按钮状态
    BotMagicMgr.selectedClassIndex = 1
    SafeCall(UpdateClassButtonStates, 1)
    DebugPrint("默认选中职业: " .. CLASS_DATA[1].name)

    DebugPrint("所有UI组件初始化完成")
end

--------------------------------------------------
-- 显示/隐藏切换函数（供CircleMenu调用）
--------------------------------------------------
function BotMagicMgr_Toggle()
    if BotMagicMgrFrame:IsVisible() then
        BotMagicMgrFrame:Hide()
        BotMagicMgr.isVisible = false
        DebugPrint("佣兵法宝管理界面已隐藏")
    else
        BotMagicMgrFrame:Show()
        BotMagicMgr.isVisible = true
        DebugPrint("佣兵法宝管理界面已显示")
    end
end

--------------------------------------------------
-- 事件处理框架
--------------------------------------------------
local eventFrame = CreateFrame("Frame")
eventFrame:RegisterEvent("ADDON_LOADED")
eventFrame:RegisterEvent("PLAYER_LOGIN")

eventFrame:SetScript("OnEvent", function(_, event, addonName)
    if event == "ADDON_LOADED" and addonName == "godPanel" then
        DebugPrint("godPanel插件加载完成，初始化佣兵法宝管理系统")
        SafeCall(InitializeAllComponents)
    elseif event == "PLAYER_LOGIN" then
        DebugPrint("玩家登录完成，佣兵法宝管理系统准备就绪")
        -- 发送操作码
        Ghost_SendData("BOT_FABAO_READY","1");
    end
end)

DebugPrint("佣兵法宝管理系统初始化完成")
