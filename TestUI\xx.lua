local DES = {}

function Split(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 

function OnReceive(self, event, h, msg, classtype, sender)
	if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
		if h == "TOOLTIP_EXTDES" then
			local list = Split(msg,"#")
			DES[tonumber(list[1])] = list[2]
		end
		
		if h == "TOOLTIP_EXTDES" and msg == "RESET" then
			DES = nil;
			DES = {}
		end
		
		if h == "TOOLTIP_EXTDES_RESETDATE" then
			local list = Split(msg,"#")
			DES[tonumber(list[1])] = list[2]
		end 
	end
end

local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("CHAT_MSG_ADDON")
MsgReceivers:SetScript("OnEvent", OnReceive)



function GhostOnTooltipShows(self)
	local _, itemLink = self:GetItem()
	local _,_,_,_, itemid, _, _, _, _, _, _, _,_,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")
	local i = self:NumLines()-1;
	local f = _G[self:GetName().."TextLeft"..i]
	local s = f:GetText()
	
	if DES[tonumber(itemid)] ~= nil then
		f:SetText(s.."|n"..DES[tonumber(itemid)])
	end
	-- f:SetText(s.."|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t五件触发：使你移动速度提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t六件触发：受到所有伤害降低25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t七件触发：受到暴击几率降低25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n|TInterface\\ICONS\\Ability_Ambush.blp:25|t八件触发：使你生命属性提高25%|n")
	
end

GameTooltip:HookScript("OnTooltipSetItem", GhostOnTooltipShows)
ItemRefTooltip:HookScript("OnTooltipSetItem", GhostOnTooltipShows)
ShoppingTooltip1:HookScript("OnShow", GhostOnTooltipShows)
ShoppingTooltip2:HookScript("OnShow", GhostOnTooltipShows)
ShoppingTooltip3:HookScript("OnShow", GhostOnTooltipShows)
print("67890")