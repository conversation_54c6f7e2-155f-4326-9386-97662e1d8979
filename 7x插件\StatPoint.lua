---@diagnostic disable: undefined-global

--15个数据
local Frozen_StatPointData = { "", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
local Frozen_StatOne = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
local Frozen_StatMax = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
local Frozen_MaxLevel = 0;
local LeftIndex = 5;


local DoStatType = {
	[1] = { "|cFF00FF00力量|r", "0" },
	[2] = { "|cFF00FF00敏捷|r", "1" },
	[3] = { "|cFF00FF00耐力|r", "2" },
	[4] = { "|cFF00FF00智力|r", "3" },
	[5] = { "|cFF00FF00全能|r", "4" },
	[6] = { "|cFF00FF00攻强|r", "5" },
	[7] = { "|cFF00FF00法强|r", "6" },
	[8] = { "|cFF00FF00急速|r", "7" },
	[9] = { "|cFF00FF00命中|r", "8" },
	[10] = { "|cFF00FF00暴击|r", "9" },
	[11] = { "|cFF00FF00精准|r", "10" },
	[12] = { "|cFF00FF00精通|r", "11" },
	[13] = { "|cFF00FF00PVP强度|r", "12" },
};

--静态弹出框
StaticPopupDialogs["ResetStatPt"] = {
	text = "%s",
	button1 = YES,
	button2 = NO,
	OnAccept = function(self)
		Ghost_SendData("AcceptResetStat");
	end,
	timeout = 0,
	exclusive = 1,
	hideOnEscape = 1
};

StaticPopupDialogs["StatCount"] = {
	text = "%s\n\n在下面输入想加的点数|r",
	button1 = ACCEPT,
	button2 = CANCEL,
	hasEditBox = 1,
	maxLetters = 4,
	numEric = 1,
	OnAccept = function(self)
		local text = self.editBox:GetText();
		Ghost_SendData("StatIncrease", self.Type .. "#" .. text);
	end,
	EditBoxOnEnterPressed = function(self)
		local text = self:GetParent().editBox:GetText();
		Ghost_SendData("StatIncrease", self:GetParent().Type .. "#" .. text);
		self:GetParent():Hide();
	end,
	OnShow = function(self)
		self.editBox:SetFocus();
	end,
	OnHide = function(self)
		ChatEdit_FocusActiveWindow();
		self.editBox:SetText("");
	end,
	timeout = 0,
	exclusive = 1,
	whileDead = 1,
	hideOnEscape = 1
};

local StatTypeStr = {};
local StatNowPointStr = {};
local StatClickRightButton = {};

local StatPointFrame = CreateFrame("Frame", "StatPointFrame789", UIParent);
StatPointFrame:SetSize(520, 680);
StatPointFrame:RegisterForDrag("LeftButton");
StatPointFrame:SetPoint("CENTER");
StatPointFrame:SetToplevel(true);
StatPointFrame:SetClampedToScreen(true);
StatPointFrame:SetBackdrop({ bgFile = "Interface\\Icons\\StatRes" });

tinsert(UISpecialFrames, StatPointFrame:GetName());

function CreatePlayerIcon(Parent, Name)
	local img1 = Parent:CreateTexture("$parent" .. Name, "OVERLAY")
	img1:SetSize(73, 80)
	img1:SetPoint("TOPLEFT", 8, -12)
	SetPortraitTexture(img1, "player")
	return img1
end

if StatPointFrame.PP == nil then
	StatPointFrame.PP = CreatePlayerIcon(StatPointFrame, "PlayerIcon")
end


--关闭按钮
local StatButtonClose = CreateFrame("Button", "StatButtonClose", StatPointFrame, "UIPanelCloseButton");
StatButtonClose:SetPoint("TOPRIGHT", -3, -10);
StatButtonClose:SetSize(45, 45);

--标题
local StatTitleText = StatPointFrame:CreateFontString("StatTitleText");
StatTitleText:SetFont(Frozen_GameFont, 18);
StatTitleText:SetPoint("TOP", 0, -22);
--StatTitleText:SetText("|cFF00FF00斗气属性|r");

--点数提示
local AttributesPointsComment = StatPointFrame:CreateFontString("AttributesPointsComment");
AttributesPointsComment:SetFont(Frozen_GameFont, 18);
AttributesPointsComment:SetPoint("TOP", 0, -60);
AttributesPointsComment:SetText("|cFF00FFFF当前可用点数|r |cFFFF66FF" .. Frozen_StatPointData[2] .. "|r");

--来源提示
local QueryPointsText = StatPointFrame:CreateFontString("QueryPointsText");
QueryPointsText:SetFont(Frozen_GameFont, 16);
QueryPointsText:SetPoint("TOP", 0, -90);
QueryPointsText:SetText("|cFFFF3366击杀指定怪物或使用某些消耗品可获得经验|r");

--等级提示
local RankPointsText = StatPointFrame:CreateFontString("RankPointsText");
RankPointsText:SetFont(Frozen_GameFont, 16);
RankPointsText:SetPoint("BOTTOM", 0, 120);
RankPointsText:SetText("|cFF00FFFF当前等级|r |cFFFF66FF" .. Frozen_StatPointData[3] .. "|r");


--进度条
local ButtonBar = CreateFrame("Button", "ButtonBar", StatPointFrame)
ButtonBar:SetSize(360, 24)
ButtonBar:SetPoint("BOTTOM", 0, 90)
ButtonBar:SetBackdrop(
	{
		bgFile = "Interface\\DialogFrame\\UI-DialogBox-Background",
		edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
		edgeSize = 12,
		insets = { left = 3, right = 3, top = 3, bottom = 3 }
	});
local AtStatusBarOne = CreateFrame("StatusBar", "AtStatusBarOne", ButtonBar)
AtStatusBarOne:SetOrientation("HORIZONTAL")
AtStatusBarOne:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar")
AtStatusBarOne:SetStatusBarColor(255 / 255, 102 / 255, 0 / 255)
AtStatusBarOne:SetPoint("CENTER")
AtStatusBarOne:SetSize(ButtonBar:GetWidth() - 8, ButtonBar:GetHeight() - 8)
AtStatusBarOne:SetMinMaxValues(1, 100)

local ButtonBarTexts = AtStatusBarOne:CreateFontString("ButtonBarTexts")
ButtonBarTexts:SetFont(Frozen_GameFont, 16)
ButtonBarTexts:SetAllPoints(AtStatusBarOne)

local Schedule = (Frozen_StatPointData[5] > 0) and (Frozen_StatPointData[4] * 100 / Frozen_StatPointData[5]) or 0;
ButtonBarTexts:SetText("|cFF00FF00当前经验" .. Frozen_StatPointData[4] .. "/" .. Frozen_StatPointData[5] .. " 进度 " .. string.format("%.f", Schedule) .. "%|r")
AtStatusBarOne:SetValue(math.floor(Schedule))

for i = 1, 13 do
	StatTypeStr[i] = StatPointFrame:CreateFontString("StatTypeStr" .. i);
	StatTypeStr[i]:SetFont(Frozen_GameFont, 18);
	StatTypeStr[i]:SetSize(120, 30);
	StatTypeStr[i]:SetPoint("TOPLEFT", 60, -80 + (-32 * i));
	StatTypeStr[i]:SetText(DoStatType[i][1]);

	StatNowPointStr[i] = StatPointFrame:CreateFontString("StatNowPointStr" .. i);
	StatNowPointStr[i]:SetFont(Frozen_GameFont, 17);
	StatNowPointStr[i]:SetSize(280, 30);
	StatNowPointStr[i]:SetPoint("TOPLEFT", 115, -80 + (-32 * i));
	StatNowPointStr[i]:SetVertexColor(255 / 255, 255 / 255, 0 / 255)
	StatNowPointStr[i]:SetText(tostring(Frozen_StatPointData[i + LeftIndex]));

	StatClickRightButton[i] = CreateFrame("Button", "StatClickRightButton" .. i, StatPointFrame);
	StatClickRightButton[i]:SetPoint("TOPLEFT", 400, -86 + (-32 * i));
	StatClickRightButton[i]:SetSize(24, 24);
	StatClickRightButton[i]:SetNormalTexture("Interface\\BUTTONS\\UI-PlusButton-Up")
	StatClickRightButton[i]:SetHighlightTexture("Interface\\BUTTONS\\UI-Panel-MinimizeButton-Highlight")
	StatClickRightButton[i]:SetPushedTexture("Interface\\BUTTONS\\UI-PlusButton-Disabled")
	StatClickRightButton[i]:SetScript("OnClick",
		function(Self)
			if (Frozen_StatPointData[2] <= 0) then
				DEFAULT_CHAT_FRAME:AddMessage("|cffFFC125[温馨提示]: 你没有足够的点数 无法操作|r");
				return;
			end
			if (Frozen_StatOne[i] == 0) then
				StaticPopup_Hide("StatCount");
				DEFAULT_CHAT_FRAME:AddMessage("|cffFFC125[温馨提示]: 这个属性不能加点|r");
				return;
			end

			local EditboxTxt = DoStatType[i][1] .. "|cFF00FF00加点\n\n" .. "|cFF00FF00总" .. DoStatType[i][1] .. "|cFF00FF00最大可加点数为" .. Frozen_StatMax[i];
			if (Frozen_StatMax[i] == 0) then
				EditboxTxt = DoStatType[i][1] .. "|cFF00FF00加点";
			end
			SendStatPointCDKEY(EditboxTxt, DoStatType[i][2]);
		end)

	StatClickRightButton[i]:SetScript("OnEnter",
		function(self)
			GameTooltip:SetOwner(StatClickRightButton[i], "ANCHOR_RIGHT");
			GameTooltip.default = 1;
			local OnEnterTxt = DoStatType[i][1] .. "|cFF00FF00每点奖励[" .. Frozen_StatOne[i] .. "]\n\n|cFF00FF00最大可加点数为" .. Frozen_StatMax[i];
			if (Frozen_StatMax[i] == 0 and Frozen_StatOne[i] > 0) then
				OnEnterTxt = DoStatType[i][1] .. "|cFF00FF00每点奖励[" .. Frozen_StatOne[i] .. "]";
			elseif (Frozen_StatOne[i] == 0) then
				OnEnterTxt = DoStatType[i][1] .. "|cFFFF0000禁止加点.";
			end
			GameTooltip:SetText(OnEnterTxt);
			GameTooltip:Show();
		end)
	StatClickRightButton[i]:SetScript("OnLeave",
		function(self)
			GameTooltip:Hide();
		end)
end

--重置按钮
local StatResetButton = CreateFrame("Button", "StatResetButton", StatPointFrame);
StatResetButton:SetSize(180, 30);
StatResetButton:SetPoint("BOTTOM", 0, 60);
StatResetButton:EnableMouse(true);
StatResetButton:SetBackdrop({ bgFile = "Interface\\Icons\\BtnNormal" })
StatResetButton:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight")
StatResetButton:SetScript("OnClick",
	function(Self)
		Ghost_SendData("GetResetStat");
	end)

StatPointFrame:SetScript("OnHide",
	function(self)
		StaticPopup_Hide("StatCount");
		StaticPopup_Hide("ResetStatPt");
	end)

local FontStringReset = StatResetButton:CreateFontString("FontStringReset");
FontStringReset:SetFont(Frozen_GameFont, 15);
StatResetButton:SetFontString(FontStringReset);
StatResetButton:SetText("|cffFFC125重置属性点数|r");

function SendStatPointCDKEY(Stattext, Stattype)
	local Dialog = StaticPopup_Show("StatCount", Stattext, Stattype);
	StatPointFrame:SetAlpha(0.7);
	Dialog.Type = Stattype;
	Dialog:SetScript("OnHide",
		function(self)
			StatPointFrame:SetAlpha(1.0);
		end)
end

--消息处理
local function Dream_OnStatEvent(Self, event, Packet, Msg, Type, Sender)
	if (event == "CHAT_MSG_ADDON") then
		if (Packet == "SendStatData") then
			local Args = Frozen_Split(Msg, "#");
			Frozen_StatPointData[1] = Args[1];
			for i = 2, #Args do
				Frozen_StatPointData[i] = tonumber(Args[i]);
			end

			for i = 1, 13 do
				StatNowPointStr[i]:SetText(Frozen_StatPointData[i + LeftIndex] .. "点   奖励 :[" .. tostring(Frozen_StatPointData[i + LeftIndex] * Frozen_StatOne[i]) .. "]");
			end

			RankPointsText:SetText("|cFF00FFFF当前等级|r |cFFFF66FF" .. Frozen_StatPointData[3] .. " / " .. Frozen_MaxLevel .. "|r");
			AttributesPointsComment:SetText(Frozen_StatPointData[1] .. "  |cFF00FFFF点数|r |cFFFF66FF(" .. Frozen_StatPointData[2] .. ")|r");

			local Sce = (Frozen_StatPointData[5] > 0) and (Frozen_StatPointData[4] * 100 / Frozen_StatPointData[5]) or 0;
			if (Frozen_MaxLevel == Frozen_StatPointData[3]) then
				ButtonBarTexts:SetText("|cFF00FF00MAX|r")
				AtStatusBarOne:SetValue(math.floor(100))
			else
				ButtonBarTexts:SetText("|cFF00FF00当前经验" .. Frozen_StatPointData[4] .. "/" .. Frozen_StatPointData[5] .. " 进度 " .. string.format("%.f", Sce) .. "%|r")
				AtStatusBarOne:SetValue(math.floor(Sce))
			end
		elseif (Packet == "SendResetStat") then
			if (Msg == "CanNotDo") then
				DEFAULT_CHAT_FRAME:AddMessage("|cffFFC125[提示]: 没有开放属性重置|r");
			else
				local Dialog = StaticPopup_Show("ResetStatPt", "\n|cFF00FF00重置属性需求|r\n\n" .. Msg .. "\n|r\n|cFF00FF00你确定要这么做吗?|r", "");
				StatPointFrame:SetAlpha(0.6);
				Dialog:SetScript("OnHide",
					function(self)
						StatPointFrame:SetAlpha(1.0);
					end)
			end
		elseif (Packet == "SendStatConf") then
			local Args = Frozen_Split(Msg, "#");
			StatTitleText:SetText("|cFF00FF00" .. Args[1] .. "|r");
			Frozen_MaxLevel = tonumber(Args[28]);
			for i = 1, 13 do
				Frozen_StatOne[i] = tonumber(Args[i + 1]);
				Frozen_StatMax[i] = tonumber(Args[i + 14]);
				StatNowPointStr[i]:SetText(Frozen_StatPointData[i + LeftIndex] .. "点   奖励 :[" .. tostring(Frozen_StatPointData[i + LeftIndex] * Frozen_StatOne[i]) .. "]");
			end
			local Sce = (Frozen_StatPointData[5] > 0) and (Frozen_StatPointData[4] * 100 / Frozen_StatPointData[5]) or 0;
			if (Frozen_MaxLevel == Frozen_StatPointData[3]) then
				ButtonBarTexts:SetText("|cFF00FF00MAX|r")
				AtStatusBarOne:SetValue(math.floor(100))
			else
				ButtonBarTexts:SetText("|cFF00FF00当前经验" .. Frozen_StatPointData[4] .. "/" .. Frozen_StatPointData[5] .. " 进度 " .. string.format("%.f", Sce) .. "%|r")
				AtStatusBarOne:SetValue(math.floor(Sce))
			end
		end
	elseif (event == "PLAYER_LOGIN") then
		Ghost_SendData("GetStatConf"); --获取配置属性
		Ghost_SendData("GetStatData"); --获取玩家属性
	end
	--DEFAULT_CHAT_FRAME:AddMessage("event    "..event);
end

--交互消息事件
local EventStatFrame = CreateFrame("Frame", "EventStatFrame");
EventStatFrame:RegisterEvent("CHAT_MSG_ADDON");
EventStatFrame:RegisterEvent("PLAYER_LOGIN");
EventStatFrame:RegisterEvent("SOCKET_INFO_UPDATE");
EventStatFrame:RegisterEvent("CHALLENGE_MODE_MAPS_UPDATE");
EventStatFrame:RegisterEvent("AUCTION_ITEM_LIST_UPDATE");
EventStatFrame:RegisterEvent("AUCTION_HOUSE_SHOW");
EventStatFrame:RegisterEvent("MERCHANT_SHOW");
--EventStatFrame:RegisterAllEvents()
EventStatFrame:SetScript("OnEvent", Dream_OnStatEvent);


SLASH_DKDFDJ1 = '/dfdj';

local function handlers(msg, editBox)
	if StatPointFrame:IsShown() then
		StatPointFrame:Hide()
	else
		StatPointFrame:Show()
	end
end

SlashCmdList["DKDFDJ"] = handlers;