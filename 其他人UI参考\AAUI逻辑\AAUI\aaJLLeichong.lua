 aaJLLeichong = {};
 aaJLLeichong.LevelViews = {};
 aaJLLeichong.levels = {};
 local titletop = -100;
local titleleft = 50;
 function aaJLLeichong:ClickDoneBtn(index,btn) 
 local level = aaJLLeichong.levels[index];
 aaData:sendAddonMsg(10005, level, "GUILD");
 end;
 aaJLLeichong.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaJLLeichong.mainView:SetFrameStrata("TOOLTIP");
 aaJLLeichong.mainView:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-lcjl"});
 aaJLLeichong.mainView:SetWidth(512);
 aaJLLeichong.mainView:SetHeight(512);
 aaJLLeichong.mainView:SetPoint("CENTER",0,0);
 aaJLLeichong.mainView:SetMovable(1);
 aaJLLeichong.mainView:EnableMouse();
 aaJLLeichong.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaJLLeichong.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaJLLeichong.mainView:Hide();
 end;
 local titlewidth1 = 80;
local titlewidth2 = 262;
local titlewidth3 = 80;
local titletop = -120;
local titletop1 = titletop-30;
 aaJLLeichong.title = aaJLLeichong.mainView:CreateFontString("aaJLLeichong.title", "OVERLAY", "GameFontNormal") do 
 aaJLLeichong.title:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLeichong.title:SetPoint("TOPLEFT", aaJLLeichong.mainView, "TOPLEFT", 45,titletop);
 aaJLLeichong.title:SetWidth(titlewidth1+titlewidth2+titlewidth3);
 aaJLLeichong.title:SetHeight(20);
 aaJLLeichong.title:SetSpacing(5);
 aaJLLeichong.title:SetText(aaData.color_green.."asdada阿斯达撒大所大所大所大所多");
 end;
 aaJLLeichong.leveltitle = aaJLLeichong.mainView:CreateFontString("aaJLLeichong.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLeichong.leveltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLeichong.leveltitle:SetPoint("TOPLEFT", aaJLLeichong.mainView, "TOPLEFT", 45,titletop1);
 aaJLLeichong.leveltitle:SetWidth(titlewidth1);
 aaJLLeichong.leveltitle:SetHeight(20);
 aaJLLeichong.leveltitle:SetSpacing(5);
 aaJLLeichong.leveltitle:SetText(aaData.color_green.."累计充值");
 end;
 aaJLLeichong.jltitle = aaJLLeichong.mainView:CreateFontString("aaJLLeichong.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLeichong.jltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLeichong.jltitle:SetPoint("TOPLEFT", aaJLLeichong.mainView, "TOPLEFT", 45+titlewidth1,titletop1);
 aaJLLeichong.jltitle:SetWidth(titlewidth2);
 aaJLLeichong.jltitle:SetHeight(20);
 aaJLLeichong.jltitle:SetSpacing(5);
 aaJLLeichong.jltitle:SetText(aaData.color_green.."奖励");
 end;
 aaJLLeichong.zttitle = aaJLLeichong.mainView:CreateFontString("aaJLLeichong.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLeichong.zttitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLeichong.zttitle:SetPoint("TOPLEFT", aaJLLeichong.mainView, "TOPLEFT", 45+titlewidth1+titlewidth2,titletop1);
 aaJLLeichong.zttitle:SetWidth(titlewidth3);
 aaJLLeichong.zttitle:SetHeight(20);
 aaJLLeichong.zttitle:SetSpacing(5);
 aaJLLeichong.zttitle:SetText(aaData.color_green.."状态");
 end;
 function aaJLLeichong:CreateCell(index,x,y) 
 local menuView = CreateFrame("Frame",nil,aaJLLeichong.mainView) do 
 menuView:SetFrameStrata("TOOLTIP");
 menuView:SetWidth(titlewidth1+titlewidth2+titlewidth3);
 menuView:SetHeight(30);
 menuView:SetPoint("TOPLEFT",aaJLLeichong.mainView,'TOPLEFT',x,y);
 menuView:Hide();
 end;
 menuView.levelText = menuView:CreateFontString("menuView.levelText", "OVERLAY", "GameFontNormal") do 
 menuView.levelText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.levelText:SetPoint("TOPLEFT", menuView, "TOPLEFT", 0,0);
 menuView.levelText:SetWidth(titlewidth1);
 menuView.levelText:SetHeight(30);
 menuView.levelText:SetSpacing(5);
 menuView.levelText:SetText(aaData.color_blue.."Lv888");
 end;
 menuView.itemText = menuView:CreateFontString("menuView.itemText", "OVERLAY", "GameFontNormal") do 
 menuView.itemText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.itemText:SetPoint("TOPLEFT", menuView, "TOPLEFT", titlewidth1,0);
 menuView.itemText:SetWidth(titlewidth2);
 menuView.itemText:SetHeight(30);
 menuView.itemText:SetSpacing(5);
 menuView.itemText:SetText(aaData.color_blue.."Lv888");
 end;
 local btnwidth = 70;
 local btnheight = 30;
 menuView.levelBtn = CreateFrame('Button', nil, menuView, 'UIPanelButtonTemplate') do 
 menuView.levelBtn:SetPoint('TOPLEFT', menuView, 'TOPLEFT', titlewidth1+titlewidth2+(titlewidth3-btnwidth)/2, 0);
 menuView.levelBtn:SetSize(btnwidth, btnheight);
 menuView.levelBtn:Disable();
 menuView.levelBtn:SetText("已领取");
 menuView.levelBtn:SetScript('OnClick', function() aaJLLeichong:ClickDoneBtn(index,menuView.levelBtn);
 end);
 end;
 return menuView;
 end;
 local top = -175;
 local topspace = -35;
 aaJLLeichong.LevelViews[1] = aaJLLeichong:CreateCell(1,45,top+topspace*0);
 aaJLLeichong.LevelViews[2] = aaJLLeichong:CreateCell(2,45,top+topspace*1);
 aaJLLeichong.LevelViews[3] = aaJLLeichong:CreateCell(3,45,top+topspace*2);
 aaJLLeichong.LevelViews[4] = aaJLLeichong:CreateCell(4,45,top+topspace*3);
 aaJLLeichong.LevelViews[5] = aaJLLeichong:CreateCell(5,45,top+topspace*4);
 aaJLLeichong.LevelViews[6] = aaJLLeichong:CreateCell(6,45,top+topspace*5);
 aaJLLeichong.LevelViews[7] = aaJLLeichong:CreateCell(7,45,top+topspace*6);
 aaJLLeichong.LevelViews[8] = aaJLLeichong:CreateCell(8,45,top+topspace*7);
 aaJLLeichong.LevelViews[9] = aaJLLeichong:CreateCell(9,45,top+topspace*8);
 function aaJLLeichong:reload() 
 local index = 1;
 aaJLLeichong.levels = {};
 for k,v in pairs(aa_jlleichong) do 
 aaJLLeichong.levels[index] = k;
 index = index + 1;
 end;
 table.sort(aaJLLeichong.levels);
 index = 1;
 for i = 1,#aaJLLeichong.levels do 
 local k = aaJLLeichong.levels[i];
 local v = aa_jlleichong[k];
 if k>0 and v~=nil and v~="" and v~={} then aaJLLeichong.LevelViews[index]:Show();
 local isOk = v[1]+0;
 local jfall = v[2]+0;
 local level = k+0;
 local reward = v[3];
 aaJLLeichong.title:SetText("当前累计充值："..aaData.color_blue..jfall..worldconf[15].."|r");
 aaJLLeichong.LevelViews[index].levelText:SetText(level..worldconf[15]);
 aaJLLeichong.LevelViews[index].itemText:SetText(reward);
 aaJLLeichong.LevelViews[index]:Show();
 if isOk == 0 then aaJLLeichong.LevelViews[index].levelBtn:SetText("领取");
 aaJLLeichong.LevelViews[index].levelBtn:Enable();
 elseif isOk == 1 then aaJLLeichong.LevelViews[index].levelBtn:SetText("已领取");
 aaJLLeichong.LevelViews[index].levelBtn:Disable();
 elseif isOk == 2 then aaJLLeichong.LevelViews[index].levelBtn:SetText("未达到");
 aaJLLeichong.LevelViews[index].levelBtn:Disable();
 end;
 index = index + 1;
 else aaJLLeichong.LevelViews[index]:Hide();
 end;
 end;
 end;
 local CancelButton = CreateFrame('Button', nil, aaJLLeichong.mainView, 'UIPanelButtonTemplate') do 
 CancelButton:SetPoint('TOPRIGHT', aaJLLeichong.mainView, 'TOPRIGHT', -30, -20);
 CancelButton:SetSize(60, 30);
 CancelButton:SetText("关闭");
 CancelButton:SetScript('OnClick', function() aaJLLeichong:hide();
 end);
 end;
 function aaJLLeichong:show() aaJLLeichong.mainView:Show();
 aaJLLeichong:reload();
 end;
 function aaJLLeichong:hide() aaJLLeichong.mainView:Hide();
 aaJLLeichong:reload();
 end;
