
local ButtonBar = nil
local MyStatusBarOne = nil
local status = nil
local ButtonBar2 = nil
local MyStatusBarOne2 = nil
local status2 = nil
local font = GameFontNormal:GetFont()
local MAX_ITEM_LEVEL = 10000000

do
    ButtonBar = CreateFrame("Button", "ButtonBar", GameTooltip)
    ButtonBar:SetSize(200, 30)
    ButtonBar:SetPoint("TOPLEFT", 50, -32)
    ButtonBar:SetNormalTexture("Interface\\GLUES\\LoadingBar\\Loading-BarBorder")
    ButtonBar:Hide()

    MyStatusBarOne = CreateFrame("StatusBar", "MyStatusBarOne", ButtonBar, nil)
    MyStatusBarOne:SetSize(172, 9)
    MyStatusBarOne:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar")
    MyStatusBarOne:SetStatusBarColor(51/255, 102/255, 255/255, 1)
    MyStatusBarOne:SetPoint("CENTER")
    MyStatusBarOne:SetMinMaxValues(0, 1000)
    MyStatusBarOne:Hide()

    status = MyStatusBarOne:CreateFontString("status", "OVERLAY", "TextStatusBarText")
    status:SetSize(172, 4)
    status:SetPoint("CENTER", 0, 1)

    ButtonBar2 = CreateFrame("Button", "ButtonBar2", ItemRefTooltip)
    ButtonBar2:SetSize(200, 30)
    ButtonBar2:SetPoint("TOPLEFT", 50, -32)
    ButtonBar2:SetNormalTexture("Interface\\GLUES\\LoadingBar\\Loading-BarBorder")
    ButtonBar2:Hide()

    MyStatusBarOne2 = CreateFrame("StatusBar", "MyStatusBarOne", ButtonBar2, nil)
    MyStatusBarOne2:SetSize(172, 9)
    MyStatusBarOne2:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar")
    MyStatusBarOne2:SetStatusBarColor(51/255, 102/255, 255/255, 1)
    MyStatusBarOne2:SetPoint("CENTER")
    MyStatusBarOne2:SetMinMaxValues(0, 1000)
    MyStatusBarOne2:Hide()
    
    status2 = MyStatusBarOne2:CreateFontString("status", "OVERLAY", "TextStatusBarText")
    status2:SetSize(172, 4)
    status2:SetPoint("CENTER", 0, 1)
end

local function OnItemTip(self)
	local _, itemLink = self:GetItem()
	if self:GetName() ~= "GameTooltip" and self:GetName() ~= "ItemRefTooltip" then
		return
	end

	if self:GetName() == "GameTooltip" and GetMouseFocus() then
		local name = GetMouseFocus():GetName()
		if string.find(name, "ItemInfoButton%d") ~= nil then
			ButtonBar:Hide()
			MyStatusBarOne:Hide()
			return
		end
	end

	if itemLink then
	    local _,_,_,_, itemId, _, _, _, _, _, _, uniqueId,_,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")

		if uniqueId == "0" and EntryLevelSet[itemId] == nil then
			ButtonBar:Hide()
			MyStatusBarOne:Hide()
            return
        end

		ItemLevelSet["0"] = nil
        local level = ItemLevelSet[uniqueId] or EntryLevelSet[itemId]

        if level == nil then
			if uniqueId ~= "0" then
				local link = "item:"..tostring(MAX_ITEM_LEVEL + tonumber(uniqueId))..":0:0:0:0:0:0:0:1"
				GameTooltip:SetHyperlink(link)
			end
            return
        end

        local toolTip = _G[self:GetName().."TextLeft1"]
        if toolTip:GetText() ~= nil then
		    if string.find(toolTip:GetText(),"|cFF33CCFF〈自动升级〉") == nil then
			toolTip:SetText(toolTip:GetText().."|cFF33CCFF〈自动升级〉\n \n吞噬经验                                                  \n ")
			end
            if self:GetWidth() < 270 then
                self:SetWidth(270)
            end
			
			local y = level.yyy - 33
			if self:GetName() == "GameTooltip" then
				ButtonBar:SetPoint("TOPLEFT", 80, y)
				MyStatusBarOne:SetValue(level.exp)
				MyStatusBarOne:SetMinMaxValues(0, level.maxExp)
				status:SetText(level.exp.."/"..level.maxExp)
				ButtonBar:Show()
				MyStatusBarOne:Show()
			end
            
			if self:GetName() == "ItemRefTooltip" then
				ButtonBar2:SetPoint("TOPLEFT", 80, y)
				MyStatusBarOne2:SetValue(level.exp)
				MyStatusBarOne2:SetMinMaxValues(0, level.maxExp)
				status2:SetText(level.exp.."/"..level.maxExp)
				ButtonBar2:Show()
				MyStatusBarOne2:Show()
			end
        end
	end
end

GameTooltip:HookScript("OnHide", function()
    ButtonBar:Hide()
    MyStatusBarOne:Hide()
end)
ItemRefTooltip:HookScript("OnHide", function()
    ButtonBar2:Hide()
    MyStatusBarOne2:Hide()
end)

hooksecurefunc(GameTooltip, "SetHyperlink", function(self, link)
	OnItemTip(self)
end)

hooksecurefunc(ItemRefTooltip, "SetHyperlink", function(self, link)
	OnItemTip(self)
end)

GameTooltip:HookScript("OnShow", OnItemTip)
ItemRefTooltip:HookScript("OnShow", OnItemTip)
ItemRefShoppingTooltip1:HookScript("OnShow", OnItemTip)
ItemRefShoppingTooltip2:HookScript("OnShow", OnItemTip)
ItemRefShoppingTooltip3:HookScript("OnShow", OnItemTip)
ShoppingTooltip1:HookScript("OnShow", OnItemTip)
ShoppingTooltip2:HookScript("OnShow", OnItemTip)
ShoppingTooltip3:HookScript("OnShow", OnItemTip)