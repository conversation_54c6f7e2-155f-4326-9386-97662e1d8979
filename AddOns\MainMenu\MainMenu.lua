local MainMenu = C<PERSON><PERSON><PERSON>e("But<PERSON>", "MainMenu", UIParent)
MainMenu:SetSize(64, 64)
MainMenu:SetPoint("TOPLEFT", 10, -115)
MainMenu:SetMovable(true)
MainMenu:EnableMouse(true)
MainMenu:RegisterForDrag("LeftButton")
MainMenu:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
MainMenu:SetBackdrop({bgFile = "Interface\\Icons\\info1"})
MainMenu:SetScript("OnDragStart", MainMenu.StartMoving)
MainMenu:SetScript("OnDragStop", MainMenu.StopMovingOrSizing)
MainMenu:SetScript("OnMouseUp", 
function(self)
GLPEXGETGLMB()
GLPGETGLMB()
SignInMenuONOFF()
MenuButtonONOFF()
end)

MainMenu:SetScript("OnEnter", 
    function(self)
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
        GameTooltip.default = 1
        GameTooltip:SetText("|cFFFF0033主菜单|r")
        GameTooltip:Show()
    end)

MainMenu:SetScript("OnLeave", 
    function(self)
        GameTooltip:Hide()
    end)

MainMenu:Show()