local gradientColors = {"|cFFFFCC00","|cFFFFC404","|cFFFFBB08","|cFFFFB20D","|cFFFFAA11","|cFFFFA215","|cFFFF991A","|cFFFF901E","|cFFFF8822","|cFFFF8026","|cFFFF772A"}
local ColSize = 10;

local function countChineseAndNonChinese(str)
    local chineseCount = 0
    local nonChineseCount = 0
	local allChars = {}
    for utf8Char in string.gmatch(str, "([%z\1-\127\194-\244][\128-\191]*)") do
		table.insert(allChars, utf8Char)  -- 将每个字符存入表中
        if utf8Char:match("[\228-\233][\128-\191][\128-\191]") then
            chineseCount = chineseCount + 1     --汉字数量
        else
            nonChineseCount = nonChineseCount + 1   --非汉字数量
        end
    end
    return chineseCount, nonChineseCount, allChars
end

local function splitStringToTable(str)
	local allChars = {}
    for utf8Char in string.gmatch(str, "([%z\1-\127\194-\244][\128-\191]*)") do
		table.insert(allChars, utf8Char)  -- 将每个字符存入表中
    end
	return allChars
end

local function JianBian(msg)
	if ColSize > 1 then ColSize = ColSize - 1; else ColSize = 10; end
	msg = "超级无敌"..msg
	local msg = splitStringToTable(msg)
	local coloredString = ""
	for i = 1, #msg do
		local colorIndex = (i + ColSize - 1) % #gradientColors + 1  -- 计算颜色索引
		local color = gradientColors[colorIndex]
		coloredString = coloredString .. color ..msg[i]
	end
	return coloredString
end

function ItemChangeName(self,uid)
	local itemName = self:GetItem()
	local tooltipname = self:GetName()
	local strs;
	local m = string.find(tooltipname,"Shopping")
    itemName = JianBian(itemName)
	if m then
		strs = _G[self:GetName().."TextLeft"..2]:GetText() --第一行是图标+名字 或名字
		local pos = string.find(strs, "|t")
		if pos ~= nil then
			local Iconpic = string.sub(strs, 1, pos +1)
			_G[self:GetName().."TextLeft"..2]:SetText(Iconpic..itemName);
		else
			_G[self:GetName().."TextLeft"..2]:SetText(itemName);
		end
	else
		strs = _G[self:GetName().."TextLeft"..1]:GetText() --第一行是图标+名字 或名字
		local pos = string.find(strs, "|t")
		if pos ~= nil then
			local Iconpic = string.sub(strs, 1, pos +1)
			_G[self:GetName().."TextLeft"..1]:SetText(Iconpic..itemName);
		else
			_G[self:GetName().."TextLeft"..1]:SetText(itemName);
		end
	end
end

function GhostOnTooltipShow(self)
	local _, itemLink = self:GetItem()
	local itemSplits={strsplit(":", itemLink)};
	local guid=tonumber(itemSplits[9]) or 0;
	ItemChangeName(self,guid)

	-- local needShow = (AuctionFrame and AuctionFrame:IsShown());		--拍卖行 可以判断
	-- local bankShow = (GuildBankFrame and GuildBankFrame:IsShown());	--公会银行 可以判断
end


GameTooltip:HookScript("OnTooltipSetItem", GhostOnTooltipShow)	-- 735这个是实时更新	--API网站搜索script handlers
ItemRefTooltip:HookScript("OnTooltipSetItem",GhostOnTooltipShow)
ShoppingTooltip1:HookScript("OnTooltipSetItem", GhostOnShoppingTooltipShow)
ShoppingTooltip2:HookScript("OnTooltipSetItem", GhostOnShoppingTooltipShow)