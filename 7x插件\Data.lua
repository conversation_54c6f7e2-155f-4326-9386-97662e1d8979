ITEMCUSENTRY = 200000000

DongConf = {
	CONF_RandomEnchant 	= 103,
	CONF_RandomSpell 	= 104,
}

ITEM_QUALITY_COLORS = {}
-- idk why but the old UIParent version included -1, 8 and 9
ITEM_QUALITY_COLORS[-1] = CreateColor(1, 1, 1) -- invalid (White)
ITEM_QUALITY_COLORS[0] = CreateColor(0.62, 0.62, 0.62) -- Poor (Grey)
ITEM_QUALITY_COLORS[1] = CreateColor(1, 1, 1) -- Common (White)
ITEM_QUALITY_COLORS[2] = CreateColor(0.12, 1, 0) -- Uncommon (Green)
ITEM_QUALITY_COLORS[3] = CreateColor(0, 0.44, 0.87) -- Rare (Blue)
ITEM_QUALITY_COLORS[4] = CreateColor(0.64, 0.21, 0.93) -- Epic (Purple)
ITEM_QUALITY_COLORS[5] = CreateColor(1, 0.5, 0) -- Legendary (Orange)
ITEM_QUALITY_COLORS[6] = CreateColor(0.9, 0.8, 0.5) -- Artifact / Vanity (Light Gold)
ITEM_QUALITY_COLORS[7] = CreateColor(0.9, 0.8, 0.5) -- Artifact / Vanity (Light Gold)
ITEM_QUALITY_COLORS[8] = CreateColor(0, 0.8, 1) -- Heirloom (Light Blue)
ITEM_QUALITY_COLORS[9] = CreateColor(0, 0.8, 1) -- Heirloom (Light Blue)

KP_SPELLICON_HIGHLIGHT_PHAT = {
    "Interface\\Draft\\HighlightTex1",
    "Interface\\Draft\\HighlightTex2",
    "Interface\\Draft\\HighlightTex3",
    "Interface\\Draft\\HighlightTex4",
    "Interface\\Draft\\HighlightTex5",
    "Interface\\Draft\\HighlightTex6",
    "Interface\\Draft\\HighlightTex7",
}

KP_CLASS_ICON = {
    "Interface\\Draft\\warrior",
    "Interface\\Draft\\paladin",
    "Interface\\Draft\\hunter",
    "Interface\\Draft\\rogue",
    "Interface\\Draft\\priest",
    "Interface\\Draft\\deathknight",
    "Interface\\Draft\\shaman",
    "Interface\\Draft\\mage",
    "Interface\\Draft\\warlock",
    "Interface\\Draft\\monk",
    "Interface\\Draft\\druid",
    "Interface\\Draft\\demonhunter",
}

TalentIconId = {
136866,
136870,
136874,
136878,
136882,
136886,
136890,
136894,
136898,
136902,
136906,
136910,
136914,
136918,
136922,
136926,
136930,
136934,
136938,
136942,
136946,
136950,
136954,
136958,
136967,
136971,
136975,
136979,
136983,
136987,
136991,
}

KPDATA = {
	[1] = {
		SpellId = 0,
		Quality = 0,
		ClassId = 0,
		SpecId = 0,
		BannerName = "",
	},
	[2] = {
		Spell = 0,
		Quality = 0,
		ClassId = 0,
		SpecId = 0,
		BannerName = "",
	},
	[3] = {
		Spell = 0,
		Quality = 0,
		ClassId = 0,
		SpecId = 0,
		BannerName = "",
	}	
}

CurrItemEffRefine = {
	[1] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
	[2] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
	[3] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
	[4] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
	[5] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
	--[6] = {guid = 0, successRate = 0, failRew = 0, reqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}},
}

KP_Card = {}

Frozen_GameFont = GameFontNormal:GetFont();

local function InitData()
    if not DongBar then
        DongBar = {}
	else
		if next(DongBar) ~= nil then
			for k, v in pairs(DongBar) do
				v.updatestat = 1;
			end
		end
    end

	if not SwitchConf then
		SwitchConf = {}
	end
end

local function CreateItemTableData(guid)
	if DongBar and DongBar[guid] == nil then
		DongBar[guid]={prefix="", suffix="", quality=0, runeslot=0, runeword=0, upgrade={0, 0}, runeitems={}, slots={}, spells={spellid = {}, spelldes = {}}, stat={"", ""}, stack={"", ""}, enchants = {}, OwnerGuid = 0, updatestat = 0 };
	end
end

function DataSplit(szFullString, szSeparator)
	local nFindStartIndex = 1
	local nSplitIndex = 1
	local nSplitArray = {}
	while true do
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)
		   if not nFindLastIndex then
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))
			break
		   end
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)
		   nSplitIndex = nSplitIndex + 1
	end
	
	return nSplitArray
end

local function FetchData_RandomEnchantDes(msg)
	local enchant_des = DataSplit(msg,":")
	local guid = tonumber(enchant_des[1]);
	if guid ~= nil then
		if DongBar[guid] == nil then
			DongBar[guid]={prefix="", suffix="", quality=0, runeslot=0, runeword=0, upgrade={0, 0}, runeitems={}, slots={}, spells={spellid = {}, spelldes = {}}, stat={"", ""}, stack={"", ""}, enchants = {}, OwnerGuid = 0, updatestat = 0 };
		else
			DongBar[guid].enchants = {}
		end
		for key, value in pairs(enchant_des) do
			if key > 1 then
				table.insert(DongBar[guid].enchants,value);
			end
		end
	end
end

local function FetchData_SwitchConf(msg)
	local data = DataSplit(msg,"#")
	for k,v in pairs(data) do
		local m_key, val = strsplit("~",v);
		local key = tonumber(m_key);
		if m_key and key and val then
			SwitchConf[key] = val;
		end
	end
end

local function FetchData_ItemGuidUpdate(msg)
	local m_itemguid, m_value = strsplit("~",msg);
	local itemguid = tonumber(m_itemguid)
	local value = tonumber(m_value)
	if DongBar[itemguid] == nil then
		DongBar[itemguid]={prefix="", suffix="", quality=0, runeslot=0, runeword=0, upgrade={0, 0}, runeitems={}, slots={}, spells={}, stat={"", ""}, stack={"", ""}, enchants = {}, OwnerGuid = 0, updatestat = 0 };
	end
	DongBar[itemguid].updatestat = value
end

local function FetchData_ItemOwnerGuid(msg)
	local m_itemguid, playerguid = strsplit("~",msg);
	local itemguid = tonumber(m_itemguid)
	if DongBar[itemguid] == nil then
		DongBar[itemguid]={prefix="", suffix="", quality=0, runeslot=0, runeword=0, upgrade={0, 0}, runeitems={}, slots={}, spells={}, stat={"", ""}, stack={"", ""}, enchants = {}, OwnerGuid = 0, updatestat = 0 };
	end
	DongBar[itemguid].OwnerGuid = tonumber(playerguid)
end

local function FetchData_ItemGuidEffects(msg)
	local data = DataSplit(msg,"#")
	local guid = tonumber(data[1])
	if guid ~= nil then
		if DongBar[guid] == nil then
			DongBar[guid]={prefix="", suffix="", quality=0, runeslot=0, runeword=0, upgrade={0, 0}, runeitems={}, slots={}, spells={spellid = {}, spelldes = {}}, stat={"", ""}, stack={"", ""}, enchants = {}, OwnerGuid = 0, updatestat = 0 };
		else
			DongBar[guid].spells = {spellid = {}, spelldes = {}};
		end
		for k,v in pairs(data) do
			if k > 1 then
				local m_key, des = strsplit(",",v);
				local spellid = tonumber(m_key)
				if des == "1" then des = nil end
				table.insert(DongBar[guid].spells.spellid, spellid)
				if des then
					table.insert(DongBar[guid].spells.spelldes, des)
				end
			end
		end
		RefreshCheckButtonShowInfo(guid)
	end
end

local function FetchData_KPData(msg)
	local data = DataSplit(msg,"#")
	for k,v in pairs(data) do
		local SpellId, Quality, ClassId, SpecId, BannerName = strsplit(",",v);
		SpellId, Quality, ClassId, SpecId = tonumber(SpellId), tonumber(Quality), tonumber(ClassId), tonumber(SpecId)
		KPDATA[k].SpellId = tonumber(SpellId)
		KPDATA[k].Quality = tonumber(Quality)
		KPDATA[k].ClassId = tonumber(ClassId)
		KPDATA[k].SpecId = tonumber(SpecId)
		KPDATA[k].BannerName = BannerName
		-- print("k : "..k)
		-- print("SpellId : "..SpellId)
		-- print("Quality : "..Quality)
		-- print("ClassId : "..ClassId)
		-- print("SpecId : "..SpecId)
		-- print("BannerName : "..BannerName)
		UpdateAllVariables(KP_Card[k], SpellId, Quality, ClassId, SpecId, BannerName)
		KP_Card[k].KPAG:Play()
	end
end

local function PrintCurrItemEffRefine()
    for slot, data in pairs(CurrItemEffRefine) do
        print("Slot: " .. slot)
        print("  guid: " .. data.guid)
        print("  successRate: " .. data.successRate)
        print("  failRew: " .. data.failRew)
        print("  reqData:")
        print("    meetLevel: " .. data.reqData.meetLevel)
        print("    desXp: " .. data.reqData.desXp)
        print("    desGoldCount: " .. data.reqData.desGoldCount)
        print("    desTokenCount: " .. data.reqData.desTokenCount)
        print("    tokenName: " .. data.reqData.tokenName)
        print("    itemids: " .. table.concat(data.reqData.itemids, ", "))
        print("    itemcount: " .. table.concat(data.reqData.itemcount, ", "))
    end
end

local function FetchData_InitItemEffRefineData(msg)
	--print(msg)
	local data = DataSplit(msg,"#")
	local guid, effslot, successRate, failRew, reqMsg = unpack(data);
	if guid == nil or effslot == nil then return end
	effslot = tonumber(effslot) ; guid = tonumber(guid); successRate = tonumber(successRate); failRew = tonumber(failRew);
	local reqDatas;
	if reqMsg ~= nil and  type(reqMsg) == "string" then
		reqDatas = DataSplit(reqMsg,",")
	end
	local TempReqData = {meetLevel = 0 , desXp = 0, desGoldCount = 0, desTokenCount = 0, tokenName = "", itemids={}, itemcount = {}}
	
	if reqDatas ~= nil and type(reqDatas) == "table" then
		TempReqData.meetLevel 		= tonumber(reqDatas[1])
		TempReqData.desXp 			= tonumber(reqDatas[2])
		TempReqData.desGoldCount 	= tonumber(reqDatas[3])
		TempReqData.desTokenCount 	= tonumber(reqDatas[4])
		TempReqData.tokenName 		= reqDatas[5]
		for k,v in pairs(reqDatas) do
			if k > 5 then
				if k % 2 == 0 then
					GhostGetItemLink(tonumber(v)) --一定要在这里获取物品链接，不然在其他地方第一此GhostGetItemLink可能会出现nil值
					table.insert(TempReqData.itemids, tonumber(v))
				else
					table.insert(TempReqData.itemcount,tonumber(v))
				end
			end
		end
	end

	if effslot and CurrItemEffRefine[effslot] then
		CurrItemEffRefine[effslot] = {guid = guid, effslot = effslot, successRate = successRate, failRew = failRew, reqData = TempReqData}
		-- PrintCurrItemEffRefine()
	end
	SetHasReqData(true)
end

local function FetchData_ItemEffRefineDataNull(msg)
	IsIfHasReqData()
end

local function RexEvent(self, event, opcode, msg, classtype, sender)
	-- print("event : "..event)	--结果 CHAT_MSG_ADDON
	-- print("opcode : "..opcode)			--结果 MSG_DD
	-- print("msg : "..msg)		--结果 msg
	if opcode == "SER_ITEMOWNGUID" 	then 	FetchData_ItemOwnerGuid(msg)			end
	if opcode == "SER_UPDATESTAT" 	then 	FetchData_ItemGuidUpdate(msg)			end
	if opcode == "SER_ITEMGUID"		then 	FetchData_RandomEnchantDes(msg)			end
	if opcode == "SER_SWITCH" 		then 	FetchData_SwitchConf(msg)				end
	if opcode == "SER_EFFECTS_DES" 	then 	FetchData_ItemGuidEffects(msg)			end
	if opcode == "SER_KPDATA" 		then 	FetchData_KPData(msg)					end
	if opcode == "SER_REFINEDATA" 	then 	FetchData_InitItemEffRefineData(msg)	end
	if opcode == "SER_DATA_NULL" 	then 	FetchData_ItemEffRefineDataNull(msg)	end
end

local DataReceiveMsg = CreateFrame("Frame")
DataReceiveMsg:RegisterEvent("CHAT_MSG_ADDON")
DataReceiveMsg:SetScript("OnEvent", RexEvent)

local DataMsg = CreateFrame("Frame")
DataMsg:RegisterEvent("PLAYER_LOGIN")
DataMsg:SetScript("OnEvent", InitData)