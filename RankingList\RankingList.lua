local MapName ={
[0] = "东部王国",
[1] = "卡利姆多",
[13] = "Testing",
[25] = "Scott Test",
[30] = "奥特兰克山谷",
[33] = "影牙城堡",
[34] = "暴风城监狱",
[35] = "暴风城牢房",
[36] = "死亡矿井",
[37] = "艾萨拉盆地",
[42] = "Collin's Test",
[43] = "哀嚎洞穴",
[44] = "修道院内部",
[47] = "剃刀沼泽",
[48] = "黑暗深渊",
[70] = "奥达曼",
[90] = "诺莫瑞根",
[109] = "沉没的神庙",
[129] = "剃刀高地",
[169] = "翡翠梦境",
[189] = "血色修道院",
[209] = "祖尔法拉克",
[229] = "黑石塔",
[230] = "黑石深渊",
[249] = "奥妮克希亚的巢穴",
[269] = "开启黑暗之门",
[289] = "通灵学院",
[309] = "祖尔格拉布",
[329] = "斯坦索姆",
[349] = "玛拉顿",
[369] = "矿道地铁",
[389] = "怒焰裂谷",
[409] = "熔火之心",
[429] = "厄运之槌",
[449] = "联盟PvP兵营",
[450] = "部落PvP兵营",
[451] = "Development Land",
[469] = "黑翼之巢",
[489] = "战歌峡谷",
[509] = "安其拉废墟",
[529] = "阿拉希盆地",
[530] = "外域",
[531] = "安其拉神殿",
[532] = "卡拉赞",
[533] = "纳克萨玛斯",
[534] = "海加尔山之战",
[540] = "地狱火堡垒：破碎大厅",
[542] = "地狱火堡垒：鲜血熔炉",
[543] = "地狱火堡垒：城墙",
[544] = "玛瑟里顿的巢穴",
[545] = "盘牙湖泊：蒸汽地窟",
[546] = "盘牙湖泊：幽暗沼泽",
[547] = "盘牙湖泊：奴隶围栏",
[548] = "盘牙湖泊：毒蛇神殿",
[550] = "风暴要塞",
[552] = "风暴要塞：禁魔监狱",
[553] = "风暴要塞：生态船",
[554] = "风暴要塞：能源舰",
[555] = "奥金顿：暗影迷宫",
[556] = "奥金顿：塞泰克大厅",
[557] = "奥金顿：法力墓穴",
[558] = "奥金顿：奥金尼地穴",
[559] = "纳格兰竞技场",
[560] = "逃离敦霍尔德",
[562] = "刀锋山竞技场",
[564] = "黑暗神殿",
[565] = "格鲁尔的巢穴",
[566] = "风暴之眼",
[568] = "祖阿曼",
[571] = "诺森德",
[572] = "洛丹伦废墟",
[573] = "ExteriorTest",
[574] = "乌特加德城堡",
[575] = "乌特加德之巅",
[576] = "魔枢",
[578] = "魔环",
[580] = "太阳之井",
[582] = "运输 - 鲁瑟兰到奥伯丁",
[584] = "运输 - 米奈希尔到塞拉摩",
[585] = "魔导师平台",
[586] = "运输 - 埃索达到奥伯丁",
[587] = "运输 - 羽月渡口",
[588] = "运输 - 米奈希尔到奥伯丁",
[589] = "运输 - 奥格瑞玛到格罗姆高",
[590] = "运输 - 格罗姆高到幽暗城",
[591] = "运输 - 幽暗城到奥格瑞玛",
[592] = "Transport: Borean Tundra Test",
[593] = "运输 - 藏宝海湾到棘齿城",
[594] = "Transport: Howling Fjord Sister Mercy (Quest)",
[595] = "净化斯坦索姆",
[596] = "Transport: Naglfar",
[597] = "Craig Test",
[598] = "Sunwell Fix (Unused)",
[599] = "岩石大厅",
[600] = "达克萨隆要塞",
[601] = "艾卓-尼鲁布",
[602] = "闪电大厅",
[603] = "奥杜尔",
[604] = "古达克",
[605] = "Development Land (non-weighted textures)",
[606] = "QA and DVD",
[607] = "远古海滩",
[608] = "紫罗兰监狱",
[609] = "黑锋要塞",
[610] = "Transport: Tirisfal to Vengeance Landing",
[612] = "Transport: Menethil to Valgarde",
[613] = "Transport: Orgrimmar to Warsong Hold",
[614] = "Transport: Stormwind to Valiance Keep",
[615] = "黑曜石圣殿",
[616] = "永恒之眼",
[617] = "达拉然下水道",
[618] = "勇气竞技场",
[619] = "安卡赫特：古代王国",
[620] = "Transport: Moa'ki to Unu'pe",
[621] = "Transport: Moa'ki to Kamagua",
[622] = "Transport: Orgrim's Hammer",
[623] = "Transport: The Skybreaker",
[624] = "阿尔卡冯的宝库",
[628] = "征服之岛",
[631] = "冰冠堡垒",
[632] = "灵魂洪炉",
[641] = "Transport: Alliance Airship BG",
[642] = "Transport: HordeAirshipBG",
[647] = "运输 - 奥格瑞玛到雷霆崖",
[649] = "十字军的试炼",
[650] = "冠军的试炼",
[658] = "萨隆深渊",
[668] = "映像大厅",
[672] = "Transport: The Skybreaker (Icecrown Citadel Raid)",
[673] = "Transport: Orgrim's Hammer (Icecrown Citadel Raid)",
[712] = "Transport: The Skybreaker (IC Dungeon)",
[713] = "Transport: Orgrim's Hammer (IC Dungeon)",
[718] = "Trasnport: The Mighty Wind (Icecrown Citadel Raid)",
[723] = "暴风城",
[724] = "红玉圣殿",
}

local tempmapstr = "269,289,309,329,349,369,389,409,429,449,450,451,469,489,509,529,530,531,532,533,534,540,542,543,544,545,546,547,548,550,552,553,554,555,556,557,558,559,560,562,564,565,566,568,571,572,573,574,575,576,578,580,582,584,585,586,587,588,589,590,591,592,593,594,595,596,597,598,599,600,601,602,603,604,605,606,607"

local InstanceRankRewData = {}
local InstanceRankingData = {}
local InstanceRankingOwnerData = {}
local RankingList_RandInfoTable = {}
local showRank = 30
local GeneralRew = {itemid = 0, count = 0}
local MapIds = {}
local RankingList_InstanceWidth = 150
local RankingOwnerFrame = {}
local currMapid = 0

local function GhostGetItemLink(Entry)
	local itemName, itemLink, itemRarity, itemLevel, itemMinLevel, itemType, itemSubType, itemStackCount, itemEquipLoc, itemTexture, itemSellPrice = GetItemInfo(Entry);
	if not itemLink then
		GameTooltip:SetHyperlink("item:" .. Entry .. ":0:0:0:0:0:0:0");
	end

	return itemLink;
end

local function GhostGetItemIcon(Entry)
	local itemName, itemLink, itemRarity, itemLevel, itemMinLevel, itemType, itemSubType, itemStackCount, itemEquipLoc, itemTexture, itemSellPrice = GetItemInfo(Entry);

	return "|T"..itemTexture..":24|t";
end

local function DataSplit(szFullString, szSeparator)
	local nFindStartIndex = 1
	local nSplitIndex = 1
	local nSplitArray = {}
	while true do
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)
		   if not nFindLastIndex then
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))
			break
		   end
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)
		   nSplitIndex = nSplitIndex + 1
	end
	
	return nSplitArray
end

local function GetMapIdsCount()
    local count = 0
    for k,v in pairs(MapIds) do
        count = count + 1
    end
    return count
end

--清除自己的所有排名
local function HandleClearOwnerRankinglist()
    RankingOwnerFrame.OwnerInstanceStr:SetText("")
    RankingOwnerFrame.OwnerRankingStr:SetText("")
    RankingOwnerFrame.OwnerViewRewardsButton.ItemLink = nil
    RankingOwnerFrame.OwnerViewRewardsButton:Hide()
    RankingOwnerFrame.OwnerTimeStr:SetText("")
    RankingOwnerFrame.OwnerNameStr:SetText("")
end

--清除当前所有副本所有排名显示
local function HandleClearAllRankinglist()
    for k,v in pairs(RankingList_RandInfoTable) do
        v.PlayerRanking:SetText("")
        v.PlayerTime:SetText("")
        v.PlayerName:SetText("")
        v.InstanceName:SetText("")
        v.ViewRewardsButtonStr:SetText("")
        v.ViewRewardsButton:Hide()
    end
    HandleClearOwnerRankinglist()
    InstanceRankingOwnerData = {}
    InstanceRankingData={}
end

local function GetInstanceRankRew(mapid, rank)
    local itemid, count = 0,0

    if InstanceRankRewData[mapid] ~= nil then
        if InstanceRankRewData[mapid].rank ~= nil then
            if InstanceRankRewData[mapid].rank[rank] ~= nil then
                itemid = InstanceRankRewData[mapid].rank[rank].itemid
                count = InstanceRankRewData[mapid].rank[rank].itemcount
            end
        end
    end

    if itemid == 0 and count == 0 then
        itemid = GeneralRew.itemid
        count = GeneralRew.count
    end

    return itemid, count
end

-- 获取排名奖励数据
function GetInstanceRankRewData(msg)
    local mapID, data = strsplit("#", msg)
    local mapid = tonumber(mapID) or 999
    InstanceRankRewData[mapid] = {}
    InstanceRankRewData[mapid].rank = {}
    local rewData = DataSplit(data,",")
    local nCount = 0
    local currRank = 1
    for k,v in pairs(rewData) do
        local val = k - 3 * nCount
        if val == 1 then
            currRank = tonumber(v) or 1
            InstanceRankRewData[mapid].rank[currRank] = {}
        elseif val == 2 then
            InstanceRankRewData[mapid].rank[currRank].itemid =tonumber(v)
            GhostGetItemLink(tonumber(v))
        elseif val == 3 then
            InstanceRankRewData[mapid].rank[currRank].itemcount = tonumber(v)
            nCount = nCount + 1
        end
    end
end

-- 获取所有能显示的玩家排名数据
function GetInstanceRankData(msg)
    local _mapid, _rank, playername, _totalTime = strsplit("#", msg)
    local mapid, rank, totalTime = tonumber(_mapid) or 999, tonumber(_rank) or 99999, tonumber(_totalTime)
    if mapid == 999 or rank == 99999 then return end
    InstanceRankingData[mapid] = InstanceRankingData[mapid] or {}
    InstanceRankingData[mapid][rank] = InstanceRankingData[mapid][rank] or {}
    InstanceRankingData[mapid][rank].playername = playername
    InstanceRankingData[mapid][rank].totalTime = totalTime
    for k,v in pairs(InstanceRankingData[mapid]) do
        print("rank = "..rank)
        print("playername = "..v.playername)
        print("totalTime = "..v.totalTime)
    end
    if mapid == currMapid then HandleCreateRankinglist(mapid) end
end

-- 获取指定地图玩家自己当前排名
function GetInstanceRankOwnerData(msg)
    local _mapid, _rank, _totalTime = strsplit("#", msg)
    local mapid, rank, totalTime = tonumber(_mapid) or 999, tonumber(_rank) or 99999, tonumber(_totalTime) or 99999
    if mapid == 999 or rank == 99999 then return end
    InstanceRankingOwnerData[mapid] = InstanceRankingOwnerData[mapid] or {}
    InstanceRankingOwnerData[mapid].rank = rank
    InstanceRankingOwnerData[mapid].totalTime = totalTime
    local itemid, itemcount = GetInstanceRankRew(mapid, rank)
    GhostGetItemLink(itemid)
    RankingOwnerFrame.OwnerViewRewardsButton.ItemLink = GhostGetItemLink(itemid)
    if mapid == currMapid then HandleCreateOwnerRankinglist(mapid) end
end

function GetInstanceRankshowRank(msg)
    showRank = tonumber(msg) or 30
    if showRank < 30 then 
        local begin = showRank + 1
        for i = begin, 30, 1 do
            RankingList_RandInfoTable[i].PlayerRanking:Hide()
            RankingList_RandInfoTable[i].PlayerName:Hide()
            RankingList_RandInfoTable[i].PlayerTime:Hide()
            RankingList_RandInfoTable[i].InstanceName:Hide()
            RankingList_RandInfoTable[i].ViewRewardsButtonStr:Hide()
            RankingList_RandInfoTable[i].ViewRewardsButton.ItemLink = nil
        end 
    end

    if showRank > 30 then
        local begin = 30 + 1
        for i = begin, showRank, 1 do
            CreateRankingListRankInfo(i, "-", 999, nil, 0,1)
        end
    end
end

function GetInstanceRankGeneralRew(msg)
    local _itemid, _count = strsplit("#", msg)
    GeneralRew.itemid, GeneralRew.count = tonumber(_itemid) or 0, tonumber(_count) or 0
    GhostGetItemLink(GeneralRew.itemid)
end

function GetInstanceRankMapIDs(msg)
    local mapids = DataSplit(msg,",")
    local i = 1
    for k,v in pairs(mapids) do
        MapIds[tonumber(v)] = i
        i = i + 1
    end
    CreateRankingListInstanceButtonsFromDatas()
end

local function GetMapName(mapID)
    return MapName[mapID] or "未知地图";
end

local RankingList_InstanceButtonTable = {}

local function FormatTime(seconds)
    if seconds < 60 then
        return string.format("%d秒", seconds)
    elseif seconds < 3600 then
        local minutes = math.floor(seconds / 60)
        local remainingSeconds = seconds % 60
        return string.format("%d分%d秒", minutes, remainingSeconds)
    else
        local hours = math.floor(seconds / 3600)
        local remainingMinutes = math.floor((seconds % 3600) / 60)
        local remainingSeconds = seconds % 60
        return string.format("%d时%d分%d秒", hours, remainingMinutes, remainingSeconds)
    end
end

function HandleCreateOwnerRankinglist(mapid)
    if InstanceRankingOwnerData[mapid] ~= nil then
        local rank = InstanceRankingOwnerData[mapid].rank
        local itemid, count = GetInstanceRankRew(mapid, rank)
        local rewStr = itemid > 0 and (GhostGetItemLink(itemid).." * "..count) or "无奖励"
        RankingOwnerFrame.OwnerRankingStr:SetText(InstanceRankingOwnerData[mapid].rank)
        RankingOwnerFrame.OwnerInstanceStr:SetText(GetMapName(mapid))
        RankingOwnerFrame.OwnerTimeStr:SetText(FormatTime(InstanceRankingOwnerData[mapid].totalTime))
        RankingOwnerFrame.OwnerViewRewardsButtonStr:SetText(rewStr)
        RankingOwnerFrame.OwnerViewRewardsButton.ItemLink = GhostGetItemLink(itemid)
    else
        RankingOwnerFrame.OwnerRankingStr:SetText("-")
        RankingOwnerFrame.OwnerInstanceStr:SetText(GetMapName(mapid))
        RankingOwnerFrame.OwnerTimeStr:SetText("-")
        RankingOwnerFrame.OwnerViewRewardsButtonStr:SetText("无奖励")
    end
end

function HandleCreateRankinglist(mapid)
    if InstanceRankingData[mapid] ~= nil then
        local count = 0
        for rank, v in pairs(InstanceRankingData[mapid]) do
            if rank > showRank then return end
            local totaltime = v.totalTime
            local playername = v.playername
            local itemid , itemcount = GetInstanceRankRew(mapid, rank)
            local rewStr = itemid > 0 and (GhostGetItemLink(itemid).." * "..itemcount) or "无奖励"
            if RankingList_RandInfoTable[rank] == nil then
                RankingList_RandInfoTable[rank] = CreateRankingListRankInfo(rank, mapid, totaltime, itemid, itemcount)
            else
                RankingList_RandInfoTable[rank].PlayerName:SetText(playername)
                RankingList_RandInfoTable[rank].PlayerTime:SetText(FormatTime(totaltime))
                RankingList_RandInfoTable[rank].InstanceName:SetText(GetMapName(mapid))
                RankingList_RandInfoTable[rank].ViewRewardsButtonStr:SetText(rewStr)
                RankingList_RandInfoTable[rank].ViewRewardsButton.ItemLink = GhostGetItemLink(itemid)
            end
            count = count + 1
        end
        if count < showRank then
            count = count + 1
            for rank = count, showRank do
                local itemid ,itemcount = GetInstanceRankRew(mapid, rank)
                local rewStr = itemid > 0 and (GhostGetItemLink(itemid).." * "..itemcount) or "无奖励"
                RankingList_RandInfoTable[rank].PlayerName:SetText("-")
                RankingList_RandInfoTable[rank].PlayerTime:SetText("-")
                RankingList_RandInfoTable[rank].InstanceName:SetText(GetMapName(mapid))
                RankingList_RandInfoTable[rank].ViewRewardsButtonStr:SetText(rewStr)
                RankingList_RandInfoTable[rank].ViewRewardsButton.ItemLink = GhostGetItemLink(itemid)
            end
        end
    else
        for rank = 1, showRank do
            local itemid ,count = GetInstanceRankRew(mapid, rank)
            local rewStr = itemid > 0 and (GhostGetItemLink(itemid).." * "..count) or "无奖励"
            RankingList_RandInfoTable[rank].PlayerName:SetText("-")
            RankingList_RandInfoTable[rank].PlayerTime:SetText("-")
            RankingList_RandInfoTable[rank].InstanceName:SetText(GetMapName(mapid))
            RankingList_RandInfoTable[rank].ViewRewardsButtonStr:SetText(rewStr)
            RankingList_RandInfoTable[rank].ViewRewardsButton.ItemLink = GhostGetItemLink(itemid)
        end
    end
end

local RankingList = CreateFrame("Frame", "RankingList", UIParent);
RankingList:SetSize(1200, 680);
RankingList:RegisterForDrag("LeftButton");
RankingList:SetPoint("CENTER");
RankingList:SetToplevel(true);
RankingList:SetClampedToScreen(true);
RankingList:SetBackdrop({ bgFile = "Interface/Tooltips/UI-Tooltip-Background" });
RankingList:Hide()

local RankingList_Title = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
RankingList_Title:SetFont("Fonts\\ZYKai_C.TTF", 20, "OUTLINE");
RankingList_Title:SetTextColor(1, 1, 1, 1);
RankingList_Title:SetPoint("TOP", 0, -10);
RankingList_Title:SetText("副本时速排行榜");

local RankingList_RankStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
RankingList_RankStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
RankingList_RankStr:SetTextColor(1, 1, 1, 1);
RankingList_RankStr:SetPoint("TOPLEFT", 50, -100);
RankingList_RankStr:SetText("名次");

local RankingList_NameStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
RankingList_NameStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
RankingList_NameStr:SetTextColor(1, 1, 1, 1);
RankingList_NameStr:SetPoint("TOPLEFT", 250, -100);
RankingList_NameStr:SetText("玩家");

local RankingList_InstanceStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
RankingList_InstanceStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
RankingList_InstanceStr:SetTextColor(1, 1, 1, 1);
RankingList_InstanceStr:SetPoint("TOPLEFT", 550, -100);
RankingList_InstanceStr:SetText("副本名称");

local RankingList_TimeStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
RankingList_TimeStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
RankingList_TimeStr:SetTextColor(1, 1, 1, 1);
RankingList_TimeStr:SetPoint("TOPLEFT", 830, -100);
RankingList_TimeStr:SetText("用时");

local RankingList_RewStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
RankingList_RewStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
RankingList_RewStr:SetTextColor(1, 1, 1, 1);
RankingList_RewStr:SetPoint("TOPLEFT", 1050, -100);
RankingList_RewStr:SetText("奖励");

local RankingList_CloseButton = CreateFrame("Button", nil, RankingList, "UIPanelCloseButton");
RankingList_CloseButton:SetSize(32, 32);
RankingList_CloseButton:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up");
RankingList_CloseButton:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down");
RankingList_CloseButton:SetDisabledTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up");
RankingList_CloseButton:SetHighlightTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight");
RankingList_CloseButton:SetScript("OnClick", function() RankingList:Hide() end);
RankingList_CloseButton:SetPoint("TOPRIGHT", -10, -10);

local RankingList_ScrollFrame = CreateFrame("ScrollFrame","RankingList_ScrollFrame", RankingList)
RankingList_ScrollFrame:SetSize(1140,40)
RankingList_ScrollFrame:SetPoint("TOPLEFT",30,-50)
RankingList_ScrollFrame:Show()
RankingList_ScrollFrame:EnableMouse(true)
RankingList_ScrollFrame:EnableMouseWheel(true)
--RankingList_ScrollFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});

local RankingList_ScrollChildFrame = CreateFrame("Frame", "RankingList_ScrollChildFrame", RankingList_ScrollFrame)
RankingList_ScrollChildFrame:SetSize(5000, 40)
RankingList_ScrollChildFrame:SetPoint("CENTER",0,0)
RankingList_ScrollChildFrame:Show()
RankingList_ScrollFrame:SetScrollChild(RankingList_ScrollChildFrame)

local function CreateInstanceButton(x, y, mapid, i)
    local RankingList_InstanceButton = CreateFrame("Button", "RankingList_InstanceButton"..tostring(i), RankingList_ScrollChildFrame, "UIPanelButtonTemplate");
    RankingList_InstanceButton:SetPoint("TOPLEFT", x, y);
    RankingList_InstanceButton:SetSize(RankingList_InstanceWidth, 30);
    RankingList_InstanceButton:SetText(GetMapName(mapid));
    RankingList_InstanceButton.mapid = mapid
    RankingList_InstanceButton:SetScript("OnClick", function(self) 
    print(self.mapid)
    currMapid = self.mapid
    HandleCreateRankinglist(currMapid)
    HandleCreateOwnerRankinglist(currMapid)
    end);
    return RankingList_InstanceButton
end

RankingList_ScrollChildFrame:SetWidth((RankingList_InstanceWidth + 20) * 30)

function CreateRankingListInstanceButtonsFromData(j, ...)
    for i = 1, j do
        local RankingList_InstanceButton = CreateInstanceButton((i - 1) * (RankingList_InstanceWidth + 20), 0, 999, i)
        RankingList_InstanceButtonTable[i] = RankingList_InstanceButton
    end
end
CreateRankingListInstanceButtonsFromData(30)

function CreateRankingListInstanceButtonsFromDatas()
    local count = 0
    for mapid,v in pairs(MapIds) do
        if RankingList_InstanceButtonTable[v] == nil then
            local RankingList_InstanceButton = CreateInstanceButton((v - 1) * (RankingList_InstanceWidth + 20), 0, mapid, v)
            RankingList_InstanceButtonTable[v] = RankingList_InstanceButton
        else
            RankingList_InstanceButtonTable[v]:SetText(GetMapName(mapid))
            RankingList_InstanceButtonTable[v].mapid = mapid
        end
        count = count + 1
    end
    for k,v in pairs(RankingList_InstanceButtonTable) do
        if k > count then
            v:Hide()
            RankingList_InstanceButtonTable[k] = nil
        end
    end
    RankingList_ScrollChildFrame:SetWidth((RankingList_InstanceWidth + 20) * count)
end

local currValue = 0
RankingList_ScrollFrame:SetScript("OnMouseWheel",
function (self,delta)
    local newValue = currValue - delta * 5
    newValue = max(newValue, 0)
    newValue = min(newValue, 100)
    RankingList_ScrollFrame:SetHorizontalScroll((RankingList_ScrollChildFrame:GetWidth() - RankingList_ScrollFrame:GetWidth()) / 100 * newValue)
    currValue = newValue
    print(RankingList_ScrollFrame:GetHorizontalScroll())
end)


local RankingList_InfoScrollFrame = CreateFrame("ScrollFrame","RankingList_InfoScrollFrame", RankingList)
RankingList_InfoScrollFrame:SetSize(1140,515)
RankingList_InfoScrollFrame:SetPoint("TOP",0,-130)
RankingList_InfoScrollFrame:Show()
RankingList_InfoScrollFrame:EnableMouse(true)
RankingList_InfoScrollFrame:EnableMouseWheel(true)
--RankingList_InfoScrollFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});

local RankingList_InfoScrollChildFrame = CreateFrame("Frame", "RankingList_InfoScrollChildFrame", RankingList_InfoScrollFrame)
RankingList_InfoScrollChildFrame:SetSize(1140, 5000)
RankingList_InfoScrollChildFrame:SetPoint("CENTER",0,0)
RankingList_InfoScrollChildFrame:Show()
RankingList_InfoScrollFrame:SetScrollChild(RankingList_InfoScrollChildFrame)
--RankingList_InfoScrollChildFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});

local InfoScrollChildPlayerNameStr = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
InfoScrollChildPlayerNameStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
InfoScrollChildPlayerNameStr:SetTextColor(1, 1, 1, 1);
InfoScrollChildPlayerNameStr:SetPoint("TOPLEFT", 220, 20);
InfoScrollChildPlayerNameStr:SetText("玩家");

local InfoScrollChildInstanceStr = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
InfoScrollChildInstanceStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
InfoScrollChildInstanceStr:SetTextColor(1, 1, 1, 1);
InfoScrollChildInstanceStr:SetPoint("TOPLEFT", 520, 20);
InfoScrollChildInstanceStr:SetText("副本名称");

local InfoScrollChildTimeStr = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal");
InfoScrollChildTimeStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
InfoScrollChildTimeStr:SetTextColor(1, 1, 1, 1);
InfoScrollChildTimeStr:SetPoint("TOPLEFT", 800, 20);
InfoScrollChildTimeStr:SetText("用时");

local InfoScrollChildRewStr = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal");
InfoScrollChildRewStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
InfoScrollChildRewStr:SetTextColor(1, 1, 1, 1);
InfoScrollChildRewStr:SetPoint("TOPLEFT", 1020, 20);
InfoScrollChildRewStr:SetText("奖励");


local currValue_Info = 0
RankingList_InfoScrollFrame:SetScript("OnMouseWheel",
function (self,delta)
    local newValue = currValue_Info - delta * 5
    newValue = max(newValue, 0)
    newValue = min(newValue, 100)
    RankingList_InfoScrollFrame:SetVerticalScroll((RankingList_InfoScrollChildFrame:GetHeight() - RankingList_InfoScrollFrame:GetHeight()) / 100 * newValue)
    currValue_Info = newValue
    --print(RankingList_InfoScrollFrame:GetVerticalScroll())
end)

function CreateRankingListRankInfo(val, PlayerName, Mapid, time, itemid, count)
    local Frame = {}

    local RankingList_PlayerRanking = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
    RankingList_PlayerRanking:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
    RankingList_PlayerRanking:SetTextColor(1, 1, 1, 1);
    if val == 1 then
        RankingList_PlayerRanking:SetPoint("TOPLEFT", 35, -10);
    else
        RankingList_PlayerRanking:SetPoint("TOP",RankingList_RandInfoTable[val - 1].PlayerRanking,"BOTTOM", 0, -10);
    end
    RankingList_PlayerRanking:SetText(val);
    Frame.PlayerRanking = RankingList_PlayerRanking

    local RankingList_PlayerName = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
    RankingList_PlayerName:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
    RankingList_PlayerName:SetTextColor(1, 1, 1, 1);
    if val == 1 then
        RankingList_PlayerName:SetPoint("TOP", InfoScrollChildPlayerNameStr, "BOTTOM",0, -10);
    else
        RankingList_PlayerName:SetPoint("TOP", RankingList_RandInfoTable[val - 1].PlayerName, "BOTTOM",0, -10);
    end
    RankingList_PlayerName:SetText(PlayerName);
	RankingList_PlayerName:SetJustifyH("CENTER")

    Frame.PlayerName = RankingList_PlayerName

    local RankingList_InstanceName = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
    RankingList_InstanceName:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
    RankingList_InstanceName:SetTextColor(1, 1, 1, 1);
    if val == 1 then
    RankingList_InstanceName:SetPoint("TOP",InfoScrollChildInstanceStr,"BOTTOM", 0, -10);
    else
        RankingList_InstanceName:SetPoint("TOP", RankingList_RandInfoTable[val - 1].InstanceName, "BOTTOM",0, -10);
    end
    RankingList_InstanceName:SetText(GetMapName(Mapid));
    Frame.InstanceName = RankingList_InstanceName

    local RankingList_PlayerTime = RankingList_InfoScrollChildFrame:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge");
    RankingList_PlayerTime:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
    RankingList_PlayerTime:SetTextColor(1, 1, 1, 1);
    if val == 1 then
        RankingList_PlayerTime:SetPoint("TOP",InfoScrollChildTimeStr, "BOTTOM", 0, -10 * val);
    else
        RankingList_PlayerTime:SetPoint("TOP", RankingList_RandInfoTable[val - 1].PlayerTime, "BOTTOM",0, -10);
    end
    if time ~= nil then
        RankingList_PlayerTime:SetText(FormatTime(time));
    else
        RankingList_PlayerTime:SetText("-");
    end
    Frame.PlayerTime = RankingList_PlayerTime

    local RankingList_ViewRewardsButton = CreateFrame("Button", "RankingList_ViewRewardsButton"..val, RankingList_InfoScrollChildFrame);
    RankingList_ViewRewardsButton:SetSize(150, 20);
    if val == 1 then
        RankingList_ViewRewardsButton:SetPoint("TOP", InfoScrollChildRewStr, "BOTTOM", 0, -5 * val);
    else
        RankingList_ViewRewardsButton:SetPoint("TOP", RankingList_RandInfoTable[val - 1].ViewRewardsButton, "BOTTOM", 0, -8);
    end
    
    RankingList_ViewRewardsButton:SetScript("OnClick", function(self) print("骄傲") HandleClearAllRankinglist()  end);
    RankingList_ViewRewardsButton:SetScript("OnEnter", function(self) if self.ItemLink then
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
        GameTooltip:SetHyperlink(self.ItemLink)
    end end)
    RankingList_ViewRewardsButton:SetScript("OnEnter", function(self) if self.ItemLink then
        GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
        GameTooltip:SetHyperlink(self.ItemLink)
    end end)
    RankingList_ViewRewardsButton:SetScript("OnLeave", function(self) GameTooltip_Hide() end)
    Frame.ViewRewardsButton = RankingList_ViewRewardsButton
    Frame.ViewRewardsButton.ItemLink = GhostGetItemLink(itemid)

    local RankingList_ViewRewardsButtonStr = RankingList_ViewRewardsButton:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
    RankingList_ViewRewardsButtonStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
    RankingList_ViewRewardsButtonStr:SetTextColor(1, 1, 1, 1);
    if val == 1 then
        RankingList_ViewRewardsButtonStr:SetPoint("TOP",InfoScrollChildRewStr,"BOTTOM", 0, -10)
    else
        RankingList_ViewRewardsButtonStr:SetPoint("TOP", RankingList_RandInfoTable[val - 1].ViewRewardsButtonStr, "BOTTOM", 0, -10);
    end
    if itemid == 0 then
        RankingList_ViewRewardsButtonStr:SetText("无奖励")
    else
        RankingList_ViewRewardsButtonStr:SetText(GhostGetItemIcon(itemid).." * "..count)
    end

    Frame.ViewRewardsButtonStr = RankingList_ViewRewardsButtonStr

    return Frame
end

for i = 1, showRank do
    RankingList_RandInfoTable[i] = {}
    RankingList_RandInfoTable[i] = CreateRankingListRankInfo(i, "-", 1, nil, 49778,1)
end

RankingList_InfoScrollChildFrame:SetHeight(28*(showRank))

local OwnerRankingStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
OwnerRankingStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
OwnerRankingStr:SetTextColor(1, 1, 1, 1);
OwnerRankingStr:SetPoint("TOP", RankingList_RankStr, "BOTTOM", 0 , -540);
OwnerRankingStr:SetText("-");
RankingOwnerFrame.OwnerRankingStr = OwnerRankingStr

local OwnerNameStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
OwnerNameStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
OwnerNameStr:SetTextColor(1, 1, 1, 1);
OwnerNameStr:SetPoint("TOP", RankingList_NameStr, "BOTTOM", 0 , -540);
OwnerNameStr:SetText(UnitName("player"));
RankingOwnerFrame.OwnerNameStr = OwnerNameStr

local OwnerInstanceStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
OwnerInstanceStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
OwnerInstanceStr:SetTextColor(1, 1, 1, 1);
OwnerInstanceStr:SetPoint("TOP", RankingList_InstanceStr, "BOTTOM", 0, -540);
OwnerInstanceStr:SetText("-");
RankingOwnerFrame.OwnerInstanceStr = OwnerInstanceStr

local OwnerTimeStr = RankingList:CreateFontString(nil, "OVERLAY", "GameFontNormal");
OwnerTimeStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
OwnerTimeStr:SetTextColor(1, 1, 1, 1);
OwnerTimeStr:SetPoint("TOP",RankingList_TimeStr, "BOTTOM", 0, -540);
OwnerTimeStr:SetText("-");
RankingOwnerFrame.OwnerTimeStr = OwnerTimeStr

local OwnerViewRewardsButton = CreateFrame("Button", "OwnerViewRewardsButton", RankingList);
OwnerViewRewardsButton:SetSize(150, 20);
OwnerViewRewardsButton:SetPoint("TOP", RankingList_RewStr, "BOTTOM", 0, -540);   
OwnerViewRewardsButton:SetScript("OnClick", function(self) print(self:GetName()) end);
OwnerViewRewardsButton:SetScript("OnEnter", function(self) if self.ItemLink then
    GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
    GameTooltip:SetHyperlink(self.ItemLink)
end end)
OwnerViewRewardsButton:SetScript("OnLeave", function(self) GameTooltip_Hide() end)
RankingOwnerFrame.OwnerViewRewardsButton = OwnerViewRewardsButton

local OwnerViewRewardsButtonStr = OwnerViewRewardsButton:CreateFontString(nil, "OVERLAY", "GameFontNormalLarge")
OwnerViewRewardsButtonStr:SetFont("Fonts\\ZYKai_C.TTF", 18, "OUTLINE");
OwnerViewRewardsButtonStr:SetTextColor(1, 1, 1, 1);
OwnerViewRewardsButtonStr:SetPoint("TOP",RankingList_RewStr,"BOTTOM", 0, -540)
OwnerViewRewardsButtonStr:SetText("无奖励")
RankingOwnerFrame.OwnerViewRewardsButtonStr = OwnerViewRewardsButtonStr

local KillOpenCloseButton = CreateFrame("Button", "KillOpenCloseButton", UIParent);
KillOpenCloseButton:SetSize(120, 120);
KillOpenCloseButton:SetPoint("CENTER", 0, 0);
KillOpenCloseButton:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
KillOpenCloseButton:SetBackdrop({bgFile = "Interface\\icons\\C5big"})
KillOpenCloseButton:SetScript("OnClick", function(self) if RankingList:IsShown() then RankingList:Hide() else RankingList:Show() end end);
KillOpenCloseButton:SetScript("OnEnter", function(self) 
    GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
    if RankingList:IsShown() then
        GameTooltip:SetText("点击关闭排行榜")
    else
        GameTooltip:SetText("点击打开排行榜")
    end
end)
KillOpenCloseButton:SetScript("OnLeave", function(self) GameTooltip_Hide() end)

local TempButton = CreateFrame("Button", "KillOpenCloseButton", UIParent);
TempButton:SetSize(120, 120);
TempButton:SetPoint("TOPRIGHT", 0, -200);
TempButton:SetPushedTexture("Interface\\Buttons\\UI-Quickslot-Depress")
TempButton:SetBackdrop({bgFile = "Interface\\icons\\C5big"})
TempButton:SetScript("OnClick", function(self)  HandleClearAllRankinglist() end);

local function InstanceRankingListEvent(self, event, h, msg, classtype, sender)
    if event == "CHAT_MSG_ADDON" then
        if h == "KILLBOSSMAPIDS" then
            GetInstanceRankMapIDs(msg)
        elseif h == "KILLBOSSSHOWMAX" then
            GetInstanceRankshowRank(msg)
        elseif h == "KILLBOSSGENREW" then
            GetInstanceRankGeneralRew(msg)
        elseif h == "KILLBOSSREWDATA" then
            GetInstanceRankRewData(msg)
        elseif h == "KILLBOSSALLRANK" then
            GetInstanceRankData(msg)
        elseif h == "KILLBOSSRANK" then
            GetInstanceRankData(msg)
        elseif h == "KILLBOSSOWNER" then
            GetInstanceRankOwnerData(msg)
        elseif h == "CLEARKILLBOSS" then
            HandleClearAllRankinglist()
        end
    end
end

local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("CHAT_MSG_ADDON")
MsgReceivers:SetScript("OnEvent", InstanceRankingListEvent)

local killBossRankingLoad = CreateFrame("Frame")
killBossRankingLoad:RegisterEvent("PLAYER_LOGIN")
killBossRankingLoad:SetScript("OnEvent", function ()
    SendAddonMessage("KILLBOSSRANK",nil,"GUILD", UnitName("player"))
end)



--本地测试
--GetInstanceRankMapIDs(tempmapstr)

print("排行榜加载完毕")

