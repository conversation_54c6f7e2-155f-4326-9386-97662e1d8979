<Ui xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xsi:schemaLocation="http://www.blizzard.com/wow/ui/ ..\FrameXML\UI.xsd" xmlns="http://www.blizzard.com/wow/ui/">
	<Frame name="GhostStatPointsFrame" inherits="GhostStaticFrameTemplate3.0" parent="UIParent">
		<Size>
			<AbsDimension x="300" y="380" />
		</Size>
		<Anchors>
			<Anchor point="CENTER">
				<Offset />
			</Anchor>
		</Anchors>
		<Frames>
			<Frame name="$parentMainTextFrame" inherits="GhostStatPointsTextTemplate">
				<Anchors>
					<Anchor point="TOP">
						<Offset y="-180" />
					</Anchor>
				</Anchors>
			</Frame>
			<Button name="$parentCancelButton">
				<Size x="20" y="20" />
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset x="-5" y="-5" />
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:SetNormalTexture("Interface\\RAIDFRAME\\ReadyCheck-NotReady");self:SetHighlightTexture("Interface\\Minimap\\UI-Minimap-Ping-Expand");
					</OnLoad>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
					<OnEnter>
						SetCursor("ATTACK_CURSOR")
					</OnEnter>
					<OnLeave>
						SetCursor("POINT_CURSOR")
					</OnLeave>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				tinsert(UISpecialFrames, self:GetName());
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GhostTalismanFrame" hidden="true">
		<Size x="384" y="512" />
		<Anchors>
			<Anchor point="CENTER">
				<Offset />
			</Anchor>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="0" right="30" top="0" bottom="70" />
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentPortrait">
					<Size x="64" y="64" />
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset x="5" y="-5" />
						</Anchor>
					</Anchors>
				</Texture>
			</Layer>
			<Layer>
				<Texture name="$parentBackground" file="Interface\Spellbook\UI-GlyphFrame">
					<Size x="352" y="441" />
					<Anchors>
						<Anchor point="TOPLEFT" />
					</Anchors>
					<TexCoords left="0" right="0.6875" top="0" bottom="0.8613281" />
				</Texture>
				<FontString name="$parentTitleText" inherits="GameFontNormal">
					<Anchors>
						<Anchor point="TOP">
							<Offset y="-18" />
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="CENTER" relativePoint="TOPRIGHT">
						<Offset x="-44" y="-25" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton1" inherits="GhostTalismanButtonTemplate" id="1">
				<Anchors>
					<Anchor point="CENTER">
						<Offset x="-15" y="140" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton4" inherits="GhostTalismanButtonTemplate" id="4">
				<Anchors>
					<Anchor point="BOTTOMRIGHT">
						<Offset x="-56" y="168" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton6" inherits="GhostTalismanButtonTemplate" id="6">
				<Anchors>
					<Anchor point="BOTTOMLEFT">
						<Offset x="26" y="168" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton2" inherits="GhostTalismanButtonTemplate" id="2">
				<Anchors>
					<Anchor point="CENTER">
						<Offset x="-14" y="-103" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton5" inherits="GhostTalismanButtonTemplate" id="5">
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset x="-56" y="-133" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton3" inherits="GhostTalismanButtonTemplate" id="3">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset x="28" y="-133" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentButton7" inherits="GhostTalismanButtonTemplate" id="7">
				<Anchors>
					<Anchor point="CENTER">
						<Offset x="-14" y="20" />
					</Anchor>
				</Anchors>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				tinsert(UISpecialFrames, self:GetName());
			</OnLoad>
			<OnShow>
				Ghost_SendData("GC_C_TALISMAN","1");SetPortraitTexture(GhostTalismanFramePortrait, "PLAYER");
			</OnShow>
		</Scripts>
	</Frame>
	<StatusBar name="GhostSpiritPowerStatusBar">
		<Size x="147" y="10" />
		<Anchors>
			<Anchor point="CENTER">
				<Offset>
					<AbsDimension x="0" y="-230" />
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer>
				<Texture parentKey="Border" file="Interface\CastingBar\UI-CastingBar-Border">
					<Size>
						<AbsDimension x="200" y="64" />
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="28" />
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString name="$parentText" inherits="TextStatusBarText" text="">
					<Size>
						<AbsDimension x="185" y="16" />
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="5" />
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<BarTexture file="Interface\TargetingFrame\UI-StatusBar" />
		<BarColor r="1" g="0.7" b="0" />
	</StatusBar>
	<StatusBar name="GhostRankValueStatusBar">
		<Size x="147" y="10" />
		<Anchors>
			<Anchor point="CENTER">
				<Offset>
					<AbsDimension x="0" y="-253" />
				</Offset>
			</Anchor>
		</Anchors>
		<Layers>
			<Layer>
				<Texture parentKey="Border" file="Interface\CastingBar\UI-CastingBar-Border">
					<Size>
						<AbsDimension x="200" y="64" />
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="28" />
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString name="$parentText" inherits="TextStatusBarText" text="">
					<Size>
						<AbsDimension x="185" y="16" />
					</Size>
					<Anchors>
						<Anchor point="TOP">
							<Offset>
								<AbsDimension x="0" y="5" />
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<BarTexture file="Interface\TargetingFrame\UI-StatusBar" />
		<BarColor r="1" g="0.3" b="0" />
	</StatusBar>
	<Frame name="LuckDrawFrame" inherits="GhostStaticFrameTemplate3.0" parent="UIParent">
		<Size>
			<AbsDimension x="500" y="600" />
		</Size>
		<Anchors>
			<Anchor point="CENTER">
				<Offset />
			</Anchor>
		</Anchors>
		<Frames>
			<Button>
				<Size x="45" y="25" />
				<Anchors>
					<Anchor point="TOP">
						<Offset y="-80" />
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<FontString name="LuckDrawTitle" inherits="ErrorFont" setAllPoints="true" text="">
							<Anchors>
								<Anchor point="CENTER">
									<Offset />
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Button>
			<Button name="$parentCancelButton">
				<Size x="20" y="20" />
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset x="-5" y="-5" />
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:SetNormalTexture("Interface\\RAIDFRAME\\ReadyCheck-NotReady");
					</OnLoad>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
					<OnEnter>
						SetCursor("ATTACK_CURSOR")
					</OnEnter>
					<OnLeave>
						SetCursor("POINT_CURSOR")
					</OnLeave>
				</Scripts>
			</Button>
			<Button name="$parentConfirmButton" inherits="GameMenuButtonTemplate">
				<Size x="160" y="30" />
				<Anchors>
					<Anchor point="BOTTOM">
						<Offset x="0" y="30" />
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:RegisterForClicks("LeftButtonUp","RightButtonUp");
					</OnLoad>
					<OnClick>
						LuckDrawButtonClick(self);
					</OnClick>
					<OnEnter>
						SetCursor("ATTACK_CURSOR");
					</OnEnter>
					<OnLeave>
						SetCursor("POINT_CURSOR");
					</OnLeave>
				</Scripts>
			</Button>
			<Button name="$parentConfirmButton10" inherits="GameMenuButtonTemplate">
				<Size x="160" y="30" />
				<Anchors>
					<Anchor point="BOTTOM">
						<Offset x="80" y="30" />
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:RegisterForClicks("LeftButtonUp","RightButtonUp");
					</OnLoad>
					<OnClick>
						LuckDrawButtonClick(self);
					</OnClick>
					<OnEnter>
						SetCursor("ATTACK_CURSOR");
					</OnEnter>
					<OnLeave>
						SetCursor("POINT_CURSOR");
					</OnLeave>
				</Scripts>
			</Button>
			<Button name="LuckDrawNoticeButton">
				<Size x="80" y="30" />
				<Anchors>
					<Anchor point="BOTTOM">
						<Offset y="30" />
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="OVERLAY">
						<FontString name="LuckDrawNoticeText" inherits="ErrorFont" setAllPoints="true" text="">
							<Anchors>
								<Anchor point="CENTER">
									<Offset />
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				tinsert(UISpecialFrames, self:GetName());
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GhostRecoveryFrame" inherits="GhostStaticFrameTemplate3.0" parent="UIParent">
		<Size>
			<AbsDimension x="400" y="600" />
		</Size>
		<Anchors>
			<Anchor point="CENTER">
				<Offset />
			</Anchor>
		</Anchors>
		<Layers>
			<Layer>
				<FontString name="GhostRecoveryCategoryTitle" inherits="TextStatusBarText" text="">
					<Anchors>
						<Anchor point="TOP">
							<Offset y="-80" />
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="GhostRecoveryButton" inherits="GhostRecoveryButtonTemplate">
				<Anchors>
					<Anchor point="BOTTOM">
						<Offset y="40" />
					</Anchor>
				</Anchors>
			</Button>
			<Button name="$parentCancelButton">
				<Size x="20" y="20" />
				<Anchors>
					<Anchor point="TOPRIGHT">
						<Offset x="-5" y="-5" />
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						self:SetNormalTexture("Interface\\RAIDFRAME\\ReadyCheck-NotReady");self:SetHighlightTexture("Interface\\Minimap\\UI-Minimap-Ping-Expand");
					</OnLoad>
					<OnClick>
						self:GetParent():Hide();
					</OnClick>
					<OnEnter>
						SetCursor("ATTACK_CURSOR")
					</OnEnter>
					<OnLeave>
						SetCursor("POINT_CURSOR")
					</OnLeave>
				</Scripts>
			</Button>
		</Frames>
		<Scripts>
			<OnLoad>
				tinsert(UISpecialFrames, self:GetName());
			</OnLoad>
		</Scripts>
	</Frame>
	<Frame name="GhostAntiFarmFrame" inherits="GhostStaticFrameTemplate3.0" parent="UIParent">
		<Size>
			<AbsDimension x="250" y="350" />
		</Size>
		<Anchors>
			<Anchor point="CENTER">
				<Offset />
			</Anchor>
		</Anchors>
		<Layers>
			<Layer>
				<FontString inherits="TextStatusBarText" text="人机验证系统">
					<Anchors>
						<Anchor point="TOP">
							<Offset y="-70" />
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
			<Layer>
				<FontString name="$parentText" inherits="TextStatusBarText" text="">
					<Anchors>
						<Anchor point="TOP">
							<Offset y="-100" />
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Scripts>
			<OnLoad>
				self:Hide();
			</OnLoad>
			<OnShow>
			</OnShow>
		</Scripts>
	</Frame>
</Ui>