--这里是商城UI草稿纸



--第一种实验 滚动窗口整体框架
--[[
local scrolltest = CreateFrame("ScrollFrame","scrolltest",UIParent,nil)
scrolltest:SetSize(600,700)
scrolltest:SetPoint("CENTER")
local textest = scrolltest:CreateTexture("textest","BACKGROUND",nil)
textest:SetAllPoints(scrolltest)
textest:SetTexture("Interface\\ICONS\\progress_frame_left")

local textest1 = scrolltest:CreateTexture("textest1","ARTWORK",nil)
textest1:SetPoint("TOPRIGHT",-10,-20)
textest1:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest1:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest1:SetSize(31,256)
textest1:SetTexCoord(0,0.484375,0,1.0)

local textest2 = scrolltest:CreateTexture("textest2","ARTWORK",nil)
textest2:SetPoint("BOTTOMRIGHT",-10,20)
textest2:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest2:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest2:SetSize(31,106)
textest2:SetTexCoord(0.515625,1.0,0,0.4140625)

local textest3 = scrolltest:CreateTexture("textest3","ARTWORK",nil)
textest3:SetPoint("TOPRIGHT",-10,-20)
textest3:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest3:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest3:SetSize(31,600)
textest3:SetTexCoord(0,0.484375,0.2,1.0)



local scrollchildtest = CreateFrame("Frame","scrollchildtest",scrolltest,nil)
scrolltest:SetScrollChild(scrollchildtest)
--scrollchildtest:SetPoint("TOP")
scrollchildtest:SetSize(900, 500)
local textests = scrollchildtest:CreateTexture("textests","ARTWORK",nil)
textests:SetTexture("Interface\\ICONS\\jf1")
textests:SetSize(50, 50)
textests:SetPoint("TOPLEFT",30,-20) --调整这个的位置 可以使得移动滚动条显得正常

--local slidertest = CreateFrame("Slider","slidertest",scrolltest,nil)
local slidertest = CreateFrame("Slider","slidertest",scrolltest,nil)
slidertest:SetOrientation("HORIZONTAL")
slidertest:SetMinMaxValues(0,700)
slidertest:SetValue(50)
slidertest:SetValueStep(1.0)
slidertest:SetPoint("TOP","scrolltest","BOTTOM",0,40)
slidertest:SetSize(700,25)
--slidertest:Enables()
local tt = slidertest:CreateTexture("tt","HIGHLIGHT",nil)
tt:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
tt:SetSize(25, 25)
local slidertesttexture = slidertest:SetThumbTexture(tt)


local slidertest1 = CreateFrame("Slider","slidertest1",scrolltest,nil)
slidertest1:SetOrientation("VERTICAL")
slidertest1:SetMinMaxValues(0,700)
slidertest1:SetValue(0)
slidertest1:SetValueStep(1.0)
slidertest1:SetPoint("LEFT","scrolltest","RIGHT",-37.5,0)
slidertest1:SetSize(25,630)
--slidertest:Enables()
local tt1 = slidertest1:CreateTexture("tt1","HIGHLIGHT",nil)
tt1:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
tt1:SetSize(33, 33)
local slidertesttexture1 = slidertest1:SetThumbTexture(tt1)


--这个才是正确的
slidertest:SetScript("OnValueChanged",
function(self)
scrolltest:SetHorizontalScroll(-1 * self:GetValue())
end
)

slidertest1:SetScript("OnValueChanged",
function(self)
scrolltest:SetVerticalScroll(1 * self:GetValue())
end
)


scrolltest:SetScript("OnMouseWheel", 
function(self,val)  

	--往上滑 val是正数	方向：上，左
	--往下滑 val是负数	方向：下，右
	
	local s1 = slidertest	-- 滚动条
	--获取最小最大值
	local minv,maxv = s1:GetMinMaxValues()
	--获取滚动条当前值
	local cv = s1:GetValue()
	--计算新数值
	local nv = cv - ( val * 3 )
	--新数值等于 nv 和 minv中最大的那个
	nv = max(nv, minv)
	--新数值等于 nv 和 maxv中最小的那个
	nv = min(nv, maxv)
	
	--如果新值 不等于 旧值 
	--设置滚动条的值为新值  -->设置滚动到哪个点(值)
	if ( nv ~= cv ) then
	--print("3")
		s1:SetValue(nv);
	end
	
end)
scrolltest:EnableMouseWheel(true)


--下面这样是不行的

--slidertest:SetScript("OnValueChanged",ttt(self))
--function ttt(frame)
	--scrolltest:SetHorizontalScroll(1 * frame:GetValue())
--end
]]--



--第二种实验 滚动窗口整体框架 因为没有设置滚动子窗口 所以无法实现滚动效果 
--[[
local scrolltest = CreateFrame("ScrollFrame","scrolltest",UIParent,nil)
scrolltest:SetSize(600,700)
scrolltest:SetPoint("CENTER")
local textest = scrolltest:CreateTexture("textest","BACKGROUND",nil)
textest:SetAllPoints(scrolltest)
textest:SetTexture("Interface\\ICONS\\progress_frame_left")

local textest1 = scrolltest:CreateTexture("textest1","ARTWORK",nil)
textest1:SetPoint("TOPRIGHT",-10,-20)
textest1:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest1:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest1:SetSize(31,256)
textest1:SetTexCoord(0,0.484375,0,1.0)

local textest2 = scrolltest:CreateTexture("textest2","ARTWORK",nil)
textest2:SetPoint("BOTTOMRIGHT",-10,20)
textest2:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest2:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest2:SetSize(31,106)
textest2:SetTexCoord(0.515625,1.0,0,0.4140625)

local textest3 = scrolltest:CreateTexture("textest3","ARTWORK",nil)
textest3:SetPoint("TOPRIGHT",-10,-20)
textest3:SetTexture("Interface\\PaperDollInfoFrame\\UI-Character-ScrollBar")
--textest3:SetTexture("Interface\\ICONS\\UI-Character-ScrollBar")
textest3:SetSize(31,600)
textest3:SetTexCoord(0,0.484375,0.2,1.0)

local textest4 = scrolltest:CreateTexture("textest4","ARTWORK",nil)
textest4:SetPoint("CENTER",0,0)
textest4:SetTexture(T["JiFen"])
textest4:SetSize(66,66)

local slidertest1 = CreateFrame("Slider","slidertest1",scrolltest,nil)
slidertest1:SetOrientation("VERTICAL")
slidertest1:SetMinMaxValues(0,700)
slidertest1:SetValue(0)
slidertest1:SetValueStep(1.0)
slidertest1:SetPoint("LEFT","scrolltest","RIGHT",-37.5,0)
slidertest1:SetSize(25,630)
--slidertest:Enables()
local tt1 = slidertest1:CreateTexture("tt1","HIGHLIGHT",nil)
tt1:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
tt1:SetSize(33, 33)
local slidertesttexture1 = slidertest1:SetThumbTexture(tt1)



slidertest1:SetScript("OnValueChanged",
function(self)
scrolltest:SetVerticalScroll(1 * self:GetValue())
end
)


scrolltest:SetScript("OnMouseWheel", 
function(self,val)  

	--往上滑 val是正数	方向：上，左
	--往下滑 val是负数	方向：下，右
	
	local s1 = slidertest1	-- 滚动条
	--获取最小最大值
	local minv,maxv = s1:GetMinMaxValues()
	--获取滚动条当前值
	local cv = s1:GetValue()
	--计算新数值
	local nv = cv - ( val * 5 )
	--新数值等于 nv 和 minv中最大的那个
	nv = max(nv, minv)
	--新数值等于 nv 和 maxv中最小的那个
	nv = min(nv, maxv)
	
	--如果新值 不等于 旧值 
	--设置滚动条的值为新值  -->设置滚动到哪个点(值)
	if ( nv ~= cv ) then
	--print("3")
		s1:SetValue(nv);
	end
	
end)
scrolltest:EnableMouseWheel(true)
]]--


--判断玩家是否进入战斗状态
--[[
local MsgReceivers = CreateFrame("Frame")
MsgReceivers:RegisterEvent("PLAYER_REGEN_DISABLED")
MsgReceivers:SetScript("OnEvent", Eventes)

function Eventes()
print("进入战斗状态了！！！！！！！")
end
]]--

--在默认聊天窗口 发送纯字符串数据
--DEFAULT_CHAT_FRAME:AddMessage("123456",1.0,1.0,0.2,1)



--间隔10秒打印一次
--[[
DONGD_DELAY_ONE_MINUTE = 10

function upupdate(self,var)

local timer = DONGD_DELAY_ONE_MINUTE

	if timer > 0 then

	timer = timer - var
	
	DONGD_DELAY_ONE_MINUTE = timer
	
	else
	
	print("DONGD_DELAY_ONE_MINUTE = "..DONGD_DELAY_ONE_MINUTE)
	
	DONGD_DELAY_ONE_MINUTE = 10
	
	end

end


local MsgReceiverss = CreateFrame("Frame")
MsgReceiverss:SetScript("OnUpdate", upupdate)
]]--


--[[

String.format("%02d", year)
year格式化为至少2位十进制整数 

譬如
int year = 5;
结果为05

TIMEMANAGER_TICKER_12HOUR = "%d:%02d";

format(TIMEMANAGER_TICKER_12HOUR, hour, minute);


	local hour, minute = tonumber(date("%H")), tonumber(date("%M"));
	return GameTime_GetFormattedTime(hour, minute, wantAMPM), hour, minute;
	
]]--

--[[
 --pairs: 迭代 table，可以遍历表中所有的 key 可以返回 nil
 --ipairs: 迭代数组，不能返回 nil,如果遇到 nil 则退出
 
 --代码示例：

local tab= { 
[1] = "a", 
[3] = "b", 
[4] = "c" 
} 
for i,v in pairs(tab) do        -- 输出 "a" ,"b", "c"  ,
    print( tab[i] ) 
end 

for i,v in ipairs(tab) do    -- 输出 "a" ,k=2时断开 
    print( tab[i] ) 
end


local Buttons = {}

Buttons.a = 1   --a 属于数组

for key,value in pairs(Buttons) 
do
    print(key,value)
end

]]--

--[[

local Buttontest1 = 100

local Buttontest2 = Buttontest1

Buttontest2 = 90

print("Buttontest1 = "..Buttontest1)  --其结果只是100


	--Buttontest = MenuOnButton  --MenuOnButton是一个创建好的按钮  那么Buttontest就是这个按钮的另一个名字而已
	
	--Buttontest:SetSize(200,200)
	
	--Buttontest:Hide() 	--MenuOnButton也隐藏了
	
	--Buttontest.s2 = 1
	
	--print(Buttontest.s2)

]]--


--滑动窗口请详细了解成就窗口 Blizzard_AchievementUI.xml Blizzard_AchievementUI.lua HybridScrollFrame.lua
--参考下面的创建坐标模式
--button = CreateFrame("BUTTON", buttonName .. i, scrollChild, buttonTemplate);
--button:SetPoint(point, buttons[i-1], relativePoint, offsetX, offsetY);


--这是一种HTML 魔兽魔改的 暂时只实验了这么多 这是WIKI上的示例 超级链接的 不会用
--[[
local MySimpleHTMLObject = CreateFrame("SimpleHTML","MySimpleHTMLObject",UIParent,nil);
MySimpleHTMLObject:SetPoint("CENTER")
MySimpleHTMLObject:SetSize(500,500)
MySimpleHTMLObject:SetText('<html><body><h1 align="center">SimpleHTML Demo: Ambush</h1><img src="Interface\\Icons\\Ability_Ambush" width="32" height="32" align="CENTER"/><p align="center">|cffee4400"You think this hurts? Just wait."|r</p><br/><p align="center">Among every ability a rogue has at his disposal,<br/>Ambush is without a doubt the hardest hitting Rogue ability.</p><br/></body></html>');
MySimpleHTMLObject:SetFont('Fonts\\FRIZQT__.TTF', 11);

]]--


--[[比较两个时间，返回相差多少时间
function timediff(long_time,short_time)
	local n_short_time,n_long_time,carry,diff = date('*t',short_time),date('*t',long_time),false,{}
	local colMax = {60,60,24,date('*t',time{year=n_short_time.year,month=n_short_time.month+1,day=0}).day,12,0}
	n_long_time.hour = n_long_time.hour - (n_long_time.isdst and 1 or 0) + (n_short_time.isdst and 1 or 0) -- handle dst
	for i,v in ipairs({'sec','min','hour','day','month','year'}) do
		diff[v] = n_long_time[v] - n_short_time[v] + (carry and -1 or 0)
		carry = diff[v] < 0
		if carry then
			diff[v] = diff[v] + colMax[i]
		end
	end
	return diff
end



local n_long_time = date(time{year=2014,month=6,day=11,hour=16,min=0,sec=0});
local n_short_time = date(time{year=2013,month=5,day=11,hour=16,min=0,sec=0});



local t_time = timediff(n_long_time,n_short_time);
local time_txt = string.format("%04d", t_time.year).."年"..string.format("%02d", t_time.month).."月"..string.format("%02d", t_time.day).."日   "..string.format("%02d", t_time.hour)..":"..string.format("%02d", t_time.min)..":"..string.format("%02d", t_time.sec);
print(time_txt);

]]--



--[[--[[


--获取GUID 利用suffixId 或者 uniqueId 在核心 ITEM::LoadFromDB 
function adds(self)
local _, itemLink = self:GetItem()

local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")


local s1, s2, s3, s4, s5, s6, s7, s8, s9, s10 = strsplit(":", string.match(itemLink, "item[%-?%d:]+"))

	if itemId == "56" or itemId == "23758" or itemId == "727" then
		print("suffixId = "..suffixId)
		print("uniqueId = "..uniqueId)
	end
end

-- GameTooltip:HookScript("OnShow", adds);
-- ItemRefTooltip:HookScript("OnShow", adds);

ItemRefTooltip:HookScript("OnTooltipSetItem",adds)
GameTooltip:HookScript("OnTooltipSetItem",adds)

--关闭打开我的名字 我友方名字 
function DYDY_PlayerNameUpdate()
	SetCVar("UnitNameOwn", 0)
	SetCVar("UnitNameOwn", 1)
	SetCVar("UnitNameFriendlyPlayerName", 0)
	SetCVar("UnitNameFriendlyPlayerName", 1)
	SetCVar("UnitNameEnemyPlayerName", 0)
	SetCVar("UnitNameEnemyPlayerName", 1)
end

]]--]]--