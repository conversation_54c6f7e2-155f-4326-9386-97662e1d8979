aaPetFrame = {};
 aaPetFrame.selectItemId = 0;
 aaPetFrame.selectItemEntry = 0;
 aaPetFrame.mainView = {};
 local msgdata = {};
 local selectItemDBName = "";
 local selectItemName = "";
 local btns;
 local selectIndex = 1;
 local buttonTitles = {"开始孵化","开始强化","开始进化","开始符文","开始重生"};
 local ClickDoneBtn = function() if selectIndex == 1 then aaData:sendAddonMsg(100,aaPetFrame.selectItemId.."\t"..aaPetFrame.selectItemEntry, "GUILD");
 elseif selectIndex == 2 then aaData:sendAddonMsg(101,aaPetFrame.selectItemId.."\t"..aaPetFrame.selectItemEntry, "GUILD");
 elseif selectIndex == 3 then aaData:sendAddonMsg(102,aaPetFrame.selectItemId.."\t"..aaPetFrame.selectItemEntry, "GUILD");
 elseif selectIndex == 4 then aaData:sendAddonMsg(103,aaPetFrame.selectItemId.."\t"..aaPetFrame.selectItemEntry, "GUILD");
 elseif selectIndex == 5 then aaData:sendAddonMsg(104,aaPetFrame.selectItemId.."\t"..aaPetFrame.selectItemEntry, "GUILD");
 end;
 end;
 function aaPetFrame:CallBack(tag) tag = tag + 0;
 if tag == 100 or tag == 101 or tag == 102 or tag == 103 or tag == 104 then aaDataPet:GetHttpItem(aaPetFrame.selectItemId, aaPetFrame.selectItemEntry);
 end;
 end;
 function aaPetFrame:ClearData() aaPetFrame.mainView.bgView.DoneButton:Disable();
 aaPetFrame.selectItemId = 0;
 aaPetFrame.selectItemEntry = 0;
 local icon = GetItemIcon(7082);
 aaPetFrame.mainView.bgView.ItemButton:SetNormalTexture(icon);
 ClearCursor();
 stattext:SetText("");
 needtext:SetText("");
 aaPetFrame:reload();
 end;
 local ClickMenuBtn = function(index) if index ~= 7 then selectIndex = index;
 for i = 1,#btns do btns[i]:SetAlpha(1);
 end;
 aaPetFrame.mainView.bgView.DoneButton:SetText(buttonTitles[index]);
 btns[index]:SetAlpha(0.5);
 aaPetFrame:reload();
 else local infoType, _, itemLink = GetCursorInfo();
 if (infoType == "item") then local itemString = string.match(itemLink, "item[%-?%d:]+");
 local _, itementry, _, _, _, suffixId, _, _, _, _= strsplit(":", itemString);
 aaPetFrame.selectItemEntry = itementry - 0;
 aaPetFrame.selectItemId = suffixId - 0;
 local item = aaDataPet:getItem(aaPetFrame.selectItemId, aaPetFrame.selectItemEntry);
 if aaPetFrame.selectItemId > 0 and item then itemName = aaData:getItemName(itementry);
 local icon = GetItemIcon(itementry);
 aaPetFrame.mainView.bgView.ItemButton:SetNormalTexture(icon);
 ClearCursor();
 aaPetFrame:reload();
 end;
 else aaPetFrame:ClearData();
 end;
 end;
 end;
 function aaPetFrame:show() aaPetFrame.mainView:Show();
 aaPetFrame:reload();
 end;
 function aaPetFrame:hide() aaPetFrame.mainView:Hide();
 aaPetFrame:ClearData();
 end;
 function aaPetFrame:reload() if aaPetFrame.selectItemId > 0 then for k,v in pairs(msgdata) do 
 selectItemName = aaDataPet:get_name(aaPetFrame.selectItemId, aaPetFrame.selectItemEntry, v);
 selectItemDBName = aaDataPet:get_dbname(aaPetFrame.selectItemId, aaPetFrame.selectItemEntry, v);
 end;
 aaPetFrame.mainView.bgView.DoneButton:Enable();
 local item = aaDataPet:getItem(aaPetFrame.selectItemId, aaPetFrame.selectItemEntry);
 if item then if selectIndex == 1 then if item[2] == 0 then if petnonsuch[1] then stattext:SetText(aaData.color_blue.."宠物掉落："..aaData.color_yellow.."\n任何怪物都有几率掉落。\n\n"..aaData.color_blue.."宠物孵化：\n"..aaData.color_yellow.."获取资质并领悟天赋");
 local needid = petnonsuch[1][3];
 local need = aaData:getNeed(needid);
 if need ~= "" then needtext:SetText(selectItemName.."\n\n"..aaData.color_blue.."孵化需要：\n"..aaData.color_yellow..need);
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("无法孵化");
 needtext:SetText("");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("该宠物已孵化");
 needtext:SetText("");
 end;
 elseif selectIndex == 2 then if item[2] > 0 then 
 local upgradeid = item[8] + 1;
 if petupgrade[upgradeid] then local value = "";
 if petupgrade[upgradeid][2] > petupgrade[upgradeid][3] then value = value.."+".." "..petupgrade[upgradeid][2].."%";
 elseif petupgrade[upgradeid][3] > petupgrade[upgradeid][2] then value = value.."+".." "..petupgrade[upgradeid][2].."% ~ "..petupgrade[upgradeid][3].."%\n";
 end;
 stattext:SetText(aaData.color_blue.."强化成功预览".."\n\n"..aaData.color_blue.."强化等级：\n"..aaData:get_star(upgradeid).."\n"..aaData.color_blue.."所有属性继续提升：\n"..aaData.color_green..value);
 local needid = petupgrade[upgradeid][5];
 local need = aaData:getNeed(needid);
 if need ~= "" then local destroy = "";
 if petupgrade[upgradeid][4] == 0 then destroy = aaData.color_blue.."无变化";
 elseif petupgrade[upgradeid][4] == 1 then destroy = aaData.color_red.."强化等级清零";
 elseif petupgrade[upgradeid][4] == 2 then destroy = aaData.color_red.."宠物销毁";
 end;
 needtext:SetText(selectItemName.."\n\n"..aaData.color_blue.."强化需要：\n"..aaData.color_yellow..need.."\n\n"..aaData.color_blue.."成功几率：\n"..aaData.color_white..petupgrade[upgradeid][1].."% ".."\n\n"..aaData.color_blue.."强化失败："..destroy);
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("无法强化");
 needtext:SetText("");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("该宠物未孵化");
 needtext:SetText("");
 end;
 elseif selectIndex == 3 then if item[2] > 0 then local level = item[2];
 if petnonsuch[level] then 
 local pztext = "";
 for i = 1,#petnonsuch do if i % 3 == 0 then pztext = pztext..petnonsuch[i][2]..petnonsuch[i][1].."\n";
 else if i == #petnonsuch then pztext = pztext..petnonsuch[i][2]..petnonsuch[i][1];
 else pztext = pztext..petnonsuch[i][2]..petnonsuch[i][1].."≮";
 end;
 end;
 end;
 stattext:SetText(aaData.color_blue.."当前品质：\n"..petnonsuch[level][2]..petnonsuch[level][1]..aaData.color_blue.."\n\n品质说明：\n"..pztext);
 if petnonsuch[level+1] then 
 local needid = petnonsuch[level+1][3];
 local need = aaData:getNeed(needid);
 if need ~= "" then needtext:SetText(selectItemName.."\n\n"..aaData.color_blue.."进化需要：\n"..aaData.color_yellow..need);
 end;
 else needtext:SetText("已达到最大等级");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("无法进化");
 needtext:SetText("");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("该宠物未孵化");
 needtext:SetText("");
 end;
 elseif selectIndex == 4 then if item[2] > 0 then 
 local upgradeid = item[12] + 1;
 if petrune[upgradeid] then stattext:SetText(aaData.color_blue.."符文成功预览".."\n\n"..aaData.color_blue.."符文等级：\n"..aaData:get_star(upgradeid).."\n"..aaData.color_blue.."符文说明：\n"..aaData.color_green.."宠物随机学习符文技能");
 local needid = petrune[upgradeid][4];
 local need = aaData:getNeed(needid);
 if need ~= "" then local destroy = "";
 if petrune[upgradeid][3] == 0 then destroy = aaData.color_blue.."无变化";
 elseif petrune[upgradeid][3] == 1 then destroy = aaData.color_red.."符文等级清零";
 elseif petrune[upgradeid][3] == 2 then destroy = aaData.color_red.."宠物销毁";
 end;
 needtext:SetText(selectItemName.."\n\n"..aaData.color_blue.."符文需要：\n"..aaData.color_yellow..need.."\n\n"..aaData.color_blue.."成功几率：\n"..aaData.color_white..petrune[upgradeid][2].."% ".."\n\n"..aaData.color_blue.."符文失败："..destroy);
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("宠物无法符文");
 needtext:SetText("");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("该宠物未孵化");
 needtext:SetText("");
 end;
 elseif selectIndex == 5 then if item[2] > 0 then local level = item[2];
 if petnonsuch[level] then stattext:SetText("重生说明：\n"..aaData.color_green.."重生会改变宠物的资质属性和天赋技能");
 if worldconf[3] then local needid = worldconf[3];
 local need = aaData:getNeed(needid);
 needtext:SetText(selectItemName.."\n\n"..aaData.color_blue.."重生需要：\n"..aaData.color_yellow..need);
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("无法重生");
 needtext:SetText("");
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 stattext:SetText("该宠物未孵化");
 needtext:SetText("");
 end;
 end;
 end;
 else aaPetFrame.mainView.bgView.DoneButton:Disable();
 end;
 end;
 aaPetFrame.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaPetFrame.mainView:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView:SetWidth(640);
 aaPetFrame.mainView:SetHeight(512);
 aaPetFrame.mainView:SetPoint("CENTER",0,0);
 aaPetFrame.mainView:SetMovable(1);
 aaPetFrame.mainView:EnableMouse();
 aaPetFrame.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaPetFrame.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaPetFrame.mainView:Hide();
 end;
 aaPetFrame.mainView.bgView1 = CreateFrame("Frame",nil,aaPetFrame.mainView) do 
 aaPetFrame.mainView.bgView1:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView1:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-cwxt1"});
 aaPetFrame.mainView.bgView1:SetWidth(512);
 aaPetFrame.mainView.bgView1:SetHeight(512);
 aaPetFrame.mainView.bgView1:SetPoint("TOPLEFT",0,0);
 aaPetFrame.mainView.bgView1:Show();
 end;
 aaPetFrame.mainView.bgView2 = CreateFrame("Frame",nil,aaPetFrame.mainView) do 
 aaPetFrame.mainView.bgView2:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView2:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-cwxt2"});
 aaPetFrame.mainView.bgView2:SetWidth(128);
 aaPetFrame.mainView.bgView2:SetHeight(512);
 aaPetFrame.mainView.bgView2:SetPoint("TOPLEFT",512,0);
 aaPetFrame.mainView.bgView2:Show();
 end;
 aaPetFrame.mainView.bgView = CreateFrame("Frame",nil,aaPetFrame.mainView) do 
 aaPetFrame.mainView.bgView:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView:SetWidth(640);
 aaPetFrame.mainView.bgView:SetHeight(512);
 aaPetFrame.mainView.bgView:SetPoint("TOPLEFT",0,0);
 aaPetFrame.mainView.bgView:Show();
 end;
 aaPetFrame.mainView.bgView.menuView = CreateFrame("Frame",nil,aaPetFrame.mainView) do 
 aaPetFrame.mainView.bgView.menuView:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView.menuView:SetWidth(160);
 aaPetFrame.mainView.bgView.menuView:SetHeight(380);
 aaPetFrame.mainView.bgView.menuView:SetPoint("TOPLEFT",0,-10);
 aaPetFrame.mainView.bgView.menuView:Show();
 end;
 aaPetFrame.mainView.bgView.titletext = aaPetFrame.mainView.bgView:CreateFontString("titletext", "OVERLAY", "GameFontNormal") do 
 aaPetFrame.mainView.bgView.titletext:SetPoint("TOPLEFT", aaPetFrame.mainView.bgView, "TOPLEFT", 0, -20);
 aaPetFrame.mainView.bgView.titletext:SetWidth(640);
 aaPetFrame.mainView.bgView.titletext:SetText("宠物系统");
 aaPetFrame.mainView.bgView.titletext:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 48);
 end;
 local buttonWidth = 120;
 local buttonHeight = 50;
 local buttonTop = -100;
 aaPetFrame.mainView.bgView.menuView.QHButton = CreateFrame('Button', nil, aaPetFrame.mainView.bgView.menuView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.menuView.QHButton:SetPoint('TOPLEFT', aaPetFrame.mainView.bgView.menuView, 'TOPLEFT', 20,  buttonTop);
 aaPetFrame.mainView.bgView.menuView.QHButton:SetSize(buttonWidth, buttonHeight);
 aaPetFrame.mainView.bgView.menuView.QHButton:SetText("孵化");
 aaPetFrame.mainView.bgView.menuView.QHButton:SetAlpha(0.5);
 aaPetFrame.mainView.bgView.menuView.QHButton:SetScript('OnClick', function() ClickMenuBtn(1);
 end);
 end;
 aaPetFrame.mainView.bgView.menuView.QHButton1 = CreateFrame('Button', nil, aaPetFrame.mainView.bgView.menuView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.menuView.QHButton1:SetPoint('TOPLEFT', aaPetFrame.mainView.bgView.menuView, 'TOPLEFT', 20,  buttonTop-(10+buttonHeight));
 aaPetFrame.mainView.bgView.menuView.QHButton1:SetSize(buttonWidth, buttonHeight);
 aaPetFrame.mainView.bgView.menuView.QHButton1:SetText("强化");
 aaPetFrame.mainView.bgView.menuView.QHButton1:SetScript('OnClick', function() ClickMenuBtn(2);
 end);
 end;
 aaPetFrame.mainView.bgView.menuView.QHButton2 = CreateFrame('Button', nil, aaPetFrame.mainView.bgView.menuView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.menuView.QHButton2:SetPoint('TOPLEFT', aaPetFrame.mainView.bgView.menuView, 'TOPLEFT', 20,  buttonTop-(10+buttonHeight)*2);
 aaPetFrame.mainView.bgView.menuView.QHButton2:SetSize(buttonWidth, buttonHeight);
 aaPetFrame.mainView.bgView.menuView.QHButton2:SetText("进化");
 aaPetFrame.mainView.bgView.menuView.QHButton2:SetScript('OnClick', function() ClickMenuBtn(3);
 end);
 end;
 aaPetFrame.mainView.bgView.menuView.QHButton3 = CreateFrame('Button', nil, aaPetFrame.mainView.bgView.menuView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.menuView.QHButton3:SetPoint('TOPLEFT', aaPetFrame.mainView.bgView.menuView, 'TOPLEFT', 20,  buttonTop-(10+buttonHeight)*3);
 aaPetFrame.mainView.bgView.menuView.QHButton3:SetSize(buttonWidth, buttonHeight);
 aaPetFrame.mainView.bgView.menuView.QHButton3:SetText("符文");
 aaPetFrame.mainView.bgView.menuView.QHButton3:SetScript('OnClick', function() ClickMenuBtn(4);
 end);
 end;
 aaPetFrame.mainView.bgView.menuView.QHButton4 = CreateFrame('Button', nil, aaPetFrame.mainView.bgView.menuView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.menuView.QHButton4:SetPoint('TOPLEFT', aaPetFrame.mainView.bgView.menuView, 'TOPLEFT', 20,  buttonTop-(10+buttonHeight)*4);
 aaPetFrame.mainView.bgView.menuView.QHButton4:SetSize(buttonWidth, buttonHeight);
 aaPetFrame.mainView.bgView.menuView.QHButton4:SetText("重生");
 aaPetFrame.mainView.bgView.menuView.QHButton4:SetScript('OnClick', function() ClickMenuBtn(5);
 end);
 end;
 btns = {aaPetFrame.mainView.bgView.menuView.QHButton,aaPetFrame.mainView.bgView.menuView.QHButton1,aaPetFrame.mainView.bgView.menuView.QHButton2,aaPetFrame.mainView.bgView.menuView.QHButton3,aaPetFrame.mainView.bgView.menuView.QHButton4};
 aaPetFrame.mainView.bgView.ItemButton = CreateFrame('Button', nil, aaPetFrame.mainView.bgView, '') do 
 aaPetFrame.mainView.bgView.ItemButton:SetPoint('TOPRIGHT', aaPetFrame.mainView.bgView, 'TOPRIGHT', -30, -103);
 aaPetFrame.mainView.bgView.ItemButton:SetSize(40, 40);
 aaPetFrame.mainView.bgView.ItemButton:SetText("拖入装备");
 local icon = GetItemIcon(7082);
 aaPetFrame.mainView.bgView.ItemButton:SetNormalTexture(icon);
 aaPetFrame.mainView.bgView.ItemButton:SetScript('OnClick', function() ClickMenuBtn(7);
 end);
 aaPetFrame.mainView.bgView.ItemButton:Show();
 end;
 aaPetFrame.mainView.bgView.DoneButton = CreateFrame('Button', nil, aaPetFrame.mainView.bgView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.DoneButton:SetPoint('TOPRIGHT', aaPetFrame.mainView.bgView, 'TOPRIGHT', -80, -100);
 aaPetFrame.mainView.bgView.DoneButton:SetSize(120, 40);
 aaPetFrame.mainView.bgView.DoneButton:SetText("开始孵化");
 aaPetFrame.mainView.bgView.DoneButton:Disable();
 aaPetFrame.mainView.bgView.DoneButton:SetScript('OnClick', function() ClickDoneBtn();
 end);
 end;
 aaPetFrame.mainView.bgView.CancelButton = CreateFrame('Button', nil, aaPetFrame.mainView.bgView, 'UIPanelButtonTemplate') do 
 aaPetFrame.mainView.bgView.CancelButton:SetPoint('TOPRIGHT', aaPetFrame.mainView.bgView, 'TOPRIGHT', -5, -5);
 aaPetFrame.mainView.bgView.CancelButton:SetSize(60, 30);
 aaPetFrame.mainView.bgView.CancelButton:SetText("关闭");
 aaPetFrame.mainView.bgView.CancelButton:SetScript('OnClick', function() aaPetFrame:hide();
 end);
 end;
 aaPetFrame.mainView.bgView.detailView1 = CreateFrame("Frame",nil,aaPetFrame.mainView.bgView) do 
 aaPetFrame.mainView.bgView.detailView1:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView.detailView1:SetBackdrop ({bgFile = "Interface\\Tooltips\\UI-Tooltip-Background", tile = true, tileSize = 16});
 aaPetFrame.mainView.bgView.detailView1:SetBackdropColor(0, 0, 0, 0.5);
 aaPetFrame.mainView.bgView.detailView1:SetWidth(230);
 aaPetFrame.mainView.bgView.detailView1:SetHeight(300);
 aaPetFrame.mainView.bgView.detailView1:SetPoint("TOPLEFT",aaPetFrame.mainView.bgView,'TOPLEFT',160,-180);
 aaPetFrame.mainView.bgView.detailView1:Show();
 stattext = aaPetFrame.mainView.bgView.detailView1:CreateFontString("stattext", "OVERLAY", "GameFontNormal") do 
 stattext:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 16);
 stattext:SetPoint("TOPLEFT", aaPetFrame.mainView.bgView.detailView1, "TOPLEFT", 5, -10);
 stattext:SetAllPoints(aaPetFrame.mainView.bgView.detailView1);
 stattext:SetSpacing(5);
 end;
 end;
 aaPetFrame.mainView.bgView.detailView2 = CreateFrame("Frame",nil,aaPetFrame.mainView.bgView) do 
 aaPetFrame.mainView.bgView.detailView2:SetFrameStrata("TOOLTIP");
 aaPetFrame.mainView.bgView.detailView2:SetBackdrop ({bgFile = "Interface\\Tooltips\\UI-Tooltip-Background", tile = true, tileSize = 16});
 aaPetFrame.mainView.bgView.detailView2:SetBackdropColor(0, 0, 0, 0.5);
 aaPetFrame.mainView.bgView.detailView2:SetWidth(230);
 aaPetFrame.mainView.bgView.detailView2:SetHeight(300);
 aaPetFrame.mainView.bgView.detailView2:SetPoint("TOPLEFT",aaPetFrame.mainView.bgView,'TOPLEFT',400,-180);
 aaPetFrame.mainView.bgView.detailView2:Show();
 needtext = aaPetFrame.mainView.bgView.detailView2:CreateFontString("needtext", "OVERLAY", "GameFontNormal") do 
 needtext:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 15);
 needtext:SetPoint("TOP", aaPetFrame.mainView.bgView.detailView2, "TOP", 0, -10);
 needtext:SetAllPoints(aaPetFrame.mainView.bgView.detailView2);
 needtext:SetSpacing(5);
 end;
 end;