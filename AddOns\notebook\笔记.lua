--创建一个窗口，在其中显示m2文件
local f = CreateFrame("Frame","f",UIParent)
f:SetFrameStrata("TOOLTIP")
f:SetSize(330, 300)
f:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
f:SetPoint("CENTER",0,0)
f:SetBackdropColor(0,0,0,0.5)	-- 不会显示原贴图
f:Show()
local m = CreateFrame("PlayerModel","m",f)
m:SetPoint("CENTER",0,0)
m:SetCamera(0)
m:SetSize(500, 500)
m:SetModel("Wings\\Wings2019-2.mdx")		--设置m2文件路径	
m:SetModelScale(0.7)						--缩放
-- m:SetPosition(-1.5,1,-1);				--无法使武器立起来

--a 对应动作ID 对应dbc的动作id名字 对应模型查看器动作名字
local s = 2000;local a = 0;m:SetScript("OnUpdate", function(self, e)
	s = s + (e * 1000);
	self:SetSequenceTime(a, s);
	if s > 2000 then 
		s = 0;
		a=a+1;
DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)	

--机械的不完整转圈动作
local c = 0;local s = 0;local a = 0;m:SetScript("OnUpdate", function(self, e)	
	s = s + (e * 1000);self:SetFacing(a) ;
	if s > 1000 then 
		s = 0;
	if c == 6 then 
		c = 0;
	end
	if c <= 3 then 
		c = c + 1
		a = a + 1 
	else 
		a = a - 1 
	end;
DEFAULT_CHAT_FRAME:AddMessage("第"..a.."号动作开始...") end end)

--创建一个窗口，在其中显示贴图及文字
local f = CreateFrame("Frame","f",UIParent)
f:SetFrameStrata("TOOLTIP")
f:SetSize(330, 300)
f:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
f:SetPoint("CENTER",0,0)
f:SetBackdropColor(0,0,0,0.5)	-- 不会显示原贴图
f:Show()
local fs = f:CreateFontString("$parent".."TitleText","ARTWORK","GameFontNormal")
fs:SetText("大天使的庇佑")
fs:SetFont("Fonts\\ZYHei.ttf", 15)
fs:SetPoint("TOP",0,-18)
f.FontString = fs
local t = f:CreateTexture("$parent".."Background","ARTWORK")
t:SetTexture("Interface\\icons\\大天使")
t:SetSize(150, 150)
t:SetPoint("CENTER",0,30)
f.texture = t

--失败无法自适应
GameTooltip:HookScript("OnShow", function()	
local h = GameTooltip:GetHeight()
local w = GameTooltip:GetWidth()
-- print("11111111111")
local m = CreateFrame("PlayerModel","m",GameTooltip)
m:SetPoint("CENTER",0,0)
m:SetCamera(0)
m:SetSize(128*2, 256*2)
-- m:SetModel("Wings\\Wings2019-2.mdx")		--设置m2文件路径	
-- m:SetModel("Spells\\faeriefire.mdx")		--设置m2文件路径	
m:SetModel("spells\\itemmessage1.mdx")		--设置m2文件路径	
m:SetRotation(80)
m:SetModelScale(0.5)						--缩放
m:Show()
end)