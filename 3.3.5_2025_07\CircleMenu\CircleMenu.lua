-- 圆形菜单系统
-- 作者：专用插件
-- 功能：可交互的圆形按钮菜单系统

-- 全局变量定义
CircleMenu = {}
CircleMenu.isSubMenuVisible = false  -- 子菜单显示状态
CircleMenu.isDragging = false        -- 拖拽状态
CircleMenu.savedPosition = nil       -- 保存的位置

-- SavedVariables数据库初始化
CircleMenuDB = CircleMenuDB or {
    position = nil,
    settings = {
        version = "1.0",
        lastSaved = nil
    }
}

-- 常量定义
local SUB_BUTTON_COUNT = 9           -- 子按钮数量
local CIRCLE_RADIUS = 80             -- 圆形布局半径（像素）

-- 动态tooltip文本常量
local MAIN_BUTTON_TOOLTIP_HIDDEN = "圆形菜单\n|cFFFFFF00左键点击：|r 显示功能菜单\n|cFFFFFF00拖拽：|r 移动菜单位置"
local MAIN_BUTTON_TOOLTIP_VISIBLE = "圆形菜单\n|cFFFFFF00左键点击：|r 关闭功能菜单\n|cFFFFFF00拖拽：|r 移动菜单位置"

-- WoW界面状态检测函数
function CircleMenu_GetInterfaceState(interfaceType)
    local frames = {
        DoubleArmorBack = "DoubleArmorBackImgFrame",
        SoulStone = "HGLP",
        SoulStoneEx = "HGLPEX",
        RankingListFrame = "RankingList",
        LuckDrawFrameName = "LuckDrawFrame",
        ItemShopFrameName = "ItemShopFrame",
        StatFrameName = "StatFrame",
        MagicRuneEnergyBackImgFrameName = "MagicRuneEnergyBackImgFrame",
        BotMagicMgrFrame = "BotMagicMgrFrame",
        help = "HelpFrame"
    }

    local frameName = frames[interfaceType]
    if not frameName then
        return false
    end

    local frame = _G[frameName]
    return frame and frame:IsVisible()
end

-- 动态tooltip文本生成
function CircleMenu_GetDynamicMainTooltip()
    if CircleMenu.isSubMenuVisible then
        return MAIN_BUTTON_TOOLTIP_VISIBLE
    else
        return MAIN_BUTTON_TOOLTIP_HIDDEN
    end
end

-- 子按钮动态tooltip文本定义
local SUB_BUTTON_TOOLTIP_TEMPLATES = {
    [1] = {
        name = "混沌灵甲",
        interface = "DoubleArmorBack",
        openText = "点击打开混沌灵甲面板",
        closeText = "点击关闭混沌灵甲面板",
        description = "查看角色混沌灵甲信息"
    },
    [2] = {
        name = "魂玉",
        interface = "SoulStone",
        openText = "点击打开魂玉界面",
        closeText = "点击关闭魂玉界面",
        description = "管理魂玉镶嵌和拆卸"
    },
    [3] = {
        name = "祭灵骨牌",
        interface = "SoulStoneEx",
        openText = "点击打开祭灵骨牌界面",
        closeText = "点击关闭祭灵骨牌界面",
        description = "查看和管理祭灵骨牌"
    },
    [4] = {
        name = "副本挑战排行榜",
        interface = "RankingListFrame",
        openText = "点击打开副本挑战排行榜面板",
        closeText = "点击关闭副本挑战排行榜面板",
        description = "查看当前副本挑战排行榜信息"
    },
    [5] = {
        name = "幸运大转盘",
        interface = "LuckDrawFrameName",
        openText = "点击打开幸运轮盘面板",
        closeText = "点击关闭幸运轮盘面板",
        description = "好运转转转，惊喜连连看！"
    },
    [6] = {
        name = "游戏商城",
        interface = "ItemShopFrameName",
        openText = "点击打开游戏商城界面",
        closeText = "点击关闭游戏商城界面",
        description = "查看和购买商城道具"
    },
    [7] = {
        name = "本体法宝",
        interface = "MagicRuneEnergyBackImgFrameName",
        openText = "点击打开本体法宝界面",
        closeText = "点击关闭本体法宝界面",
        description = "查看和设置本地法宝"
    },
    [8] = {
        name = "斗气面板",
        interface = "StatFrameName",
        openText = "点击打开斗气面板",
        closeText = "点击关闭斗气面板",
        description = "查看和设置斗气"
    },
    [9] = {
        name = "佣兵法宝",
        interface = "BotMagicMgrFrame",
        openText = "点击打开佣兵法宝管理界面",
        closeText = "点击关闭佣兵法宝管理界面",
        description = "管理佣兵法宝镶嵌和配置"
    }
}

-- 生成动态子按钮tooltip
function CircleMenu_GetDynamicSubTooltip(buttonIndex)
    if not buttonIndex or buttonIndex < 1 or buttonIndex > SUB_BUTTON_COUNT then
        return "无效按钮"
    end

    local template = SUB_BUTTON_TOOLTIP_TEMPLATES[buttonIndex]
    if not template then
        return "功能按钮 " .. buttonIndex
    end

    local isInterfaceVisible = CircleMenu_GetInterfaceState(template.interface)
    local actionText = isInterfaceVisible and template.closeText or template.openText

    return string.format("%s\n|cFFFFFF00%s|r\n%s",
        template.name, actionText, template.description)
end

-- 圆形布局计算函数
-- 计算第i个子按钮在圆形布局中的位置
-- @param buttonIndex: 按钮索引（1-9）
-- @return x, y: 相对于主按钮中心的偏移坐标
function CircleMenu_CalculateCirclePosition(buttonIndex)
    if buttonIndex < 1 or buttonIndex > SUB_BUTTON_COUNT then
        return 0, 0
    end

    -- 计算角度：每个按钮间隔40度，从0度开始
    local angleInDegrees = (buttonIndex - 1) * 40
    local angleInRadians = math.rad(angleInDegrees)

    -- 使用三角函数计算位置
    local x = CIRCLE_RADIUS * math.cos(angleInRadians)
    local y = CIRCLE_RADIUS * math.sin(angleInRadians)

    -- 四舍五入到整数像素
    return math.floor(x + 0.5), math.floor(y + 0.5)
end

-- 应用圆形布局到所有子按钮
function CircleMenu_ApplyCircleLayout()
    for i = 1, SUB_BUTTON_COUNT do
        local button = _G["CircleMenuSubButton" .. i]
        if button then
            local x, y = CircleMenu_CalculateCirclePosition(i)
            button:ClearAllPoints()
            button:SetPoint("CENTER", _G["CircleMenuMainButton"], "CENTER", x, y)
        end
    end
end


-- 主按钮初始化（整合原框架初始化逻辑）
function CircleMenu_MainButton_OnLoad(self)
    -- 设置按钮可点击和可拖拽
    print("|cFF888888主按钮初始化完成，拖拽功能已启用|r")
    self:RegisterForDrag("LeftButton")
    self:EnableMouse(true)

    -- 设置主按钮可移动（现在直接拖拽主按钮）
    self:SetMovable(true)

    -- 验证按钮对象和基本属性
    if not self then
        print("|cFFFF0000错误：主按钮OnLoad - 按钮对象无效|r")
        return
    end
    
    -- 设置按钮基本属性
    self:SetAlpha(1.0)      -- 设置透明度
    self:Show()             -- 主按钮默认显示

    print("|cFF888888主按钮初始化完成，拖拽功能已启用|r")
end

-- 主按钮点击事件
function CircleMenu_MainButton_OnClick(self)
    -- 防止在拖拽过程中触发点击
    if CircleMenu.isDragging then
        print("|cFF888888拖拽过程中，忽略点击事件|r")
        return
    end

    local tex = _G[self:GetName() .."Background"]

    -- 切换子菜单显示状态
    if CircleMenu.isSubMenuVisible then
        CircleMenu_HideSubButtons()
        print("|cFFFFFF00圆形菜单已隐藏|r")
        tex:SetTexture("Interface\\AddOns\\godPanel\\Icons\\006")
    else
        CircleMenu_ShowSubButtons()
        print("|cFFFFFF00圆形菜单已显示|r")
        tex:SetTexture("Interface\\AddOns\\godPanel\\Icons\\007")
    end

    CircleMenu_MainButton_OnEnter(self)
    -- 可选：播放点击音效
    -- PlaySound("igMainMenuOption")
end

-- 显示子按钮
function CircleMenu_ShowSubButtons()
    -- 确保布局是最新的
    CircleMenu_ApplyCircleLayout()

    local successCount = 0
    for i = 1, SUB_BUTTON_COUNT do
        local button = _G["CircleMenuSubButton" .. i]
        if button then
            button:Show()
            -- 设置完全不透明
            button:SetAlpha(1.0)
            -- 确保按钮可用
            button:Enable()
            successCount = successCount + 1
        else
            print(string.format("|cFFFF0000警告：子按钮%d不存在|r", i))
        end
    end

    -- 更新状态
    CircleMenu.isSubMenuVisible = true

    -- 验证显示结果
    if successCount == SUB_BUTTON_COUNT then
        print(string.format("|cFF00FF00成功显示%d个子按钮|r", successCount))
    else
        print(string.format("|cFFFFAA00部分显示：%d/%d个子按钮|r", successCount, SUB_BUTTON_COUNT))
    end

    -- 播放音效反馈（如果需要）
    -- PlaySound("igMainMenuOptionCheckBoxOn")
end

-- 隐藏子按钮
function CircleMenu_HideSubButtons()
    local successCount = 0
    for i = 1, SUB_BUTTON_COUNT do
        local button = _G["CircleMenuSubButton" .. i]
        if button then
            -- 设置半透明（为动画效果做准备）
            button:SetAlpha(0.8)
            -- 隐藏按钮
            button:Hide()
            successCount = successCount + 1
        else
            print(string.format("|cFFFF0000警告：子按钮%d不存在|r", i))
        end
    end

    -- 更新状态
    CircleMenu.isSubMenuVisible = false

    -- 验证隐藏结果
    if successCount == SUB_BUTTON_COUNT then
        print(string.format("|cFF00FF00成功隐藏%d个子按钮|r", successCount))
    else
        print(string.format("|cFFFFAA00部分隐藏：%d/%d个子按钮|r", successCount, SUB_BUTTON_COUNT))
    end

    -- 播放音效反馈（如果需要）
    -- PlaySound("igMainMenuOptionCheckBoxOff")
end

-- 主按钮拖拽开始
function CircleMenu_MainButton_OnDragStart(self)
    -- 验证按钮对象
    if not self then
        print("|cFFFF0000错误：拖拽开始 - 按钮对象无效|r")
        return
    end

    -- 验证主按钮可移动性
    if not self:IsMovable() then
        print("|cFFFF0000错误：主按钮不可移动|r")
        return
    end

    -- 设置拖拽状态
    CircleMenu.isDragging = true

    -- 开始移动主按钮（子按钮通过锚点自动跟随）
    self:StartMoving()

    -- 用户反馈
    print("|cFF00FFFF开始拖拽圆形菜单|r")
end

-- 主按钮拖拽结束
function CircleMenu_MainButton_OnDragStop(self)
    -- 验证按钮对象
    if not self then
        print("|cFFFF0000错误：拖拽结束 - 按钮对象无效|r")
        -- 重置拖拽状态
        CircleMenu.isDragging = false
        return
    end

    -- 停止移动主按钮
    self:StopMovingOrSizing()

    -- 重置拖拽状态（在保存位置之前重置，避免状态不一致）
    CircleMenu.isDragging = false
end

-- 主按钮鼠标进入事件
function CircleMenu_MainButton_OnEnter(self)
    -- 验证按钮对象
    if not self then
        return
    end

    -- 验证GameTooltip存在
    local gameTooltip = _G["GameTooltip"]
    if not gameTooltip then
        print("|cFFFF0000错误：GameTooltip不可用|r")
        return
    end

    -- 获取动态tooltip文本
    local tooltipText = CircleMenu_GetDynamicMainTooltip()

    -- 设置tooltip
    gameTooltip:SetOwner(self, "ANCHOR_RIGHT")
    gameTooltip:SetText(tooltipText, 1, 1, 1, true) -- 启用文本换行
    gameTooltip:Show()

end

-- 主按钮鼠标离开事件
function CircleMenu_MainButton_OnLeave(_)
    local gameTooltip = _G["GameTooltip"]
    if gameTooltip then
        gameTooltip:Hide()
    end
end

-- 子按钮加载事件
function CircleMenu_SubButton_OnLoad(self)
    -- 验证按钮对象
    if not self then
        print("|cFFFF0000错误：子按钮OnLoad - 按钮对象无效|r")
        return
    end

    -- 获取按钮名称和编号
    local buttonName = self:GetName()
    if not buttonName then
        print("|cFFFF0000错误：子按钮OnLoad - 无法获取按钮名称|r")
        return
    end

    -- 解析按钮编号
    local buttonNumber = string.match(buttonName, "CircleMenuSubButton(%d+)")
    if not buttonNumber then
        print(string.format("|cFFFF0000错误：子按钮OnLoad - 无法解析按钮编号，按钮名称: %s|r", buttonName))
        return
    end

    local buttonIndex = tonumber(buttonNumber)
    if not buttonIndex or buttonIndex < 1 or buttonIndex > SUB_BUTTON_COUNT then
        print(string.format("|cFFFF0000错误：子按钮OnLoad - 无效的按钮编号 %d|r", buttonIndex or -1))
        return
    end

    -- 初始化按钮属性
    self:SetAlpha(1.0)  -- 设置透明度
    self:Hide()         -- 初始状态为隐藏

    -- 设置按钮可点击和可拖拽属性
    self:EnableMouse(true)
    self:SetMovable(false)  -- 子按钮不可移动

    -- 验证按钮尺寸（应该是32x32像素）
    local width = self:GetWidth()
    local height = self:GetHeight()
    if width ~= 32 or height ~= 32 then
        print(string.format("|cFFFFAA00警告：子按钮 %d 尺寸异常 (%dx%d)，期望32x32|r", buttonIndex, width, height))
    end

    -- 调试信息
    print(string.format("|cFF888888子按钮 %d 初始化完成 (%s)|r", buttonIndex, buttonName))
end

-- 子按钮点击事件
function CircleMenu_SubButton_OnClick(self)
    -- 验证按钮对象
    if not self then
        print("|cFFFF0000错误：子按钮对象无效|r")
        return
    end

    -- 获取按钮名称
    local buttonName = self:GetName()
    if not buttonName then
        print("|cFFFF0000错误：无法获取按钮名称|r")
        return
    end

    -- 解析按钮编号
    local buttonNumber = string.match(buttonName, "CircleMenuSubButton(%d+)")

    if buttonNumber then
        local buttonIndex = tonumber(buttonNumber)

        -- 验证按钮编号范围
        if buttonIndex < 1 or buttonIndex > SUB_BUTTON_COUNT then
            print(string.format("|cFFFF0000错误：无效的按钮编号 %d|r", buttonIndex))
            return
        end

        -- 用户反馈
        print(string.format("|cFF33FF33点击了功能按钮 %d|r", buttonIndex))

        -- 执行按钮功能
        local success = CircleMenu_ExecuteButtonFunction(self, buttonIndex)

        if not success then
            print(string.format("|cFFFF0000功能按钮 %d 执行失败|r", buttonIndex))
        end

        -- 可选：播放点击音效
        -- PlaySound("igMainMenuOptionCheckBoxOn")

    else
        print(string.format("|cFFFF0000错误：无法解析按钮编号，按钮名称: %s|r", buttonName))
    end
end

-- 执行按钮功能（可扩展）
function CircleMenu_ExecuteButtonFunction(self, buttonNumber)
    -- 验证按钮编号
    if not buttonNumber or type(buttonNumber) ~= "number" then
        print("|cFFFF0000错误：无效的按钮编号|r")
        return false
    end

    if buttonNumber < 1 or buttonNumber > SUB_BUTTON_COUNT then
        print(string.format("|cFFFF0000错误：按钮编号超出范围 (1-%d)|r", SUB_BUTTON_COUNT))
        return false
    end

    -- 根据按钮编号执行不同功能
    local success = true

    if buttonNumber == 1 then
        -- 功能1：灵甲界面
        print("|cFF888888执行功能1:打开灵甲界面|r")
        DoubleArmorShowHide()

    elseif buttonNumber == 2 then
        -- 功能2：魂玉界面
        print("|cFFFF6600执行功能2：打开魂玉界面|r")
        GLPHandleShowAndHide()

    elseif buttonNumber == 3 then
        -- 功能3：祭灵骨牌
        print("|cFFFF6600执行功能3：打开祭灵骨牌面板|r")
        GLPExHandleShowAndHide()

    elseif buttonNumber == 4 then
        -- 功能4：副本挑战排行榜
        print("|cFFFF6600执行功能4：打开副本挑战排行榜|r")
        HandleRankinglistShowAndHide()

    elseif buttonNumber == 5 then
        -- 功能5：幸运大转盘
        print("|cFFFF6600执行功能5：打开幸运大转盘面板|r")
        if _G["LuckDrawFrame"] then
            if _G["LuckDrawFrame"]:IsVisible() then
                _G["LuckDrawFrame"]:Hide()
            else
                -- 发送数据包给客户端
                Ghost_SendData("OPENLUCKDRAW", "1")
            end
        end

    elseif buttonNumber == 6 then
        -- 功能6：游戏商城
        print("|cFFFF6600执行功能6：打开游戏商城|r")
        DItemShopFrame_ShowHide()
        -- if _G["ItemShopFrame"] then
        --     if _G["ItemShopFrame"]:IsVisible() then
        --         _G["ItemShopFrame"]:Hide()
        --     else
        --         Ghost_SendData("OPENITEMSHOP","1")
        --     end
        -- else
        --     print("|cFFFFAA00警告：游戏商城不可用|r")
        --     success = false
        -- end

    elseif buttonNumber == 7 then
        -- 功能7：本体法宝
        print("|cFFFF6600执行功能7：打开本体法宝界面|r")

        if _G["MagicRuneEnergyBackImgFrame"] then
            if _G["MagicRuneEnergyBackImgFrame"]:IsVisible() then
                _G["MagicRuneEnergyBackImgFrame"]:Hide()
            else
                Ghost_SendData("OPENTALISMAN","1")
            end
        else
            print("|cFFFFAA00警告：本体法宝界面不可用|r")
            success = false
        end


    elseif buttonNumber == 8 then
        -- 功能8：斗气面板
        print("|cFFFF6600执行功能8：打开斗气面板|r")
        
        if _G["StatFrame"] then
            if _G["StatFrame"]:IsVisible() then
                _G["StatFrame"]:Hide()
            else
                Ghost_SendData("OPENSTATPOINT","1")
            end
        else
            print("|cFFFFAA00警告：斗气面板不可用|r")
            success = false
        end

    elseif buttonNumber == 9 then
        -- 功能9：佣兵法宝管理
        print("|cFFFF6600执行功能9：打开佣兵法宝管理界面|r")
        if BotMagicMgr_Toggle then
            BotMagicMgr_Toggle()
        else
            print("|cFFFFAA00警告：佣兵法宝管理系统不可用|r")
            success = false
        end

    else
        print(string.format("|cFFFF0000错误：未定义的按钮功能 %d|r", buttonNumber))
        success = false
    end

    if success and buttonNumber >= 1 and buttonNumber <= SUB_BUTTON_COUNT then
        CircleMenu_SubButton_OnEnter(self)
    end

    return success
end

-- 子按钮鼠标进入事件
function CircleMenu_SubButton_OnEnter(self)
    -- 验证按钮对象
    if not self then
        return
    end

    -- 验证GameTooltip存在
    local gameTooltip = _G["GameTooltip"]
    if not gameTooltip then
        print("|cFFFF0000错误：GameTooltip不可用|r")
        return
    end

    -- 获取按钮名称和编号
    local buttonName = self:GetName()
    if not buttonName then
        return
    end

    local buttonNumber = string.match(buttonName, "CircleMenuSubButton(%d+)")
    if buttonNumber then
        local buttonIndex = tonumber(buttonNumber)

        -- 验证按钮编号范围
        if buttonIndex and buttonIndex >= 1 and buttonIndex <= SUB_BUTTON_COUNT then
            -- 获取动态tooltip文本
            local tooltipText = CircleMenu_GetDynamicSubTooltip(buttonIndex)

            gameTooltip:SetOwner(self, "ANCHOR_RIGHT")
            gameTooltip:SetText(tooltipText, 1, 1, 1, true) -- 启用文本换行
            gameTooltip:Show()

        end
    end
end

-- 子按钮鼠标离开事件
function CircleMenu_SubButton_OnLeave(_)
    local gameTooltip = _G["GameTooltip"]
    if gameTooltip then
        gameTooltip:Hide()
    end
end