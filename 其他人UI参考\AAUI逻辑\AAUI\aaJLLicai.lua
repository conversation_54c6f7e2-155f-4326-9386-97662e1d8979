aaJLLicai = {};
 aaJLLicai.LevelViews = {};
 aaJLLicai.levels = {};
 local titletop = -100;
local titleleft = 50;
 function aaJLLicai:ClickDoneBtn() aaData:sendAddonMsg(10003, "0", "GUILD");
 end;
 aaJLLicai.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaJLLicai.mainView:SetFrameStrata("TOOLTIP");
 aaJLLicai.mainView:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-grlc"});
 aaJLLicai.mainView:SetWidth(512);
 aaJLLicai.mainView:SetHeight(512);
 aaJLLicai.mainView:SetPoint("CENTER",0,0);
 aaJLLicai.mainView:SetMovable(1);
 aaJLLicai.mainView:EnableMouse();
 aaJLLicai.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaJLLicai.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaJLLicai.mainView:Hide();
 end;
 local titlewidth = 422;
local titletop = -150;
 aaJLLicai.title = aaJLLicai.mainView:CreateFontString("aaJLLicai.title", "OVERLAY", "GameFontNormal") do 
 aaJLLicai.title:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 25);
 aaJLLicai.title:SetPoint("TOPLEFT", aaJLLicai.mainView, "TOPLEFT", 45,titletop);
 aaJLLicai.title:SetWidth(titlewidth);
 aaJLLicai.title:SetHeight(25);
 aaJLLicai.title:SetSpacing(5);
 end;
 aaJLLicai.detail = aaJLLicai.mainView:CreateFontString("aaJLLicai.detail", "OVERLAY", "GameFontNormal") do 
 aaJLLicai.detail:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 22);
 aaJLLicai.detail:SetPoint("TOPLEFT", aaJLLicai.mainView, "TOPLEFT", 45,titletop-25);
 aaJLLicai.detail:SetWidth(titlewidth);
 aaJLLicai.detail:SetHeight(200);
 aaJLLicai.detail:SetSpacing(5);
 end;
 aaJLLicai.btn = CreateFrame('Button', nil, aaJLLicai.mainView, 'UIPanelButtonTemplate') do 
 aaJLLicai.btn:SetPoint('TOPLEFT', aaJLLicai.mainView, 'TOPLEFT', 512/2-100/2, titletop-30-10-250);
 aaJLLicai.btn:SetSize(100, 40);
 aaJLLicai.btn:Disable();
 aaJLLicai.btn:SetScript('OnClick', function() aaJLLicai:ClickDoneBtn();
 end);
 end;
 function aaJLLicai:reload() if aa_jllicai~=nil and aa_jllicai~="" and aa_jllicai~={} then 
 local isOk = aa_jllicai[1];
 local day = aa_jllicai[2];
 local level = aa_jllicai[3];
 aaJLLicai.title:SetText("你还可以领取："..aaData.color_blue..day.."天");
 aaJLLicai.detail:SetText(level);
 if isOk == 0 then aaJLLicai.btn:SetText("领取奖励");
 aaJLLicai.btn:Enable();
 elseif isOk == 1 then aaJLLicai.btn:SetText("已领取");
 aaJLLicai.btn:Disable();
 elseif isOk == 2 then aaJLLicai.btn:SetText("我要续费");
 aaJLLicai.btn:Enable();
 end;
 else aaJLLicai.LevelViews[index]:Hide();
 end;
 end;
 local CancelButton = CreateFrame('Button', nil, aaJLLicai.mainView, 'UIPanelButtonTemplate') do 
 CancelButton:SetPoint('TOPRIGHT', aaJLLicai.mainView, 'TOPRIGHT', -30, -20);
 CancelButton:SetSize(60, 30);
 CancelButton:SetText("关闭");
 CancelButton:SetScript('OnClick', function() aaJLLicai:hide();
 end);
 end;
 function aaJLLicai:show() aaJLLicai.mainView:Show();
 aaJLLicai:reload();
 end;
 function aaJLLicai:hide() aaJLLicai.mainView:Hide();
 aaJLLicai:reload();
 end;