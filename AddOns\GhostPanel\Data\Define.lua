LEGACY_BENEFIT_PER_SPECIALTY = 0.6180339887;
LEGACY_STAT_MASK_LV1 = 35;
LEGACY_STAT_MASK_LV2 = 1225;
LEGACY_STAT_MASK_LV3 = 42875;
LEG<PERSON>Y_STAT_MASK_LV4 = 1500625;
L<PERSON><PERSON>Y_STAT_MASK_LV5 = 52521875;
LEGACY_STAT_FACTOR = 0.1;
LEGACY_STAT_FACTOR_MIN = 1;
LEGACY_GP_COST_PER_SPECIALTY = 15;
LEGACY_GP_COST_PER_MF = 95;
LEGACY_NAV_PREV = 1;
LEGACY_NAV_NEXT = 2;
LEG<PERSON>Y_MAX_PAGE = 8;
LEGACY_PAGE_CLASSSKILL = 0;
LEGACY_PAGE_RUNE = 1;
LEGACY_PAGE_TRANSMOG = 2;
LEGACY_PAGE_GUILD = 3;
LEGACY_PAGE_LEGACY = 4;
LEGACY_PAGE_MARKET = 5;
LEGACY_PAGE_REPUTATION = 6;
LEGACY_PAGE_REWARD = 7;
LEGACY_MAX_LEGACY_ITEM_PER_PAGE = 36;
LEGACY_MAX_MARKET_ITEM_PER_PAGE = 11;
LEGACY_MAX_MARKET_GOLD_PER_PAGE = 12;
LEGACY_MAX_MARKET_SPELL_PER_PAGE = 11;
LEGACY_MAX_MARKET_BUFF_PER_PAGE = 12;
LEGACY_MAX_REPUTATION_ITEM_PER_PAGE = 11;
LEGACY_MAX_REPUTATION_SPELL_PER_PAGE = 11;
LEGACY_MAX_REPUTATION_SUPPLY_PER_PAGE = 11;
LEGACY_MAX_REWARD_ITEM_PER_PAGE = 12;
LEGACY_MAX_REWARD_KEY_PER_PAGE = 11;
LEGACY_MAX_ACTIVE_SPELL_PER_PAGE = 10;
LEGACY_MAX_ABILITY_SLOT = 10;
LEGACY_MAX_ACTIVE_ABILITY_UI_SLOT = 10;
LEGACY_MAX_PASSIVE_ABILITY_UI_SLOT = 10;
LEGACY_MAX_ABILITY_UPGRADE_UI_SLOT = 9;
LEGACY_MAX_REPLACABLE_ABILITY_UI_SLOT = 60;
LEGACY_ACTIVE_CLASS_SLOT = 10;
LEGACY_MAX_TALENT_ABILITY_SLOT = 10;
LEGACY_MAX_GUILD_RANK = 15;
LEGACY_MAX_GUILD_BONUS = 9;
LEGACY_MAX_TRANSMOG_TYPE_SLOT = 11;
LEGACY_MAX_TRANSMOG_COLL_SLOT = 33;
LEGACY_TRANSMOG_ITEM_UPDATE_INTERVAL = 0.5;
LEGACY_TRANSMOG_SLOT_UPDATE_INTERVAL = 0.5;
LEGACY_COMBINE_IDENTIFIED_STATS = false;
LEGACY_HIDE_LOCKED_SPELLS = false;
LEGACY_DEBUG = false;
LEGACY_MAX_CLASSSKILL_RANK = 5;

LEGACY_REWARD_TYPE_ITEM = 1;
LEGACY_REWARD_TYPE_KEY = 2;

LEGACY_SPECIALTY_ARMFORCE = 1;
LEGACY_SPECIALTY_INSTINCT = 2;
LEGACY_SPECIALTY_WILLPOWER = 3;
LEGACY_SPECIALTY_SANITY = 4;
LEGACY_SPECIALTY_PYSCHE = 5;
LEGACY_SPECIALTY_TRIAL = 6;

LEGACY_MAX_SKILL_PER_PAGE = 10;
LEGACY_MAX_MEMORY_PER_PAGE = 11;
LEGACY_MAX_SPELL_PER_PAGE = 10;

LEGACY_SPEC_BFACTOR = 0.1

LEGACY_AP_PER_STRENGTH_BASE = 1
LEGACY_AP_PER_AGILITY_BASE = 1
LEGACY_DPS_PER_AP_BASE = 0.3
LEGACY_SP_PER_INTELLECT_BASE = 1
LEGACY_SP_PER_SPIRIT_BASE = 1
LEGACY_HP_PER_STAT_BASE = 10
LEGACY_STAT_FACTOR_BASE = 0.5
LEGACY_STAT_BENEFIT_FACTOR_BASE = 1
LEGACY_SPECIALTY_BENEFIT_CAP = 6
LEGACY_SPECIALTY_BONUS_BASE = 0.5
LEGACY_SPECIALTY_BONUS = 0.05
LEGACY_WEAPON_CRIT_DAMAGE_DEFENSE_PER_STAT = 2
LEGACY_SPELL_CRIT_DAMAGE_DEFENSE_PER_STAT = 2
LEGACY_WEAPON_CRIT_DMG_PER_STAT_BASE = 2
LEGACY_SPELL_CRIT_DMG_PER_STAT_BASE = 2

LEGACY_PHYSICAL_ACCURACY_CAP_BASE = 30
LEGACY_MAGICAL_ACCURACY_CAP_BASE = 30
LEGACY_BLOCK_ABSORB_CAP_BASE = 50
LEGACY_PHYSICAL_DEFENSE_CAP_BASE = 85
LEGACY_DODGE_CAP_BASE = 30
LEGACY_WEAPON_CRIT_CAP_BASE = 25
LEGACY_WEAPON_CRIT_DMG_CAP_BASE = 80
LEGACY_CRIT_TAKEN_CAP = 30
LEGACY_SPELL_CRIT_DMG_CAP_BASE = 80
LEGACY_MAGICAL_DEFENSE_CAP_BASE = 60
LEGACY_SPELL_CRIT_CAP = 25
LEGACY_POWER_REGENERATE_RATE_CAP = 100
LEGACY_ARMOR_FLAT_REDUCTION_CAP = 200
LEGACY_MAX_MANA_BONUS_CAP = 100
LEGACY_HP_REGEN_PER_STAMINA = 1
LEGACY_HP_REGEN_IN_COMBAT_PER_STAMINA = 0.25

LEGACY_SPECIALTY_BONUS_R1 = 0.25
LEGACY_SPECIALTY_BONUS_R2 = 0.5
LEGACY_SPECIALTY_BONUS_R3 = 1
LEGACY_SPECIALTY_EFFECT_CAP = 50

LEGACY_STAT_BONUS_FACTOR = 1
LEGACY_ARMOR_RES_EFFICIENCY_BASE = 0.25
LEGACY_STAT_BONUS_LIMIT = 10

LEGACY_DENO = 680

LEGACY_DAMAGE_LIMIT = 295
LEGACY_DEFENSE_LIMIT = 75
LEGACY_FORCE_LIMIT = 195
LEGACY_ARMOR_EFFECIENCY_LIMIT = 75
LEGACY_RES_EFFECIENCY_LIMIT = 50

LEGACY_DAMAGE_DDENO = 680
LEGACY_DEFENSE_DDENO = 340
LEGACY_FORCE_DDENO = 680
LEGACY_ARMOR_RES_DDENO_M = 10
LEGACY_HIT_CRIT_DDENO_M = 10

LEGACY_RATING_BONUS_LIMIT = 50
LEGACY_RATING_DDENO_M = 10

LEGACY_SPECIALTY_BONUS_FACTOR_POS = 
{
	[1] = 3,
	[2] = 3,
	[3] = 3,
	[4] = 3,
	[5] = 3,
	[6] = 3,
};
LEGACY_SPECIALTY_BONUS_FACTOR_NEG = 
{
	[1] = 3,
	[2] = 3,
	[3] = 3,
	[4] = 3,
	[5] = 3,
	[6] = 3,
};
LEGACY_SPECIALTY_BONUS_FACTOR_PSYCHE = 50;
LEGACY_SPECIALTY_DDENO = 100;

LEGACY_CLASS_COLOR = {
    ["DEATHKNIGHT"] = { r = 0.77, b = 0.12, b = 0.23 },
    ["DRUID"] = { r = 1.00, g = 0.49, b = 0.04 },
    ["HUNTER"] = { r = 0.67, g = 0.83, b = 0.45 },
    ["MAGE"] = { r = 0.41, g = 0.8, b = 0.94 },
    ["PALADIN"] = { r = 0.96, g = 0.55, b = 0.73 },
    ["PRIEST"] = { r = 1, g = 1, b = 1 },
    ["ROGUE"] = { r = 1, g = 0.96, b = 0.41 },
    ["SHAMAN"] = { r = 0, g = 0.44, b = 0.87 },
    ["WARLOCK"] = { r = 0.58, g = 0.51, b = 0.79 },
    ["WARRIOR"] = { r = 0.78, g = 0.61, b = 0.43 }
};

LEGACY_QUALITY_COLOR = {
	[0] = { r = 0.62, g = 0.62, b = 0.62 },
	[1] = { r = 1.0, g = 1.0, b = 1.0 },
	[2] = { r = 0.12, g = 1.0, b = 0.0 },
	[3] = { r = 0.0, g = 0.44, b = 0.87 },
	[4] = { r = 0.64, g = 0.21, b = 0.93 },
	[5] = { r = 1.0, g = 0.5, b = 0 },
	[6] = { r = 0.9, g = 0.8, b = 0.5 },
};

LegacyBuyGoldAmounts = {
	[1] = 1,
	[2] = 5,
	[3] = 10,
	[4] = 50,
	[5] = 100,
	[6] = 200,
	[7] = 500,
	[8] = 1000,
	[9] = 2000,
	[10] = 5000,
	[11] = 10000,
	[12] = -1,
};

LEGACY_TRANSMOG_SLOT_NONE = 0;
LEGACY_TRANSMOG_SLOT_HEAD_CLOTH = 1;
LEGACY_TRANSMOG_SLOT_SHOULDER_CLOTH = 2;
LEGACY_TRANSMOG_SLOT_CHEST_CLOTH = 3;
LEGACY_TRANSMOG_SLOT_WRIST_CLOTH = 4;
LEGACY_TRANSMOG_SLOT_HAND_CLOTH = 5;
LEGACY_TRANSMOG_SLOT_WAIST_CLOTH = 6;
LEGACY_TRANSMOG_SLOT_LEG_CLOTH = 7;
LEGACY_TRANSMOG_SLOT_FEET_CLOTH = 8;
LEGACY_TRANSMOG_SLOT_HEAD_LIGHT = 9;
LEGACY_TRANSMOG_SLOT_SHOULDER_LIGHT = 10;
LEGACY_TRANSMOG_SLOT_CHEST_LIGHT = 11;
LEGACY_TRANSMOG_SLOT_WRIST_LIGHT = 12;
LEGACY_TRANSMOG_SLOT_HAND_LIGHT = 13;
LEGACY_TRANSMOG_SLOT_WAIST_LIGHT = 14;
LEGACY_TRANSMOG_SLOT_LEG_LIGHT = 15;
LEGACY_TRANSMOG_SLOT_FEET_LIGHT = 16;
LEGACY_TRANSMOG_SLOT_HEAD_CHAIN = 17;
LEGACY_TRANSMOG_SLOT_SHOULDER_CHAIN = 18;
LEGACY_TRANSMOG_SLOT_CHEST_CHAIN = 19;
LEGACY_TRANSMOG_SLOT_WRIST_CHAIN = 20;
LEGACY_TRANSMOG_SLOT_HAND_CHAIN = 21;
LEGACY_TRANSMOG_SLOT_WAIST_CHAIN = 22;
LEGACY_TRANSMOG_SLOT_LEG_CHAIN = 23;
LEGACY_TRANSMOG_SLOT_FEET_CHAIN = 24;
LEGACY_TRANSMOG_SLOT_HEAD_HEAVY = 25;
LEGACY_TRANSMOG_SLOT_SHOULDER_HEAVY = 26;
LEGACY_TRANSMOG_SLOT_CHEST_HEAVY = 27;
LEGACY_TRANSMOG_SLOT_WRIST_HEAVY = 28;
LEGACY_TRANSMOG_SLOT_HAND_HEAVY = 29;
LEGACY_TRANSMOG_SLOT_WAIST_HEAVY = 30;
LEGACY_TRANSMOG_SLOT_LEG_HEAVY = 31;
LEGACY_TRANSMOG_SLOT_FEET_HEAVY = 32;
LEGACY_TRANSMOG_SLOT_CLOAK = 33;
LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_SWORD = 34;
LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_AXE = 35;
LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_MACE = 36;
LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_DAGGER = 37;
LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_SWORD = 38;
LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_AXE = 39;
LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_MACE = 40;
LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_POLEARM = 41;
LEGACY_TRANSMOG_SLOT_R_WEAPON_BOW = 42;
LEGACY_TRANSMOG_SLOT_R_WEAPON_CROSSBOW = 43;
LEGACY_TRANSMOG_SLOT_R_WEAPON_GUN = 44;
LEGACY_TRANSMOG_SLOT_O_SHIELD = 45;
LEGACY_TRANSMOG_SLOT_O_HOLDABLE = 46;
LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_STAFF = 47;
LEGACY_TRANSMOG_SLOT_R_WEAPON_WAND = 48;
LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_SWORD = 49;
LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_AXE = 50;
LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_MACE = 51;
LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_DAGGER = 52;
LEGACY_TRANSMOG_SLOT_SHIRT = 53;

LEGACY_EQUIP_SLOT_HEAD = 0;
LEGACY_EQUIP_SLOT_SHOULDERS = 2;
LEGACY_EQUIP_SLOT_SHIRT = 3;
LEGACY_EQUIP_SLOT_CHEST = 4;
LEGACY_EQUIP_SLOT_WAIST = 5;
LEGACY_EQUIP_SLOT_LEGS = 6;
LEGACY_EQUIP_SLOT_FEET = 7;
LEGACY_EQUIP_SLOT_WRIST = 8;
LEGACY_EQUIP_SLOT_HANDS = 9;
LEGACY_EQUIP_SLOT_BACK = 14;
LEGACY_EQUIP_SLOT_MAINHAND = 15;
LEGACY_EQUIP_SLOT_OFFHAND = 16;
LEGACY_EQUIP_SLOT_RANGED = 17;

LEGACY_TRANSMOG_SLOT =
{
	[LEGACY_EQUIP_SLOT_HEAD] = { Name = "头部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Head" },
	[LEGACY_EQUIP_SLOT_SHOULDERS] = { Name = "肩部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Shoulder" },
	[LEGACY_EQUIP_SLOT_SHIRT] = { Name = "衬衣", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Shirt" },
	[LEGACY_EQUIP_SLOT_CHEST] = { Name = "胸部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Chest" },
	[LEGACY_EQUIP_SLOT_WAIST] = { Name = "腰部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Waist" },
	[LEGACY_EQUIP_SLOT_LEGS] = { Name = "腿部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Legs" },
	[LEGACY_EQUIP_SLOT_FEET] = { Name = "脚部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Feet" },
	[LEGACY_EQUIP_SLOT_WRIST] = { Name = "腕部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Wrists" },
	[LEGACY_EQUIP_SLOT_HANDS] = { Name = "手部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Hands" },
	[LEGACY_EQUIP_SLOT_BACK] = { Name = "背部", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Chest" },
	[LEGACY_EQUIP_SLOT_MAINHAND] = { Name = "主手", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-MainHand" },
	[LEGACY_EQUIP_SLOT_OFFHAND] = { Name = "副手", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-SecondaryHand" },
	[LEGACY_EQUIP_SLOT_RANGED] = { Name = "远程", Icon = "Interface\\PaperDoll\\UI-PaperDoll-Slot-Ranged" },
};

LEGACY_EQUIP_SLOTS = 
{
	[LEGACY_EQUIP_SLOT_HEAD] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_HEAD_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_HELMET_29" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_HEAD_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_HELMET_41" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_HEAD_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_HELMET_09" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_HEAD_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_HELMET_03" },
	},
	[LEGACY_EQUIP_SLOT_SHOULDERS] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_SHOULDER_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_SHOULDER_02" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_SHOULDER_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_SHOULDER_23" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_SHOULDER_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_SHOULDER_29" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_SHOULDER_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_SHOULDER_71" },
	},
	[LEGACY_EQUIP_SLOT_SHIRT] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_SHIRT, Name = "衬衣", Icon = "Interface\\Icons\\INV_SHIRT01_PLAIN" },
	},
	[LEGACY_EQUIP_SLOT_CHEST] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_CHEST_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_CHEST_CLOTH_03" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_CHEST_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_CHEST_CLOTH_07" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_CHEST_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_CHEST_CHAIN_11" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_CHEST_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_CHEST_PLATE16" },
	},
	[LEGACY_EQUIP_SLOT_WAIST] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_WAIST_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_BELT_42C" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_WAIST_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_BELT_23" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_WAIST_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_BELT_45C" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_WAIST_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_BELT_48A" },
	},
	[LEGACY_EQUIP_SLOT_LEGS] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_LEG_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_PANTS_CLOTH_07" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_LEG_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_PANTS_06" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_LEG_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_PANTS_03" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_LEG_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_PANTS_04" },
	},
	[LEGACY_EQUIP_SLOT_FEET] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_FEET_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_BOOTS_CLOTH_01" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_FEET_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_BOOTS_08" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_FEET_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_MISC_DESECRATED_MAILBOOTS" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_FEET_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_BOOTS_PLATE_03" },
	},
	[LEGACY_EQUIP_SLOT_WRIST] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_WRIST_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_BRACER_09" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_WRIST_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_BRACER_02" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_WRIST_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_BRACER_16" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_WRIST_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_BRACER_19" },
	},
	[LEGACY_EQUIP_SLOT_HANDS] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_HAND_CLOTH, Name = "布甲", Icon = "Interface\\Icons\\INV_MISC_DESECRATED_CLOTHGLOVE" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_HAND_LIGHT, Name = "轻甲", Icon = "Interface\\Icons\\INV_GAUNTLETS_21" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_HAND_CHAIN, Name = "链甲", Icon = "Interface\\Icons\\INV_GAUNTLETS_11" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_HAND_HEAVY, Name = "重甲", Icon = "Interface\\Icons\\INV_GAUNTLETS_10" },
	},
	[LEGACY_EQUIP_SLOT_BACK] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_BACK, Name = "披风", Icon = "Interface\\Icons\\INV_GUILD_CLOAK_ALLIANCE_A" },
	},
	[LEGACY_EQUIP_SLOT_MAINHAND] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_SWORD, Name = "单手剑", Icon = "Interface\\Icons\\INV_SWORD_41" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_AXE, Name = "单手斧", Icon = "Interface\\Icons\\INV_AXE_17" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_MACE, Name = "单手锤", Icon = "Interface\\Icons\\INV_MACE_07" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_1H_DAGGER, Name = "匕首", Icon = "Interface\\Icons\\INV_WEAPON_SHORTBLADE_26" },
		[4] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_SWORD, Name = "双手剑", Icon = "Interface\\Icons\\INV_SWORD_19" },
		[5] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_AXE, Name = "双手斧", Icon = "Interface\\Icons\\INV_AXE_02" },
		[6] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_MACE, Name = "双手锤", Icon = "Interface\\Icons\\INV_HAMMER_03" },
		[7] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_POLEARM, Name = "长柄武器", Icon = "Interface\\Icons\\INV_SPEAR_07" },
		[8] = { Slot = LEGACY_TRANSMOG_SLOT_M_WEAPON_2H_STAFF, Name = "法杖", Icon = "Interface\\Icons\\INV_STAFF_07" },
	},
	[LEGACY_EQUIP_SLOT_OFFHAND] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_SWORD, Name = "单手剑", Icon = "Interface\\Icons\\INV_SWORD_41" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_AXE, Name = "单手斧", Icon = "Interface\\Icons\\INV_AXE_17" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_MACE, Name = "单手锤", Icon = "Interface\\Icons\\INV_MACE_07" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_O_WEAPON_1H_DAGGER, Name = "匕首", Icon = "Interface\\Icons\\INV_WEAPON_SHORTBLADE_26" },
		[4] = { Slot = LEGACY_TRANSMOG_SLOT_O_SHIELD, Name = "盾牌", Icon = "Interface\\Icons\\INV_SHIELD_06" },
		[5] = { Slot = LEGACY_TRANSMOG_SLOT_O_HOLDABLE, Name = "法器", Icon = "Interface\\Icons\\INV_OFFHAND_OUTLANDRAID_03BLUE" },
	},
	[LEGACY_EQUIP_SLOT_RANGED] = 
	{
		[0] = { Slot = LEGACY_TRANSMOG_SLOT_R_WEAPON_BOW, Name = "弓", Icon = "Interface\\Icons\\INV_WEAPON_BOW_01" },
		[1] = { Slot = LEGACY_TRANSMOG_SLOT_R_WEAPON_CROSSBOW, Name = "弩", Icon = "Interface\\Icons\\INV_WEAPON_CROSSBOW_09" },
		[2] = { Slot = LEGACY_TRANSMOG_SLOT_R_WEAPON_GUN, Name = "枪械", Icon = "Interface\\Icons\\INV_WEAPON_RIFLE_07" },
		[3] = { Slot = LEGACY_TRANSMOG_SLOT_R_WEAPON_WAND, Name = "魔杖", Icon = "Interface\\Icons\\INV_WAND_07" },
	},
};

LEGACY_MARKET_TYPE_ITEM = 1;
LEGACY_MARKET_TYPE_BUFF = 2;
LEGACY_MARKET_TYPE_SPELL = 3;
LEGACY_REPUTATION_TYPE_SUPPLY = 1;
LEGACY_REPUTATION_TYPE_ITEM = 2;
LEGACY_REPUTATION_TYPE_SPELL = 3;
LEGACY_REWARD_TYPE_EVENT = 1;
LEGACY_REWARD_TYPE_ACCOUNT = 2;
LEGACY_REWARD_TYPE_CHARACTER = 3;

LEGACY = true;

LEGACY_REWARD_TYPE_SHARED = 1;
LEGACY_REWARD_TYPE_ACCOUNT = 2;
LEGACY_REWARD_TYPE_CHARACTER = 3;

LEGACY_SPELL_TYPE_ACTIVE = 1;
LEGACY_SPELL_TYPE_PASSIVE = 2;
LEGACY_SPELL_TYPE_TALENT = 3;
LEGACY_SPELL_TYPE_SPECIALTY = 4;

LEGACY_MARKET_ITEM_TYPE_ITEM = 1;
LEGACY_MARKET_ITEM_TYPE_GOLD = 2;
LEGACY_MARKET_ITEM_TYPE_SPELL = 3;
LEGACY_MARKET_ITEM_TYPE_BUFF = 4;

LEGACY_REPUTATION_REWARD = 1;
LEGACY_REPUTATION_ITEM = 2;
LEGACY_REPUTATION_SPELL = 3;

LEGACY_STAT_STRENGTH = 1
LEGACY_STAT_AGILITY = 2
LEGACY_STAT_STAMINA = 3
LEGACY_STAT_INTELLECT = 4
LEGACY_STAT_SPIRIT = 5
LEGACY_STAT_POWER_MELEE = 6
LEGACY_STAT_POWER_RANGED = 7
LEGACY_STAT_POWER_HOLY = 8
LEGACY_STAT_POWER_FIRE = 9
LEGACY_STAT_POWER_NATURE = 10
LEGACY_STAT_POWER_FROST = 11
LEGACY_STAT_POWER_SHADOW = 12
LEGACY_STAT_POWER_ARCANE = 13
LEGACY_STAT_POWER_HEALING = 14
LEGACY_STAT_ARMOR = 15
LEGACY_STAT_RES_HOLY = 16
LEGACY_STAT_RES_FIRE = 17
LEGACY_STAT_RES_NATURE = 18
LEGACY_STAT_RES_FROST = 19
LEGACY_STAT_RES_SHADOW = 20
LEGACY_STAT_RES_ARCANE = 21
LEGACY_STAT_WEAPON_DAMAGE_BASE = 22
LEGACY_STAT_WEAPON_DAMAGE_OFF = 23
LEGACY_STAT_WEAPON_DAMAGE_RANGED = 24
LEGACY_STAT_WEAPON_SPEED_BASE = 25
LEGACY_STAT_WEAPON_SPEED_OFF = 26
LEGACY_STAT_WEAPON_SPEED_RANGED = 27
LEGACY_STAT_MANA_REGEN = 28
LEGACY_STAT_PHYSICAL_CRIT_POWER = 29
LEGACY_STAT_MAGICAL_CRIT_POWER = 30
LEGACY_STAT_PHYSICAL_HIT_POWER = 31
LEGACY_STAT_MAGICAL_HIT_POWER = 32
LEGACY_STAT_PHYSICAL_HASTE_POWER = 33
LEGACY_STAT_MAGICAL_HASTE_POWER = 34
LEGACY_STAT_PHYSICAL_CRIT_DEFENSE_POWER = 35
LEGACY_STAT_MAGICAL_CRIT_DEFENSE_POWER = 36
LEGACY_STAT_PHYSICAL_HIT_DEFENSE_POWER = 37
LEGACY_STAT_MAGICAL_HIT_DEFENSE_POWER = 38
LEGACY_STAT_DODGE_POWER = 39
LEGACY_STAT_PARRY_POWER = 40
LEGACY_STAT_BLOCK_POWER = 41
LEGACY_STAT_RESIST_POWER = 42
LEGACY_STAT_PARRY_CHANCE = 43
LEGACY_STAT_DODGE_CHANCE = 44
LEGACY_STAT_BLOCK_CHANCE = 45
LEGACY_STAT_RESIST_CHANCE = 46
LEGACY_STAT_MELEE_CRIT_CHANCE = 47
LEGACY_STAT_RANGED_CRIT_CHANCE = 48
LEGACY_STAT_HOLY_CRIT_CHANCE = 49
LEGACY_STAT_FIRE_CRIT_CHANCE = 50
LEGACY_STAT_NATURE_CRIT_CHANCE = 51
LEGACY_STAT_FROST_CRIT_CHANCE = 52
LEGACY_STAT_SHADOW_CRIT_CHANCE = 53
LEGACY_STAT_ARCANE_CRIT_CHANCE = 54
LEGACY_STAT_MANA_REGEN_RESTING = 55
LEGACY_STAT_CHAOS_POWER = 56

LEGACY_RUNE_TIER0 = 0;
LEGACY_RUNE_TIER1 = 1;
LEGACY_RUNE_TIER2 = 2;

LEGACY_PAGE_STATIC_RUNE = 8;
LEGACY_STATIC_RUNE_TYPE_ITEM = 1;
LEGACY_STATIC_RUNE_TYPE_BUFF = 2;
LEGACY_STATIC_RUNE_TYPE_SPELL = 3;