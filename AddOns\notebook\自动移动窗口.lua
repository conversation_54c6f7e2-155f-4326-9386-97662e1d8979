-- local frame = CreateFrame("Frame",nil,UIParent)
-- frame:SetSize(600, 30)
-- frame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 2, right = 2, top = 2, bottom = 2 }});
-- frame:SetBackdropColor(24/255,24/255,24/255)
-- frame:SetPoint("CENTER",nil,"CENTER")
-- local pi = math.pi
-- local cos = math.cos

-- local function cosineInterpolation(point1, point2,mu)
	-- return point1+(point2-point1)* (1 - cos (pi*mu))/2
-- end

-- local timeToMove = 5
-- local start, stop = 0,1200
-- local totalElapsed = 0


-- local function onupdate(self, elapsed)
	-- totalElapsed = totalElapsed + elapsed
	-- if totalElapsed > timeToMove then
		-- totalElapsed = 0
		-- print("name = "..self:GetName())
		-- frame:Hide()
		-- frame:Setscript("OnUpdate",nil)
	-- return
	-- end
-- offset = cosineInterpolation(start,stop,1/timeToMove*totalElapsed)
-- self:SetPoint("LEFT",self:GetParent(),"LEFT",offset,100)
-- end

-- local function startMove(self)
-- self:SetPoint("LEFT",self:GetParent(),"LEFT",0,100)
-- self:SetScript("OnUpdate", onupdate)
-- end


-- startMove(frame)

--滚动播放文字效果
local frame = CreateFrame("Frame",nil,UIParent)
frame:SetSize(600, 50)
-- frame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 2, right = 2, top = 2, bottom = 2 }});
-- frame:SetBackdropColor(24/255,24/255,24/255)
frame:SetPoint("CENTER",nil,"CENTER")
local pi = math.pi
local cos = math.cos

local MoveText = frame:CreateFontString("MoveText")
MoveText:SetFont("Fonts\\ZYHei.ttf", 18)
MoveText:SetPoint("CENTER", 0, 0)
MoveText:SetText("|cffFFC125大家看的见我吗?我这么潇洒英俊帅气风度翩翩，小姐姐全喜欢我！！|r")

local function cosineInterpolation(point1, point2,mu)
	return point1+(point2-point1)* (1 - cos (pi*mu))/2
end

local timeToMove = 20
local start, stop = -1500,1500
local totalElapsed = 0


local function onupdate(self, elapsed)
	totalElapsed = totalElapsed + elapsed
	if totalElapsed > timeToMove then
		totalElapsed = 0
		-- print("name = "..self:GetName())
		frame:Setscript("OnUpdate",nil)
		frame:Hide()
	return
	end
offset = cosineInterpolation(start,stop,1/timeToMove*totalElapsed)
self:SetPoint("LEFT",self:GetParent(),"LEFT",offset,50)
end

local function startMove(self)
self:SetPoint("LEFT",self:GetParent(),"LEFT",0,50)
self:SetScript("OnUpdate", onupdate)
end


startMove(frame)

