local GLP = {}
local GLMB = {}
local HGLP
local NUM = 8
local ServerNum
local TabTex = 
{
"Interface\\ICONS\\I",
"Interface\\ICONS\\II",
"Interface\\ICONS\\III",
"Interface\\ICONS\\IV",
"Interface\\ICONS\\V",
"Interface\\ICONS\\VI",
"Interface\\ICONS\\VII",
"Interface\\ICONS\\VIII",
"Interface\\ICONS\\I-D",
"Interface\\ICONS\\II-D",
"Interface\\ICONS\\III-D",
"Interface\\ICONS\\IV-D",
"Interface\\ICONS\\V-D",
"Interface\\ICONS\\VI-D",
"Interface\\ICONS\\VII-D",
"Interface\\ICONS\\VIII-D",
}

-- print("|T"..TabTex[1]..":20|t")

function GLPSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 


function GLPGetLMNum(i)
	if i == 1 then
		return "I"
	elseif i == 2 then
		return "II"
	elseif i == 3 then
		return "III"
	elseif i == 4 then
		return "IV"
	elseif i == 5 then
		return "V"
	elseif i == 6 then
		return "VI"
	elseif i == 7 then
		return "VII"
	elseif i == 8 then
		return "VIII"
	end
end


function CreateHGLP(Name)
local F = CreateFrame("Frame",Name,UIParent)
F:SetSize(384, 512);
F:SetPoint("CENTER",0,0)
F:Hide()
return F
end


function CreateGLPFrame(Name)
local GL = CreateFrame("Frame",Name,HGLP)
GL:SetSize(384, 512);
local t = GL:CreateTexture("$parent".."Background","ARTWORK")
t:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
t:SetSize(352, 441)
t:SetPoint("TOPLEFT",0,0)
t:SetTexCoord(0, 0.6875, 0, 0.861328125)
local fs = GL:CreateFontString("$parent".."TitleText","ARTWORK","GameFontNormal")
fs:SetText("魂玉")
fs:SetPoint("TOP",0,-18)
GL.FontString = fs
GL.texture = t
GL:SetPoint("CENTER",0,0)
GL:Hide()
return GL
end


function GenerateGLPFrame()
	for i = 1,NUM do
		GLP[i] = CreateGLPFrame("GLP"..i);
		if i == 1 then
			GLP[i]:Show()
		end
	end
end 


function GLPFrameHide()
	for i = 1,NUM do
		GLP[i]:Hide()
	end
end


function HGLPPHide(count)
	for i = 1,NUM do
		if i > count then
			HGLP.P[i]:Hide()
			HGLP.LineTab[i]:Hide()
		end
	end
end


function HGLPPUnHigh()
	for i = 1,NUM do
		HGLP.P[i]:UnlockHighlight()
	end
end


function CreateGLPButton(Parent,Point,Name,x,y)
local B1 = CreateFrame("Button","$parent"..Name,Parent)
B1:SetSize(90, 90);
B1:SetPoint(Point,x,y)
local bt1 = B1:CreateTexture("$parent".."B1","ARTWORK")
bt1:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt1:SetSize(82, 82)
bt1:SetPoint("CENTER",0,0)
bt1:SetTexCoord(0.767578125, 0.92578125, 0.32421875, 0.482421875)
local bt2 = B1:CreateTexture("$parent".."B2","BACKGROUND")
bt2:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt2:SetSize(108, 108)
bt2:SetPoint("CENTER",0,0)
bt2:SetTexCoord(0.740234375, 0.953125, 0.484375, 0.697265625)
local bt3 = B1:CreateTexture("$parent".."B3","BORDER")
bt3:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt3:SetSize(64, 64)
bt3:SetPoint("CENTER",0,0)
bt3:SetTexCoord(0.78125,0.91015625,0.69921875,0.828125)
B1:Show()
return B1
end


function CreateGLPITEMButton(Parent,Name,Point,x,y,Tex)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(90,90)
Button:SetPoint(Point,x,y)
Button:SetFrameStrata("TOOLTIP")
local bt1 = Button:CreateTexture("$parent".."Portrait","ARTWORK")
bt1:SetSize(70, 70)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
Button:Show()
return Button
end


function GLPGenerateGLPButton() 
	for i=1,NUM do
		GLP[i].BI = CreateGLPButton(GLP[i],"CENTER","BI",-15,140)
		GLP[i].BII = CreateGLPButton(GLP[i],"BOTTOMRIGHT","BII",-56,168)
		GLP[i].BIII = CreateGLPButton(GLP[i],"BOTTOMLEFT","BIII",26,168)
		GLP[i].BIV = CreateGLPButton(GLP[i],"CENTER","BIV",-14,-103)
		GLP[i].BV = CreateGLPButton(GLP[i],"TOPRIGHT","BV",-56,-133)
		GLP[i].BVI = CreateGLPButton(GLP[i],"TOPLEFT","BVI",28,-133)
		GLP[i].BJ = CreateGLPButton(GLP[i],"CENTER","BJ",-15,15)
		GLP[i].BI.ID = (tostring(i).."#1")
		GLP[i].BII.ID = (tostring(i).."#2")
		GLP[i].BIII.ID = (tostring(i).."#3")
		GLP[i].BIV.ID = (tostring(i).."#4")
		GLP[i].BV.ID = (tostring(i).."#5")
		GLP[i].BVI.ID = (tostring(i).."#6")
		GLP[i].BJ.ID = (tostring(i).."#7")
		GLP[i].BJ.ACTI = 0;
		GLP[i].BJ:Hide()
	end
end 


function GLPCreateXXButton(Parent,Name)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",-28,-9)
Button:SetFrameStrata("HIGH")
Button:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
Button:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
Button:SetDisabledTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
Button:Show()
return Button
end


function GLPCreateActEff(Parent,Name)	--激活
local jihuo = Parent:CreateTexture("$parent"..Name,"OVERLAY")
jihuo:SetTexture("Interface\\Spellbook\\UI-GlyphFrame-Glow")
jihuo:SetSize(352, 441)
jihuo:SetPoint("TOPLEFT",-9,-38)
jihuo:SetTexCoord(0, 0.6875, 0, 0.861328125)
jihuo:SetVertexColor(1.0,1.0,1.0,0.8)	
jihuo:Hide()
return jihuo
end


function GenerateGLPActEff() 
	for i=1,NUM do
		GLP[i].ActEff = GLPCreateActEff(GLP[i],"ActEff")
	end
end


-- print("888")


function GLPCreateLineTab(Parent,Name,x,y)
local LineTab = Parent:CreateTexture("$parent"..Name,"ARTWORK")
LineTab:SetTexture("Interface\\Spellbook\\SpellBook-SkillLineTab")
LineTab:SetSize(64, 64)
LineTab:SetPoint("TOPRIGHT",x,y)
return LineTab
end


function GLPGenerateLineTab() 
HGLP.LineTab = {}
	for i=1,NUM do
		HGLP.LineTab[i] = GLPCreateLineTab(HGLP,"LineTab"..i,30,-30 -(i-1) * 50)
	end
end


function GLPCreateTabButton(Parent,Name,x,y,Tex,DTex)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",x,y)
Button:SetNormalTexture(Tex)
Button:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
Button:SetDisabledTexture(DTex)
Button:Show()
return Button
end


function GLPCreateBuyButton(Parent,Name,x,y)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",x,y)
Button:Hide()
return Button
end


function GLPGenerateTabButton() 
HGLP.P = {}
HGLP.BUY = {}
	for i=1,NUM do
		HGLP.P[i] = GLPCreateTabButton(HGLP,"P"..i,0,-42 -(i-1) * 50,TabTex[i],TabTex[i+NUM])
		HGLP.BUY[i] = GLPCreateBuyButton(HGLP,"BUY"..i,0,-42 -(i-1) * 50)
		HGLP.P[i].ID = tostring(i)
		HGLP.BUY[i].ID = tostring(i)
	end
end


function GLPCreatePP(Parent,Name)
local img1 = Parent:CreateTexture("$parent"..Name, "OVERLAY")
img1:SetSize(64,64)
img1:SetPoint("TOPLEFT", 6, -6)
SetPortraitTexture(img1, "player")
return img1
end


function GLPPrepareScript(object, text, script, itemlink)
	  if text then	
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
	  end
	  	  
	  if type(script) == "function" then	
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end


function GLPGenerateButtonScript() 
	for i=1,NUM do
		local j = GLPGetLMNum(i)
		GLPPrepareScript(HGLP.P[i],j,function(self,button) if button == "LeftButton" then GLPFrameHide() HGLPPUnHigh() local i = tonumber(self.ID) GLP[i]:Show() self:LockHighlight() end end)
	end
end


function GLPGenerateBUYScript() 
	for i=1,NUM do
		local j = GLPGetLMNum(i)
		GLPPrepareScript(HGLP.BUY[i],"购买"..j.."页面",function(self,button) SendAddonMessage("SSC_BUY_PAGE",tostring(self.ID),"GUILD", UnitName("player")) end)
	end
end


function GLPHandleButtonShowHide(count)
	for i=1,NUM do
		if i > count then
			HGLP.P[i]:Disable()
			HGLP.BUY[i]:Show()
			HGLP.BUY[i]:SetFrameStrata("HIGH")
		end
	end
end


function GLPHandleButtonScript(Button,Type)
	
	if Type == "GLMENU" then
		FOOBARTT={}
		if HGLP.PP == nil then
			HGLP.PP = GLPCreatePP(HGLP,"HGLPPP")
		end
		if Button.ON:IsShown() then
			Button.ON:Hide()
			Button.OFF:Show()
			GLPBScript()
			HGLP:Show()
			HGLP.P[1]:Click()
		else
			Button.ON:Show()
			Button.OFF:Hide()
			HGLP:Hide()
		end
	end
	
	if string.match(Type,"CLOSE")  then
		Button.ON:Show()
		Button.OFF:Hide()
		HGLP:Hide()
	end
	
	if string.match(Type,"PAGE") then
		
	end
end


function GLPCreateMenuButton(Name,x,y,Tex)
local Button = CreateFrame("Button","$parent"..Name,UIParent)
Button:SetSize(64,64)
Button:SetPoint("TOPLEFT",x,y)
Button:SetNormalTexture(Tex)
Button:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
Button:Show()
return Button
end


function GLPHandleMenuScript()
	HGLP.CLOSE = GLPCreateXXButton(HGLP,"CLOSE")
	GLMB.ON = GLPCreateMenuButton("GLMBON",230,-115,"Interface\\ICONS\\jf1")
	GLMB.OFF = GLPCreateMenuButton("GLMBOFF",230,-115,"Interface\\ICONS\\jinbi")
	GLMB.OFF:Hide()
	GLPPrepareScript(GLMB.ON,"打开菜单",function() GLPHandleButtonScript(GLMB,"GLMENU") end)
	GLPPrepareScript(GLMB.OFF,"关闭菜单",function() GLPHandleButtonScript(GLMB,"GLMENU") end)
	GLPPrepareScript(HGLP.CLOSE,"关闭",function() GLPHandleButtonScript(GLMB,"CLOSE") end)
end


function GLPCreateItemTex(Parent,Name,Tex)
local bt1 = Parent:CreateTexture("$parent"..Name,"ARTWORK")
bt1:SetSize(30, 30)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
local bt2 = Parent:CreateTexture("$parent".."quanquan","ARTWORK")
bt2:SetSize(35, 35)
bt2:SetPoint("CENTER",0,0)
bt2:SetTexture("Interface\\Icons\\Circle")
return bt1
end


function GLPGetSlotButton(page,num)
	
	if num == 1 then
	return GLP[page].BI;
	elseif num == 2 then
	return GLP[page].BII;
	elseif num == 3 then
	return GLP[page].BIII;
	elseif num == 4 then
	return GLP[page].BIV;
	elseif num == 5 then
	return GLP[page].BV;
	elseif num == 6 then
	return GLP[page].BVI;
	elseif num == 7 then
	return GLP[page].BJ;
	end

end


function GLPGetItemInSlotText(link)
	if link ~= nil then
	return ""
	else
	return "未镶嵌"
	end
end

--VARIABLES_LOADED

function GLPBScriptHandle(self,button)
	if button == "LeftButton" then 
		local ctype,itemid,itemlink = GetCursorInfo() 
		if ctype == "item" then 
			local msg = self.ID.."#"..itemid;
			
			SendAddonMessage("SSC_ITEM_TO_SLOT",msg,"GUILD", UnitName("player"))
		end 
		
		if ctype == nil then 
			local msg = self.ID;
			SendAddonMessage("SSC_REMOVE_SLOT_ITEM",msg,"GUILD",UnitName("player"))
		end 
	end
end


function GLPBScript()
	for i=1,NUM do
		GLPPrepareScript(GLP[i].BI,GLPGetItemInSlotText(GLP[i].BI.itemlink),GLPBScriptHandle,GLP[i].BI.itemlink)
		GLPPrepareScript(GLP[i].BII,GLPGetItemInSlotText(GLP[i].BII.itemlink),GLPBScriptHandle,GLP[i].BII.itemlink)
		GLPPrepareScript(GLP[i].BIII,GLPGetItemInSlotText(GLP[i].BIII.itemlink),GLPBScriptHandle,GLP[i].BIII.itemlink)
		GLPPrepareScript(GLP[i].BIV,GLPGetItemInSlotText(GLP[i].BIV.itemlink),GLPBScriptHandle,GLP[i].BIV.itemlink)
		GLPPrepareScript(GLP[i].BV,GLPGetItemInSlotText(GLP[i].BV.itemlink),GLPBScriptHandle,GLP[i].BV.itemlink)
		GLPPrepareScript(GLP[i].BVI,GLPGetItemInSlotText(GLP[i].BVI.itemlink),GLPBScriptHandle,GLP[i].BVI.itemlink)
		GLPPrepareScript(GLP[i].BJ,GLPGetItemInSlotText(GLP[i].BJ.itemlink),GLPBScriptHandle,GLP[i].BJ.itemlink)
	end
end


function GLPBatchHandle()
	
	HGLP = CreateHGLP("HGLP")
		
	GLPHandleMenuScript()
	
	GenerateGLPFrame()
	
	GLPGenerateGLPButton()
	
	GenerateGLPActEff()
	
	GLPGenerateLineTab()
	
	GLPGenerateTabButton()
	
	GLPGenerateButtonScript()
	
	GLPBScript()
	
	GLPGenerateBUYScript()
	
end

function GLPGETGLMB()
	if GLMB.ON:IsShown() then
		GLMB.ON:Hide()
	else
		if GLMB.OFF:IsShown() then
			GLMB.OFF:Click()
			GLMB.ON:Hide()
		else
			GLMB.ON:Show()
		end
	end
end


GLPBatchHandle()


function GLPEvent(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
		
		if h == "TT" and msg == "HIDE" then
			
			if( StaticPopup_Visible( "DEATH" ) ) then
			-- print("来这了么？")
				StaticPopup_Hide( "DEATH" );
			end
		end
		--镶嵌 覆盖
		if h == "SSS_XQ_FG" then
			local list = GLPSplit(msg,"#")
			local p = tonumber(list[1]);
			local n = tonumber(list[2]);
			local id = tonumber(list[3]);
			if id == 0 then return; end 
			local link = list[4];
			local B = GLPGetSlotButton(p,n);
			if B.itemlink == nil then
			B.itemlink = link
			else
			B.itemlink = nil
			B.itemlink = link
			end
			local icon = "|T"..GetItemIcon(id)..":30|t"; 
			if B.tex == nil then
				B.tex = GLPCreateItemTex(B,"itemtex",GetItemIcon(id))
				B.itemid = id
			else
				B.tex:Hide()
				_G[B:GetName().."itemtex"] = nil
				B.tex = nil
				B.tex = GLPCreateItemTex(B,"itemtex",GetItemIcon(id))
				B.itemid = id
			end
			GLPBScript()
			-- B:Hide()
		end
		
		--拆卸
		if h == "SSS_CX" then
			local list = GLPSplit(msg,"#")
			local p = tonumber(list[1]);
			local n = tonumber(list[2]);
			local B = GLPGetSlotButton(p,n);
			local pname = B:GetName();
			_G[pname.."quanquan"]:Hide()
			B.tex:Hide()
			B.tex = nil
			_G[B:GetName().."itemtex"] = nil
			-- B.tex = GLPCreateItemTex(B,"itemtex",GetItemIcon(id))
			B.itemlink = nil;
			GLPBScript()
		end
		
		--模式2
		if h == "SSS_LIMIT_VAL" then
		-- print("SSS_LIMIT_VAL")
		print(msg)
			local val = tonumber(msg)
			if val == 0 then
				GLMB.ON:Hide();
				return;
			end
			GLPHandleButtonShowHide(val);
			return;
		end
		
		--模式1
		if h == "SSS_LIMIT_NUM"then
		-- print("SSS_LIMIT_NUM")
			local val = tonumber(msg)
			if val == 0 then
				GLMB.ON:Hide();
				return;
			end
			HGLPPHide(val);
			return;
		end
	
		--激活
		if h == "SSS_ACTI_VAL" then 
			local list = GLPSplit(msg,"#")
			local p = tonumber(list[1]);
			local id = tonumber(list[2]);
			if id == 0 then return; end 
			local link = list[3];
			local B = GLPGetSlotButton(p,7);
			GLP[p].ActEff:Show()
			B.tex = GLPCreateItemTex(B,"itemtex",GetItemIcon(id))
			B.itemid = id
			B.itemlink = link
			B:Show()
			GLPBScript()
		end
		
		--失活
		if h == "SSS_ACTI_UN" then
			local p = tonumber(msg)
			local B = GLPGetSlotButton(p,7);
			GLP[p].ActEff:Hide()
			B.tex:Hide()
			B.tex = nil
			_G[B:GetName().."itemtex"] = nil
			B.itemlink = nil;
			B:Hide()
			GLPBScript()
		end
		
		if h == "SSS_BUY_PAGE" then
			local p = tonumber(msg)
			HGLP.P[p]:Enable()
			HGLP.BUY[p]:Disable()
			HGLP.BUY[p]:Hide()
		end
	end
end


function GLPSendMutualOp(self, event,...)
	SendAddonMessage("SSC_LIMIT","VAL","GUILD", UnitName("player"))
	SendAddonMessage("SSC_ACTI","ISOK","GUILD", UnitName("player"))
end


local MsgReceiversGLP = CreateFrame("Frame")
MsgReceiversGLP:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiversGLP:SetScript("OnEvent", GLPEvent)

local PLOAD = CreateFrame("Frame")
PLOAD:RegisterEvent("PLAYER_LOGIN")
PLOAD:SetScript("OnEvent", GLPSendMutualOp)


-- print("1010")

