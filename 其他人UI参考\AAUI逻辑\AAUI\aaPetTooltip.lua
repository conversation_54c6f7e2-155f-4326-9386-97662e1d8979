aaPetTooltip = {};
 aaPetTooltip.httpOk = {};
 local get_fm_fontstring = function() 
 local selectItemId;
 for i=1,GameTooltip:NumLines() do 
 local mytext=_G["GameTooltipTextLeft"..i];
 local text=mytext:GetText();
 if text == nil or text == " " then return _G["GameTooltipTextLeft"..i-1];
 end;
 end;
 return _G["GameTooltipTextLeft"..GameTooltip:NumLines()];
 end;
 GameTooltip:HookScript("OnTooltipSetItem",function(self) 
 local name, itemLink = self:GetItem();
 local itemString = string.match(itemLink, "item[%-?%d:]+");
 local _, itementry, _, _, _, suffixId, _, _, _, _= strsplit(":", itemString);
 itementry = itementry + 0;
 selectItemId = suffixId - 0;
 if aaPetTooltip.httpOk[selectItemId] == nil then aaPetTooltip.httpOk[selectItemId] = 1;
 end;
 if aaPetTooltip.httpOk[selectItemId] == 1 then aaDataPet:GetHttpItem(selectItemId, itementry);
 end;
 aaPetTooltip.httpOk[selectItemId] = aaPetTooltip.httpOk[selectItemId] + 1;
 if aaPetTooltip.httpOk[selectItemId] >= 15 then aaPetTooltip.httpOk[selectItemId] = 1;
 end;
 local itemitem = aaData:getItem(selectItemId, itementry);
 local itemitementry = 0;
 if itemitem then itemitementry = itemitem[2]+0;
 end;
 local item = aaDataPet:getItem(selectItemId, itementry) if selectItemId > 0 and item and item ~= {} and itemitementry ~= itementry then 
 local fm = get_fm_fontstring();
 if fm ~= nil then fm:SetSpacing(4);
 if item then GameTooltipTextLeft2:SetText("宠物\n"..GameTooltipTextLeft2:GetText());
 GameTooltipTextLeft2:SetSpacing(4);
 local tiaojiantext = "";
 if item[2] > 0 then GameTooltipTextLeft1:SetText(aaDataPet:get_dbname(selectItemId, itementry,item[1]));
 local level = item[2];
 local leveltext = "";
 if petnonsuch[level] then leveltext = petnonsuch[level][2]..petnonsuch[level][1];
 end;
 local upgradeid = item[8];
 local mjtext = "";
 if item[3] then mjtext = item[3].."%";
 end;
 local lltext = "";
 if item[4] then lltext = item[4].."%";
 end;
 local zltext = "";
 if item[5] then zltext = item[5].."%";
 end;
 local jstext = "";
 if item[6] then jstext = item[6].."%";
 end;
 local nltext = "";
 if item[7] then nltext = item[7].."%";
 end;
 if item[9] > 0 then mjtext = mjtext.." + "..item[9].."%";
 lltext = lltext.." + "..item[9].."%";
 zltext = zltext.." + "..item[9].."%";
 jstext = jstext.." + "..item[9].."%";
 nltext = nltext.." + "..item[9].."%";
 end;
 local runeid = item[10];
 local spellid = item[12];
 local runeids = aaData:toVectorInt(item[11]);
 local spellids = aaData:toVectorInt(item[13]);
 local runetext = "";
 for i=1,#runeids do 
 local spellid = runeids[i];
 local name = "";
 if GetSpellInfo(spellid) then name = GetSpellInfo(spellid);
 end;
 local spelltext = "";
 if name and name ~= "" then spelltext = name;
 end;
 if spelltext ~= "" then runetext = runetext.."["..spelltext.."]  ";
 end;
 end;
 if runetext and runetext ~= "" then runetext = aaData:autoLine(runetext, 30);
 end;
 local runetext1 = "";
 for i=1,#spellids do 
 local spellid = spellids[i];
 local name = "";
 if GetSpellInfo(spellid) then name = GetSpellInfo(spellid);
 end;
 local spelltext = "";
 if name and name ~= "" then spelltext = name;
 end;
 if spelltext ~= "" then runetext1 = runetext1.."["..spelltext.."]  ";
 end;
 end;
 if runetext1 and runetext1 ~= "" then runetext1 = aaData:autoLine(runetext1, 30);
 end;
 local lrtf = "";
 if item[14] then lrtf = item[14];
 end;
 local fwlevel = aaData:get_star(spellid);
 if fwlevel == "无" then fwlevel = aaData.color_red.."可符文";
 end;
 local qhlevel = aaData:get_star(upgradeid);
 if qhlevel == "无" then qhlevel = aaData.color_red.."可强化";
 end;
 tiaojiantext = aaData.color_yellow.."\n[宠物属性]等级："..qhlevel..aaData.color_green.."\n进化品质："..leveltext.."\n"..aaData.color_green.."力量资质："..lltext.."\n".."敏捷资质："..mjtext.."\n".."智力资质："..zltext.."\n".."精神资质："..jstext.."\n".."耐力资质："..nltext.."\n\n"..aaData.color_yellow.."["..lrtf.."天赋]等级："..aaData:get_star(runeid).."\n"..aaData.color_yellows..runetext.."\n\n"..aaData.color_yellow.."[符文技能]等级："..fwlevel.."\n"..aaData.color_yellows..runetext1;
 local upgradeid = item[8] + 1;
 if petupgrade[upgradeid] then 
 local needid = petupgrade[upgradeid][5];
 local need = aaData:getNeed(needid);
 if need ~= "" then tiaojiantext = tiaojiantext..aaData.color_reds.."\n〓 强化消耗 〓|r\n"..need;
 end;
 end;
 local level = item[2];
 if petnonsuch[level] then if petnonsuch[level+1] then 
 local needid = petnonsuch[level+1][3];
 local need = aaData:getNeed(needid);
 if need ~= "" then tiaojiantext = tiaojiantext..aaData.color_reds.."\n〓 进化消耗 〓|r\n"..need;
 end;
 end;
 end;
 local runeid = item[7] + 1;
 if petrune[runeid] then 
 local needid = petrune[runeid][4];
 local need = aaData:getNeed(needid);
 if need ~= "" then tiaojiantext = tiaojiantext..aaData.color_reds.."\n〓 符文消耗 〓|r\n"..need;
 end;
 end;
 if item[2] > 0 then 
 local level = item[2];
 if petnonsuch[level] then if worldconf[3] then 
 local needid = worldconf[3];
 local need = aaData:getNeed(needid);
 if need ~= "" then tiaojiantext = tiaojiantext..aaData.color_reds.."\n〓 重生消耗 〓|r\n"..need;
 end;
 end;
 end;
 end;
 else if petnonsuch[1] then 
 local needid = petnonsuch[1][3];
 local need = aaData:getNeed(needid);
 if need ~= "" then tiaojiantext = tiaojiantext..aaData.color_reds.."\n〓 孵化消耗 〓|r\n"..need;
 end;
 end;
 GameTooltipTextLeft1:SetText(aaData.color_white..aaDataPet:get_dbname(selectItemId, itementry, item[1]).."（未孵化）");
 end;
 fm:SetText(fm:GetText()..tiaojiantext);
 end;
 end;
 self:Show();
 end;
 end);