PromoLog = {}
PromoLog.width=800;
PromoLog.height=400;
PromoLog.paihang={};

local PromoData = {}
local TotalPromo = 0
local Remains = 0
local totalPromoPage = 1
local nowPromoPage = 1
local Promonum = 0
local Promodaynum = 0
local synchrotimer = 20;

local gradientColors = {
    {1, 0, 0},     -- 红色
    {1, 0.5, 0},   -- 橙色
    {1, 1, 0},     -- 黄色
    {0, 1, 0},     -- 绿色
    {0, 0.7, 1},   -- 蓝色
    {0.5, 0, 1},   -- 紫色
	{1,1,1},	   -- 白色
}

local GetSpellIcon = function(index) 
	local _, _, icon, _, _, _ = GetSpellInfo(index);
	return icon;
end;

local function countTableElements(tbl)
    local count = 0
    for _, _ in pairs(tbl) do
        count = count + 1
    end
    return count
end

function PromoLog:CreateCell(index,x,y,colorstr,iconstr,namestr,dmgstr,rewardstr)

	local menuView=CreateFrame("Frame",nil,PromoLog.mainView) 
	menuView:SetFrameStrata("TOOLTIP");
	menuView:SetWidth(PromoLog.width);
	menuView:SetHeight(24);
	menuView:SetPoint("TOPLEFT",PromoLog.mainView,'TOPLEFT',x,y);
	menuView:Show();
	
	
	menuView.keytext=menuView:CreateFontString("menuView.keytext","OVERLAY","GameFontNormal") 
	menuView.keytext:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.keytext:SetPoint("TOPLEFT",menuView,"TOPLEFT",0,-2);
	menuView.keytext:SetWidth(30);
	menuView.keytext:SetHeight(20);
	menuView.keytext:SetText("");
	menuView.keytext:SetTextColor(unpack(gradientColors[3]))
	menuView.keytext:SetShadowOffset(1, -2)
	menuView.keytext:SetShadowColor(0, 0, 0)
	
	menuView.iconView=CreateFrame('Button',nil,menuView,'')
	menuView.iconView:SetPoint('TOPLEFT',menuView,'TOPLEFT',20+10,-4);
	menuView.iconView:SetButtonState('NORMAL');
	menuView.iconView:IsEnabled();
	menuView.iconView:SetNormalTexture(iconstr);
	menuView.iconView:SetDisabledTexture(iconstr);
	menuView.iconView:SetHighlightTexture(iconstr);
	menuView.iconView:SetPushedTexture(iconstr);
	menuView.iconView:SetWidth(24);
	menuView.iconView:SetHeight(24);
	
	
	menuView.accnametext=menuView:CreateFontString("menuView.accnametext","OVERLAY","GameFontNormal")
	menuView.accnametext:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.accnametext:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2+70,-2);
	menuView.accnametext:SetWidth(0);
	menuView.accnametext:SetHeight(20);
	menuView.accnametext:SetText("");
	menuView.accnametext:SetTextColor(unpack(gradientColors[3]))
	menuView.accnametext:SetShadowOffset(1, -2)
	menuView.accnametext:SetShadowColor(0, 0, 0)
	
	
	menuView.datatext=menuView:CreateFontString("menuView.datatext","OVERLAY","GameFontNormal")
	menuView.datatext:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.datatext:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2+170,-2);
	menuView.datatext:SetWidth(PromoLog.width/3);
	menuView.datatext:SetHeight(20);
	menuView.datatext:SetText("");
	menuView.datatext:SetTextColor(unpack(gradientColors[3]))
	menuView.datatext:SetShadowOffset(1, -2)
	menuView.datatext:SetShadowColor(0, 0, 0)
	
	
	menuView.getpromostr=menuView:CreateFontString("menuView.getpromostr","OVERLAY","GameFontNormal")
	menuView.getpromostr:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.getpromostr:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2+370,-2);
	menuView.getpromostr:SetWidth(PromoLog.width/3);
	menuView.getpromostr:SetHeight(20);
	menuView.getpromostr:SetText("");
	menuView.getpromostr:SetTextColor(unpack(gradientColors[3]))
	menuView.getpromostr:SetShadowOffset(1, -2)
	menuView.getpromostr:SetShadowColor(0, 0, 0)
	

	menuView.BalanceStr=menuView:CreateFontString("menuView.IsSettle","OVERLAY","GameFontNormal")
	menuView.BalanceStr:SetFont("Fonts\\ZYKai_C.ttf",16,"OUTLINE");
	menuView.BalanceStr:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2+490,-2);
	menuView.BalanceStr:SetWidth(PromoLog.width/3);
	menuView.BalanceStr:SetHeight(30);
	menuView.BalanceStr:SetText("");
	menuView.BalanceStr:SetTextColor(unpack(gradientColors[4]))
	menuView.BalanceStr:SetShadowOffset(1, -2)
	menuView.BalanceStr:SetShadowColor(0, 0, 0)
	
	return menuView;
end;


PromoLog.mainView=CreateFrame("Frame",nil,UIParent)
PromoLog.mainView:SetFrameStrata("TOOLTIP");
PromoLog.mainView:SetBackdrop({bgFile="Interface\\Tooltips\\UI-Tooltip-Background",edgeFile="Interface\\Tooltips\\UI-Tooltip-Border",edgeSize="16",tile=true});
PromoLog.mainView:SetBackdropColor(0,0,0,0.8);
PromoLog.mainView:SetWidth(PromoLog.width);
PromoLog.mainView:SetHeight(PromoLog.height);
PromoLog.mainView:SetPoint("CENTER",0,0);
PromoLog.mainView:SetMovable(1);
PromoLog.mainView:EnableMouse();
PromoLog.mainView:Hide();

PromoLog.CloseButton = CreateFrame('Button', nil, PromoLog.mainView, 'UIPanelButtonTemplate') 
PromoLog.CloseButton:SetPoint('BOTTOMRIGHT', PromoLog.mainView, 'TOPRIGHT', 0, -30);
PromoLog.CloseButton:SetSize(80, 30);
PromoLog.CloseButton:SetText("关闭");
PromoLog.CloseButton:SetScript('OnClick', function() PromoLog.mainView:Hide();SendAddonMessage("PROMO_CLOSE","","GUILD", UnitName("player")); end);

PromoLog.title = PromoLog.mainView:CreateFontString("title", "OVERLAY", "GameFontNormal") 
-- PromoLog.title:SetFont("Fonts\\ZYKai_C.ttf", 35);
PromoLog.title:SetFont("Fonts\\ZYKai_C.ttf", 40, "OUTLINE")
PromoLog.title:SetText("推广记录明细");
PromoLog.title:SetPoint("TOPLEFT", PromoLog.mainView, "TOPLEFT", 310, -10);
PromoLog.title:SetHeight(20);
-- PromoLog.title:SetTextColor(unpack(gradientColors[3]))
PromoLog.title:SetShadowOffset(3, -3)
PromoLog.title:SetShadowColor(0, 0, 0)

PromoLog.lineFrame = CreateFrame("Frame",nil,PromoLog.mainView) 
PromoLog.lineFrame:SetFrameStrata("TOOLTIP");
PromoLog.lineFrame:SetBackdrop ({bgFile="Interface\\TutorialFrame\\TutorialFrameBackground"});
PromoLog.lineFrame:SetBackdropColor(1, 1, 1, 1);
PromoLog.lineFrame:SetWidth(PromoLog.width-8);
PromoLog.lineFrame:SetHeight(2);
PromoLog.lineFrame:SetPoint("TOPLEFT",4,-10-20-10-20-15);

PromoLog.totalpromostr = PromoLog.mainView:CreateFontString("totalpromostr", "OVERLAY", "GameFontNormal") 
PromoLog.totalpromostr:SetText("");
PromoLog.totalpromostr:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.totalpromostr:SetPoint("TOPLEFT", PromoLog.mainView, "TOPLEFT", 10, -10-20-10-10);
PromoLog.totalpromostr:SetHeight(20);
PromoLog.totalpromostr:SetShadowOffset(3, -2)
PromoLog.totalpromostr:SetShadowColor(0, 0, 0)

PromoLog.key = PromoLog.mainView:CreateFontString("PromoLog.key", "OVERLAY", "GameFontNormal") 
PromoLog.key:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.key:SetPoint("TOPLEFT", PromoLog.mainView, "TOPLEFT", -80,-10-20-10-20-25);
PromoLog.key:SetWidth(PromoLog.width/3);
PromoLog.key:SetHeight(20);
PromoLog.key:SetSpacing(5);
PromoLog.key:SetText("|cFFFF0033序号");
PromoLog.key:SetShadowOffset(2, -2)
PromoLog.key:SetShadowColor(0, 0, 0)

PromoLog.fromAccName = PromoLog.mainView:CreateFontString("PromoLog.fromAccName", "OVERLAY", "GameFontNormal") 
PromoLog.fromAccName:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.fromAccName:SetPoint("TOPLEFT", PromoLog.mainView, "TOPLEFT", 50,-10-20-10-20-25);
PromoLog.fromAccName:SetWidth(PromoLog.width/3);
PromoLog.fromAccName:SetHeight(20);
PromoLog.fromAccName:SetSpacing(5);
PromoLog.fromAccName:SetText("|cFFFF0033贡献账号");
PromoLog.fromAccName:SetShadowOffset(2, -2)
PromoLog.fromAccName:SetShadowColor(0, 0, 0)

PromoLog.datatimer = PromoLog.mainView:CreateFontString("PromoLog.datatimer", "OVERLAY", "GameFontNormal") 
PromoLog.datatimer:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.datatimer:SetPoint("TOPLEFT", PromoLog.mainView, "TOPLEFT", 250,-10-20-10-20-25);
PromoLog.datatimer:SetWidth(PromoLog.width/3);
PromoLog.datatimer:SetHeight(20);
PromoLog.datatimer:SetSpacing(5);
PromoLog.datatimer:SetText("|cFFFF0033日期时间");
PromoLog.datatimer:SetShadowOffset(2, -2)
PromoLog.datatimer:SetShadowColor(0, 0, 0)

PromoLog.getPromo = PromoLog.mainView:CreateFontString("PromoLog.getPromo", "OVERLAY", "GameFontNormal") 
PromoLog.getPromo:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.getPromo:SetPoint("TOPRIGHT", PromoLog.mainView, "TOPRIGHT", -80,-10-20-10-20-25);
PromoLog.getPromo:SetWidth(PromoLog.width/3);
PromoLog.getPromo:SetHeight(20);
PromoLog.getPromo:SetSpacing(5);
PromoLog.getPromo:SetText("|cFFFF0033获得推广值");
PromoLog.getPromo:SetShadowOffset(2, -2)
PromoLog.getPromo:SetShadowColor(0, 0, 0)

PromoLog.IsSettle = PromoLog.mainView:CreateFontString("PromoLog.IsSettle", "OVERLAY", "GameFontNormal") 
PromoLog.IsSettle:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.IsSettle:SetPoint("TOPRIGHT", PromoLog.mainView, "TOPRIGHT", 50,-10-20-10-20-25);
PromoLog.IsSettle:SetWidth(PromoLog.width/3);
PromoLog.IsSettle:SetHeight(20);
PromoLog.IsSettle:SetSpacing(5);
PromoLog.IsSettle:SetText("|cFFFF0033可兑换推广值");
PromoLog.IsSettle:SetShadowOffset(2, -2)
PromoLog.IsSettle:SetShadowColor(0, 0, 0)

PromoLog.paihang[1] = PromoLog:CreateCell(1,37,-10-20-10-20-10-20-15,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[2] = PromoLog:CreateCell(2,37,-10-20-10-20-10-20-15-24*1,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[3] = PromoLog:CreateCell(3,37,-10-20-10-20-10-20-15-24*2,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[4] = PromoLog:CreateCell(4,37,-10-20-10-20-10-20-15-24*3,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[5] = PromoLog:CreateCell(5,37,-10-20-10-20-10-20-15-24*4,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[6] = PromoLog:CreateCell(6,37,-10-20-10-20-10-20-15-24*5,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[7] = PromoLog:CreateCell(7,37,-10-20-10-20-10-20-15-24*6,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[8] = PromoLog:CreateCell(8,37,-10-20-10-20-10-20-15-24*7,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[9] = PromoLog:CreateCell(9,37,-10-20-10-20-10-20-15-24*8,nil,GetSpellIcon(0),"","","");
PromoLog.paihang[10] = PromoLog:CreateCell(10,37,-10-20-10-20-10-20-15-24*9,nil,GetSpellIcon(0),"","","");

PromoLog.UpPage = CreateFrame('Button', nil, PromoLog.mainView, 'UIPanelButtonTemplate') 
PromoLog.UpPage:SetPoint('CENTER', PromoLog.mainView, 'BOTTOM', -150, 30);
PromoLog.UpPage:SetSize(80, 30);
PromoLog.UpPage:SetText("上一页");
PromoLog.UpPage:SetScript('OnClick', function() 
	if nowPromoPage > 1 then nowPromoPage = nowPromoPage - 1 end 
end);

PromoLog.NextPage = CreateFrame('Button', nil, PromoLog.mainView, 'UIPanelButtonTemplate') 
PromoLog.NextPage:SetPoint('CENTER', PromoLog.mainView, 'BOTTOM', 150, 30);
PromoLog.NextPage:SetSize(80, 30);
PromoLog.NextPage:SetText("下一页");
PromoLog.NextPage:SetScript('OnClick', function() 
	if nowPromoPage < totalPromoPage then nowPromoPage = nowPromoPage + 1 end 
end);

PromoLog.PageStr = PromoLog.mainView:CreateFontString("PromoLog.PageStr", "OVERLAY", "GameFontNormal") 
PromoLog.PageStr:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.PageStr:SetPoint("CENTER", PromoLog.mainView, "BOTTOM", 0,33);
PromoLog.PageStr:SetWidth(PromoLog.width/3);
PromoLog.PageStr:SetHeight(20);
PromoLog.PageStr:SetSpacing(5);
PromoLog.PageStr:SetText("第99页 / 第99页");

PromoLog.PromoNum = PromoLog.mainView:CreateFontString("PromoLog.PromoNum", "OVERLAY", "GameFontNormal") 
PromoLog.PromoNum:SetFont("Fonts\\ZYKai_C.ttf", 18,"OUTLINE");
PromoLog.PromoNum:SetPoint("TOPRIGHT", PromoLog.mainView, "TOPRIGHT", -20,-50);
-- PromoLog.PromoNum:SetWidth(PromoLog.width/3);
PromoLog.PromoNum:SetHeight(20);
PromoLog.PromoNum:SetSpacing(5);
PromoLog.PromoNum:SetText(""); -- 推广总人数：9999 今日推广人数：99
PromoLog.PromoNum:SetShadowOffset(2, -2)
PromoLog.PromoNum:SetShadowColor(0, 0, 0)

-- PromoLog.mainView:Show();

function PromoDataOnDataRecv(self, event, opcode, msg, type, sender)
	
	if event == "CHAT_MSG_ADDON" then
		if opcode == "PROMO_DATA_TOTALPROMO" then
			-- print("TotalPromo = "..msg)
			TotalPromo = tonumber(msg)	-- 推广点数总计
		end

		if opcode == "PROMO_DATA_REMAINSPROMO" then
			-- print("Remains = "..msg)
			Remains = tonumber(msg) 	-- 推广剩余点数
		end

		if opcode == "PROMO_DATA_PROMONUM" then
			-- print("Promonum = "..msg)
			Promonum = tonumber(msg) 	-- 推广总人数
		end

		if opcode == "PROMO_DATA_PROMODAYNUM" then
			-- print("Promodaynum = "..msg)
			Promodaynum = tonumber(msg) -- 当日推广人数
		end
		
		if opcode == "PROMO_DATA" then
			-- 序号,来源账号,记录日期时间,获得推广点数,是否结清
			local num,fromaccname,datas,getpromoval,balance = strsplit("#", msg)
			-- print("msg = "..msg)
			PromoData[tonumber(num)] = nil
			PromoData[tonumber(num)] = {}
			PromoData[tonumber(num)].fromaccname = fromaccname
			PromoData[tonumber(num)].datas = datas
			PromoData[tonumber(num)].getpromoval = getpromoval
			PromoData[tonumber(num)].balance = balance
		end
		
		if opcode == "PROMO_DATA_LOG_OPEN" then
			-- print("PROMO_DATA_LOG_OPEN")
			local totals = countTableElements(PromoData)
			totalPromoPage = math.ceil(totals/10)
			if totalPromoPage == 0 then totalPromoPage = 1 end
			nowPromoPage = 1
			PromoLog:reload()
			PromoLog.mainView:Show();
		end

		if opcode == "PROMO_DATA_LOG_CLOSE" then
			local totals = countTableElements(PromoData)
			totalPromoPage = math.ceil(totals/10)
			nowPromoPage = 1
			PromoLog.mainView:Hide();
		end
	end
end


function PromoLog:reload() 
		PromoLog.totalpromostr:SetText("推广值总计："..TotalPromo.."  可兑换推广值："..Remains);
		PromoLog.PromoNum:SetText("推广总人数："..Promonum.." 今日推广人数："..Promodaynum);
		local count = 0;
		for k, v in pairs(PromoData) do
			if ((nowPromoPage-1) * 10) < k and k <= (nowPromoPage * 10)  then
				local _num = k - (nowPromoPage-1) * 10
				PromoLog.paihang[_num].keytext:SetText(tostring(k)); 		--序号
				PromoLog.paihang[_num].accnametext:SetText(v.fromaccname); 	--贡献账号
				PromoLog.paihang[_num].datatext:SetText(v.datas); 			--日期时间
				PromoLog.paihang[_num].getpromostr:SetText(v.getpromoval);	--获得推广点数
				local _val = tonumber(v.balance)
				if _val == 0 then
					PromoLog.paihang[_num].BalanceStr:SetTextColor(unpack(gradientColors[1]))
				else
					PromoLog.paihang[_num].BalanceStr:SetTextColor(unpack(gradientColors[4]))
				end
				PromoLog.paihang[_num].BalanceStr:SetText(v.balance);		--可兑换推广值
				count = count + 1
			end
		end

		if(count < 10) then
			local max = 10
			for i = count +1,max do
				PromoLog.paihang[i].keytext:SetText(""); 		--序号
				PromoLog.paihang[i].accnametext:SetText(""); 	--贡献账号
				PromoLog.paihang[i].datatext:SetText(""); 		--日期时间
				PromoLog.paihang[i].getpromostr:SetText("");	--获得推广点数
				PromoLog.paihang[i].BalanceStr:SetText("");		--可兑换推广值
			end
		end

end

local PRL_RECV_FRAME = CreateFrame("Frame")
PRL_RECV_FRAME:RegisterEvent("CHAT_MSG_ADDON")
PRL_RECV_FRAME:SetScript("OnEvent", PromoDataOnDataRecv)

local PromoLogOnUpdate = CreateFrame("Frame")
PromoLogOnUpdate:SetScript("OnUpdate", function() 
	if PromoLog.mainView:IsShown() then 
		PromoLog.PageStr:SetText("第"..nowPromoPage.."页 / 第"..totalPromoPage.."页");
		if synchrotimer <= 0 then
			PromoLog:reload() 
			synchrotimer = 20
		else
			synchrotimer = synchrotimer - 1
		end
	end 
end)
