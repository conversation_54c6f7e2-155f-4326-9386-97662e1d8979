local GLPEX = {}
local GLMBEX = {}
local HGLP
local NUM = 8
local ServerNum
local TabTex = 
{
"Interface\\ICONS\\I",
"Interface\\ICONS\\II",
"Interface\\ICONS\\III",
"Interface\\ICONS\\IV",
"Interface\\ICONS\\V",
"Interface\\ICONS\\VI",
"Interface\\ICONS\\VII",
"Interface\\ICONS\\VIII",
"Interface\\ICONS\\I-D",
"Interface\\ICONS\\II-D",
"Interface\\ICONS\\III-D",
"Interface\\ICONS\\IV-D",
"Interface\\ICONS\\V-D",
"Interface\\ICONS\\VI-D",
"Interface\\ICONS\\VII-D",
"Interface\\ICONS\\VIII-D",
}


function GLPExSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 


function GLPExGetLMNum(i)
	if i == 1 then
		return "I"
	elseif i == 2 then
		return "II"
	elseif i == 3 then
		return "III"
	elseif i == 4 then
		return "IV"
	elseif i == 5 then
		return "V"
	elseif i == 6 then
		return "VI"
	elseif i == 7 then
		return "VII"
	elseif i == 8 then
		return "VIII"
	end
end


function CreateHGLPEx(Name)
local F = CreateFrame("Frame",Name,UIParent)
F:SetSize(384, 512);
F:SetPoint("CENTER",0,0)
F:Hide()
return F
end


function CreateGLPExFrame(Name)
local GL = CreateFrame("Frame",Name,HGLP)
GL:SetSize(384, 512);
local t = GL:CreateTexture("$parent".."Background","ARTWORK")
t:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
t:SetSize(352, 441)
t:SetPoint("TOPLEFT",0,0)
t:SetTexCoord(0, 0.6875, 0, 0.861328125)
local fs = GL:CreateFontString("$parent".."TitleText","ARTWORK","GameFontNormal")
fs:SetText("魂玉扩展")
fs:SetPoint("TOP",0,-18)
GL.FontString = fs
GL.texture = t
GL:SetPoint("CENTER",0,0)
GL:Hide()
return GL
end


function GenerateGLPExFrame()
	for i = 1,NUM do
		GLPEX[i] = CreateGLPExFrame("GLPEX"..i);
		if i == 1 then
			GLPEX[i]:Show()
		end
	end
end 


function GLPExFrameHide()
	for i = 1,NUM do
		GLPEX[i]:Hide()
	end
end


function HGLPPExHide(count)
	for i = 1,NUM do
		if i > count then
			HGLP.P[i]:Hide()
			HGLP.LineTab[i]:Hide()
		end
	end
end


function HGLPPExUnHigh()
	for i = 1,NUM do
		HGLP.P[i]:UnlockHighlight()
	end
end


function CreateGLPExButton(Parent,Point,Name,x,y)
local B1 = CreateFrame("Button","$parent"..Name,Parent)
B1:SetSize(90, 90);
B1:SetPoint(Point,x,y)
local bt1 = B1:CreateTexture("$parent".."B1","ARTWORK")
bt1:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt1:SetSize(82, 82)
bt1:SetPoint("CENTER",0,0)
bt1:SetTexCoord(0.767578125, 0.92578125, 0.32421875, 0.482421875)
local bt2 = B1:CreateTexture("$parent".."B2","BACKGROUND")
bt2:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt2:SetSize(108, 108)
bt2:SetPoint("CENTER",0,0)
bt2:SetTexCoord(0.740234375, 0.953125, 0.484375, 0.697265625)
local bt3 = B1:CreateTexture("$parent".."B3","BORDER")
bt3:SetTexture("Interface\\Spellbook\\UI-GlyphFrame")
bt3:SetSize(64, 64)
bt3:SetPoint("CENTER",0,0)
bt3:SetTexCoord(0.78125,0.91015625,0.69921875,0.828125)
B1:Show()
return B1
end


function CreateGLPExITEMButton(Parent,Name,Point,x,y,Tex)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(90,90)
Button:SetPoint(Point,x,y)
Button:SetFrameStrata("TOOLTIP")
local bt1 = Button:CreateTexture("$parent".."Portrait","ARTWORK")
bt1:SetSize(70, 70)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
Button:Show()
return Button
end


function GLPExGenerateGLPButton() 
	for i=1,NUM do
		GLPEX[i].BI = CreateGLPExButton(GLPEX[i],"CENTER","BI",-15,140)
		GLPEX[i].BII = CreateGLPExButton(GLPEX[i],"BOTTOMRIGHT","BII",-56,168)
		GLPEX[i].BIII = CreateGLPExButton(GLPEX[i],"BOTTOMLEFT","BIII",26,168)
		GLPEX[i].BIV = CreateGLPExButton(GLPEX[i],"CENTER","BIV",-14,-103)
		GLPEX[i].BV = CreateGLPExButton(GLPEX[i],"TOPRIGHT","BV",-56,-133)
		GLPEX[i].BVI = CreateGLPExButton(GLPEX[i],"TOPLEFT","BVI",28,-133)
		GLPEX[i].BJ = CreateGLPExButton(GLPEX[i],"CENTER","BJ",-15,15)
		GLPEX[i].BI.ID = (tostring(i).."#1")
		GLPEX[i].BII.ID = (tostring(i).."#2")
		GLPEX[i].BIII.ID = (tostring(i).."#3")
		GLPEX[i].BIV.ID = (tostring(i).."#4")
		GLPEX[i].BV.ID = (tostring(i).."#5")
		GLPEX[i].BVI.ID = (tostring(i).."#6")
		GLPEX[i].BJ.ID = (tostring(i).."#7")
		GLPEX[i].BJ.ACTI = 0;
		GLPEX[i].BJ:Hide()
	end
end 


function GLPExCreateXXButton(Parent,Name)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",-28,-9)
Button:SetFrameStrata("HIGH")
Button:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
Button:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
Button:SetDisabledTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
Button:Show()
return Button
end


function GLPExCreateActEff(Parent,Name)	--激活
local jihuo = Parent:CreateTexture("$parent"..Name,"OVERLAY")
jihuo:SetTexture("Interface\\Spellbook\\UI-GlyphFrame-Glow")
jihuo:SetSize(352, 441)
jihuo:SetPoint("TOPLEFT",-9,-38)
jihuo:SetTexCoord(0, 0.6875, 0, 0.861328125)
jihuo:SetVertexColor(1.0,1.0,1.0,0.8)	
jihuo:Hide()
return jihuo
end


function GenerateGLPExActEff() 
	for i=1,NUM do
		GLPEX[i].ActEff = GLPExCreateActEff(GLPEX[i],"ActEff")
	end
end


function GLPExCreateLineTab(Parent,Name,x,y)
local LineTab = Parent:CreateTexture("$parent"..Name,"ARTWORK")
LineTab:SetTexture("Interface\\Spellbook\\SpellBook-SkillLineTab")
LineTab:SetSize(64, 64)
LineTab:SetPoint("TOPRIGHT",x,y)
return LineTab
end


function GLPExGenerateLineTab() 
HGLP.LineTab = {}
	for i=1,NUM do
		HGLP.LineTab[i] = GLPExCreateLineTab(HGLP,"LineTab"..i,30,-30 -(i-1) * 50)
	end
end


function GLPExCreateTabButton(Parent,Name,x,y,Tex,DTex)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",x,y)
Button:SetNormalTexture(Tex)
Button:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
Button:SetDisabledTexture(DTex)
Button:Show()
return Button
end


function GLPExCreateBuyButton(Parent,Name,x,y)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",x,y)
Button:Hide()
return Button
end


function GLPExGenerateTabButton() 
HGLP.P = {}
HGLP.BUY = {}
	for i=1,NUM do
		HGLP.P[i] = GLPExCreateTabButton(HGLP,"P"..i,0,-42 -(i-1) * 50,TabTex[i],TabTex[i+NUM])
		HGLP.BUY[i] = GLPExCreateBuyButton(HGLP,"BUY"..i,0,-42 -(i-1) * 50)
		HGLP.P[i].ID = tostring(i)
		HGLP.BUY[i].ID = tostring(i)
	end
end


function GLPExCreatePP(Parent,Name)
local img1 = Parent:CreateTexture("$parent"..Name, "OVERLAY")
img1:SetSize(64,64)
img1:SetPoint("TOPLEFT", 6, -6)
SetPortraitTexture(img1, "player")
return img1
end


function GLPExPrepareScript(object, text, script, itemlink)
	  if text then	
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end) 
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)	
	  end
	  	  
	  if type(script) == "function" then	
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end


function GLPExGenerateButtonScript() 
	for i=1,NUM do
		local j = GLPExGetLMNum(i)
		GLPExPrepareScript(HGLP.P[i],j,function(self,button) if button == "LeftButton" then GLPExFrameHide() HGLPPExUnHigh() local i = tonumber(self.ID) GLPEX[i]:Show() self:LockHighlight() end end)
	end
end


function GLPExGenerateBUYScript() 
	for i=1,NUM do
		local j = GLPExGetLMNum(i)
		GLPExPrepareScript(HGLP.BUY[i],"购买"..j.."页面",function(self,button) SendAddonMessage("SSCEX_BUY_PAGE",tostring(self.ID),"GUILD", UnitName("player")) end)
	end
end


function GLPExHandleButtonShowHide(count)
	for i=1,NUM do
		if i > count then
			HGLP.P[i]:Disable()
			HGLP.BUY[i]:Show()
			HGLP.BUY[i]:SetFrameStrata("HIGH")
		end
	end
end


function GLPExHandleButtonScript(Button,Type)
	
	if Type == "GLMENU" then
		if HGLP.PP == nil then
			HGLP.PP = GLPExCreatePP(HGLP,"HGLPPP")
		end
		if Button.ON:IsShown() then
			Button.ON:Hide()
			Button.OFF:Show()
			GLPExBScript()
			HGLP:Show()
			HGLP.P[1]:Click()
		else
			Button.ON:Show()
			Button.OFF:Hide()
			HGLP:Hide()
		end
	end
	
	if string.match(Type,"CLOSE")  then
		Button.ON:Show()
		Button.OFF:Hide()
		HGLP:Hide()
	end
	
	if string.match(Type,"PAGE") then
		
	end
end


function GLPExCreateMenuButton(Name,x,y,Tex)
local Button = CreateFrame("Button","$parent"..Name,UIParent)
Button:SetSize(64,64)
Button:SetPoint("TOPLEFT",x,y)
Button:SetNormalTexture(Tex)
Button:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
Button:Show()
return Button
end


function GLPExHandleMenuScript()
	HGLP.CLOSE = GLPExCreateXXButton(HGLP,"CLOSE")
	GLMBEX.ON = GLPExCreateMenuButton("GLMBEXON",300,-115,"Interface\\ICONS\\jf1")
	GLMBEX.OFF = GLPExCreateMenuButton("GLMBEXOFF",300,-115,"Interface\\ICONS\\jinbi")
	GLMBEX.OFF:Hide()
	GLPExPrepareScript(GLMBEX.ON,"打开菜单",function() GLPExHandleButtonScript(GLMBEX,"GLMENU") end)
	GLPExPrepareScript(GLMBEX.OFF,"关闭菜单",function() GLPExHandleButtonScript(GLMBEX,"GLMENU") end)
	GLPExPrepareScript(HGLP.CLOSE,"关闭",function() GLPExHandleButtonScript(GLMBEX,"CLOSE") end)
end


function GLPExCreateItemTex(Parent,Name,Tex)
local bt1 = Parent:CreateTexture("$parent"..Name,"ARTWORK")
bt1:SetSize(30, 30)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
local bt2 = Parent:CreateTexture("$parent".."quanquan","ARTWORK")
bt2:SetSize(35, 35)
bt2:SetPoint("CENTER",0,0)
bt2:SetTexture("Interface\\Icons\\Circle")
return bt1
end


function GLPExGetSlotButton(page,num)
	
	if num == 1 then
	return GLPEX[page].BI;
	elseif num == 2 then
	return GLPEX[page].BII;
	elseif num == 3 then
	return GLPEX[page].BIII;
	elseif num == 4 then
	return GLPEX[page].BIV;
	elseif num == 5 then
	return GLPEX[page].BV;
	elseif num == 6 then
	return GLPEX[page].BVI;
	elseif num == 7 then
	return GLPEX[page].BJ;
	end

end


function GLPExGetItemInSlotText(link)
	if link ~= nil then
	return ""
	else
	return "未镶嵌"
	end
end

--VARIABLES_LOADED

function GLPExBScriptHandle(self,button)
	if button == "LeftButton" then 
		local ctype,itemid,itemlink = GetCursorInfo() 
		if ctype == "item" then 
			local msg = self.ID.."#"..itemid;
			
			SendAddonMessage("SSCEX_ITEM_TO_SLOT",msg,"GUILD", UnitName("player"))
		end 
		
		if ctype == nil then 
			local msg = self.ID;
			SendAddonMessage("SSCEX_REMOVE_SLOT_ITEM",msg,"GUILD",UnitName("player"))
		end 
	end
end


function GLPExBScript()
	for i=1,NUM do
		GLPExPrepareScript(GLPEX[i].BI,GLPExGetItemInSlotText(GLPEX[i].BI.itemlink),GLPExBScriptHandle,GLPEX[i].BI.itemlink)
		GLPExPrepareScript(GLPEX[i].BII,GLPExGetItemInSlotText(GLPEX[i].BII.itemlink),GLPExBScriptHandle,GLPEX[i].BII.itemlink)
		GLPExPrepareScript(GLPEX[i].BIII,GLPExGetItemInSlotText(GLPEX[i].BIII.itemlink),GLPExBScriptHandle,GLPEX[i].BIII.itemlink)
		GLPExPrepareScript(GLPEX[i].BIV,GLPExGetItemInSlotText(GLPEX[i].BIV.itemlink),GLPExBScriptHandle,GLPEX[i].BIV.itemlink)
		GLPExPrepareScript(GLPEX[i].BV,GLPExGetItemInSlotText(GLPEX[i].BV.itemlink),GLPExBScriptHandle,GLPEX[i].BV.itemlink)
		GLPExPrepareScript(GLPEX[i].BVI,GLPExGetItemInSlotText(GLPEX[i].BVI.itemlink),GLPExBScriptHandle,GLPEX[i].BVI.itemlink)
		GLPExPrepareScript(GLPEX[i].BJ,GLPExGetItemInSlotText(GLPEX[i].BJ.itemlink),GLPExBScriptHandle,GLPEX[i].BJ.itemlink)
	end
end


function GLPExBatchHandle()
	
	HGLP = CreateHGLPEx("HGLP")
		
	GLPExHandleMenuScript()
	
	GenerateGLPExFrame()
	
	GLPExGenerateGLPButton()
	
	GenerateGLPExActEff()
	
	GLPExGenerateLineTab()
	
	GLPExGenerateTabButton()
	
	GLPExGenerateButtonScript()
	
	GLPExBScript()
	
	GLPExGenerateBUYScript()
	
end


GLPExBatchHandle()

function GLPEXGETGLMB()
	if GLMBEX.ON:IsShown() then
		GLMBEX.ON:Hide()
	else
		if GLMBEX.OFF:IsShown() then
			GLMBEX.OFF:Click()
			GLMBEX.ON:Hide()
		else
			GLMBEX.ON:Show()
		end
	end
end

function GLPExEvent(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
		
		--镶嵌 覆盖
		if h == "SSSEX_XQ_FG" then
			local list = GLPExSplit(msg,"#")
			local p = tonumber(list[1]);
			local n = tonumber(list[2]);
			local id = tonumber(list[3]);
			if id == 0 then return; end 
			local link = list[4];
			local B = GLPExGetSlotButton(p,n);
			if B.itemlink == nil then
			B.itemlink = link
			else
			B.itemlink = nil
			B.itemlink = link
			end
			local icon = "|T"..GetItemIcon(id)..":30|t"; 
			if B.tex == nil then
				B.tex = GLPExCreateItemTex(B,"itemtex",GetItemIcon(id))
				B.itemid = id
			else
				B.tex:Hide()
				_G[B:GetName().."itemtex"] = nil
				B.tex = nil
				B.tex = GLPExCreateItemTex(B,"itemtex",GetItemIcon(id))
				B.itemid = id
			end
			GLPExBScript()
			-- B:Hide()
		end
		
		--拆卸
		if h == "SSSEX_CX" then
			local list = GLPExSplit(msg,"#")
			local p = tonumber(list[1]);
			local n = tonumber(list[2]);
			local B = GLPExGetSlotButton(p,n);
			local pname = B:GetName();
			_G[pname.."quanquan"]:Hide()
			B.tex:Hide()
			B.tex = nil
			_G[B:GetName().."itemtex"] = nil
			-- B.tex = GLPExCreateItemTex(B,"itemtex",GetItemIcon(id))
			B.itemlink = nil;
			GLPExBScript()
		end
		
		--模式2
		if h == "SSSEX_LIMIT_VAL" then
			local val = tonumber(msg)
			if val == 0 then
				GLMBEX.ON:Hide();
				return;
			end
			GLPExHandleButtonShowHide(val);
			return;
		end
		
		--模式1
		if h == "SSSEX_LIMIT_NUM"then
			local val = tonumber(msg)
			if val == 0 then
				GLMBEX.ON:Hide();
				return;
			end
			HGLPPExHide(val);
			return;
		end
	
		--激活
		if h == "SSSEX_ACTI_VAL" then 
			local list = GLPExSplit(msg,"#")
			local p = tonumber(list[1]);
			local id = tonumber(list[2]);
			if id == 0 then return; end 
			local link = list[3];
			local B = GLPExGetSlotButton(p,7);
			GLPEX[p].ActEff:Show()
			B.tex = GLPExCreateItemTex(B,"itemtex",GetItemIcon(id))
			B.itemid = id
			B.itemlink = link
			B:Show()
			GLPExBScript()
		end
		
		--失活
		if h == "SSSEX_ACTI_UN" then
			local p = tonumber(msg)
			local B = GLPExGetSlotButton(p,7);
			GLPEX[p].ActEff:Hide()
			B.tex:Hide()
			B.tex = nil
			_G[B:GetName().."itemtex"] = nil
			B.itemlink = nil;
			B:Hide()
			GLPExBScript()
		end
		
		if h == "SSSEX_BUY_PAGE" then
			local p = tonumber(msg)
			HGLP.P[p]:Enable()
			HGLP.BUY[p]:Disable()
			HGLP.BUY[p]:Hide()
		end
	end
end


function GLPExSendMutualOp(self, event,...)
	SendAddonMessage("SSCEX_LIMIT","VAL","GUILD", UnitName("player"))
	SendAddonMessage("SSCEX_ACTI","ISOK","GUILD", UnitName("player"))
end


local MsgReceiversGLPEx = CreateFrame("Frame")
MsgReceiversGLPEx:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiversGLPEx:SetScript("OnEvent", GLPExEvent)

local PLOADEx = CreateFrame("Frame")
PLOADEx:RegisterEvent("PLAYER_LOGIN")
PLOADEx:SetScript("OnEvent", GLPExSendMutualOp)
