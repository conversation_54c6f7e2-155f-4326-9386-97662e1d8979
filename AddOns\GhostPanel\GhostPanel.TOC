﻿## Interface: 30300
## Title: GhostCore
## Notes: GhostCore.31核心综合插件
## Author: GhostCore
3.0\Template.xml
3.0\FrameXML.xml
3.0\Defines.lua
3.0\GC3.0.lua
GhostDefines.lua
GhostMain.lua
GhostRune.lua
GhostItem.lua
GhostVIP.lua
GhostHR.lua
GhostGS.lua
GhostReqRewPop.lua
GhostRewPanel.lua
GhostReqPanel.lua
GhostTooltip.lua
GhostData.lua
GhostBlackMarket.lua
GhostTalent.lua
GhostTransMog.lua
GhostLuckDraw.lua
Data\Define.lua
Data\Variable.lua
Data\String.lua
Data\ClassSpell.lua
Data\Chrono.lua
Data\Opcode.lua
Data\Transmog.lua
Data\UI\Popup.lua
Actor\UI.lua
Actor\Utility\Misc.lua
Actor\Utility\Math.lua
Actor\Utility\Spell.lua
Actor\Utility\Item.lua
Actor\Utility\Guild.lua
Actor\Utility\Market.lua
Actor\Utility\Transmog.lua
Actor\Utility\Legacy.lua
Actor\Query.lua
Actor\QueryHandler.lua
Actor\GameTooltip\Misc.lua
Actor\GameTooltip\Item.lua
Actor\GameTooltip\Spell.lua
Template.xml
Token\ClassSpell.lua
Token\Rune.lua
Token\SpellMod.lua
Token\Transmog.lua
Token\Market.lua
Token\Reputation.lua
Token\Reward.lua
Token\Specialty.lua
Token\Guild.lua
Token\Home.lua
Token\Legacy.lua
GhostPanel.lua
GhostPanel.xml
Binding.xml
