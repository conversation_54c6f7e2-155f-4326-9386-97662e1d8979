<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xsi:schemaLocation="http://www.blizzard.com/wow/ui/ G:\Legacy\Client\Interface\FrameXML\UI.xsd">
    <!--StaticFrame-->
    <Frame name="GhostStaticFrameTemplate3.0" parent="UIParent" virtual="true">
    	<Size x="300" y="500"/>
        <Backdrop bgFile="Interface\TutorialFrame\TutorialFrameBackground"
           edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
    	<EdgeSize val="16"/>
    	<TileSize val="32"/>
     	<BackgroundInsets left="5" right="5" top="5" bottom="5"/>
        </Backdrop>
    	<Frames>
    		<!-- logo-->
    		<Button name="$parentLogoButton">
    			<Size x="192" y="96"/>
    			<Anchors>
    				<Anchor point="TOP">
    					<Offset x="0" y="30" />
    				</Anchor>
    			</Anchors>
    			<Layers>
    				<Layer level="OVERLAY">	
    					<FontString name = "$parentFontString" setAllPoints="true" justifyV="MIDDLE" justifyH="CENTER" inherits="ErrorFont" text="">
    						<Anchors>
    							<Anchor point="TOP">
    								<Offset x="0" y="-140" />
    							</Anchor>
    						</Anchors>
    					</FontString>
    				</Layer>
    			</Layers>
    			<Scripts>
    				<OnLoad>
    					self:SetNormalTexture("Interface\\TUTORIALFRAME\\UI-TutorialFrame-Logo");
    				</OnLoad>
    			</Scripts>
    		</Button>
    	</Frames>
    </Frame>
    <!--CircleButton-->
    <Button name="GhostSmallCircleButtonTemplate" virtual="true">
    	<Size x="20" y="20"/>
    	<Anchors>
    		<Anchor point="CENTER">
    			<Offset x="0" y="0" />
    		</Anchor>
    	</Anchors>
    	<Layers>
    		<Layer level="OVERLAY">
    			<Texture name="$parentRollTexture">
    				<Size x="20" y="20"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    		</Layer>
    		<Layer level="OVERLAY">
    			<Texture file="Interface\Minimap\UI-Minimap-Ping-Expand">
    				<Size x="22.5" y="22.5"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    		</Layer>
    		<Layer level="ARTWORK">
    			<Texture name="$parentTexture">
    				<Size x="20" y="20"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    			<FontString name="$parentCount" inherits="TextStatusBarText">
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</FontString>
    		</Layer>
    	</Layers>
    </Button>
    <Button name="GhostCircleButtonTemplate" virtual="true">
    	<Size x="45" y="45"/>
    	<Anchors>
    		<Anchor point="CENTER">
    			<Offset x="0" y="0" />
    		</Anchor>
    	</Anchors>
    	<Layers>
    		<Layer level="OVERLAY">
    			<Texture name="$parentRollTexture">
    				<Size x="40" y="40"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    		</Layer>
    		<Layer level="OVERLAY">
    			<Texture name = "$parentBorderTexture">
    				<Size x="45" y="45"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    		</Layer>
    		<Layer level="ARTWORK">
    			<Texture name="$parentTexture">
    				<Size x="40" y="40"/>
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</Texture>
    			<FontString name="$parentCount" inherits="TextStatusBarText">
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="0"/>
    					</Anchor>
    				</Anchors>
    			</FontString>
    		</Layer>
    	</Layers>
    </Button>
    <!--Talisman-->
    <Button name="GhostTalismanButtonTemplate" virtual="true">
	    <Size x="90" y="90"/>
	    <Layers>
	    	<Layer level="BACKGROUND">
	    		<Texture name="$parentSetting" file="Interface\Spellbook\UI-GlyphFrame">
	    			<Size x="86" y="86"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="0" y="0"/>
	    				</Anchor>
	    			</Anchors>
	    			<TexCoords left="0.765625" right="0.927734375" top="0.15625" bottom="0.31640625"/>
	    		</Texture>
	    	</Layer>
	    	<Layer level="BORDER">
	    		<Texture name="$parentBackground" file="Interface\Spellbook\UI-GlyphFrame">
	    			<Size x="64" y="64"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="0" y="0"/>
	    				</Anchor>
	    			</Anchors>
	    			<TexCoords left="0.78125" right="0.91015625" top="0.69921875" bottom="0.828125"/>
	    		</Texture>
	    	</Layer>
	    	<Layer level="ARTWORK">
	    		<Texture name="$parentTexture">
	    			<Size x="53" y="53"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="0" y="0"/>
	    				</Anchor>
	    			</Anchors>
	    		</Texture>
	    	</Layer>
	    	<Layer level="HIGHLIGHT">
	    		<Texture name="$parentHighLightTexture">
	    			<Size x="65" y="65"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="0" y="0"/>
	    				</Anchor>
	    			</Anchors>
	    		</Texture>
	    	</Layer>
	    	<Layer level="OVERLAY">
	    		<Texture name="$parentRing" file="Interface\Spellbook\UI-GlyphFrame">
	    			<Size x="62" y="62"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="0" y="1"/>
	    				</Anchor>
	    			</Anchors>
	    			<TexCoords left="0.787109375" right="0.908203125" top="0.033203125" bottom="0.154296875"/>
	    		</Texture>
	    		<Texture name="$parentShine" file="Interface\Spellbook\UI-GlyphFrame">
	    			<Size x="16" y="16"/>
	    			<Anchors>
	    				<Anchor point="CENTER">
	    					<Offset x="-9" y="12"/>
	    				</Anchor>
	    			</Anchors>
	    			<TexCoords left="0.9609375" right="1" top="0.921875" bottom="0.9609375"/>
	    		</Texture>
	    	</Layer>
	    </Layers>
	    <Scripts>
	    	<OnClick>Ghost_TalismanButton_Click(self);</OnClick>
	    </Scripts>
    </Button>
    <!--StatPoints-->
    <Button name="GhostStatPointsInsButtonTemplate" inherits="GhostSmallCircleButtonTemplate" virtual="true">
    	<Size x="25" y="25"/>
    	<Scripts>
    		<OnLoad>self:RegisterForClicks("LeftButtonUp","RightButtonUp");</OnLoad>
    		<OnClick>StatPointsIns(self);</OnClick>
    		<OnEnter>SetCursor("ATTACK_CURSOR");</OnEnter>
    		<OnLeave>SetCursor("POINT_CURSOR");</OnLeave>
    	</Scripts>
    </Button>
    <Button name="GhostStatPointsDesButtonTemplate" inherits="GhostSmallCircleButtonTemplate" virtual="true">
    	<Size x="25" y="25"/>
    	<Scripts>
    		<OnLoad>self:RegisterForClicks("LeftButtonUp","RightButtonUp");</OnLoad>
    		<OnClick>StatPointsDes(self);</OnClick>
    		<OnEnter>SetCursor("ATTACK_CURSOR");</OnEnter>
    		<OnLeave>SetCursor("POINT_CURSOR");</OnLeave>
    	</Scripts>
    </Button>
    <Frame name="GhostStatPointsTextTemplate" virtual="true">
    	<Anchors><Anchor point="CENTER"><Offset x="0" y="0" /></Anchor></Anchors>
    	<Size><AbsDimension x="300" y="20"/></Size>
    	<Layers>
    		<Layer level="ARTWORK">
    			<FontString name="$parentText" inherits="TextStatusBarText">
    				<Anchors>
    					<Anchor point="CENTER">
    						<Offset x="0" y="110"/>
    					</Anchor>
    				</Anchors>
    			</FontString>
    		</Layer>
    	</Layers>
    </Frame>
	<!--AntiFarm-->
	<Button name="GhostAntiFarmFrameButtonTemplate" inherits="GameMenuButtonTemplate" virtual="true">
		<Size><AbsDimension x="30" y="30"/></Size>
		<Scripts>
			<OnClick>
				Ghost_AntiFarmButton_Click(self);
			</OnClick>
		</Scripts>
	</Button>
    <!--Recovery-->
    <Button name="GhostRecoveryCategoryButtonTemplate"  virtual="true" inherits="GameMenuButtonTemplate" text="分类">
    	<Size x="150" y="25"/>
    	<Scripts>
    		<OnLoad>self:RegisterForClicks("LeftButtonUp","RightButtonUp");self:Hide();</OnLoad>
    		<OnClick>Ghost_RecoveryCategoryButton_Click(self);</OnClick>
    		<OnEnter>SetCursor("ATTACK_CURSOR");</OnEnter>
    		<OnLeave>SetCursor("POINT_CURSOR");</OnLeave>
    	</Scripts>
    </Button>

    <Button name="GhostRecoveryButtonTemplate"  virtual="true" inherits="GameMenuButtonTemplate" text="回收">
    	<Size x="100" y="25"/>
    	<Scripts>
    		<OnLoad>self:RegisterForClicks("LeftButtonUp","RightButtonUp");self:Hide();</OnLoad>
    		<OnClick>Ghost_RecoveryButton_Click(self);</OnClick>
    		<OnEnter>SetCursor("ATTACK_CURSOR");</OnEnter>
    		<OnLeave>SetCursor("POINT_CURSOR");</OnLeave>
    	</Scripts>
    </Button>
</Ui>