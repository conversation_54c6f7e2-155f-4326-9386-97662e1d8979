-- ObjectiveTrackerHeaderTemplate.lua
-- 将XML模板转换为Lua代码实现

-- 调试模式控制开关
local DEBUG_ENABLED = true  -- 设置为 true 开启调试输出，false 关闭调试输出
local HEAD_NAME_TEXT = "天灾入侵"
local BLOCKS_TYPE = "试炼块"
-- 全局测试框架变量
local TheHeaderFrame = nil
local TheHeaderScenarioFrame = nil

-- 统一调试函数
-- @param message string 调试信息
local function DebugPrint(message)
    if DEBUG_ENABLED then
        -- 获取当前本地时间戳（毫秒级）
        local currentTime = time()  -- 获取当前Unix时间戳
        local gameTime = GetTime()  -- 获取游戏时间用于毫秒计算

        -- 使用date函数获取本地时间
        local timeTable = date("*t", currentTime)
        local hours = timeTable.hour
        local minutes = timeTable.min
        local seconds = timeTable.sec

        -- 从游戏时间中提取毫秒部分
        local milliseconds = math.floor((gameTime - math.floor(gameTime)) * 1000)

        -- 格式化时间戳 [HH:MM:SS.mmm]
        local timestamp = string.format("|cff888888[%02d:%02d:%02d.%03d]|r",
                                      hours, minutes, seconds, milliseconds)

        -- 输出带时间戳的调试信息
        print(timestamp .. " " .. message)
    end
end

ObjectiveTrackerHeaderTemplate = {}

-- 创建目标追踪器头部模板框架
-- @param parent Frame 父框架
-- @param name string 框架名称
-- @return Frame 创建的框架对象
function ObjectiveTrackerHeaderTemplate:CreateFrame(parent, name)
    -- 创建主框架 - 对应XML中的Frame
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateFrame: ")
    local frame = CreateFrame("Frame", name, parent)
    frame:SetSize(235, 25) -- 对应XML中的Size x="235" y="25"
    frame:Hide() -- 对应XML中的hidden="true"

    -- 启用拖拽功能
    frame:SetMovable(true)          -- 设置可移动
    frame:EnableMouse(true)         -- 启用鼠标
    frame:RegisterForDrag('LeftButton') -- 注册拖拽【左键】
    frame:SetScript('OnDragStart', frame.StartMoving) -- 设置脚本 开始移动
    frame:SetScript('OnDragStop', frame.StopMovingOrSizing) -- 设置脚本 停止移动
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateFrame: 拖拽功能已启用")

    -- 创建ARTWORK层的纹理和字体元素
    ObjectiveTrackerHeaderTemplate:CreateArtworkLayer(frame)
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateFrame: A")
    -- 创建动画组
    ObjectiveTrackerHeaderTemplate:CreateAnimations(frame)
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateFrame: B")
    -- 设置脚本事件
    ObjectiveTrackerHeaderTemplate:SetupScripts(frame)
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateFrame: C")
    return frame
end

-- 创建Atlas纹理的辅助函数 - 参考ChallengesKeystoneFrameUI.lua
-- @param parent Frame 父框架
-- @param atlasName string Atlas纹理名称
-- @param layer string 纹理层级 (可选)
-- @param sublevel number 子层级 (可选)
-- @param useAtlasSize boolean 是否使用Atlas原始尺寸
-- @return Texture 创建的纹理对象
function ObjectiveTrackerHeaderTemplate:CreateAtlasTexture(parent, atlasName, layer, sublevel, useAtlasSize)
    local texture = parent:CreateTexture(nil, layer or "ARTWORK", nil, sublevel or 0)

    -- 获取Atlas纹理信息
    local atlasInfo = GetAtlasTextureInfo(atlasName)
    if atlasInfo then
        texture:SetTexture(atlasInfo.atlasPath)
        texture:SetTexCoord(atlasInfo.left, atlasInfo.right, atlasInfo.top, atlasInfo.bottom)

        if useAtlasSize then
            texture:SetSize(atlasInfo.width, atlasInfo.height)
        end
    else
        DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 无法找到Atlas纹理: " .. atlasName)
        -- 设置一个默认纹理或颜色作为后备
        texture:SetColorTexture(1, 1, 1, 0.5) -- 白色半透明作为占位符
    end

    return texture
end

-- 创建ARTWORK层的所有元素
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:CreateArtworkLayer(frame)
    -- Background纹理 - 对应XML中的Texture parentKey="Background"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 1")
    local background = self:CreateAtlasTexture(frame, "Objective-Header", "ARTWORK", 0, true)
    background:SetPoint("TOPLEFT", frame, -29, 14) -- 对应Anchors中的设置
    background:SetAlpha(1) -- 对应alpha="1"
    background:Show() -- 对应hidden="false"
    frame.Background = background -- 对应parentKey="Background"
    
    -- Text字体字符串 - 对应XML中的FontString parentKey="Text"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 2")
    local text = frame:CreateFontString(nil, "ARTWORK", "SystemFont_Shadow_Med2")
    text:SetSize(170, 16) -- 对应Size x="170" y="16"
    text:SetPoint("LEFT", frame, 0, 0) -- 对应Anchors中的设置
    text:SetJustifyH("LEFT") -- 对应justifyH="LEFT"
    frame.Text = text -- 对应parentKey="Text"
    
    -- LineGlow纹理 - 对应XML中的Texture parentKey="LineGlow"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 3")
    local lineGlow = self:CreateAtlasTexture(frame, "OBJFX_LineGlow", "ARTWORK", 0, true)
    lineGlow:SetPoint("LEFT", background, -50, 18) -- 对应relativeKey="$parent.Background"
    lineGlow:SetAlpha(0) -- 对应alpha="0"
    lineGlow:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    lineGlow:Show() -- 对应hidden="false"
    frame.LineGlow = lineGlow -- 对应parentKey="LineGlow"

    -- SoftGlow纹理 - 对应XML中的Texture parentKey="SoftGlow"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 4")
    local softGlow = self:CreateAtlasTexture(frame, "OBJFX_Glow", "ARTWORK", 0, true)
    softGlow:SetPoint("CENTER", background, 20, 20) -- 对应relativePoint="LEFT"
    softGlow:SetAlpha(0) -- 对应alpha="0"
    softGlow:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    softGlow:Show() -- 对应hidden="false"
    frame.SoftGlow = softGlow -- 对应parentKey="SoftGlow"

    -- StarBurst纹理 - 对应XML中的Texture parentKey="StarBurst"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 5")
    local starBurst = self:CreateAtlasTexture(frame, "OBJFX_StarBurst", "ARTWORK", 0, true)
    starBurst:SetPoint("CENTER", softGlow) -- 对应relativeKey="$parent.SoftGlow"
    starBurst:SetAlpha(0) -- 对应alpha="0"
    starBurst:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    starBurst:Show() -- 对应hidden="false"
    frame.StarBurst = starBurst -- 对应parentKey="StarBurst"
    
    -- LineSheen纹理 - 对应XML中的Texture parentKey="LineSheen"
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateArtworkLayer: 6")
    local lineSheen = self:CreateAtlasTexture(frame, "OBJFX_LineBurst", "ARTWORK", 0, false)
    lineSheen:SetSize(60, 15) -- 对应Size x="60" y="15"
    lineSheen:SetPoint("CENTER", softGlow, 0, -13) -- 对应相对定位
    lineSheen:SetAlpha(0) -- 对应alpha="0"
    lineSheen:SetBlendMode("ADD") -- 对应alphaMode="ADD"
    lineSheen:Show() -- 对应hidden="false"
    frame.LineSheen = lineSheen -- 对应parentKey="LineSheen"
end

-- 创建动画组 - 对应XML中的AnimationGroup
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:CreateAnimations(frame)
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: ")
    -- 为Background创建动画组
    frame.BackgroundAnim = frame.Background:CreateAnimationGroup()

    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 1")
    -- Background的Alpha动画1 - 对应第一个Alpha childKey="Background"
    local bgAlpha1 = frame.BackgroundAnim:CreateAnimation("Alpha")
    bgAlpha1:SetStartDelay(0) -- 对应startDelay="0"
    bgAlpha1:SetDuration(0) -- 对应duration="0"
    bgAlpha1:SetChange(0) -- 对应fromAlpha="0" toAlpha="0"
    bgAlpha1:SetOrder(1) -- 对应order="1"

    -- Background的Alpha动画2 - 对应第二个Alpha childKey="Background"
    local bgAlpha2 = frame.BackgroundAnim:CreateAnimation("Alpha")
    bgAlpha2:SetStartDelay(0.25) -- 对应startDelay="0.25"
    bgAlpha2:SetDuration(0.5) -- 对应duration="0.5"
    bgAlpha2:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    bgAlpha2:SetOrder(1)

    -- 为LineGlow创建动画组
    frame.LineGlowAnim = frame.LineGlow:CreateAnimationGroup()

    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 2")
    -- LineGlow的Alpha动画1
    local lineGlowAlpha1 = frame.LineGlowAnim:CreateAnimation("Alpha")
    lineGlowAlpha1:SetDuration(0.15) -- 对应duration="0.15"
    lineGlowAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    lineGlowAlpha1:SetOrder(1)

    -- LineGlow的Alpha动画2
    local lineGlowAlpha2 = frame.LineGlowAnim:CreateAnimation("Alpha")
    lineGlowAlpha2:SetStartDelay(0.25)
    lineGlowAlpha2:SetDuration(0.65) -- 对应duration="0.65"
    lineGlowAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    lineGlowAlpha2:SetOrder(1)
    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 2.1")
    -- LineGlow的Scale动画
    local lineGlowScale = frame.LineGlowAnim:CreateAnimation("Scale")
    lineGlowScale:SetDuration(0.15)
    lineGlowScale:SetScale(2, 2) -- 对应toScaleX="2" toScaleY="2"，SetScale支持两个参数(scaleX, scaleY)
    lineGlowScale:SetOrder(1)
    -- frame.LineGlow:SetOrigin("CENTER", -50, 0) -- 对应Origin point="CENTER" Offset x="-50" y="0"

    -- -- LineGlow的Translation动画
    -- local lineGlowTranslation = frame.LineGlowAnim:CreateAnimation("Translation")
    -- lineGlowTranslation:SetDuration(0.75) -- 对应duration="0.75"
    -- lineGlowTranslation:SetOrder(1)
    -- lineGlowTranslation:SetOffset(50, 0) -- 对应offsetX="50" offsetY="0"

    -- 为SoftGlow创建动画组
    frame.SoftGlowAnim = frame.SoftGlow:CreateAnimationGroup()

    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 3")
    -- SoftGlow的Alpha动画1
    local softGlowAlpha1 = frame.SoftGlowAnim:CreateAnimation("Alpha")
    softGlowAlpha1:SetDuration(0.25)
    softGlowAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    softGlowAlpha1:SetOrder(1)

    -- SoftGlow的Alpha动画2
    local softGlowAlpha2 = frame.SoftGlowAnim:CreateAnimation("Alpha")
    softGlowAlpha2:SetStartDelay(0.25)
    softGlowAlpha2:SetDuration(0.5)
    softGlowAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    softGlowAlpha2:SetOrder(1)

    -- SoftGlow的Scale动画
    local softGlowScale = frame.SoftGlowAnim:CreateAnimation("Scale")
    softGlowScale:SetDuration(0.25)
    softGlowScale:SetScale(0.8, 0.8) -- 对应toScaleX="0.8" toScaleY="0.8"，SetScale支持两个参数(scaleX, scaleY)
    softGlowScale:SetOrder(1)

    -- 为StarBurst创建动画组
    frame.StarBurstAnim = frame.StarBurst:CreateAnimationGroup()

    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 4")
    -- StarBurst的Alpha动画1
    local starBurstAlpha1 = frame.StarBurstAnim:CreateAnimation("Alpha")
    starBurstAlpha1:SetDuration(0.25)
    starBurstAlpha1:SetChange(1) -- 对应fromAlpha="0" toAlpha="1"
    starBurstAlpha1:SetOrder(1)

    -- StarBurst的Alpha动画2
    local starBurstAlpha2 = frame.StarBurstAnim:CreateAnimation("Alpha")
    starBurstAlpha2:SetStartDelay(0.25)
    starBurstAlpha2:SetDuration(0.5)
    starBurstAlpha2:SetChange(-1) -- 对应fromAlpha="1" toAlpha="0"
    starBurstAlpha2:SetOrder(1)

    -- StarBurst的Scale动画
    local starBurstScale = frame.StarBurstAnim:CreateAnimation("Scale")
    starBurstScale:SetDuration(0.25)
    starBurstScale:SetScale(1, 1) -- 对应toScaleX="1" toScaleY="1"，SetScale支持两个参数(scaleX, scaleY)
    starBurstScale:SetOrder(1)

    -- 为LineSheen创建动画组
    frame.LineSheenAnim = frame.LineSheen:CreateAnimationGroup()

    DebugPrint("ObjectiveTrackerHeaderTemplate:CreateAnimations: 5")
    -- LineSheen的Alpha动画1
    local lineSheenAlpha1 = frame.LineSheenAnim:CreateAnimation("Alpha")
    lineSheenAlpha1:SetStartDelay(0.15) -- 对应startDelay="0.15"
    lineSheenAlpha1:SetDuration(0.5)
    lineSheenAlpha1:SetChange(0.75) -- 对应fromAlpha="0" toAlpha="0.75"
    lineSheenAlpha1:SetOrder(1)

    -- LineSheen的Alpha动画2
    local lineSheenAlpha2 = frame.LineSheenAnim:CreateAnimation("Alpha")
    lineSheenAlpha2:SetStartDelay(0.75) -- 对应startDelay="0.75"
    lineSheenAlpha2:SetDuration(0.5)
    lineSheenAlpha2:SetChange(-0.75) -- 对应fromAlpha="0.75" toAlpha="0"
    lineSheenAlpha2:SetOrder(1)

    -- LineSheen的Translation动画
    local lineSheenTranslation = frame.LineSheenAnim:CreateAnimation("Translation")
    lineSheenTranslation:SetStartDelay(0.15)
    lineSheenTranslation:SetDuration(1.5) -- 对应duration="1.5"
    lineSheenTranslation:SetOrder(1)
    lineSheenTranslation:SetOffset(250, 0) -- 对应offsetX="250" offsetY="0"

    -- -- 创建HeaderOpenAnim动画组来统一控制所有动画 - 对应XML中的AnimationGroup parentKey="HeaderOpenAnim"
    -- frame.HeaderOpenAnim = frame:CreateAnimationGroup()
    -- frame.HeaderOpenAnim:SetToFinalAlpha(true) -- 对应setToFinalAlpha="true"

    -- -- 设置动画完成事件 - 对应XML中的OnFinished function="ObjectiveTrackerHeader_OnAnimFinished"
    -- frame.HeaderOpenAnim:SetScript("OnFinished", function(self)
    --     ObjectiveTrackerHeader_OnAnimFinished(self)
    -- end)
end

-- 设置脚本事件 - 对应XML中的Scripts
-- @param frame Frame 主框架
function ObjectiveTrackerHeaderTemplate:SetupScripts(frame)
    -- OnLoad事件 - 对应XML中的OnLoad脚本
    frame:SetScript("OnLoad", function(self)
        self.height = 25 -- 对应XML中的脚本内容
    end)
end

-- 动画完成回调函数 - 对应XML中引用的函数
-- @param self AnimationGroup 动画组对象
function ObjectiveTrackerHeader_OnAnimFinished(self)
    -- 这里可以添加动画完成后的处理逻辑
    -- 具体实现需要根据实际需求来定义
    DebugPrint("ObjectiveTrackerHeader animation finished")
end

-- 创建模板实例的便捷函数
-- @param parent Frame 父框架
-- @param name string 框架名称（可选）
-- @return Frame 创建的框架对象
function CreateObjectiveTrackerHeaderTemplate(parent, name)
    local frameName = name or ("ObjectiveTrackerHeader" .. math.random(1000, 9999))
    return ObjectiveTrackerHeaderTemplate:CreateFrame(parent, frameName)
end

-- 播放框架所有动画的辅助函数
-- @param frame Frame 目标框架
local function PlayAllAnimations(frame)
    if not frame then
        DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 无法播放动画，框架不存在")
        return
    end

    DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 开始播放所有动画效果")

    -- 播放所有动画组
    if frame.BackgroundAnim then
        frame.BackgroundAnim:Play()
        DebugPrint("  - BackgroundAnim 已播放")
    end
    if frame.LineGlowAnim then
        frame.LineGlowAnim:Play()
        DebugPrint("  - LineGlowAnim 已播放")
    end
    if frame.SoftGlowAnim then
        frame.SoftGlowAnim:Play()
        DebugPrint("  - SoftGlowAnim 已播放")
    end
    if frame.StarBurstAnim then
        frame.StarBurstAnim:Play()
        DebugPrint("  - StarBurstAnim 已播放")
    end
    if frame.LineSheenAnim then
        frame.LineSheenAnim:Play()
        DebugPrint("  - LineSheenAnim 已播放")
    end

    DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 所有动画播放完成")
end

-- 注册斜杠命令的函数
function ObjectiveTrackerHeaderTemplate:RegisterSlashCommands()
    -- 注册 /objheader 命令
    SLASH_OBJHEADERTEST1 = "/objheader"
    SLASH_OBJHEADERTEST2 = "/testheader"

    SlashCmdList["OBJHEADERTEST"] = function(msg)
        DebugPrint("ObjectiveTrackerHeader: 收到命令参数: '" .. (msg or "nil") .. "'")
        local cmd = string.lower(msg or "")

        if cmd == "show" or cmd == "" then
            -- 创建或显示测试框架
            if not TheHeaderFrame then
                DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 开始创建测试框架...")
                TheHeaderFrame = CreateObjectiveTrackerHeaderTemplate(UIParent, "TestObjectiveTrackerHeader")
                DebugPrint("testFrame: " .. tostring(TheHeaderFrame))
                if TheHeaderFrame then
                    TheHeaderFrame:SetPoint("CENTER", UIParent, "CENTER", 0, 0)
                    TheHeaderFrame.Text:SetText(HEAD_NAME_TEXT)

                    -- 绑定 OnShow 事件处理器，当框架显示时自动播放动画
                    TheHeaderFrame:SetScript("OnShow", function(self)
                        DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: OnShow 事件触发，开始自动播放动画")
                        PlayAllAnimations(self)
                    end)
                    local typename = GetScenarioBlockTypeFromChineseName(BLOCKS_TYPE)
                    local TheHeaderScenarioFrame = CreateScenarioBlocksFrame(TheHeaderFrame, "TestScenarioBlocksFrame", typename)
                    if TheHeaderScenarioFrame then
                        TheHeaderScenarioFrame:SetPoint("BOTTOMLEFT", TheHeaderFrame, "BOTTOMLEFT", -15, -85)
                        SetScenarioBlocksFrameManagerTheScenarioFrame(TheHeaderScenarioFrame)
                    end
                    DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已创建并绑定 OnShow 事件")
                else
                    DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架创建失败")
                    return
                end
            end
            if TheHeaderFrame then
                if TheHeaderFrame:IsShown() then
                    TheHeaderFrame:Hide()
                else
                    TheHeaderFrame.Text:SetText(HEAD_NAME_TEXT)
                    TheHeaderFrame:Show()
                end
            end
            
            DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已显示")

        elseif cmd == "hide" then
            -- 隐藏测试框架
            if TheHeaderFrame then
                TheHeaderFrame:Hide()
                DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 测试框架已隐藏")
            else
                DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
            end

        elseif cmd == "anim" or cmd == "play" then
            -- 播放动画
            if TheHeaderFrame then
                PlayAllAnimations(TheHeaderFrame)
            else
                DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
            end

        elseif cmd == "stop" then
            -- 停止动画
            if TheHeaderFrame then
                -- 停止所有动画组
                if TheHeaderFrame.BackgroundAnim then TheHeaderFrame.BackgroundAnim:Stop() end
                if TheHeaderFrame.LineGlowAnim then TheHeaderFrame.LineGlowAnim:Stop() end
                if TheHeaderFrame.SoftGlowAnim then TheHeaderFrame.SoftGlowAnim:Stop() end
                if TheHeaderFrame.StarBurstAnim then TheHeaderFrame.StarBurstAnim:Stop() end
                if TheHeaderFrame.LineSheenAnim then TheHeaderFrame.LineSheenAnim:Stop() end
                DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 所有动画已停止")
            else
                DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
            end

        elseif cmd == "reset" then
            -- 重置所有元素的透明度
            if TheHeaderFrame then
                TheHeaderFrame.Background:SetAlpha(1)
                TheHeaderFrame.LineGlow:SetAlpha(0)
                TheHeaderFrame.SoftGlow:SetAlpha(0)
                TheHeaderFrame.StarBurst:SetAlpha(0)
                TheHeaderFrame.LineSheen:SetAlpha(0)
                DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 透明度已重置")
            else
                DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在")
            end

        elseif cmd == "debug on" then
            -- 开启调试模式
            DEBUG_ENABLED = true
            print("|cff00ff00ObjectiveTrackerHeader|r: 调试模式已开启（包含毫秒级时间戳）")

        elseif cmd == "debug off" then
            -- 关闭调试模式
            DEBUG_ENABLED = false
            print("|cff00ff00ObjectiveTrackerHeader|r: 调试模式已关闭")

        elseif cmd == "debug test" then
            -- 测试调试输出（包含本地时间戳）
            local oldDebugState = DEBUG_ENABLED
            DEBUG_ENABLED = true
            DebugPrint("这是一条测试调试信息（显示本地时间）")
            DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 测试彩色调试信息")
            DebugPrint("|cffff0000错误信息示例|r: 这是红色错误信息")
            DEBUG_ENABLED = oldDebugState
            print("|cff00ff00ObjectiveTrackerHeader|r: 调试输出测试完成（时间戳已修复为本地时间）")

        elseif cmd == "event test" then
            -- 测试 OnShow 事件机制
            if TheHeaderFrame then
                DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 测试 OnShow 事件机制")
                TheHeaderFrame:Hide()  -- 先隐藏
                DebugPrint("框架已隐藏，准备重新显示以触发 OnShow 事件...")
                TheHeaderFrame:Show()  -- 再显示，这将触发 OnShow 事件
            else
                DebugPrint("|cffff0000ObjectiveTrackerHeader|r: 测试框架不存在，请先执行 /objheader show")
            end

        else
            -- 显示帮助信息
            print("|cff00ff00ObjectiveTrackerHeader 测试命令:|r")
            print("  /objheader show - 显示测试框架（OnShow事件自动播放动画）")
            print("  /objheader hide - 隐藏测试框架")
            print("  /objheader anim - 手动播放动画")
            print("  /objheader stop - 停止动画")
            print("  /objheader reset - 重置透明度")
            print("  /objheader debug on - 开启调试模式（带本地时间戳）")
            print("  /objheader debug off - 关闭调试模式")
            print("  /objheader debug test - 测试调试输出（显示本地时间戳效果）")
            print("  /objheader event test - 测试 OnShow 事件机制")
        end
    end

    DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 斜杠命令已注册 - /objheader, /testheader")
end

-- 事件处理函数
function ObjectiveTrackerHeaderTemplate:OnEvent(event, ...)
    if event == "ADDON_LOADED" then
        local addonName = ...
        DebugPrint("ObjectiveTrackerHeader addonName: " .. addonName)
        if addonName == "ExtractAtlasInfos" then
            -- 插件加载完成后的初始化
            DebugPrint("|cff00ff00ObjectiveTrackerHeader|r: 插件初始化完成")
            -- 注册斜杠命令
            self:RegisterSlashCommands()
        end
    end
end

-- 创建框架用于事件处理
local eventFrame = CreateFrame("Frame")
eventFrame:SetScript("OnEvent", function(_, event, ...)
    ObjectiveTrackerHeaderTemplate:OnEvent(event, ...)
end)

-- 注册事件
eventFrame:RegisterEvent("ADDON_LOADED")
