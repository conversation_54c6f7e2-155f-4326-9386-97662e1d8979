--local fil = io.open("C:\\Users\\<USER>\\Downloads\\Worl_of_Warcraft\\World of Warcraft\\Interface\\AddOns\\ui\\ui.lua", "a")
local c1 = 1

local b = {}

local PageShowInfo,SupermarketButton,SupermarketOffButton,SupermarketFrame,testbutton

local onoff = false

--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--有一点一定要说,切记切记切记,Lua无法用数据库传来的图片路径进行拼接变成图片无法变成图片无法变成图片无法变成图片
--无法实现"|T"..服务器发送过来路径..":20|t" 
--无法实现"|T"..服务器发送过来路径..":20|t" 
--无法实现"|T"..服务器发送过来路径..":20|t"
--服务器发来的文字显示是可以改变的详情看函数内的注解 
--服务器发来的文字显示是可以改变的详情看函数内的注解
--服务器发来的文字显示是可以改变的详情看函数内的注解

local T = 
{
	ParentFrameBackgroundLeft	= "Interface\\ICONS\\progress_frame_left",
	ParentFrameBackgroundRight	= "Interface\\ICONS\\progress_frame_right",
	ParentFrameTitle			= "Interface\\ICONS\\bt",
	SubFrameBackground			= "Interface\\ICONS\\yuanjiaolan",
	TabClassFrameBackground		= "Interface\\ICONS\\LineTab1",
	ItemClassButton				= "Interface\\ICONS\\_button_h",
	FontBackground				= "Interface\\ICONS\\languangditu",
	ButtonHighlighLight1		= "Interface\\BUTTONS\\CheckButtonHilight",
	ButtonHighlighLight2		= "Interface\\ICONS\\button_h2",
	ButtonHighlighLight3		= "Interface\\BUTTONS\\UI-Listbox-Highlight",
	Font						= "Interface\\Fonts\\FRIZQT_.TTF",
	SupermarketButton			= "Interface\\ICONS\\shangchengon",
	SupermarketOffButton		= "Interface\\ICONS\\shangchengoff",
	JiFen						= "Interface\\ICONS\\jf1",
	JinBi						= "Interface\\ICONS\\jinbi",
	Vip							= "Interface\\ICONS\\Vip",
	ItemExchange				= "Interface\\ICONS\\wpdh",
	AlliancePVP					= "Interface\\ICONS\\lmry",
	HordePVP					= "Interface\\ICONS\\blry",
	AlliancePVPPoint			= "Interface\\ICONS\\lmryd",
	HordePVPPoint				= "Interface\\ICONS\\blryd",
	ArenaPVP					= "Interface\\ICONS\\jjc",
	ArenaPVPPoint				= "Interface\\ICONS\\jjd",
	EXP							= "Interface\\ICONS\\bigxp",
	XP							= "Interface\\ICONS\\xp"
}


function Event(self, event, h, msg, classtype, sender)	

    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
	
		 local NewMsg = string.format("消息头 = %s 消息 = %s", h, msg)
		 print(NewMsg)

		 b[c1] = msg

		 c1 = c1 + 1

		 --print("b = "..c)
		 
		 onoff = true
    end
end


local MsgReceiver = CreateFrame("Frame")
MsgReceiver:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiver:SetScript("OnEvent", Event)


function ColorPicker(r,g,b)
	local red,green,blue
	red = r/255
	green = g/255
	blue = b/255
	return red,green,blue
end


function CreateParentFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)
	
local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	f:SetSize(Length, Width)
	if InheritsFrame == nil then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 32, insets = { left = 10, right = 10, top = 10, bottom = 10 }});
	end
	f:SetPoint(Point,Ofsx,Ofsy)
	--只有主窗口才能移动
	if ParentFrame == UIParent then
		f:RegisterForDrag("LeftButton")
		f:SetToplevel(true)
		f:SetClampedToScreen(true)
		f:SetMovable(true)
		f:EnableMouse(true)
		f:SetScript("OnDragStart", f.StartMoving)
		f:SetScript("OnHide", f.StopMovingOrSizing)
		f:SetScript("OnDragStop", f.StopMovingOrSizing)
	end
	if ParentFrame == UIParent then
		f:Hide()
	end
	
	if ParentFrame == SupermarketFrame then
		f:Hide()
	end
	
	return f
end


function CreateFontS(Frame,Name,Inherits,Fonts,Fsize,Point,Ofsxm,Ofsym,Msg,rgb)

	local fs = Frame:CreateFontString(Name,Inherits)
		if Fonts == "" then 
			fs:SetFontObject(GameFontNormalLarge)
			else
			fs:SetFont(Fonts, Fsize)
		end

		if rgb ~= "" then
			local r,g,b = ColorPicker(strsplit(",",rgb))
			fs:SetTextColor(r,g,b)
		end	
		
		fs:SetPoint(Point,Ofsxm,Ofsym)
		fs:SetText(Msg)
		
		return fs;
end


function CreateButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,IsHideButton)

		local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		if InheritsFrame == nil then 
			Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
		end
		
		Button:SetSize(Length, Width)
		Button:SetPoint(Point, Ofsxm, Ofsym)
		Button:EnableMouse(true)
		if BgFile == T["ItemClassButton"] then
			Button:SetHighlightTexture(T["ButtonHighlighLight2"])
		elseif  InheritsFrame == "UIPanelButtonTemplate2" then
			Button:SetHighlightTexture(T["ButtonHighlighLight3"])
		else
			Button:SetHighlightTexture(T["ButtonHighlighLight1"])
		end
		local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
		ButtonFontStr:SetFont(T["Font"], 15)	--需要设置字体
		ButtonFontStr:SetAllPoints(Button)
		ButtonFontStr:SetText(Text)
		--if normaltexture ~= "" then
			--Button:SetNormalTexture(normaltexture)
		--end
		
		if IsHideButton == true then
			Button:Hide()
		end
		
		return Button;
end


function PrepareScript(object, text, script, itemlink)
	  if text then	--如果text存在 那么显示在对象的右边并对齐(ANCHOR_RIGHT)
        object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
	  end
	  
	  if type(script) == "function" then	--type(script) 返回script的类型 以字符串形式
      object:SetScript("OnClick", script)
    elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end


function HandleButtonScript(Button,Type)

	
	if Type == "SupermarketButton" then
	
		if Button == SupermarketButton then
		
			if onoff == false then
			print("又回来了")
			

			--SendAddonMessage("UI","All","GUILD", UnitName("player"))
			
			SupermarketOffButton:Show()
			
			SupermarketButton:Hide()
			
			SupermarketFrame:Show()
			
			SupermarketFrame1:Show()
			else
				
				--一定要重新生成框架窗口 才能够正确显示
				--在这里单纯用 showtext() 重置重新生成文本 而不用 OnInitialize()重置 文本依附依附依附(重要的事情说三遍)的主框架 就会出现文本重叠的现象 见1.png
				--而正确方式是只用用OnInitialize()重置文本依附依附依附(重要的事情说三遍)的主框架就会正常显示 
				--showtext()
				OnInitialize(false)
				print("到这来了")
				onoff = false
			
			end
		end
	end
	
	if Type == "SupermarketOffButton" then
	
		SupermarketOffButton:Hide()
		
		SupermarketButton:Show()
		
		SupermarketFrame:Hide()
		
		SupermarketFrame1:Hide()
	end

end



function InitOtherButtons()
	
	--商城按钮
	PrepareScript(SupermarketButton,	"打开商城",		function()	HandleButtonScript(SupermarketButton,"SupermarketButton") end)
	 
	 --关闭商城按钮
	PrepareScript(SupermarketOffButton,	"关闭商城",		function()	HandleButtonScript(SupermarketOffButton,"SupermarketOffButton") end)
	 

end


function ShowOtherButton()

	SupermarketButton = CreateButton("Button","SupermarketButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketButton"],"")

	SupermarketOffButton = CreateButton("Button","SupermarketOffButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketOffButton"],"",true)
		
	--初始化其它按钮脚本
	InitOtherButtons()
	
end


function showtext()

	if b[1] == nil then 
		--SendAddonMessage("UI","All","GUILD", UnitName("player"))
		
		print("b = nil")

		PageShowInfo = CreateFontS(SupermarketFrame1,"PageShowInfo","",T["Font"],16,"Bottom",115,35,"12","255,165,0")
		
	else
	
		print("b ！= nil")	
		
		--PageShowInfo = nil
		
		--print("123")
		--local str = "|T"..tostring(b)..":20|t"
		
		
		
		--if PageShowInfo == nil then
		--print("no")
		--else
		--print("yes")
		--end
		PageShowInfo = CreateFontS(SupermarketFrame1,"PageShowInfo","",T["Font"],16,"Bottom",115,35,b[1],"255,165,0")
		--PageShowInfo = CreateFontS(SupermarketFrame,"PageShowInfo","",T["Font"],16,"Bottom",115,35,str,"255,165,0")
		--print("456"..str)
	end


end


function showbutton()
	if b[2] == nil then
		testbutton = CreateButton("Button","testbutton",SupermarketFrame1,nil,64,64,"","CENTER",0,0,T["Vip"],"")
	else
		--testbutton = nil
		--print("b[2] type = "..type(b[2]))
		--下面这个可以
		testbutton = CreateButton("Button","testbutton",SupermarketFrame1,nil,64,64,"|T"..GetItemIcon(tonumber(b[2]))..":50|t","CENTER",0,0,"","")
		--下面这个还是不行
		--testbutton = CreateButton("Button","testbutton",SupermarketFrame1,nil,64,64,"|T"..b[2]..":30|t","CENTER",0,0,"","")
	end
end


function OnInitialize(isreload)


	--修改了文本 或者 按钮 信息 一定要重置父窗口框架 重新生成 重置主窗口框架是没用的

	--SupermarketFrame = CreateParentFrame("Frame","SupermarketFrame",UIParent,nil,950,730,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	
	
	
	if isreload == true then
	print("true")
	SupermarketFrame = CreateParentFrame("Frame","SupermarketFrame",UIParent,nil,950,730,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	--SupermarketFrame1 = CreateParentFrame("Frame","SupermarketFrame1",SupermarketFrame,nil,500,300,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	--初始化其它按钮
	ShowOtherButton()
	end
	
	--位置不要弄错了 先有主窗口再有子窗口
	SupermarketFrame1 = CreateParentFrame("Frame","SupermarketFrame1",SupermarketFrame,nil,500,300,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	
	showbutton()
	
	showtext()
	
end

OnInitialize(true)


local scrolltest = CreateFrame("ScrollFrame","scrolltest",UIParent,nil)
scrolltest:SetSize(150, 150)
scrolltest:SetPoint("CENTER")
local textest = scrolltest:CreateTexture("textest","BACKGROUND",nil)
textest:SetAllPoints(scrolltest)
textest:SetTexture(0,0,0)

local scrollchildtest = CreateFrame("Frame")
scrolltest:SetScrollChild(scrollchildtest)
scrollchildtest:SetSize(250, 250)
local textests = scrollchildtest:CreateTexture("textests"," ARTWORK",nil)
textests:SetTexture("Interface\\ICONS\\jf1")
textests:SetSize(100, 100)
textests:SetPoint("CENTER")

--local slidertest = CreateFrame("Slider","slidertest",scrolltest,nil)
local slidertest = CreateFrame("Slider")
slidertest:SetOrientation("HORIZONTAL")
slidertest:SetMinMaxValues(0,100)
slidertest:SetValue(1)
slidertest:SetValueStep(1.0)
slidertest:SetPoint("TOP","scrolltest","BOTTOM")
slidertest:SetSize(150,25)
--slidertest:Enables()
local tt = slidertest:CreateTexture("tt")
tt:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
tt:SetSize(25, 25)
local slidertesttexture = slidertest:SetThumbTexture(tt)

--这个才是正确的
slidertest:SetScript("OnValueChanged",
function(self)
scrolltest:SetHorizontalScroll(1 * self:GetValue())
end
)


scrolltest:SetScript("OnMouseWheel", 
function(self,val)  

	--往上滑 val是正数	方向：上，左
	--往下滑 val是负数	方向：下，右
	
	local s1 = slidertest
	local minv,maxv = s1:GetMinMaxValues()
	local cv = s1:GetValue()
	
	local nv = cv - ( val * 3 )
	nv = max(nv, minv)
	nv = min(nv, maxv)
	if ( nv ~= cv ) then
	--print("3")
		s1:SetValue(nv);
	end
	
end)
scrolltest:EnableMouseWheel(true)


--下面这样是不行的
--[[
slidertest:SetScript("OnValueChanged",ttt(self))
function ttt(frame)
	scrolltest:SetHorizontalScroll(1 * frame:GetValue())
end
]]--

print("666")




