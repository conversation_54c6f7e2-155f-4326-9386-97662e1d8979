-- AUTOMATICALLY GENERATED -- DO NOT EDIT!

ABANDON_PET = "你是否决定永远地遗弃你的宠物？";
ABANDON_QUEST = "放弃任务";
ABANDON_QUEST_ABBREV = "放弃";
ABANDON_QUEST_CONFIRM = "放弃\"%s\"？";
ABANDON_QUEST_CONFIRM_WITH_ITEMS = "确定要放弃\"%s\"并摧毁%s吗？";
ABILITIES = "技能";
ABSORB = "吸收";
ABSORB_TRAILER = "(%d点被吸收)";
ACCEPT = "接受";
ACCEPT_ALT = "接受";
ACCEPT_COMMENT = "设置注释";
ACHIEVEMENT = "成就通告";
ACHIEVEMENTFRAME_FILTER_ALL = "全部";
ACHIEVEMENTFRAME_FILTER_COMPLETED = "已获得";
ACHIEVEMENTFRAME_FILTER_INCOMPLETE = "未完成";
ACHIEVEMENTS = "成就";
ACHIEVEMENTS_COMPLETED = "已获得成就";
ACHIEVEMENTS_COMPLETED_CATEGORY = "已获得%s项成就";
ACHIEVEMENT_BROADCAST = "%s获得了成就%s！";
ACHIEVEMENT_BROADCAST_SELF = "你获得了成就%s！";
ACHIEVEMENT_BUTTON = "成就";
ACHIEVEMENT_CATEGORY_PROGRESS = "进展总览";
ACHIEVEMENT_META_COMPLETED_DATE = "已完成 %s";
ACHIEVEMENT_SUMMARY_CATEGORY = "总览";
ACHIEVEMENT_TITLE = "成就点数";
ACHIEVEMENT_TOOLTIP_COMPLETE = "%s在%d/%d/20%02d获得了这个成就";
ACHIEVEMENT_TOOLTIP_IN_PROGRESS = "%s正在为了获得这个成就而努力";
ACHIEVEMENT_UNLOCKED = "已获得成就";
ACHIEVEMENT_UNLOCKED_CHAT_MSG = "已获得成就：%s";
ACHIEVEMENT_WATCH_TOO_MANY = "你在同一时间内只能追踪%d项成就。";
ACTIONBARS_LABEL = "动作条";
ACTIONBARS_SUBTEXT = "动作条是一排用来放置快捷键的位置，可以使你快速使用技能和物品。你可以在这里开启额外的动作条并对它们进行设定。";
ACTIONBAR_LABEL = "动作条";
ACTION_DAMAGE_SHIELD = "伤害";
ACTION_DAMAGE_SHIELD_FULL_TEXT = "%1$s%2$s将%9$s点%7$s伤害反射给%4$s。%6$s";
ACTION_DAMAGE_SHIELD_FULL_TEXT_NO_SOURCE = "%2$s将%9$s点%7$s伤害反射给%4$s。%6$s";
ACTION_DAMAGE_SHIELD_MISSED = "未命中";
ACTION_DAMAGE_SHIELD_MISSED_BLOCK = "（格挡）";
ACTION_DAMAGE_SHIELD_MISSED_BLOCK_FULL_TEXT = "%1$s%2$s被%4$s格挡了。%6$s";
ACTION_DAMAGE_SHIELD_MISSED_BLOCK_FULL_TEXT_NO_SOURCE = "%2$s被%4$s格挡了。%6$s";
ACTION_DAMAGE_SHIELD_MISSED_BLOCK_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_DEFLECT = "（偏转）";
ACTION_DAMAGE_SHIELD_MISSED_DEFLECT_FULL_TEXT = "%1$s%2$s被%4$s偏转了。";
ACTION_DAMAGE_SHIELD_MISSED_DEFLECT_FULL_TEXT_NO_SOURCE = "%2$s被%4$s偏转了。";
ACTION_DAMAGE_SHIELD_MISSED_DEFLECT_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_DODGE = "（躲闪）";
ACTION_DAMAGE_SHIELD_MISSED_DODGE_FULL_TEXT = "%1$s%2$s被%4$s躲闪了。";
ACTION_DAMAGE_SHIELD_MISSED_DODGE_FULL_TEXT_NO_SOURCE = "%2$s被%4$s躲闪了。";
ACTION_DAMAGE_SHIELD_MISSED_DODGE_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_EVADED = "（闪避）";
ACTION_DAMAGE_SHIELD_MISSED_EVADED_FULL_TEXT = "%1$s%2$s被%4$s闪避了。";
ACTION_DAMAGE_SHIELD_MISSED_EVADED_FULL_TEXT_NO_SOURCE = "%2$s被%4$s闪避了。";
ACTION_DAMAGE_SHIELD_MISSED_EVADED_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_FULL_TEXT = "%1$s%2$s未命中%4$s。";
ACTION_DAMAGE_SHIELD_MISSED_FULL_TEXT_NO_SOURCE = "%2$s未命中%4$s。";
ACTION_DAMAGE_SHIELD_MISSED_IMMUNE = "（免疫）";
ACTION_DAMAGE_SHIELD_MISSED_IMMUNE_FULL_TEXT = "%1$s%2$s失败。%4$s对此免疫。";
ACTION_DAMAGE_SHIELD_MISSED_IMMUNE_FULL_TEXT_NO_SOURCE = "%2$s失败。%4$s对此免疫。";
ACTION_DAMAGE_SHIELD_MISSED_IMMUNE_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_MISS = "（未命中）";
ACTION_DAMAGE_SHIELD_MISSED_MISS_FULL_TEXT = "%1$s%2$s未命中%4$s。";
ACTION_DAMAGE_SHIELD_MISSED_MISS_FULL_TEXT_NO_SOURCE = "%2$s未命中%4$s。";
ACTION_DAMAGE_SHIELD_MISSED_MISS_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_PARRY = "（招架）";
ACTION_DAMAGE_SHIELD_MISSED_PARRY_FULL_TEXT = "%1$s%2$s被%4$s招架了。";
ACTION_DAMAGE_SHIELD_MISSED_PARRY_FULL_TEXT_NO_SOURCE = "%2$s被%4$s招架了。";
ACTION_DAMAGE_SHIELD_MISSED_PARRY_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_MISSED_RESIST = "（抵抗）";
ACTION_DAMAGE_SHIELD_MISSED_RESIST_FULL_TEXT = "%1$s%2$s被%4$s完全抵抗了。%6$s";
ACTION_DAMAGE_SHIELD_MISSED_RESIST_FULL_TEXT_NO_SOURCE = "%2$s被%4$s完全抵抗了。%6$s";
ACTION_DAMAGE_SHIELD_MISSED_RESIST_POSSESSIVE = "1";
ACTION_DAMAGE_SHIELD_POSSESSIVE = "1";
ACTION_DAMAGE_SPLIT = "分担伤害";
ACTION_DAMAGE_SPLIT_ABSORBED_FULL_TEXT = "%4$s %6$s吸收了%1$s的%2$s点伤害。";
ACTION_DAMAGE_SPLIT_FULL_TEXT = "%1$s%2$s对%4$s$s造成%9$s点伤害。";
ACTION_DAMAGE_SPLIT_POSSESSIVE = "0";
ACTION_DAMAGE_SPLIT_RESULT_FULL_TEXT = "%1$s的%2$s对%4$s %6$s造成%9$s点伤害。";
ACTION_ENCHANT_APPLIED = "附魔";
ACTION_ENCHANT_APPLIED_FULL_TEXT = "%1$s在%4$s%5$s上施放了%2$s。";
ACTION_ENCHANT_APPLIED_POSSESSIVE = "0";
ACTION_ENCHANT_REMOVED = "附魔消失";
ACTION_ENCHANT_REMOVED_FULL_TEXT = "%2$s从%4$s%5$s上消失了。";
ACTION_ENCHANT_REMOVED_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE = "伤害";
ACTION_ENVIRONMENTAL_DAMAGE_DROWNING = "溺水";
ACTION_ENVIRONMENTAL_DAMAGE_DROWNING_FULL_TEXT = "%4$s溺水，损失了%9$s点生命值。";
ACTION_ENVIRONMENTAL_DAMAGE_DROWNING_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_FALLING = "掉落";
ACTION_ENVIRONMENTAL_DAMAGE_FALLING_FULL_TEXT = "%4$s受到%9$s点掉落伤害。";
ACTION_ENVIRONMENTAL_DAMAGE_FALLING_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_FATIGUE = "疲倦";
ACTION_ENVIRONMENTAL_DAMAGE_FATIGUE_FULL_TEXT = "%4$s筋疲力尽，损失了%9$s点生命值。";
ACTION_ENVIRONMENTAL_DAMAGE_FATIGUE_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_FIRE = "火焰";
ACTION_ENVIRONMENTAL_DAMAGE_FIRE_FULL_TEXT = "%4$s受到了%9$s点火焰伤害。";
ACTION_ENVIRONMENTAL_DAMAGE_FIRE_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_FULL_TEXT = "%4$s因环境伤害而损失了%9$s点生命值。";
ACTION_ENVIRONMENTAL_DAMAGE_LAVA = "岩浆";
ACTION_ENVIRONMENTAL_DAMAGE_LAVA_FULL_TEXT = "%4$s在岩浆中，损失了%9$s点生命值。%6$s";
ACTION_ENVIRONMENTAL_DAMAGE_LAVA_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_POSSESSIVE = "0";
ACTION_ENVIRONMENTAL_DAMAGE_SLIME = "淤泥";
ACTION_ENVIRONMENTAL_DAMAGE_SLIME_FULL_TEXT = "%4$s在淤泥中，损失了%9$s点生命值。%6$s";
ACTION_ENVIRONMENTAL_DAMAGE_SLIME_POSSESSIVE = "0";
ACTION_PARTY_KILL = "杀死了";
ACTION_PARTY_KILL_FULL_TEXT = "%1$s杀死了%4$s！";
ACTION_PARTY_KILL_POSSESSIVE = "0";
ACTION_RANGED = "射击";
ACTION_RANGE_DAMAGE = "命中";
ACTION_RANGE_DAMAGE_FULL_TEXT = "%1$s远程射击命中%4$s造成了%5$s伤害。%6$s";
ACTION_RANGE_DAMAGE_FULL_TEXT_NO_SOURCE = "一次远程射击命中%4$s造成了%5$s伤害。%6$s";
ACTION_RANGE_DAMAGE_POSSESSIVE = "1";
ACTION_RANGE_MISSED = "未命中";
ACTION_RANGE_MISSED_ABSORB = "（吸收）";
ACTION_RANGE_MISSED_ABSORB_FULL_TEXT = "%1$s射击被%4$s吸收了。%6$s";
ACTION_RANGE_MISSED_ABSORB_POSSESSIVE = "1";
ACTION_RANGE_MISSED_BLOCK = "（格挡）";
ACTION_RANGE_MISSED_BLOCK_FULL_TEXT = "%1$s射击被%4$s格挡了。%6$s";
ACTION_RANGE_MISSED_BLOCK_POSSESSIVE = "1";
ACTION_RANGE_MISSED_DEFLECT = "（偏转）";
ACTION_RANGE_MISSED_DEFLECT_FULL_TEXT = "%1$s射击被%4$s偏转了。";
ACTION_RANGE_MISSED_DEFLECT_POSSESSIVE = "1";
ACTION_RANGE_MISSED_DODGE = "（躲闪）";
ACTION_RANGE_MISSED_DODGE_FULL_TEXT = "%1$s射击被%4$s躲闪了。";
ACTION_RANGE_MISSED_DODGE_POSSESSIVE = "1";
ACTION_RANGE_MISSED_EVADE = "（闪避）";
ACTION_RANGE_MISSED_EVADE_FULL_TEXT = "%1$s射击被%4$s闪避了。";
ACTION_RANGE_MISSED_EVADE_POSSESSIVE = "1";
ACTION_RANGE_MISSED_FULL_TEXT = "%1$s射击未命中%4$s。";
ACTION_RANGE_MISSED_IMMUNE = "（免疫）";
ACTION_RANGE_MISSED_IMMUNE_FULL_TEXT = "%1$s射击失败。%4$s对此免疫。";
ACTION_RANGE_MISSED_IMMUNE_POSSESSIVE = "1";
ACTION_RANGE_MISSED_MISS = "（未命中）";
ACTION_RANGE_MISSED_MISS_FULL_TEXT = "%1$s射击未命中%4$s。";
ACTION_RANGE_MISSED_MISS_POSSESSIVE = "1";
ACTION_RANGE_MISSED_PARRY = "（招架）";
ACTION_RANGE_MISSED_PARRY_FULL_TEXT = "%1$s射击被%4$s招架了。";
ACTION_RANGE_MISSED_PARRY_POSSESSIVE = "1";
ACTION_RANGE_MISSED_POSSESSIVE = "1";
ACTION_RANGE_MISSED_RESIST = "（抵抗）";
ACTION_RANGE_MISSED_RESIST_FULL_TEXT = "%1$s射击被%4$s完全抵抗了。%6$s";
ACTION_RANGE_MISSED_RESIST_POSSESSIVE = "1";
ACTION_SPELL_AURA_APPLIED = "附加";
ACTION_SPELL_AURA_APPLIED_BUFF = "获得";
ACTION_SPELL_AURA_APPLIED_BUFF_FULL_TEXT = "%4$s获得了%1$s%2$s。";
ACTION_SPELL_AURA_APPLIED_BUFF_FULL_TEXT_NO_SOURCE = "%4$s获得了%2$s。";
ACTION_SPELL_AURA_APPLIED_BUFF_MASTER = "2";
ACTION_SPELL_AURA_APPLIED_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_APPLIED_DEBUFF = "影响";
ACTION_SPELL_AURA_APPLIED_DEBUFF_FULL_TEXT = "%4$s受到了%1$s%2$s的影响。";
ACTION_SPELL_AURA_APPLIED_DEBUFF_FULL_TEXT_NO_SOURCE = "%4$s受到了%2$s的影响。";
ACTION_SPELL_AURA_APPLIED_DEBUFF_MASTER = "1";
ACTION_SPELL_AURA_APPLIED_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_APPLIED_DOSE = "堆叠";
ACTION_SPELL_AURA_APPLIED_DOSE_BUFF = "堆叠";
ACTION_SPELL_AURA_APPLIED_DOSE_BUFF_FULL_TEXT = "%4$s获得了%1$s%2$s（%9$s）。";
ACTION_SPELL_AURA_APPLIED_DOSE_BUFF_FULL_TEXT_NO_SOURCE = "%4$s获得了%2$s（%9$s）。";
ACTION_SPELL_AURA_APPLIED_DOSE_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_APPLIED_DOSE_DEBUFF = "影响";
ACTION_SPELL_AURA_APPLIED_DOSE_DEBUFF_FULL_TEXT = "%4$s受到了%1$s%2$s的影响（%9$s）。";
ACTION_SPELL_AURA_APPLIED_DOSE_DEBUFF_FULL_TEXT_NO_SOURCE = "%4$s受到了%2$s的影响（%9$s）。";
ACTION_SPELL_AURA_APPLIED_DOSE_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_APPLIED_MASTER = "2";
ACTION_SPELL_AURA_APPLIED_POSSESSIVE = "1";
ACTION_SPELL_AURA_BROKEN = "打破";
ACTION_SPELL_AURA_BROKEN_BUFF = "打破";
ACTION_SPELL_AURA_BROKEN_BUFF_FULL_TEXT = "%1$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_BUFF_FULL_TEXT_NO_SOURCE = "%1$s%2$s被某目标打破了。";
ACTION_SPELL_AURA_BROKEN_BUFF_POSSESSIVE = "0";
ACTION_SPELL_AURA_BROKEN_DEBUFF = "打破";
ACTION_SPELL_AURA_BROKEN_DEBUFF_FULL_TEXT = "%1$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_DEBUFF_FULL_TEXT_NO_SOURCE = "%1$s%2$s被某目标打破了。";
ACTION_SPELL_AURA_BROKEN_DEBUFF_POSSESSIVE = "0";
ACTION_SPELL_AURA_BROKEN_SPELL_BUFF = "打破";
ACTION_SPELL_AURA_BROKEN_SPELL_BUFF_FULL_TEXT = "%1$s的%5$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_SPELL_BUFF_FULL_TEXT_NO_SOURCE = "%5$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_SPELL_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_BROKEN_SPELL_DEBUFF = "打破";
ACTION_SPELL_AURA_BROKEN_SPELL_DEBUFF_FULL_TEXT = "%1$s的%5$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_SPELL_DEBUFF_FULL_TEXT_NO_SOURCE = "%5$s打破了%4$s%2$s。";
ACTION_SPELL_AURA_BROKEN_SPELL_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REFRESH = "被刷新";
ACTION_SPELL_AURA_REFRESH_BUFF = "被刷新";
ACTION_SPELL_AURA_REFRESH_BUFF_FULL_TEXT = "%1$s%2$s在%4$s身上刷新了。";
ACTION_SPELL_AURA_REFRESH_BUFF_FULL_TEXT_NO_SOURCE = "%2$s在%4$s身上刷新了。";
ACTION_SPELL_AURA_REFRESH_BUFF_MASTER = "2";
ACTION_SPELL_AURA_REFRESH_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REFRESH_DEBUFF = "被刷新";
ACTION_SPELL_AURA_REFRESH_DEBUFF_FULL_TEXT = "%1$s%2$s在%4$s身上刷新了。";
ACTION_SPELL_AURA_REFRESH_DEBUFF_FULL_TEXT_NO_SOURCE = "%2$s在%4$s身上刷新了。";
ACTION_SPELL_AURA_REFRESH_DEBUFF_MASTER = "1";
ACTION_SPELL_AURA_REFRESH_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REFRESH_MASTER = "2";
ACTION_SPELL_AURA_REMOVED = "移除";
ACTION_SPELL_AURA_REMOVED_BUFF = "消失";
ACTION_SPELL_AURA_REMOVED_BUFF_FULL_TEXT = "%1$s%2$s从%4$s身上消失了。";
ACTION_SPELL_AURA_REMOVED_BUFF_FULL_TEXT_NO_SOURCE = "%2$s从%4$s身上消失了。";
ACTION_SPELL_AURA_REMOVED_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REMOVED_DEBUFF = "消散";
ACTION_SPELL_AURA_REMOVED_DEBUFF_FULL_TEXT = "%1$s%2$s从%4$s身上消散了。";
ACTION_SPELL_AURA_REMOVED_DEBUFF_FULL_TEXT_NO_SOURCE = "%2$s从%4$s身上消散了。";
ACTION_SPELL_AURA_REMOVED_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REMOVED_DOSE = "降低";
ACTION_SPELL_AURA_REMOVED_DOSE_BUFF = "降低";
ACTION_SPELL_AURA_REMOVED_DOSE_BUFF_FULL_TEXT = "%1$s%2$s（%9$s）衰减了。";
ACTION_SPELL_AURA_REMOVED_DOSE_BUFF_FULL_TEXT_NO_SOURCE = "%2$s（%9$s）衰减了。";
ACTION_SPELL_AURA_REMOVED_DOSE_BUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REMOVED_DOSE_DEBUFF = "衰减";
ACTION_SPELL_AURA_REMOVED_DOSE_DEBUFF_FULL_TEXT = "%1$s%2$s（%9$s）消退了。";
ACTION_SPELL_AURA_REMOVED_DOSE_DEBUFF_FULL_TEXT_NO_SOURCE = "%2$s（%9$s）消退了。";
ACTION_SPELL_AURA_REMOVED_DOSE_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_AURA_REMOVED_FULL_TEXT = "%1$s%2$s被从%4$s身上移除了。";
ACTION_SPELL_AURA_REMOVED_FULL_TEXT_NO_SOURCE = "%1$s%2$s被从%4$s身上移除了。";
ACTION_SPELL_AURA_REMOVED_POSSESSIVE = "1";
ACTION_SPELL_BUILDING_DAMAGE = "打击";
ACTION_SPELL_BUILDING_DAMAGE_FULL_TEXT = "%1$s%2$s打击%4$s造成了%5$s伤害。%6$s";
ACTION_SPELL_BUILDING_DAMAGE_FULL_TEXT_NO_SOURCE = "%2$s打击%4$s造成了%5$s伤害。%6$s";
ACTION_SPELL_BUILDING_DAMAGE_MASTER = "1";
ACTION_SPELL_BUILDING_DAMAGE_POSSESSIVE = "1";
ACTION_SPELL_BUILDING_HEAL = "修理";
ACTION_SPELL_BUILDING_HEAL_FULL_TEXT = "%1$s%2$s修理%4$s，恢复了%9$s点生命值。%6$s";
ACTION_SPELL_BUILDING_HEAL_FULL_TEXT_NO_SOURCE = "%2$s修理%4$s，恢复了%9$s点生命值。%6$s";
ACTION_SPELL_BUILDING_HEAL_POSSESSIVE = "1";
ACTION_SPELL_CAST_FAILED = "失败";
ACTION_SPELL_CAST_FAILED_FULL_TEXT = "%1$s%2$s未能施放。%6$s";
ACTION_SPELL_CAST_FAILED_MASTER = "1";
ACTION_SPELL_CAST_FAILED_POSSESSIVE = "1";
ACTION_SPELL_CAST_START = "开始施放";
ACTION_SPELL_CAST_START_FULL_TEXT = "某目标开始向%4$s施放%2$s。";
ACTION_SPELL_CAST_START_FULL_TEXT_NO_DEST = "%1$s开始施放%2$s。";
ACTION_SPELL_CAST_START_FULL_TEXT_NO_SOURCE = "%1$s开始对%4$s施放%2$s。";
ACTION_SPELL_CAST_START_MASTER = "2";
ACTION_SPELL_CAST_START_POSSESSIVE = "0";
ACTION_SPELL_CAST_SUCCESS = "施放";
ACTION_SPELL_CAST_SUCCESS_FULL_TEXT = "%1$s向%4$s施放了%2$s。";
ACTION_SPELL_CAST_SUCCESS_FULL_TEXT_NO_DEST = "%1$s施放了%2$s。";
ACTION_SPELL_CAST_SUCCESS_FULL_TEXT_NO_SOURCE = "某目标对%4$s施放了%2$s。";
ACTION_SPELL_CAST_SUCCESS_MASTER = "2";
ACTION_SPELL_CAST_SUCCESS_POSSESSIVE = "0";
ACTION_SPELL_CREATE = "制造";
ACTION_SPELL_CREATE_FULL_TEXT = "%1$s%2$s制造了一个%4$s。%6$s";
ACTION_SPELL_CREATE_FULL_TEXT_NO_SOURCE = "%2$s制造了一个%4$s。%6$s";
ACTION_SPELL_CREATE_POSSESSIVE = "1";
ACTION_SPELL_DAMAGE = "命中";
ACTION_SPELL_DAMAGE_FULL_TEXT = "%1$s%2$s命中%4$s造成%5$s伤害。%6$s";
ACTION_SPELL_DAMAGE_FULL_TEXT_NO_SOURCE = "%2$s命中%4$s，造成%5$s点伤害。%6$s";
ACTION_SPELL_DAMAGE_MASTER = "1";
ACTION_SPELL_DAMAGE_POSSESSIVE = "1";
ACTION_SPELL_DISPEL = "驱散";
ACTION_SPELL_DISPEL_BUFF = "驱散";
ACTION_SPELL_DISPEL_BUFF_FULL_TEXT = "%4$s%5$s被%1$s%2$s驱散了。";
ACTION_SPELL_DISPEL_BUFF_FULL_TEXT_NO_SOURCE = "%4$s%5$s被%2$s驱散了。";
ACTION_SPELL_DISPEL_BUFF_POSSESSIVE = "1";
ACTION_SPELL_DISPEL_DEBUFF = "净化";
ACTION_SPELL_DISPEL_DEBUFF_FULL_TEXT = "%4$s%5$s被%1$s%2$s净化了。";
ACTION_SPELL_DISPEL_DEBUFF_FULL_TEXT_NO_SOURCE = "%4$s%5$s被%2$s净化了。";
ACTION_SPELL_DISPEL_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_DISPEL_FAILED = "驱散失败";
ACTION_SPELL_DISPEL_FAILED_FULL_TEXT = "%1$s%2$s未能驱散%4$s%5$s。";
ACTION_SPELL_DISPEL_FAILED_FULL_TEXT_NO_SOURCE = "%2$s未能驱散%4$s%5$s。";
ACTION_SPELL_DISPEL_FAILED_POSSESSIVE = "1";
ACTION_SPELL_DISPEL_POSSESSIVE = "1";
ACTION_SPELL_DRAIN = "吸取";
ACTION_SPELL_DRAIN_FULL_TEXT = "%1$s%2$s从%4$s身上吸取了%9$s点%8$s。";
ACTION_SPELL_DRAIN_FULL_TEXT_NO_SOURCE = " %2$s从%4$s身上吸取了%9$s点%8$s。";
ACTION_SPELL_DRAIN_POSSESSIVE = "1";
ACTION_SPELL_DURABILITY_DAMAGE = "耐久度下降";
ACTION_SPELL_DURABILITY_DAMAGE_ALL = "完全耐久度下降";
ACTION_SPELL_DURABILITY_DAMAGE_ALL_FULL_TEXT = "%1$s%2$s对%4$s造成伤害：所有装备受损。";
ACTION_SPELL_DURABILITY_DAMAGE_ALL_POSSESSIVE = "1";
ACTION_SPELL_DURABILITY_DAMAGE_FULL_TEXT = "%1$s%2$s对%4$s造成伤害：$item受损。";
ACTION_SPELL_DURABILITY_DAMAGE_POSSESSIVE = "1";
ACTION_SPELL_ENERGIZE = "补充";
ACTION_SPELL_ENERGIZE_FULL_TEXT = "%4$s因%1$s%2$s获得了%9$s点%8$s。";
ACTION_SPELL_ENERGIZE_FULL_TEXT_NO_SOURCE = "%4$s因%2$s获得了%9$s点%8$s。";
ACTION_SPELL_ENERGIZE_POSSESSIVE = "1";
ACTION_SPELL_ENERGIZE_RESULT = "获得%9$s点%8$s";
ACTION_SPELL_EXTRA_ATTACKS = "获得额外攻击";
ACTION_SPELL_EXTRA_ATTACKS_FULL_TEXT = "%1$s通过%2$s获得了%9$s次额外攻击。";
ACTION_SPELL_EXTRA_ATTACKS_FULL_TEXT_NO_SOURCE = "通过%2$s获得%9$s次额外攻击。";
ACTION_SPELL_EXTRA_ATTACKS_POSSESSIVE = "1";
ACTION_SPELL_HEAL = "治疗";
ACTION_SPELL_HEAL_FULL_TEXT = "%1$s%2$s为%4$s恢复了%9$s点生命值。%6$s";
ACTION_SPELL_HEAL_FULL_TEXT_NO_SOURCE = "%2$s为%4$s恢复了%9$s点生命值。%6$s";
ACTION_SPELL_HEAL_POSSESSIVE = "1";
ACTION_SPELL_INSTAKILL = "杀死了";
ACTION_SPELL_INSTAKILL_FULL_TEXT = "%1$s%2$s杀死了%4$s。";
ACTION_SPELL_INSTAKILL_FULL_TEXT_NO_SOURCE = "%2$s杀死了%4$s。";
ACTION_SPELL_INSTAKILL_POSSESSIVE = "1";
ACTION_SPELL_INTERRUPT = "打断";
ACTION_SPELL_INTERRUPT_FULL_TEXT = "%1$s%2$s打断了%4$s%5$s。";
ACTION_SPELL_INTERRUPT_FULL_TEXT_NO_SOURCE = "%2$s打断了%4$s%5$s。";
ACTION_SPELL_INTERRUPT_POSSESSIVE = "1";
ACTION_SPELL_LEECH = "吸取";
ACTION_SPELL_LEECH_FULL_TEXT = "%1$s%2$s从%4$s身上吸取了%9$s点%8$s。%1$s获得了%10$s点%8$s。";
ACTION_SPELL_LEECH_FULL_TEXT_NO_SOURCE = "%2$s从%4$s身上吸取了%9$s点%8$s。";
ACTION_SPELL_LEECH_POSSESSIVE = "1";
ACTION_SPELL_LEECH_RESULT = "获得%10$s点%8$s";
ACTION_SPELL_MISSED = "未命中";
ACTION_SPELL_MISSED_ABSORB = "被吸收";
ACTION_SPELL_MISSED_ABSORB_FULL_TEXT = "%1$s%2$s被%4$s吸收了。%6$s";
ACTION_SPELL_MISSED_ABSORB_FULL_TEXT_NO_SOURCE = "%2$s被%4$s吸收了。%6$s";
ACTION_SPELL_MISSED_ABSORB_POSSESSIVE = "1";
ACTION_SPELL_MISSED_BLOCK = "被格挡";
ACTION_SPELL_MISSED_BLOCK_FULL_TEXT = "%1$s%2$s被%4$s格挡了。%6$s";
ACTION_SPELL_MISSED_BLOCK_FULL_TEXT_NO_SOURCE = "%2$s被%4$s格挡了。%6$s";
ACTION_SPELL_MISSED_BLOCK_POSSESSIVE = "1";
ACTION_SPELL_MISSED_DEFLECT = "偏转";
ACTION_SPELL_MISSED_DEFLECT_FULL_TEXT = "%1$s%2$s被%4$s偏转了。";
ACTION_SPELL_MISSED_DEFLECT_FULL_TEXT_NO_SOURCE = "%2$s被%4$s偏转了。";
ACTION_SPELL_MISSED_DEFLECT_POSSESSIVE = "1";
ACTION_SPELL_MISSED_DODGE = "被躲闪";
ACTION_SPELL_MISSED_DODGE_FULL_TEXT = "%1$s%2$s被%4$s躲闪了。";
ACTION_SPELL_MISSED_DODGE_FULL_TEXT_NO_SOURCE = "%2$s被%4$s躲闪了。";
ACTION_SPELL_MISSED_DODGE_POSSESSIVE = "1";
ACTION_SPELL_MISSED_EVADE = "被闪避";
ACTION_SPELL_MISSED_EVADE_FULL_TEXT = "%1$s%2$s被%4$s闪避了。";
ACTION_SPELL_MISSED_EVADE_FULL_TEXT_NO_SOURCE = "%2$s被%4$s闪避了。";
ACTION_SPELL_MISSED_EVADE_POSSESSIVE = "1";
ACTION_SPELL_MISSED_FULL_TEXT = "%1$s%2$s未命中%4$s。";
ACTION_SPELL_MISSED_FULL_TEXT_NO_SOURCE = "%2$s未命中%4$s。";
ACTION_SPELL_MISSED_IMMUNE = "免疫";
ACTION_SPELL_MISSED_IMMUNE_FULL_TEXT = "%1$s%2$s失败。%4$s对此免疫。";
ACTION_SPELL_MISSED_IMMUNE_FULL_TEXT_NO_SOURCE = "%2$s失败。%4$s对此免疫。";
ACTION_SPELL_MISSED_MISS = "未命中";
ACTION_SPELL_MISSED_MISS_FULL_TEXT = "%1$s%2$s未命中%4$s。";
ACTION_SPELL_MISSED_MISS_FULL_TEXT_NO_SOURCE = "%2$s未命中%4$s。";
ACTION_SPELL_MISSED_MISS_POSSESSIVE = "1";
ACTION_SPELL_MISSED_PARRY = "被招架";
ACTION_SPELL_MISSED_PARRY_FULL_TEXT = "%1$s%2$s被%4$s招架了。";
ACTION_SPELL_MISSED_PARRY_FULL_TEXT_NO_SOURCE = "%2$s被%4$s招架了。";
ACTION_SPELL_MISSED_PARRY_POSSESSIVE = "1";
ACTION_SPELL_MISSED_POSSESSIVE = "1";
ACTION_SPELL_MISSED_REFLECT = "被反射";
ACTION_SPELL_MISSED_REFLECT_FULL_TEXT = "%1$s%2$s被%4$s反射了。";
ACTION_SPELL_MISSED_REFLECT_FULL_TEXT_NO_SOURCE = "%2$s被%4$s反射了。";
ACTION_SPELL_MISSED_RESIST = "被抵抗";
ACTION_SPELL_MISSED_RESIST_FULL_TEXT = "%1$s%2$s被%4$s完全抵抗了。%6$s";
ACTION_SPELL_MISSED_RESIST_FULL_TEXT_NO_SOURCE = "%2$s被%4$s完全抵抗了。%6$s";
ACTION_SPELL_MISSED_RESIST_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_DAMAGE = "伤害";
ACTION_SPELL_PERIODIC_DAMAGE_FULL_TEXT = "%4$s因%1$s%2$s受到了%5$s伤害。%6$s";
ACTION_SPELL_PERIODIC_DAMAGE_FULL_TEXT_NO_SOURCE = "%4$s因%2$s受到了%5$s伤害。%6$s";
ACTION_SPELL_PERIODIC_DAMAGE_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_DRAIN = "吸取";
ACTION_SPELL_PERIODIC_DRAIN_FULL_TEXT = "%1$s%2$s从%4$s身上吸取了%9$s点%8$s。";
ACTION_SPELL_PERIODIC_DRAIN_FULL_TEXT_NO_SOURCE = "%2$s从%4$s身上吸取了%9$s点%8$s。";
ACTION_SPELL_PERIODIC_DRAIN_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_ENERGIZE = "补充";
ACTION_SPELL_PERIODIC_ENERGIZE_FULL_TEXT = "%4$s因%1$s%2$s获得了%9$s点%8$s。";
ACTION_SPELL_PERIODIC_ENERGIZE_FULL_TEXT_NO_SOURCE = "%4$s因%2$s获得了%9$s点%8$s。";
ACTION_SPELL_PERIODIC_ENERGIZE_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_ENERGIZE_RESULT = "获得$extraAmount点%8$s";
ACTION_SPELL_PERIODIC_HEAL = "治疗";
ACTION_SPELL_PERIODIC_HEAL_FULL_TEXT = "%4$s因%1$s%2$s获得了%9$s点生命值。%6$s";
ACTION_SPELL_PERIODIC_HEAL_FULL_TEXT_NO_SOURCE = "%4$s因%2$s恢复了%9$s点生命值。%6$s";
ACTION_SPELL_PERIODIC_HEAL_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_LEECH = "吸取";
ACTION_SPELL_PERIODIC_LEECH_FULL_TEXT = "%1$s%2$s从%4$s身上吸取了%9$s点%8$s。%1$s获得了%10$s点%8$s。";
ACTION_SPELL_PERIODIC_LEECH_FULL_TEXT_NO_SOURCE = "%2$s从%4$s身上吸取了%9$s点%8$s。%1$s获得了%10$s点%8$s。";
ACTION_SPELL_PERIODIC_LEECH_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_LEECH_RESULT = "（获得%10$s点%8$s）";
ACTION_SPELL_PERIODIC_MISSED = "未命中";
ACTION_SPELL_PERIODIC_MISSED_ABSORB = "（%1$s 吸收）";
ACTION_SPELL_PERIODIC_MISSED_ABSORB_FULL_TEXT = "%1$s%2$s被%4$s吸收了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_ABSORB_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s吸收了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_ABSORB_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_BLOCK = "（周期伤害被格挡）";
ACTION_SPELL_PERIODIC_MISSED_BLOCK_FULL_TEXT = "%1$s%2$s暂时被%4$s格挡了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_BLOCK_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s格挡了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_BLOCK_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_DEFLECTED = "（周期伤害被偏转）";
ACTION_SPELL_PERIODIC_MISSED_DEFLECTED_FULL_TEXT = "%1$s%2$s暂时被%4$s偏转了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_DEFLECTED_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s偏转了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_DEFLECTED_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_DODGE = "（周期伤害被躲闪）";
ACTION_SPELL_PERIODIC_MISSED_DODGE_FULL_TEXT = "%1$s%2$s暂时被%4$s躲闪了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_DODGE_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s躲闪了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_DODGE_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_EVADED = "（周期伤害被闪避）";
ACTION_SPELL_PERIODIC_MISSED_EVADED_FULL_TEXT = "%1$s%2$s暂时被%4$s闪避了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_EVADED_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s闪避了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_EVADED_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_FULL_TEXT = "%1$s%2$s暂时未能对%4$s产生影响。%6$s";
ACTION_SPELL_PERIODIC_MISSED_FULL_TEXT_NO_SOURCE = "%2$s暂时未能影响%4$s。%6$s";
ACTION_SPELL_PERIODIC_MISSED_IMMUNE = "（免疫）";
ACTION_SPELL_PERIODIC_MISSED_IMMUNE_FULL_TEXT = "%4$s对%1$s %2$s免疫。%6$s";
ACTION_SPELL_PERIODIC_MISSED_IMMUNE_FULL_TEXT_NO_SOURCE = "%4$s对%2$s免疫。%6$s";
ACTION_SPELL_PERIODIC_MISSED_IMMUNE_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_MISS = "（周期伤害未命中）";
ACTION_SPELL_PERIODIC_MISSED_MISS_FULL_TEXT = "%1$s%2$s暂时未命中%4$s。%6$s";
ACTION_SPELL_PERIODIC_MISSED_MISS_FULL_TEXT_NO_SOURCE = "%2$s暂时未命中%4$s。%6$s";
ACTION_SPELL_PERIODIC_MISSED_MISS_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_PARRY = "（周期伤害被招架）";
ACTION_SPELL_PERIODIC_MISSED_PARRY_FULL_TEXT = "%1$s%2$s暂时被%4$s招架了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_PARRY_FULL_TEXT_NO_SOURCE = "%2$s暂时被%4$s招架了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_PARRY_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_POSSESSIVE = "1";
ACTION_SPELL_PERIODIC_MISSED_RESIST = "（周期伤害被抵抗）";
ACTION_SPELL_PERIODIC_MISSED_RESIST_FULL_TEXT = "%1$s%2$s未能对%4$s产生影响。%4$s抵抗了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_RESIST_FULL_TEXT_NO_SOURCE = "%2$s未能影响%4$s。%4$s抵抗了。%6$s";
ACTION_SPELL_PERIODIC_MISSED_RESIST_POSSESSIVE = "1";
ACTION_SPELL_RESURRECT = "复活";
ACTION_SPELL_RESURRECT_FULL_TEXT = "%1$s%2$s复活了%4$s。%6$s";
ACTION_SPELL_RESURRECT_FULL_TEXT_NO_SOURCE = "%2$s复活了%4$s。%6$s";
ACTION_SPELL_RESURRECT_POSSESSIVE = "1";
ACTION_SPELL_STOLEN = "偷取";
ACTION_SPELL_STOLEN_BUFF = "偷取";
ACTION_SPELL_STOLEN_BUFF_FULL_TEXT = "%1$s%2$s偷取了%4$s%5$s。";
ACTION_SPELL_STOLEN_BUFF_FULL_TEXT_NO_SOURCE = "%2$s偷取了%4$s%5$s。";
ACTION_SPELL_STOLEN_BUFF_POSSESSIVE = "1";
ACTION_SPELL_STOLEN_BUFF__POSSESSIVE = "1";
ACTION_SPELL_STOLEN_DEBUFF = "偷取";
ACTION_SPELL_STOLEN_DEBUFF_FULL_TEXT = "%1$s%2$s将%4$s$extraSpell转移给了%1$s。";
ACTION_SPELL_STOLEN_DEBUFF_FULL_TEXT_NO_SOURCE = "%2$s转移了%4$s$extraSpell。";
ACTION_SPELL_STOLEN_DEBUFF_POSSESSIVE = "1";
ACTION_SPELL_STOLEN_FULL_TEXT = "%1$s%2$s偷取了%4$s%5$s。";
ACTION_SPELL_STOLEN_FULL_TEXT_NO_SOURCE = "%2$s偷取了%4$s%5$s。";
ACTION_SPELL_STOLEN_POSSESSIVE = "1";
ACTION_SPELL_SUMMON = "召唤";
ACTION_SPELL_SUMMON_FULL_TEXT = "%1$s%2$s召唤了%4$s。%6$s";
ACTION_SPELL_SUMMON_FULL_TEXT_NO_SOURCE = "%2$s召唤了%4$s。%6$s";
ACTION_SPELL_SUMMON_POSSESSIVE = "1";
ACTION_SWING = "近战攻击";
ACTION_SWING_DAMAGE = "命中";
ACTION_SWING_DAMAGE_FULL_TEXT = "%1$s近战攻击命中%4$s造成了%5$s伤害。%6$s";
ACTION_SWING_DAMAGE_FULL_TEXT_NO_SOURCE = "一次近战攻击命中%4$s造成了%5$s伤害。%6$s";
ACTION_SWING_DAMAGE_POSSESSIVE = "1";
ACTION_SWING_MISSED = "未命中";
ACTION_SWING_MISSED_ABSORB = "（吸收）";
ACTION_SWING_MISSED_ABSORB_FULL_TEXT = "%1$s攻击被%4$s吸收了。%6$s";
ACTION_SWING_MISSED_ABSORB_POSSESSIVE = "1";
ACTION_SWING_MISSED_BLOCK = "（格挡）";
ACTION_SWING_MISSED_BLOCK_FULL_TEXT = "%1$s攻击被%4$s格挡了。%6$s";
ACTION_SWING_MISSED_BLOCK_POSSESSIVE = "1";
ACTION_SWING_MISSED_DEFLECT = "（偏转）";
ACTION_SWING_MISSED_DEFLECT_FULL_TEXT = "%1$s攻击被%4$s偏转了。";
ACTION_SWING_MISSED_DEFLECT_POSSESSIVE = "1";
ACTION_SWING_MISSED_DODGE = "（躲闪）";
ACTION_SWING_MISSED_DODGE_FULL_TEXT = "%1$s攻击被%4$s躲闪了。";
ACTION_SWING_MISSED_DODGE_POSSESSIVE = "1";
ACTION_SWING_MISSED_EVADE = "（闪避）";
ACTION_SWING_MISSED_EVADE_FULL_TEXT = "%1$s攻击被%4$s闪避了。";
ACTION_SWING_MISSED_EVADE_POSSESSIVE = "1";
ACTION_SWING_MISSED_FULL_TEXT = "%1$s攻击未命中%4$s。";
ACTION_SWING_MISSED_IMMUNE = "（免疫）";
ACTION_SWING_MISSED_IMMUNE_FULL_TEXT = "%1$s攻击失败。%4$s对此免疫。";
ACTION_SWING_MISSED_IMMUNE_POSSESSIVE = "1";
ACTION_SWING_MISSED_MISS = "（未命中）";
ACTION_SWING_MISSED_MISS_FULL_TEXT = "%1$s攻击未命中%4$s。";
ACTION_SWING_MISSED_MISS_POSSESSIVE = "1";
ACTION_SWING_MISSED_PARRY = "（招架）";
ACTION_SWING_MISSED_PARRY_FULL_TEXT = "%1$s攻击被%4$s招架了。";
ACTION_SWING_MISSED_PARRY_POSSESSIVE = "1";
ACTION_SWING_MISSED_POSSESSIVE = "1";
ACTION_SWING_MISSED_RESIST = "（抵抗）";
ACTION_SWING_MISSED_RESIST_FULL_TEXT = "%1$s攻击被%4$s完全抵抗了。%6$s";
ACTION_SWING_MISSED_RESIST_POSSESSIVE = "1";
ACTION_UNIT_DESTROYED = "摧毁";
ACTION_UNIT_DESTROYED_FULL_TEXT = "%4$s被摧毁了。";
ACTION_UNIT_DESTROYED_POSSESSIVE = "0";
ACTION_UNIT_DIED = "死亡";
ACTION_UNIT_DIED_FULL_TEXT = "%4$s死亡了。";
ACTION_UNIT_DIED_POSSESSIVE = "0";
ACTION_UNIT_DISSIPATES = "消散";
ACTION_UNIT_DISSIPATES_FULL_TEXT = "%4$s消散了。";
ACTION_UNIT_DISSIPATES_POSSESSIVE = "0";
ACTIVATE = "启用";
ADD = "添加";
ADDITIONAL_COMMENTS = "您有什么想要对我们提出的建议吗？我们如何可以使您的游戏体验更加愉快呢？";
ADDITIONAL_FILTERS = "更多过滤";
ADDMEMBER = "添加成员";
ADDMEMBER_TEAM = "添加成员";
ADDONS = "插件";
ADDON_ACTION_FORBIDDEN = "%s已被禁用，因为该功能只对暴雪的UI开放。\n你可以禁用这个插件并重新装载UI。";
ADDON_BANNED = "非法";
ADDON_CORRUPT = "损坏";
ADDON_DEMAND_LOADED = "只能按需加载";
ADDON_DEP_BANNED = "依赖功能非法";
ADDON_DEP_CORRUPT = "依赖功能损坏";
ADDON_DEP_DEMAND_LOADED = "依赖功能只能按需装载";
ADDON_DEP_DISABLED = "依赖功能被禁用";
ADDON_DEP_INCOMPATIBLE = "依赖功能不兼容";
ADDON_DEP_INSECURE = "依赖功能不安全";
ADDON_DEP_INTERFACE_VERSION = "依赖功能过期";
ADDON_DEP_MISSING = "依赖功能缺失";
ADDON_DISABLED = "禁用";
ADDON_INCOMPATIBLE = "不兼容";
ADDON_INSECURE = "不安全";
ADDON_INTERFACE_VERSION = "过期";
ADDON_LOAD_FAILED = "无法读取%s：%s";
ADDON_MEM_KB_ABBR = "(%.0f KB) %s";
ADDON_MEM_MB_ABBR = "(%.2f MB) %s";
ADDON_MISSING = "缺失";
ADDON_UNKNOWN_ERROR = "未知读取问题";
ADD_ANOTHER = "添加其他好友";
ADD_CHANNEL = "输入频道名称";
ADD_CHAT_CHANNEL = "增加一个频道";
ADD_FILTER = "添加过滤";
ADD_FRIEND = "添加好友";
ADD_FRIEND_LABEL = "输入想要添加的好友名字：";
ADD_GUILDMEMBER_LABEL = "添加公会成员：";
ADD_GUILDRANK_LABEL = "添加公会会阶：";
ADD_IGNORE_LABEL = "输入想要屏蔽的玩家名字\n或者在聊天窗口中\n按住Shift点击该玩家的名字：";
ADD_MUTE_LABEL = "输入玩家的名字以禁声\n或者\n按住Shift点击聊天窗口中的名字：";
ADD_RAIDMEMBER_LABEL = "增加团队成员：";
ADD_RAID_MEMBER = "添加成员";
ADD_TEAMMEMBER_LABEL = "添加战队成员：";
ADVANCED_OBJECTIVES_TEXT = "高级目标追踪";
ADVANCED_OPTIONS = "高级选项";
ADVANCED_OPTIONS_TOOLTIP = "配置高级界面选项";
ADVANCED_WATCHFRAME_OPTION_ENABLE_INTERRUPT = "禁用高级目标追踪会导致你的设置失效。你确定要禁用高级目标追踪吗？";
ADVANCED_WORLD_MAP_TEXT = "可移动的世界地图";
AFK = "离开";
AGGRO_WARNING_DISPLAY = "显示仇恨警报";
AGGRO_WARNING_IN_INSTANCE = "在副本中";
AGGRO_WARNING_IN_PARTY = "在小队中";
AGI = "Agi";
AGILITY_COLON = "敏捷：";
AGILITY_TOOLTIP = "提高所有角色的爆击几率。对潜行者|n的提升效果更高。敏捷还能提高躲避|n几率。潜行者从每一点敏捷获得的躲避加成比其它|n职业更多。敏捷直接提高护甲值。";
AIM_DOWN = "向下瞄准";
AIM_UP = "向上瞄准";
ALL = "全部";
ALLIED = "盟友";
ALL_BOSSES_ALIVE = "所有首领都已|cff20ff20刷新|r。";
ALL_INVENTORY_SLOTS = "所有空格";
ALL_SETTINGS = "所有设置";
ALL_SUBCLASSES = "所有分支";
ALREADY_BOUND = "|cffff0000与%s发生冲突！|r";
ALREADY_LEARNED = "已经学会";
ALT_KEY = "ALT键";
ALWAYS = "总是";
ALWAYS_SHOW_MULTIBARS_TEXT = "总是显示动作条";
AMBIENCE_VOLUME = "环境音";
AMMOSLOT = "弹药";
AMMO_DAMAGE_TEMPLATE = "每秒伤害提高%g";
AMMO_SCHOOL_DAMAGE_TEMPLATE = "每秒增加%g点%s系伤害";
AMOUNT_PAID_COLON = "付费金额：";
AMOUNT_RECEIVED_COLON = "收款金额：";
AMOUNT_TO_SEND = "寄送金额：";
ANIMATION = "动画";
ANISOTROPIC = "材质过滤";
APPEARANCE_LABEL = "画面表现";
APPEARANCE_SUBTEXT = "这些选项控制着游戏中各类物体和效果的显示范围和细节层次。";
APPLY = "应用";
AREA_SPIRIT_HEAL = "%d%s后复活";
ARENA = "竞技场";
ARENA_BANNER_VENDOR_GREETING = "恭喜！请选择你的竞技场战队的队标和颜色。";
ARENA_BATTLES = "竞技场战斗";
ARENA_CASUAL = "练习赛";
ARENA_CHARTER_PURCHASE = "购买战队登记表";
ARENA_CHARTER_TEMPLATE = "%s战队登记表";
ARENA_CHARTER_TURN_IN = "提交你的战队登记表";
ARENA_COMPLETE_MESSAGE = "战斗已结束。竞技场将在%s内关闭";
ARENA_MASTER_NO_SEASON_TEXT = "这么说来，你想为了胜利的荣耀而战？看来你得等一等了。上个赛季的精彩比赛结束之后，热砂集团正在进行短暂的休赛！下周再来看看吧！在此期间，你可以自由地练习，不过正式比赛还需要等一等。";
ARENA_MASTER_TEXT = "开始比赛吧！竞技场之门对所有想要获得荣耀的冒险者敞开，热砂集团欢迎大家加入比赛。如果胜利是你早已注定的命运，那么就赶快到竞技场管理员那里注册成立新的战队，或者加入一个已经成立的战队吧！";
ARENA_OFF_SEASON_TEXT = "竞技场第%d赛季已经结束了！|n|n在竞技场赛季结束后的一周内，所有头衔和奖励都将发放完毕。|n|n记得向竞技场组织者咨询第%d赛季的相关信息！";
ARENA_PETITION_LEADER_INSTRUCTIONS = "选择一位你想要邀请的玩家，然后点击<要求签名>。当你得到足够数量的签名之后，将这张登记表交给竞技场战队注册员即可创建战队。";
ARENA_PETITION_MEMBER_INSTRUCTIONS = "点击<签名>按钮以成为该竞技场战队的创建成员。";
ARENA_POINTS = "竞技场点数";
ARENA_PRACTICE_BATTLE = "练习赛：";
ARENA_RATED = "战斗";
ARENA_RATED_BATTLE = "积分赛：";
ARENA_RATED_MATCH = "积分赛";
ARENA_REGISTRAR_PURCHASE_TEXT = "要创建一支竞技场战队，你必须购买这张登记表，邀请足够数量的玩家在你的战队登记表上签字，然后把它交给我。首先，请输入你想要的战队名称。";
ARENA_SPECTATOR = "你正在观众模式下。要离开这场战斗，请右键点击微缩地图旁的竞技场图标，并选择“离开竞技场”。";
ARENA_TEAM = "竞技场战队";
ARENA_TEAM_2V2 = "2v2竞技场战队";
ARENA_TEAM_3V3 = "3v3竞技场战队";
ARENA_TEAM_5V5 = "5v5竞技场战队";
ARENA_TEAM_CAPTAIN = "竞技场战队队长";
ARENA_TEAM_INVITATION = "%s邀请你加入竞技场战队：%s";
ARENA_TEAM_LEAD_IN = "拜访竞技场管理员以创建新的竞技场战队。";
ARENA_TEAM_RATING = "战队等级";
ARENA_THIS_SEASON = "本赛季";
ARENA_THIS_SEASON_TOGGLE = "查看本赛季数据";
ARENA_THIS_WEEK = "本周";
ARENA_THIS_WEEK_TOGGLE = "查看本周成绩";
ARMOR = "护甲";
ARMOR_TEMPLATE = "%d点护甲";
ARMOR_TOOLTIP = "降低你在受到物理攻击时所承受的伤害，其效果与攻击者的等级有关。\n受到等级%d的敌人攻击时减免伤害：%.1f%%";
ASSEMBLING_GROUP = "队伍集结中……";
ASSIGNED_COLON = "当前指派：";
ASSIST_ATTACK = "协助攻击";
ATTACHMENT_TEXT = "将物品放在这里随邮件发送";
ATTACK = "攻击";
ATTACK_COLON = "攻击：";
ATTACK_POWER = "强度";
ATTACK_POWER_TOOLTIP = "攻击强度";
ATTACK_SPEED = "攻击速度";
ATTACK_SPEED_SECONDS = "攻击速度（秒）";
ATTACK_SPEED_TOOLTIP1 = "每次攻击间隔时间";
ATTACK_TOOLTIP = "攻击等级";
ATTACK_TOOLTIP_SUBTEXT = "攻击等级影响到你击中目标的几率，其数值由你对目前所装备的武器的使用熟练度决定。";
AT_WAR = "交战状态";
AUCTIONS = "拍卖";
AUCTION_BUYOUT_ERROR = "（必须高于或等于起价）";
AUCTION_CREATING = "创建中%d/%d";
AUCTION_CREATOR = "出售者";
AUCTION_DURATION = "持续时间";
AUCTION_DURATION_ERROR = "（拍卖时间将被设置为5分钟）";
AUCTION_DURATION_ONE = "12小时";
AUCTION_DURATION_THREE = "48小时";
AUCTION_DURATION_TWO = "24小时";
AUCTION_EXPIRED_MAIL_SUBJECT = "拍卖已到期：%s";
AUCTION_HOUSE_CUT_COLON = "拍卖费：";
AUCTION_INVOICE_FUNDS_DELAY = "预计投递时间%s";
AUCTION_INVOICE_FUNDS_NOT_YET_SENT = "未发送的数量";
AUCTION_INVOICE_MAIL_SUBJECT = "正在销售：%s";
AUCTION_INVOICE_PENDING_FUNDS_COLON = "等待发送的数量：";
AUCTION_ITEM = "拍卖物品";
AUCTION_ITEM_INCOMING_AMOUNT = "收入金额";
AUCTION_ITEM_SOLD = "%s - 已售出";
AUCTION_ITEM_TEXT = "将物品放在这里以供拍卖";
AUCTION_ITEM_TIME_UNTIL_DELIVERY = "%s后交付";
AUCTION_NUM_STACKS = "堆叠组数";
AUCTION_OUTBID_MAIL_SUBJECT = "竞标%s失败";
AUCTION_PRICE = "价格";
AUCTION_PRICE_PER_ITEM = "每个";
AUCTION_PRICE_PER_STACK = "每组";
AUCTION_REMOVED_MAIL_SUBJECT = "拍卖取消：%s";
AUCTION_SOLD_MAIL_SUBJECT = "拍卖成功：%s";
AUCTION_STACK_SIZE = "堆叠数量";
AUCTION_TIME_LEFT1 = "短";
AUCTION_TIME_LEFT1_DETAIL = "少于30分钟";
AUCTION_TIME_LEFT2 = "中";
AUCTION_TIME_LEFT2_DETAIL = "30分钟到2小时";
AUCTION_TIME_LEFT3 = "长";
AUCTION_TIME_LEFT3_DETAIL = "2小时到12小时";
AUCTION_TIME_LEFT4 = "非常长";
AUCTION_TIME_LEFT4_DETAIL = "大于12小时";
AUCTION_TITLE = "%s的拍卖";
AUCTION_TOOLTIP_BID_PREFIX = "单价：";
AUCTION_TOOLTIP_BUYOUT_PREFIX = "一口单价：";
AUCTION_WON_MAIL_SUBJECT = "竞拍获胜：%s";
AURAS = "光环";
AURAS_COMBATLOG_TOOLTIP = "显示有关增益和负面状态效果的信息。";
AURA_END = "<%s>效果消失";
AUTOFOLLOWSTART = "正在跟随%s";
AUTOFOLLOWSTOP = "已停止跟随%s。";
AUTOFOLLOWSTOPCOMBAT = "你停止移动，开始攻击%s";
AUTO_ADD_DISABLED_GROUPED_TOOLTIP = "如果你的队伍已满，或者你不是队长，你就不能自动向队伍中加入更多成员。";
AUTO_ADD_DISABLED_QUEUED_TOOLTIP = "如果你已经在寻求加入别的队伍，你就不能自动向队伍中加入更多成员。";
AUTO_ADD_MEMBERS = "自动添加队员";
AUTO_ADD_TOOLTIP = "自动将玩家加入你的队伍。只对地下城有效。";
AUTO_DISMOUNT_FLYING_TEXT = "自动取消飞行";
AUTO_FOLLOW_SPEED = "自动跟随速度";
AUTO_JOIN = "自动加入";
AUTO_JOIN_DISABLED_TOOLTIP = "如果你已经在自动招募更多队员加入你的队伍，你就不能自动加入别的队伍。";
AUTO_JOIN_GUILD_CHANNEL = "自动加入公会招募频道";
AUTO_JOIN_TOOLTIP = "自动将你加入一支符合你的搜索条件的队伍。只对地下城有效。";
AUTO_JOIN_VOICE = "自动加入语音聊天";
AUTO_LOOT_DEFAULT_TEXT = "自动拾取";
AUTO_LOOT_KEY_TEXT = "自动拾取按键";
AUTO_QUEST_PROGRESS_TEXT = "自动任务进展更新";
AUTO_QUEST_WATCH_TEXT = "自动任务追踪";
AUTO_RANGED_COMBAT_TEXT = "自动攻击/自动射击";
AUTO_SELF_CAST_KEY_TEXT = "自我施法";
AUTO_SELF_CAST_TEXT = "自动自我施法";
AVAILABLE = "可用";
AVAILABLE_QUESTS = "可以取得的任务";
AVAILABLE_SERVICES = "可以使用的服务";
AVERAGE_WAIT_TIME = "平均等待时间：";
A_RANDOM_DUNGEON = "一个随机地下城";
BACK = "后退";
BACKGROUND = "底色";
BACKPACK_TOOLTIP = "行囊";
BACKSLOT = "背部";
BAGSLOT = "背包";
BAGSLOTTEXT = "背包栏位";
BAGS_ONLY = "这里只能放置背包！";
BANKSLOTPURCHASE = "购买";
BANKSLOTPURCHASE_LABEL = "你想要再购买一个用来放置背包的栏位吗？";
BANK_BAG = "背包栏位";
BANK_BAG_PURCHASE = "可以购买的背包栏位";
BARBERSHOP = "理发店";
BASIC_OPTIONS_TOOLTIP = "配置基本界面选项";
BATTLEFIELDMINIMAP_OPACITY_LABEL = "改变不透明度";
BATTLEFIELDMINIMAP_OPTIONS_LABEL = "区域地图设置";
BATTLEFIELDS = "战场";
BATTLEFIELD_ALERT = "你可以进入%s了。剩余时间%s";
BATTLEFIELD_CONFIRM_STATUS = "准备进入";
BATTLEFIELD_FULL = "%s（已满）";
BATTLEFIELD_GROUP_JOIN = "小队加入";
BATTLEFIELD_IN_BATTLEFIELD = "你正在%s中\n|cffffffff<左键点击这里显示得分>\n<按住Shift点击这里显示战场地图>|r";
BATTLEFIELD_IN_QUEUE = "你正在等待%s的队列中\n平均等待时间：%s（前10个玩家）\n在队列中的时间：%s";
BATTLEFIELD_IN_QUEUE_SIMPLE = "你在%s的队列中";
BATTLEFIELD_JOIN = "加入战斗";
BATTLEFIELD_LEVEL = "等级范围：";
BATTLEFIELD_MINIMAP = "区域地图";
BATTLEFIELD_MINIMAP_SHOW_ALWAYS = "总是显示";
BATTLEFIELD_MINIMAP_SHOW_BATTLEGROUNDS = "在战场中显示";
BATTLEFIELD_MINIMAP_SHOW_NEVER = "从不显示";
BATTLEFIELD_NAME = "战场名称：";
BATTLEFIELD_QUEUE_CONFIRM = "你现在可以进入%s了\n你将在%s内离开队列";
BATTLEFIELD_QUEUE_CONFIRM_SIMPLE = "你可以进入%s";
BATTLEFIELD_QUEUE_PENDING_REMOVAL = "你可以进入%s了\n你将在短时间内离开队列。";
BATTLEFIELD_QUEUE_STATUS = "在队列中";
BATTLEGROUND = "战场";
BATTLEGROUNDS = "战场";
BATTLEGROUND_COMPLETE_MESSAGE = "战斗已结束。战场将在%s内关闭";
BATTLEGROUND_HOLIDAY = "战斗的召唤";
BATTLEGROUND_HOLIDAY_EXPLANATION = "我们急需你与我们并肩抗敌！完成该战场可使你获得额外奖励。";
BATTLEGROUND_INSTANCE = "副本";
BATTLEGROUND_INSTANCE_TOOLTIP = "当战场中有可用的空位时再加入战场。这样会比选择加入首先可用的战场的等待时间稍长。";
BATTLEGROUND_LEADER = "战场领袖";
BATTLEGROUND_MESSAGE = "战场分组";
BATTLEGROUND_REQUIRED_LEVEL_TOOLTIP = "需要达到%s级才能进入。";
BATTLEGROUND_SILENCE = "在战场中静默";
BATTLEGROUND_UNSILENCE = "解除战场静默";
BATTLENET_FRIEND = "实名";
BATTLENET_FRIEND_INFO = "这是你在现实中的好友。你能通过任何暴雪游戏与他聊天。你的好友邀请需要先经他接受，随后他的所有好友都将能够看到你的实名。";
BATTLENET_FRIEND_LABEL = "输入电子邮件地址";
BATTLENET_NAME_FORMAT = "%2$s%1$s";
BATTLENET_OPTIONS_LABEL = "战网|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
BATTLENET_OPTIONS_SUBTEXT = "这些选项可以使你改变接收到的战网信息的类型。";
BATTLENET_UNAVAILABLE = "战网不可用";
BATTLENET_UNAVAILABLE_ALERT = "战网服务暂时不可用。|n|n你的实名好友不会显示，你也无法发送或收到实名好友邀请。也许需要重启游戏以重新启用战网功能。";
BENCHMARK_TAXI_AVERAGE_FPS = "平均FPS %.3f";
BENCHMARK_TAXI_MAX_FPS = "最大FPS %.3f";
BENCHMARK_TAXI_MIN_FPS = "最低FPS %.3f";
BENCHMARK_TAXI_MODE_OFF = "Taxi Time测试关闭";
BENCHMARK_TAXI_MODE_ON = "Taxi Time测试开启";
BENCHMARK_TAXI_RESULTS = "Taxi Time测试得分：";
BENCHMARK_TAXI_TOTAL_TIME = "总时长 %.3f秒";
BENEFICIAL = "增益";
BENEFICIAL_AURA_COMBATLOG_TOOLTIP = "当你获得或失去有益光环时显示信息。";
BF_NOT_IN = "你不在一个战场中";
BG_SYSTEM_ALLIANCE = "战场联盟";
BG_SYSTEM_HORDE = "战场部落";
BG_SYSTEM_NEUTRAL = "战场中立";
BID = "竞标";
BIDS = "竞标";
BID_AUCTION_CONFIRMATION = "出价为：";
BID_STATUS = "竞标状态";
BILLING_NAG_DIALOG = "您的帐户中还有%d%s的剩余游戏时间";
BILLING_NAG_WARNING = "您的帐户中还有%d分钟的剩余游戏时间";
BINDING_HEADER_ACTIONBAR = "动作条按键";
BINDING_HEADER_BLANK = "";
BINDING_HEADER_CAMERA = "视角按键";
BINDING_HEADER_CHAT = "聊天按键";
BINDING_HEADER_INTERFACE = "界面面板按键";
BINDING_HEADER_ITUNES_REMOTE = "iTunes遥控";
BINDING_HEADER_MISC = "其他按键";
BINDING_HEADER_MOVEMENT = "移动按键";
BINDING_HEADER_MOVIE_RECORDING_SECTION = "视频录制";
BINDING_HEADER_MULTIACTIONBAR = "多动作条绑定";
BINDING_HEADER_MULTICASTFUNCTIONS = "萨满图腾栏功能";
BINDING_HEADER_RAID_TARGET = "团队目标";
BINDING_HEADER_TARGETING = "选择目标按键";
BINDING_HEADER_VEHICLE = "载具控制";
BINDING_HEADER_VOICE_CHAT = "语音聊天";
BINDING_NAME_ACTIONBUTTON1 = "快捷键1";
BINDING_NAME_ACTIONBUTTON10 = "快捷键10";
BINDING_NAME_ACTIONBUTTON11 = "快捷键11";
BINDING_NAME_ACTIONBUTTON12 = "快捷键12";
BINDING_NAME_ACTIONBUTTON2 = "快捷键2";
BINDING_NAME_ACTIONBUTTON3 = "快捷键3";
BINDING_NAME_ACTIONBUTTON4 = "快捷键4";
BINDING_NAME_ACTIONBUTTON5 = "快捷键5";
BINDING_NAME_ACTIONBUTTON6 = "快捷键6";
BINDING_NAME_ACTIONBUTTON7 = "快捷键7";
BINDING_NAME_ACTIONBUTTON8 = "快捷键8";
BINDING_NAME_ACTIONBUTTON9 = "快捷键9";
BINDING_NAME_ACTIONPAGE1 = "动作条1";
BINDING_NAME_ACTIONPAGE2 = "动作条2";
BINDING_NAME_ACTIONPAGE3 = "动作条3";
BINDING_NAME_ACTIONPAGE4 = "动作条4";
BINDING_NAME_ACTIONPAGE5 = "动作条5";
BINDING_NAME_ACTIONPAGE6 = "动作条6";
BINDING_NAME_ACTIONWINDOW1 = "移动动作条1";
BINDING_NAME_ACTIONWINDOW2 = "移动动作条2";
BINDING_NAME_ACTIONWINDOW3 = "移动动作条3";
BINDING_NAME_ACTIONWINDOW4 = "移动动作条4";
BINDING_NAME_ACTIONWINDOWDECREMENT = "将移动动作条滑向左边";
BINDING_NAME_ACTIONWINDOWINCREMENT = "将移动动作条滑向右边";
BINDING_NAME_ACTIONWINDOWMOVE = "改变移动动作条位置";
BINDING_NAME_ALLNAMEPLATES = "显示所有姓名板";
BINDING_NAME_ASSISTTARGET = "协助目标";
BINDING_NAME_ATTACKTARGET = "攻击目标";
BINDING_NAME_BONUSACTIONBUTTON1 = "副快捷键1";
BINDING_NAME_BONUSACTIONBUTTON10 = "副快捷键10";
BINDING_NAME_BONUSACTIONBUTTON2 = "副快捷键2";
BINDING_NAME_BONUSACTIONBUTTON3 = "副快捷键3";
BINDING_NAME_BONUSACTIONBUTTON4 = "副快捷键4";
BINDING_NAME_BONUSACTIONBUTTON5 = "副快捷键5";
BINDING_NAME_BONUSACTIONBUTTON6 = "副快捷键6";
BINDING_NAME_BONUSACTIONBUTTON7 = "副快捷键7";
BINDING_NAME_BONUSACTIONBUTTON8 = "副快捷键8";
BINDING_NAME_BONUSACTIONBUTTON9 = "副快捷键9";
BINDING_NAME_CAMERAZOOMIN = "放大";
BINDING_NAME_CAMERAZOOMOUT = "缩小";
BINDING_NAME_CHATBOTTOM = "翻至对话最下端";
BINDING_NAME_CHATPAGEDOWN = "对话向下翻页";
BINDING_NAME_CHATPAGEUP = "对话向上翻页";
BINDING_NAME_COMBATLOGBOTTOM = "战斗日志翻至最下端";
BINDING_NAME_COMBATLOGPAGEDOWN = "战斗日志向下翻页";
BINDING_NAME_COMBATLOGPAGEUP = "战斗日志向上翻页";
BINDING_NAME_DISMOUNT = "解散坐骑";
BINDING_NAME_FLIPCAMERAYAW = "水平视角";
BINDING_NAME_FOCUSTARGET = "焦点目标";
BINDING_NAME_FOLLOWTARGET = "跟随目标";
BINDING_NAME_FRIENDNAMEPLATES = "显示友方姓名板";
BINDING_NAME_INTERACTMOUSEOVER = "与鼠标悬停处互动";
BINDING_NAME_INTERACTTARGET = "与目标互动";
BINDING_NAME_INVERTBINDINGMODE1 = "动作条绑定模式变更";
BINDING_NAME_INVERTBINDINGMODE2 = "目标绑定模式变更";
BINDING_NAME_INVERTBINDINGMODE3 = "自定义绑定模式变更";
BINDING_NAME_ITUNES_BACKTRACK = "iTunes上一首";
BINDING_NAME_ITUNES_NEXTTRACK = "iTunes下一首";
BINDING_NAME_ITUNES_PLAYPAUSE = "iTunes播放/暂停";
BINDING_NAME_ITUNES_VOLUMEDOWN = "iTunes音量降低";
BINDING_NAME_ITUNES_VOLUMEUP = "iTunes音量提高";
BINDING_NAME_JUMP = "跳跃";
BINDING_NAME_MASTERVOLUMEDOWN = "主音量缩小";
BINDING_NAME_MASTERVOLUMEUP = "主音量放大";
BINDING_NAME_MINIMAPZOOMIN = "放大地图";
BINDING_NAME_MINIMAPZOOMOUT = "缩小地图";
BINDING_NAME_MOVEANDSTEER = "移动控制";
BINDING_NAME_MOVEBACKWARD = "后退";
BINDING_NAME_MOVEFORWARD = "前进";
BINDING_NAME_MOVEVIEWIN = "视角拉近";
BINDING_NAME_MOVEVIEWOUT = "视角拉远";
BINDING_NAME_MOVIE_RECORDING_CANCEL = "取消录制/压缩";
BINDING_NAME_MOVIE_RECORDING_COMPRESS = "压缩视频";
BINDING_NAME_MOVIE_RECORDING_GUI = "显示/隐藏用户界面";
BINDING_NAME_MOVIE_RECORDING_STARTSTOP = "开始录制/停止录制";
BINDING_NAME_MULTIACTIONBAR1BUTTON1 = "左下方快捷键1";
BINDING_NAME_MULTIACTIONBAR1BUTTON10 = "左下方快捷键10";
BINDING_NAME_MULTIACTIONBAR1BUTTON11 = "左下方快捷键11";
BINDING_NAME_MULTIACTIONBAR1BUTTON12 = "左下方快捷键12";
BINDING_NAME_MULTIACTIONBAR1BUTTON2 = "左下方快捷键2";
BINDING_NAME_MULTIACTIONBAR1BUTTON3 = "左下方快捷键3";
BINDING_NAME_MULTIACTIONBAR1BUTTON4 = "左下方快捷键4";
BINDING_NAME_MULTIACTIONBAR1BUTTON5 = "左下方快捷键5";
BINDING_NAME_MULTIACTIONBAR1BUTTON6 = "左下方快捷键6";
BINDING_NAME_MULTIACTIONBAR1BUTTON7 = "左下方快捷键7";
BINDING_NAME_MULTIACTIONBAR1BUTTON8 = "左下方快捷键8";
BINDING_NAME_MULTIACTIONBAR1BUTTON9 = "左下方快捷键9";
BINDING_NAME_MULTIACTIONBAR2BUTTON1 = "右下方快捷键1";
BINDING_NAME_MULTIACTIONBAR2BUTTON10 = "右下方快捷键10";
BINDING_NAME_MULTIACTIONBAR2BUTTON11 = "右下方快捷键11";
BINDING_NAME_MULTIACTIONBAR2BUTTON12 = "右下方快捷键12";
BINDING_NAME_MULTIACTIONBAR2BUTTON2 = "右下方快捷键2";
BINDING_NAME_MULTIACTIONBAR2BUTTON3 = "右下方快捷键3";
BINDING_NAME_MULTIACTIONBAR2BUTTON4 = "右下方快捷键4";
BINDING_NAME_MULTIACTIONBAR2BUTTON5 = "右下方快捷键5";
BINDING_NAME_MULTIACTIONBAR2BUTTON6 = "右下方快捷键6";
BINDING_NAME_MULTIACTIONBAR2BUTTON7 = "右下方快捷键7";
BINDING_NAME_MULTIACTIONBAR2BUTTON8 = "右下方快捷键8";
BINDING_NAME_MULTIACTIONBAR2BUTTON9 = "右下方快捷键9";
BINDING_NAME_MULTIACTIONBAR3BUTTON1 = "右边快捷键1";
BINDING_NAME_MULTIACTIONBAR3BUTTON10 = "右边快捷键10";
BINDING_NAME_MULTIACTIONBAR3BUTTON11 = "右边快捷键11";
BINDING_NAME_MULTIACTIONBAR3BUTTON12 = "右边快捷键12";
BINDING_NAME_MULTIACTIONBAR3BUTTON2 = "右边快捷键2";
BINDING_NAME_MULTIACTIONBAR3BUTTON3 = "右边快捷键3";
BINDING_NAME_MULTIACTIONBAR3BUTTON4 = "右边快捷键4";
BINDING_NAME_MULTIACTIONBAR3BUTTON5 = "右边快捷键5";
BINDING_NAME_MULTIACTIONBAR3BUTTON6 = "右边快捷键6";
BINDING_NAME_MULTIACTIONBAR3BUTTON7 = "右边快捷键7";
BINDING_NAME_MULTIACTIONBAR3BUTTON8 = "右边快捷键8";
BINDING_NAME_MULTIACTIONBAR3BUTTON9 = "右边快捷键9";
BINDING_NAME_MULTIACTIONBAR4BUTTON1 = "右边动作条2 快捷键1";
BINDING_NAME_MULTIACTIONBAR4BUTTON10 = "右边动作条2 快捷键10";
BINDING_NAME_MULTIACTIONBAR4BUTTON11 = "右边动作条2 快捷键11";
BINDING_NAME_MULTIACTIONBAR4BUTTON12 = "右边动作条2 快捷键12";
BINDING_NAME_MULTIACTIONBAR4BUTTON2 = "右边动作条2 快捷键2";
BINDING_NAME_MULTIACTIONBAR4BUTTON3 = "右边动作条2 快捷键3";
BINDING_NAME_MULTIACTIONBAR4BUTTON4 = "右边动作条2 快捷键4";
BINDING_NAME_MULTIACTIONBAR4BUTTON5 = "右边动作条2 快捷键5";
BINDING_NAME_MULTIACTIONBAR4BUTTON6 = "右边动作条2 快捷键6";
BINDING_NAME_MULTIACTIONBAR4BUTTON7 = "右边动作条2 快捷键7";
BINDING_NAME_MULTIACTIONBAR4BUTTON8 = "右边动作条2 快捷键8";
BINDING_NAME_MULTIACTIONBAR4BUTTON9 = "右边动作条2 快捷键9";
BINDING_NAME_MULTICASTACTIONBUTTON1 = "大地图腾";
BINDING_NAME_MULTICASTACTIONBUTTON10 = "火焰图腾";
BINDING_NAME_MULTICASTACTIONBUTTON11 = "水图腾";
BINDING_NAME_MULTICASTACTIONBUTTON12 = "空气图腾";
BINDING_NAME_MULTICASTACTIONBUTTON2 = "火焰图腾";
BINDING_NAME_MULTICASTACTIONBUTTON3 = "水图腾";
BINDING_NAME_MULTICASTACTIONBUTTON4 = "空气图腾";
BINDING_NAME_MULTICASTACTIONBUTTON5 = "大地图腾";
BINDING_NAME_MULTICASTACTIONBUTTON6 = "火焰图腾";
BINDING_NAME_MULTICASTACTIONBUTTON7 = "水图腾";
BINDING_NAME_MULTICASTACTIONBUTTON8 = "空气图腾";
BINDING_NAME_MULTICASTACTIONBUTTON9 = "大地图腾";
BINDING_NAME_MULTICASTRECALLBUTTON1 = "收回图腾";
BINDING_NAME_MULTICASTSUMMONBUTTON1 = "元素的召唤";
BINDING_NAME_MULTICASTSUMMONBUTTON2 = "先祖的召唤";
BINDING_NAME_MULTICASTSUMMONBUTTON3 = "灵魂的召唤";
BINDING_NAME_NAMEPLATES = "显示敌方姓名板";
BINDING_NAME_NEXTACTIONPAGE = "下一动作条";
BINDING_NAME_NEXTVIEW = "下一个视角";
BINDING_NAME_OPENALLBAGS = "打开/关闭所有的背包";
BINDING_NAME_OPENCHAT = "打开对话框";
BINDING_NAME_OPENCHATSLASH = "打开目标对话框";
BINDING_NAME_PETATTACK = "宠物攻击";
BINDING_NAME_PITCHDECREMENT = "减小倾角";
BINDING_NAME_PITCHDOWN = "向下倾斜";
BINDING_NAME_PITCHINCREMENT = "增大倾角";
BINDING_NAME_PITCHUP = "向上倾斜";
BINDING_NAME_PREVIOUSACTIONPAGE = "前一动作条";
BINDING_NAME_PREVVIEW = "前一个视角";
BINDING_NAME_PUSHTOTALK = "按键发言";
BINDING_NAME_RAIDTARGET1 = "为目标指定星形";
BINDING_NAME_RAIDTARGET2 = "为目标指定圆形";
BINDING_NAME_RAIDTARGET3 = "为目标指定菱形";
BINDING_NAME_RAIDTARGET4 = "为目标指定三角";
BINDING_NAME_RAIDTARGET5 = "为目标指定月亮";
BINDING_NAME_RAIDTARGET6 = "为目标指定方块";
BINDING_NAME_RAIDTARGET7 = "为目标指定十字";
BINDING_NAME_RAIDTARGET8 = "为目标指定骷髅";
BINDING_NAME_RAIDTARGETNONE = "清除团队目标图标";
BINDING_NAME_REPLY = "回复对话";
BINDING_NAME_REPLY2 = "再次密语";
BINDING_NAME_RESETVIEW1 = "重置视角1";
BINDING_NAME_RESETVIEW2 = "重置视角2";
BINDING_NAME_RESETVIEW3 = "重置视角3";
BINDING_NAME_RESETVIEW4 = "重置视角4";
BINDING_NAME_RESETVIEW5 = "重置视角5";
BINDING_NAME_SAVEVIEW1 = "保存视角1";
BINDING_NAME_SAVEVIEW2 = "保存视角2";
BINDING_NAME_SAVEVIEW3 = "保存视角3";
BINDING_NAME_SAVEVIEW4 = "保存视角4";
BINDING_NAME_SAVEVIEW5 = "保存视角5";
BINDING_NAME_SCREENSHOT = "截图";
BINDING_NAME_SETVIEW1 = "设置1号视角";
BINDING_NAME_SETVIEW2 = "设置2号视角";
BINDING_NAME_SETVIEW3 = "设置3号视角";
BINDING_NAME_SETVIEW4 = "设置4号视角";
BINDING_NAME_SETVIEW5 = "设置5号视角";
BINDING_NAME_SHAPESHIFTBUTTON1 = "特殊快捷键1";
BINDING_NAME_SHAPESHIFTBUTTON10 = "特殊快捷键10";
BINDING_NAME_SHAPESHIFTBUTTON2 = "特殊快捷键2";
BINDING_NAME_SHAPESHIFTBUTTON3 = "特殊快捷键3";
BINDING_NAME_SHAPESHIFTBUTTON4 = "特殊快捷键4";
BINDING_NAME_SHAPESHIFTBUTTON5 = "特殊快捷键5";
BINDING_NAME_SHAPESHIFTBUTTON6 = "特殊快捷键6";
BINDING_NAME_SHAPESHIFTBUTTON7 = "特殊快捷键7";
BINDING_NAME_SHAPESHIFTBUTTON8 = "特殊快捷键8";
BINDING_NAME_SHAPESHIFTBUTTON9 = "特殊快捷键9";
BINDING_NAME_SITORSTAND = "坐下/下降";
BINDING_NAME_STARTATTACK = "开始攻击";
BINDING_NAME_STOPATTACK = "停止攻击";
BINDING_NAME_STOPCASTING = "停止施法";
BINDING_NAME_STRAFELEFT = "向左平移";
BINDING_NAME_STRAFERIGHT = "向右平移";
BINDING_NAME_SWINGCAMERA = "晃动镜头";
BINDING_NAME_SWINGCAMERAANDPLAYER = "晃动镜头和玩家";
BINDING_NAME_TARGETENEMYDIRECTIONAL = "锁定前方敌人";
BINDING_NAME_TARGETFOCUS = "目标焦点";
BINDING_NAME_TARGETFRIENDDIRECTIONAL = "锁定前方友军";
BINDING_NAME_TARGETLASTHOSTILE = "选择最后一个敌对目标";
BINDING_NAME_TARGETLASTTARGET = "锁定最后一个目标";
BINDING_NAME_TARGETMOUSEOVER = "选择鼠标悬停目标";
BINDING_NAME_TARGETNEARESTENEMY = "选中最近的敌人";
BINDING_NAME_TARGETNEARESTENEMYPLAYER = "锁定最近的敌对玩家";
BINDING_NAME_TARGETNEARESTFRIEND = "选择最近的盟友";
BINDING_NAME_TARGETNEARESTFRIENDPLAYER = "锁定最近的友方玩家";
BINDING_NAME_TARGETPARTYMEMBER1 = "选择队友1";
BINDING_NAME_TARGETPARTYMEMBER2 = "选择队友2";
BINDING_NAME_TARGETPARTYMEMBER3 = "选择队友3";
BINDING_NAME_TARGETPARTYMEMBER4 = "选择队友4";
BINDING_NAME_TARGETPARTYPET1 = "选择队友宠物1";
BINDING_NAME_TARGETPARTYPET2 = "选择队友宠物2";
BINDING_NAME_TARGETPARTYPET3 = "选择队友宠物3";
BINDING_NAME_TARGETPARTYPET4 = "选择队友宠物4";
BINDING_NAME_TARGETPET = "选择宠物";
BINDING_NAME_TARGETPREVIOUSENEMY = "选择前一个敌人目标";
BINDING_NAME_TARGETPREVIOUSENEMYPLAYER = "锁定上一个敌对玩家";
BINDING_NAME_TARGETPREVIOUSFRIEND = "选择前一个盟友";
BINDING_NAME_TARGETPREVIOUSFRIENDPLAYER = "锁定上一个友方玩家";
BINDING_NAME_TARGETSELF = "选择自己";
BINDING_NAME_TARGETTALKER = "选定当前发言者";
BINDING_NAME_TOGGLEABILITYBOOK = "打开/关闭能力界面";
BINDING_NAME_TOGGLEACHIEVEMENT = "成就面板";
BINDING_NAME_TOGGLEACTIONBARLOCK = "打开/关闭动作条锁定";
BINDING_NAME_TOGGLEAUTORUN = "自动奔跑";
BINDING_NAME_TOGGLEAUTOSELFCAST = "自动自我施法";
BINDING_NAME_TOGGLEBACKPACK = "打开/关闭行囊";
BINDING_NAME_TOGGLEBAG1 = "打开/关闭1号背包";
BINDING_NAME_TOGGLEBAG2 = "打开/关闭2号背包";
BINDING_NAME_TOGGLEBAG3 = "打开/关闭3号背包";
BINDING_NAME_TOGGLEBAG4 = "打开/关闭4号背包";
BINDING_NAME_TOGGLEBAG5 = "打开/关闭5号背包";
BINDING_NAME_TOGGLEBATTLEFIELDMINIMAP = "区域地图开关";
BINDING_NAME_TOGGLEBINDINGMODE1 = "动作条绑定模式切换";
BINDING_NAME_TOGGLEBINDINGMODE2 = "目标绑定模式切换";
BINDING_NAME_TOGGLEBINDINGMODE3 = "自定义绑定模式切换";
BINDING_NAME_TOGGLECHANNELPULLOUT = "频道拖出列表";
BINDING_NAME_TOGGLECHANNELTAB = "切换频道面板";
BINDING_NAME_TOGGLECHARACTER0 = "打开/关闭角色界面";
BINDING_NAME_TOGGLECHARACTER1 = "打开/关闭技能界面";
BINDING_NAME_TOGGLECHARACTER2 = "打开/关闭声望界面";
BINDING_NAME_TOGGLECHARACTER3 = "切换宠物面板";
BINDING_NAME_TOGGLECHARACTER4 = "PvP面板";
BINDING_NAME_TOGGLECHATTAB = "聊天面板";
BINDING_NAME_TOGGLECOMBATLOG = "打开/关闭战斗日志";
BINDING_NAME_TOGGLECURRENCY = "货币页面";
BINDING_NAME_TOGGLEFPS = "开启/关闭帧数显示";
BINDING_NAME_TOGGLEFRIENDSTAB = "切换好友面板";
BINDING_NAME_TOGGLEGAMEMENU = "打开/关闭游戏菜单";
BINDING_NAME_TOGGLEGUILDTAB = "开启/关闭公会面板";
BINDING_NAME_TOGGLEIGNORETAB = "切换忽略面板";
BINDING_NAME_TOGGLEINSCRIPTION = "切换雕文";
BINDING_NAME_TOGGLEKEYRING = "钥匙链开关";
BINDING_NAME_TOGGLELFGPARENT = "切换地下城查找器框体";
BINDING_NAME_TOGGLELFRPARENT = "切换团队浏览器";
BINDING_NAME_TOGGLEMINIMAP = "打开/关闭地图";
BINDING_NAME_TOGGLEMINIMAPROTATION = "微缩地图旋转";
BINDING_NAME_TOGGLEMOUSE = "摇杆鼠标模式";
BINDING_NAME_TOGGLEMUSIC = "开启音乐";
BINDING_NAME_TOGGLEPETBOOK = "打开/关闭宠物书";
BINDING_NAME_TOGGLEPVP = "PvP面板";
BINDING_NAME_TOGGLEQUESTLOG = "打开/关闭任务日志";
BINDING_NAME_TOGGLERAIDTAB = "开启/关闭团队面板";
BINDING_NAME_TOGGLERUN = "跑/走";
BINDING_NAME_TOGGLESELFMUTE = "自我禁声";
BINDING_NAME_TOGGLESHEATH = "取出/收起武器";
BINDING_NAME_TOGGLESOCIAL = "打开/关闭社交界面";
BINDING_NAME_TOGGLESOUND = "开启声效";
BINDING_NAME_TOGGLESPELLBOOK = "打开/关闭法术书";
BINDING_NAME_TOGGLESTATISTICS = "统计数据面板";
BINDING_NAME_TOGGLETALENTS = "打开/关闭天赋面板";
BINDING_NAME_TOGGLEUI = "打开／关闭用户界面";
BINDING_NAME_TOGGLEWHOTAB = "切换查询面板";
BINDING_NAME_TOGGLEWORLDMAP = "打开/关闭世界地图";
BINDING_NAME_TOGGLEWORLDMAPSIZE = "切换世界地图大小";
BINDING_NAME_TOGGLEWORLDSTATESCORES = "积分窗口";
BINDING_NAME_TURNLEFT = "左转";
BINDING_NAME_TURNRIGHT = "右转";
BINDING_NAME_VEHICLEAIMDECREMENT = "减小仰角";
BINDING_NAME_VEHICLEAIMDOWN = "向下瞄准";
BINDING_NAME_VEHICLEAIMINCREMENT = "增大仰角";
BINDING_NAME_VEHICLEAIMUP = "向上瞄准";
BINDING_NAME_VEHICLECAMERAZOOMIN = "镜头拉近";
BINDING_NAME_VEHICLECAMERAZOOMOUT = "镜头拉远";
BINDING_NAME_VEHICLEEXIT = "离开载具";
BINDING_NAME_VEHICLENEXTSEAT = "后一座位";
BINDING_NAME_VEHICLEPREVSEAT = "前一座位";
BIND_ENCHANT = "对这件物品进行附魔将使其与你绑定。";
BIND_KEY_TO_COMMAND = "点击按键以绑定命令 -> %s";
BIND_TRADE_TIME_REMAINING = "你可以在接下来的%s内将这个物品交易给同样拥有资格拾取它的其他玩家。";
BIND_ZONE_DISPLAY = "你现在绑定在%s。";
BLIZZARD_COMBAT_LOG_MENU_BOTH = "显示所有与%s有关的信息？";
BLIZZARD_COMBAT_LOG_MENU_EVERYTHING = "全部显示";
BLIZZARD_COMBAT_LOG_MENU_INCOMING = "%s发生了什么事情？";
BLIZZARD_COMBAT_LOG_MENU_OUTGOING = "%s做了些什么？";
BLIZZARD_COMBAT_LOG_MENU_OUTGOING_ME = "%s对你做了什么？";
BLIZZARD_COMBAT_LOG_MENU_RESET = "重置";
BLIZZARD_COMBAT_LOG_MENU_REVERT = "返回上一个过滤条件";
BLIZZARD_COMBAT_LOG_MENU_SAVE = "保存为新的过滤条件";
BLIZZARD_COMBAT_LOG_MENU_SPELL_HIDE = "隐藏与这一条类似的信息。";
BLIZZARD_COMBAT_LOG_MENU_SPELL_LINK = "将%s链接到聊天频道中。";
BLIZZARD_COMBAT_LOG_MENU_SPELL_TYPE_HEADER = "信息类型";
BLOCK = "格挡";
BLOCKED_COMMUNICATION = "已阻止通讯";
BLOCKED_INVITES = "被阻止邀请";
BLOCK_CHANCE = "格挡几率";
BLOCK_COMMUNICATION = "阻止通讯";
BLOCK_INVITES = "阻止邀请";
BLOCK_INVITES_CONFIRMATION = "你确定要屏蔽任何来自%s的邀请？";
BLOCK_INVITES_TOOLTIP = "移除本次邀请并屏蔽任何来自该玩家的邀请。";
BLOCK_TRADES = "阻止交易";
BLOCK_TRAILER = "（%d点被格挡）";
BLUE_GEM = "蓝色";
BNET_BROADCAST_SENT_TIME = "（%s之前）";
BNET_INVITE_SENT_TIME = "发送于%s之前";
BNET_LAST_ONLINE_TIME = "上次在线%s前";
BNET_REPORT = "举报";
BNET_REPORT_ABUSE = "侵犯行为";
BNET_REPORT_ABUSE_BUTTON = "举报侵犯行为";
BNET_REPORT_ABUSE_LABEL = "举报的侵犯行为来自：";
BNET_REPORT_ABUSE_PROMPT = "请留下评论以协助处理该类侵犯行为。";
BNET_REPORT_CONFIRM_ABUSE = "你确定要举报%s的侵犯行为吗？";
BNET_REPORT_CONFIRM_NAME = "你确定要举报%s的不当角色名吗？";
BNET_REPORT_CONFIRM_SPAM = "你确定要举报%s的骚扰行为吗？";
BNET_REPORT_NAME = "不当角色名";
BNET_REPORT_PLAYER = "举报玩家";
BNET_REPORT_PLAYER_TOOLTIP = "举报该名玩家的骚扰、侵犯行为或不当角色名。";
BNET_REPORT_SENT = "举报已发送。";
BNET_REPORT_SPAM = "骚扰行为";
BN_BROADCAST_TOOLTIP = "向你的实名好友发送通告信息";
BN_CONVERSATION = "实名对话";
BN_INLINE_TOAST_ALERT = "战网提示";
BN_INLINE_TOAST_BROADCAST = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:2:29:2:29\124t%s: %s";
BN_INLINE_TOAST_BROADCAST_INFORM = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:2:29:2:29\124t你的通告已发送。";
BN_INLINE_TOAST_CONVERSATION = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:66:95:2:29\124t你正在与%s进行对话。";
BN_INLINE_TOAST_FRIEND_ADDED = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:98:127:2:29\124t%s现在是你的实名好友。";
BN_INLINE_TOAST_FRIEND_OFFLINE = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:2:29:34:61\124t%s已经离线。";
BN_INLINE_TOAST_FRIEND_ONLINE = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:2:29:34:61\124t%s已经上线。";
BN_INLINE_TOAST_FRIEND_PENDING = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:98:127:2:29\124t你有%s个好友请求。";
BN_INLINE_TOAST_FRIEND_REMOVED = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:2:29:34:61\124t%s不再是你的实名好友。";
BN_INLINE_TOAST_FRIEND_REQUEST = "\124TInterface\\FriendsFrame\\UI-Toast-ToastIcons.tga:16:16:0:0:128:64:98:127:2:29\124t你收到了一个新的好友请求。";
BN_TOAST_CONVERSATION = "你已经加入";
BN_TOAST_NEW_INVITE = "你收到了一个新的好友请求。";
BN_TOAST_OFFLINE = "已经|cffff0000下线|r。";
BN_TOAST_ONLINE = "已经|cff00ff00上线|r。";
BN_TOAST_PENDING_INVITES = "你共有|cff82c5ff%d|r条好友请求。";
BN_UNABLE_TO_RESOLVE_NAME = "无法向'%s'发送悄悄话。战网功能也许暂时不可用。";
BN_WHISPER = "实名好友密语";
BONUS_ARENA_POINTS = "竞技场奖励点数：";
BONUS_DAMAGE = "伤害加成";
BONUS_DAMAGE_ABBR = "B. Dmg";
BONUS_HEALING = "治疗加成";
BONUS_HEALING_ABBR = "B. Heal";
BONUS_HEALING_TOOLTIP = "使你的治疗效果提高最多%d点";
BONUS_HONOR = "奖励荣誉：";
BONUS_TALENTS = "奖励天赋点数：";
BOSS = "首领";
BOSSES = "首领：";
BOSSES_KILLED = "已击杀%d/%d个首领";
BOSS_ALIVE = "尚存活";
BOSS_DEAD = "已击杀";
BREATH_LABEL = "呼吸";
BROWSE = "浏览";
BROWSE_AUCTIONS = "浏览拍卖";
BROWSE_NO_RESULTS = "未发现物品";
BROWSE_SEARCH_TEXT = "选择搜索条件，然后按下“搜索”";
BROWSING = "浏览：";
BUFFERING = "增益法术";
BUFFER_DOUBLE = "双倍";
BUFFOPTIONS_LABEL = "增益效果和负面效果";
BUFFOPTIONS_SUBTEXT = "这些选项允许你控制增益效果和负面效果的显示方式。";
BUG_BUTTON = "Bug及建议";
BUG_CATEGORY1 = "角色职业";
BUG_CATEGORY2 = "户外区域";
BUG_CATEGORY3 = "地下城";
BUG_CATEGORY4 = "城市";
BUG_CATEGORY5 = "用户界面";
BUG_CATEGORY6 = "怪物——平衡/技能";
BUG_CATEGORY7 = "怪物——位置";
BUG_CATEGORY8 = "任务和故事";
BUG_CATEGORY9 = "美工";
BUG_CATEGORY10 = "声效";
BUG_CATEGORY11 = "物品";
BUG_CATEGORY12 = "专业技能";
BUG_CATEGORY13 = "杂项";
BUG_CATEGORY14 = "玩家对玩家";
BUG_CATEGORY15 = "语言翻译";
BUG_CATEGORY_CHOOSE = "——〉请选择一个类别";
BUG_CATEGORY_ERROR = "你必须选择一个类别来提交Bug或建议。";
BUG_SUBMITTED = "Bug已提交";
BUG_SUBMIT_FAILED = "提交Bug失败";
BUILDING_DAMAGE = "攻城";
BUILDING_DAMAGE_COMBATLOG_TOOLTIP = "当法术或技能对一座可摧毁的建筑物造成伤害时显示信息。";
BUILDING_HEAL = "修理";
BUILDING_HEAL_COMBATLOG_TOOLTIP = "当法术或技能修理一座建筑物时显示信息。";
BUTTON_LAG_AUCTIONHOUSE = "拍卖行";
BUTTON_LAG_AUCTIONHOUSE_NEWBIE = "在拍卖行中摆放、购买、搜索或是浏览物品时会有很长的延迟。";
BUTTON_LAG_AUCTIONHOUSE_TOOLTIP = "拍卖行延迟";
BUTTON_LAG_CHAT = "聊天";
BUTTON_LAG_CHAT_NEWBIE = "要过相当长一段时间，你的朋友才能看到你发给他们的信息，或者让你看到他们发给你的信息。";
BUTTON_LAG_CHAT_TOOLTIP = "聊天信息延迟";
BUTTON_LAG_LOOT = "拾取";
BUTTON_LAG_LOOT_NEWBIE = "在你点击拾取物品，或者有人给予你物品之后，需要相当长一段时间，物品才会显示在你的物品栏中。和另一名玩家进行交易也需要相当长的一段时间。";
BUTTON_LAG_LOOT_TOOLTIP = "拾取延迟";
BUTTON_LAG_MAIL = "邮件";
BUTTON_LAG_MAIL_NEWBIE = "需要相当长一段时间才能打开信件或取得信件中的物品。";
BUTTON_LAG_MAIL_TOOLTIP = "游戏邮件延迟";
BUTTON_LAG_MOVEMENT = "移动";
BUTTON_LAG_MOVEMENT_NEWBIE = "生物和玩家有可能突然凭空冒出来，或者在跑动中从一个位置在瞬间移动到另一个位置。你会发现，当你攻击一个怪物的时候，它已经不在你想象中的位置上了。";
BUTTON_LAG_MOVEMENT_TOOLTIP = "移动延迟";
BUTTON_LAG_SPELL = "法术和技能";
BUTTON_LAG_SPELL_NEWBIE = "在你使用一个法术或者技能以后，需要相当长一段时间法术或技能才会生效。技能即使不在冷却状态，也可能显示为正处于冷却状态。";
BUTTON_LAG_SPELL_TOOLTIP = "法术和技能延迟";
BUYBACK = "购回";
BUYBACK_THIS_ITEM = "买回这件物品";
BUYOUT = "一口价";
BUYOUT_AUCTION_CONFIRMATION = "以一口价购买：";
BUYOUT_COST = "一口价";
BUYOUT_PRICE = "一口价";
BUY_GUILDBANK_TAB = "购买新的公会银行标签";
BY_SOURCE = "按来源";
BY_SOURCE_COMBATLOG_TOOLTIP = "按照施法者以颜色区分。";
BY_TARGET = "按目标";
BY_TARGET_COMBATLOG_TOOLTIP = "按照目标以颜色区分。";
CALENDAR_ACCEPT_INVITATION = "接受邀请";
CALENDAR_ANNOUNCEMENT_CREATEDBY_PLAYER = "由%s创建";
CALENDAR_ANNOUNCEMENT_CREATEDBY_YOURSELF = "这是你的通告";
CALENDAR_AUTO_APPROVE = "自动批准成员";
CALENDAR_COPY_EVENT = "复制";
CALENDAR_CREATE = "创建";
CALENDAR_CREATE_ANNOUNCEMENT = "创建通告";
CALENDAR_CREATE_ARENATEAM_EVENT = "创建竞技场战队活动";
CALENDAR_CREATE_EVENT = "创建活动";
CALENDAR_CREATE_GUILD_ANNOUNCEMENT = "创建公会通告";
CALENDAR_CREATE_GUILD_EVENT = "创建公会活动";
CALENDAR_DECLINE_INVITATION = "拒绝邀请";
CALENDAR_DELETE_ANNOUNCEMENT_CONFIRM = "你确定要删除这条通告吗？";
CALENDAR_DELETE_EVENT = "删除";
CALENDAR_DELETE_EVENT_CONFIRM = "你确定要删除这个活动吗？";
CALENDAR_DELETE_GUILD_EVENT_CONFIRM = "你确定要删除这个公会活动吗？";
CALENDAR_EDIT_ANNOUNCEMENT = "编辑通告";
CALENDAR_EDIT_EVENT = "编辑活动";
CALENDAR_EDIT_GUILD_EVENT = "编辑公会活动";
CALENDAR_ERROR = "%s";
CALENDAR_ERROR_ALREADY_INVITED_TO_EVENT_S = "%s已被邀请。";
CALENDAR_ERROR_ARENA_EVENTS_EXCEEDED = "你的竞技场战队无法创建更多活动了。";
CALENDAR_ERROR_CREATEDATE_AFTER_MAX = "你不能创建%4$d年%2$s之后的活动。";
CALENDAR_ERROR_CREATEDATE_BEFORE_TODAY = "你不能为今天之前的某天创建活动。";
CALENDAR_ERROR_DELETE_CREATOR_FAILED = "你不能移处该活动的创建者。";
CALENDAR_ERROR_EVENTS_EXCEEDED = "你无法创建多于%d个活动。";
CALENDAR_ERROR_EVENT_INVALID = "未找到活动。";
CALENDAR_ERROR_EVENT_LOCKED = "该活动已被锁定。";
CALENDAR_ERROR_EVENT_PASSED = "该活动已经结束了。";
CALENDAR_ERROR_EVENT_THROTTLED = "同一时间内可以创建的活动数量有上限，请稍后再继续创建活动。";
CALENDAR_ERROR_EVENT_TIME_PASSED = "该活动已经过期了。";
CALENDAR_ERROR_EVENT_WRONG_SERVER = "你不能在这个服务器上创建活动。";
CALENDAR_ERROR_GUILD_EVENTS_EXCEEDED = "你的公会已经达到了创建活动的数量上限（%d个）。";
CALENDAR_ERROR_IGNORED = "%s屏蔽了你。";
CALENDAR_ERROR_INTERNAL = "内部日程表错误。";
CALENDAR_ERROR_INVALID_DATE = "请输入有效的日期。";
CALENDAR_ERROR_INVALID_SIGNUP = "你无法在这个活动中登记。";
CALENDAR_ERROR_INVALID_TIME = "请输入有效的时间。";
CALENDAR_ERROR_INVITES_DISABLED = "你不能邀请玩家参与该活动。";
CALENDAR_ERROR_INVITES_EXCEEDED = "你不能邀请多于%d名玩家参与该活动。";
CALENDAR_ERROR_INVITE_THROTTLED = "同一时间内可以邀请的人数有上限，请稍后再继续发送邀请。";
CALENDAR_ERROR_INVITE_WRONG_SERVER = "你不能邀请另一个服务器的玩家。";
CALENDAR_ERROR_NEEDS_TITLE = "输入标题。";
CALENDAR_ERROR_NOT_ALLIED = "你不能邀请对立阵营的玩家。";
CALENDAR_ERROR_NOT_INVITED = "你没有收到参加该活动的邀请。";
CALENDAR_ERROR_NO_GUILD_INVITES = "不允许邀请公会成员。";
CALENDAR_ERROR_NO_INVITE = "未找到邀请。";
CALENDAR_ERROR_NO_MODERATOR = "被邀请参加活动的成员不能成为管理员。";
CALENDAR_ERROR_OTHER_INVITES_EXCEEDED = "%s已经达到了活动数量的上限。这些成员必须删除一个活动才能接受其它活动的邀请。";
CALENDAR_ERROR_PERMISSIONS = "你没有权限那样做。";
CALENDAR_ERROR_RESTRICTED_LEVEL = "你的账号中必须至少有一个20级的角色。";
CALENDAR_ERROR_SELF_INVITES_EXCEEDED = "邀请人数已经达到上限。移除一个旧活动以便添加新的邀请。";
CALENDAR_EVENTNAME_FORMAT_END = "%s 结束";
CALENDAR_EVENTNAME_FORMAT_RAID_LOCKOUT = "%s解锁";
CALENDAR_EVENTNAME_FORMAT_RAID_RESET = "%s重置";
CALENDAR_EVENTNAME_FORMAT_START = "%s 开始";
CALENDAR_EVENT_ALARM_MESSAGE = "%s将在15分钟内开始。";
CALENDAR_EVENT_CREATORNAME = "由%s创建";
CALENDAR_EVENT_DESCRIPTION = "描述";
CALENDAR_EVENT_INVITEDBY_PLAYER = "被%s邀请";
CALENDAR_EVENT_INVITEDBY_YOURSELF = "这是你创建的活动";
CALENDAR_EVENT_NAME = "名称";
CALENDAR_EVENT_PICKER_TITLE = "选择一个活动";
CALENDAR_EVENT_REMOVED_MAIL_BODY = "%s取消了%s。（%s）";
CALENDAR_EVENT_REMOVED_MAIL_SUBJECT = "%s 取消";
CALENDAR_FILTERS = "过滤";
CALENDAR_FILTER_BATTLEGROUND = "战场的召唤";
CALENDAR_FILTER_DARKMOON = "暗月马戏团";
CALENDAR_FILTER_RAID_LOCKOUTS = "团队副本解锁";
CALENDAR_FILTER_RAID_RESETS = "团队重置";
CALENDAR_FILTER_WEEKLY_HOLIDAYS = "每周节日";
CALENDAR_GUILDEVENT_INVITEDBY_YOURSELF = "这是你的公会活动";
CALENDAR_INVITELIST_CLEARMODERATOR = "撤销管理权限";
CALENDAR_INVITELIST_CREATORNAME = "%s（创建者）";
CALENDAR_INVITELIST_INVITETORAID = "小队/团队邀请";
CALENDAR_INVITELIST_MODERATORNAME = "%s（管理员）";
CALENDAR_INVITELIST_SETINVITESTATUS = "设置邀请状态";
CALENDAR_INVITELIST_SETMODERATOR = "授予管理权限";
CALENDAR_INVITE_ALL = "全部邀请";
CALENDAR_INVITE_CONFIRMED = "邀请已确认/已接受的成员";
CALENDAR_INVITE_LABEL = "你想要邀请谁？";
CALENDAR_INVITE_MEMBERS = "邀请成员";
CALENDAR_INVITE_PLAYER = "邀请玩家";
CALENDAR_INVITE_REMOVED_MAIL_BODY = "%s把你从%s中移出。（%s）";
CALENDAR_INVITE_REMOVED_MAIL_SUBJECT = "你被移出了%s。";
CALENDAR_LOCK_EVENT = "锁定活动";
CALENDAR_MASSINVITE_ARENA_HELP = "邀请竞技场队员：";
CALENDAR_MASSINVITE_GUILD_HELP = "邀请所有符合下列条件的公会成员：";
CALENDAR_MASSINVITE_GUILD_MINRANK = "最低公会级别";
CALENDAR_MASS_INVITE = "批量邀请";
CALENDAR_NOT_SIGNEDUP_FOR_GUILDEVENT = "未登记";
CALENDAR_PASTE_EVENT = "粘贴";
CALENDAR_PLAYER_NAME = "玩家姓名";
CALENDAR_RAID_LOCKOUT_DESCRIPTION = "你的%1$s副本将在%2$s解锁。";
CALENDAR_RAID_RESET_DESCRIPTION = "%1$s将在%2$s重置。";
CALENDAR_REMOVE_INVITATION = "移除邀请";
CALENDAR_REMOVE_SIGNUP = "从公会活动中移除";
CALENDAR_REPEAT_BIWEEKLY = "双周";
CALENDAR_REPEAT_MONTHLY = "每月";
CALENDAR_REPEAT_NEVER = "从不";
CALENDAR_REPEAT_WEEKLY = "每周";
CALENDAR_SET_DESCRIPTION_LABEL = "设置日程表活动描述：";
CALENDAR_SIGNEDUP_FOR_GUILDEVENT_WITH_STATUS = "已登记（%s）";
CALENDAR_SIGNUP = "登记";
CALENDAR_SIGNUP_FOR_GUILDEVENT = "登记到公会活动中";
CALENDAR_STATUS_ACCEPTED = "已接受";
CALENDAR_STATUS_CONFIRMED = "已确认";
CALENDAR_STATUS_DECLINED = "已拒绝";
CALENDAR_STATUS_INVITED = "已邀请";
CALENDAR_STATUS_NOT_SIGNEDUP = "未登记";
CALENDAR_STATUS_OUT = "休息";
CALENDAR_STATUS_SIGNEDUP = "已登记";
CALENDAR_STATUS_STANDBY = "待命";
CALENDAR_STATUS_TENTATIVE = "暂定";
CALENDAR_TENTATIVE_INVITATION = "暂定邀请";
CALENDAR_TEXTURE_PICKER_TITLE_DUNGEON = "选择一个地下城";
CALENDAR_TEXTURE_PICKER_TITLE_RAID = "选择团队活动";
CALENDAR_TOOLTIP_AUTOAPPROVE = "钩选此项后，回复此活动邀请的被邀请者会被自动批准；否则你必须手动批准他们的回复。";
CALENDAR_TOOLTIP_AVAILABLEBUTTON = "将你的状态设置为接受此活动邀请。";
CALENDAR_TOOLTIP_DECLINEBUTTON = "将你的状态设置为拒绝此活动邀请。";
CALENDAR_TOOLTIP_INVITEMEMBERS_BUTTON_PARTY = "邀请已接受和已确认的玩家加入小队。";
CALENDAR_TOOLTIP_INVITEMEMBERS_BUTTON_RAID = "邀请已接受和已确认的玩家加入团队。";
CALENDAR_TOOLTIP_INVITE_RESPONDED = "回应时间：";
CALENDAR_TOOLTIP_INVITE_TOTALS = "已确认/接受/登记邀请";
CALENDAR_TOOLTIP_LOCKEVENT = "钩选此项后，被邀请者无法回复此活动。";
CALENDAR_TOOLTIP_MASSINVITE = "批量邀请将根据预设的过滤条件生成邀请名单。|n|n请注意：批量邀请将清空你现有的邀请名单。";
CALENDAR_TOOLTIP_REMOVEBUTTON = "从你的日历中移除此活动。";
CALENDAR_TOOLTIP_REMOVESIGNUPBUTTON = "将你自己从这个活动中移除。";
CALENDAR_TOOLTIP_SIGNUPBUTTON = "将你自己添加到这个活动中。";
CALENDAR_TOOLTIP_TENTATIVEBUTTON = "将参与此事件的状态设为暂定";
CALENDAR_TYPE_DUNGEON = "地下城";
CALENDAR_TYPE_MEETING = "会议";
CALENDAR_TYPE_OTHER = "其它";
CALENDAR_TYPE_PVP = "PvP";
CALENDAR_TYPE_RAID = "团队";
CALENDAR_UPDATE = "更新";
CALENDAR_VIEW_ANNOUNCEMENT = "浏览通告";
CALENDAR_VIEW_EVENT = "浏览活动";
CALENDAR_VIEW_EVENTTITLE_LOCKED = "|cff7f7f7f%s|r |cffffd200（已锁定）|r";
CALENDAR_VIEW_EVENTTYPE = "%1$s - %2$s";
CALENDAR_VIEW_EVENT_REMOVE = "移除";
CALENDAR_VIEW_EVENT_SETSTATUS = "设置你的状态：";
CALENDAR_VIEW_EVENT_TENTATIVE = "暂定";
CALENDAR_VIEW_GUILD_EVENT = "浏览公会活动";
CALIBRATION_TEXT = "你应该可以区分以下的21个灰格。否则的话请调整反差系数，直到你能够区分这些灰格。如果单单调整反差系数仍然效果不佳的话，请调节您的显示器亮度和对比度来达到最满意的效果。";
CAMERA_ALWAYS = "总是调整视角";
CAMERA_FOLLOWING_STYLE = "镜头跟随模式";
CAMERA_LABEL = "镜头";
CAMERA_LOCKED = "已锁";
CAMERA_MODE = "分离镜头";
CAMERA_NEVER = "从不调整镜头";
CAMERA_SMART = "移动时只调整水平角度";
CAMERA_SMARTER = "仅在移动时";
CAMERA_SUBTEXT = "这些选项可以帮助你调整游戏中的镜头设置。";
CAMP_NOW = "立刻返回角色选择画面";
CAMP_TIMER = "%d%s后返回角色选择画面";
CANCEL = "取消";
CANCEL_AUCTION = "取消拍卖";
CANCEL_AUCTION_CONFIRMATION = "取消拍卖将使你失去保证金。";
CANCEL_AUCTION_CONFIRMATION_MONEY = "取消拍卖会没收你所有的委托费用和：";
CANNOT_COOPERATE_LABEL = "*";
CANT_AFFORD_ITEM = "你买不起这个物品。";
CANT_USE_ITEM = "你不能使用这件物品。";
CAN_BIND_PTT = "按下“按键发言”键";
CAPSLOCK_KEY_TEXT = "Capslock";
CASH_ON_DELIVERY = "付款取信";
CAST_WHILE_MOVING = "%s失败：不能在移动中施放。";
CATEGORIES = "分类";
CATEGORY = "分类";
CHANCE_TO_BLOCK = "%.2f%%格挡几率";
CHANCE_TO_CRIT = "%.2f%%爆击几率";
CHANCE_TO_DODGE = "%.2f%%闪躲几率";
CHANCE_TO_PARRY = "%.2f%%招架几率";
CHANGE_INSTANCE = "变更副本";
CHANGE_MACRO_NAME_ICON = "改变名字/图标";
CHANGE_OPACITY = "改变透明度";
CHANNEL = "频道";
CHANNELING = "正在引导";
CHANNELPULLOUT_OPACITY_LABEL = "改变不透明度";
CHANNELPULLOUT_OPTIONS_LABEL = "频道拖出设置";
CHANNELS = "频道";
CHANNEL_CATEGORY_CUSTOM = "自定义";
CHANNEL_CATEGORY_GROUP = "小组";
CHANNEL_CATEGORY_WORLD = "世界";
CHANNEL_CHANNEL_NAME = "频道名称";
CHANNEL_INVITE = "你想要将谁邀请至%s？";
CHANNEL_JOIN_CHANNEL = "加入一个频道";
CHANNEL_NEW_CHANNEL = "新建频道";
CHANNEL_PASSWORD = "为%s输入一个密码。";
CHANNEL_ROSTER = "频道名单";
CHARACTER = "角色";
CHARACTER_BUTTON = "角色信息";
CHARACTER_FRIEND = "魔兽世界";
CHARACTER_FRIEND_INFO = "这是你乐于一同进行游戏的同服务器玩家角色，你可以与这名角色聊天和组队游戏。";
CHARACTER_FRIEND_LABEL = "输入角色名";
CHARACTER_INFO = "角色信息";
CHARACTER_KEY_BINDINGS = "%s的按键设置";
CHARACTER_POINTS2_COLON = "技能点数：";
CHARACTER_POINTS_CHANGED = "角色点数已改变";
CHARACTER_SHADOWS = "角色阴影";
CHARACTER_SPECIFIC_KEYBINDINGS = "角色专用按键设置";
CHARACTER_SPECIFIC_KEYBINDING_TOOLTIP = "点击这里在通用按键设置和本角色的专用按键设置之间切换。";
CHARACTER_SPECIFIC_MACROS = "%s专用宏";
CHAT = "聊天";
CHATCONFIG_HEADER = "%s设置";
CHATLOGDISABLED = "聊天记录已被禁止。";
CHATLOGENABLED = "聊天记录保存在Logs\\WoWChatLog.txt中";
CHAT_AFK_GET = "%s目前处于离开状态：\32";
CHAT_ANNOUNCE = "通告";
CHAT_ANNOUNCEMENTS_OFF_NOTICE = "|Hchannel:%d|h[%s]|h 频道公告已被%s禁止。";
CHAT_ANNOUNCEMENTS_OFF_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s关闭了频道公告。";
CHAT_ANNOUNCEMENTS_ON_NOTICE = "|Hchannel:%d|h[%s]|h 频道公告已被%s打开。";
CHAT_ANNOUNCEMENTS_ON_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s开启了频道公告。";
CHAT_AUTO_JOIN = "自动加入";
CHAT_BAN = "禁止进入";
CHAT_BANNED_NOTICE = "[%s] 你已被禁止进入该频道。";
CHAT_BATTLEGROUND_GET = "|Hchannel:Battleground|h[战场]|h %s:\32";
CHAT_BATTLEGROUND_LEADER_GET = "|Hchannel:Battleground|h[战场领袖]|h %s:\32";
CHAT_BATTLEGROUND_SEND = "战场：\32";
CHAT_BN_CONVERSATION_GET = "%s:\32";
CHAT_BN_CONVERSATION_GET_LINK = "|Hchannel:BN_CONVERSATION:%d|h[%s. 对话]|h";
CHAT_BN_CONVERSATION_LIST = "%s %s";
CHAT_BN_CONVERSATION_SEND = "[%d. 对话]:";
CHAT_BN_WHISPER_GET = "%s悄悄地说：\32";
CHAT_BN_WHISPER_INFORM_GET = "发送给%s：\32";
CHAT_BN_WHISPER_SEND = "告诉%s：\32";
CHAT_BUBBLES_TEXT = "聊天泡泡";
CHAT_CHANNELS = "聊天频道";
CHAT_CHANNEL_GET = "%s：\32";
CHAT_CHANNEL_JOIN_GET = "%s进入频道。";
CHAT_CHANNEL_LEAVE_GET = "%s离开频道。";
CHAT_CHANNEL_LIST_GET = "|Hchannel:%d|h[%s]|h\32";
CHAT_CHANNEL_OWNER_NOTICE = "|Hchannel:%d|h[%s]|h 频道所有者被设定为%s。";
CHAT_CHANNEL_OWNER_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 频道所有者为%s。";
CHAT_CHANNEL_SEND = "[%d. %s]：\32";
CHAT_COMBAT_MISC_INFO_GET = "";
CHAT_CONFIGURATION = "设置";
CHAT_CONVERSATION_CONVERSATION_CONVERTED_TO_WHISPER_NOTICE = "%s已转而与%s进行密语聊天。";
CHAT_CONVERSATION_MEMBER_JOINED_NOTICE = "%s：%s加入了本次对话。";
CHAT_CONVERSATION_MEMBER_LEFT_NOTICE = "%s：%s退出了本次对话。";
CHAT_CONVERSATION_YOU_JOINED_CONVERSATION_NOTICE = "你加入了%s。";
CHAT_CONVERSATION_YOU_LEFT_CONVERSATION_NOTICE = "你离开了%s。";
CHAT_DEFAULT = "默认";
CHAT_DEFAULTS = "聊天默认";
CHAT_DEMOTE = "降级";
CHAT_DND_GET = "%s不愿被打扰：\32";
CHAT_EMOTE_GET = "%s\32";
CHAT_EMOTE_SEND = "%s\32";
CHAT_EMOTE_UNKNOWN = "做了一些奇怪的手势。";
CHAT_FILTERED = "无法向%s发送信息，因为你的话语中带有屏蔽词。";
CHAT_FLAG_AFK = "<离开>";
CHAT_FLAG_DND = "<忙碌>";
CHAT_FLAG_GM = "<GM>";
CHAT_GUILD_DEMOTE_SEND = "降职";
CHAT_GUILD_GET = "|Hchannel:Guild|h[公会]|h %s：\32";
CHAT_GUILD_INVITE_SEND = "邀请加入公会：";
CHAT_GUILD_LEADER_SEND = "设定公会领袖：";
CHAT_GUILD_MOTD_SEND = "公会纲领：";
CHAT_GUILD_PROMOTE_SEND = "提升";
CHAT_GUILD_SEND = "公会：\32";
CHAT_GUILD_UNINVITE_SEND = "从公会中开除：";
CHAT_HELP_TEXT_LINE1 = "聊天命令：";
CHAT_HELP_TEXT_LINE2 = "/#, /c, /csay - 向#频道发送信息 (例如：/1 你好！)";
CHAT_HELP_TEXT_LINE3 = "/chat, /chathelp - This help";
CHAT_HELP_TEXT_LINE4 = "/join, /channel, /chan - 加入一个频道";
CHAT_HELP_TEXT_LINE5 = "/leave, /chatleave, /chatexit [channel] - Leave a channel (or all channels)";
CHAT_HELP_TEXT_LINE6 = "/chatlist, /chatwho, /chatinfo [频道] - 列出频道或频道中的用户";
CHAT_HELP_TEXT_LINE7 = "/password, /pass <频道> <密码> - 改变密码";
CHAT_HELP_TEXT_LINE8 = "/own <频道> [玩家] - 显示或改变频道管理员";
CHAT_HELP_TEXT_LINE9 = "/mod, /moderator, /unmod, /unmoderator <频道> <玩家> - 改变一个玩家的频道管理权限";
CHAT_HELP_TEXT_LINE10 = "/mute, /squelch, /unvoice, /unmute, /unsquelch, /voice <频道> <玩家> - 改变一个玩家的发言权限";
CHAT_HELP_TEXT_LINE11 = "/cinvite, /chatinvite <频道> <玩家> - 邀请玩家进入频道";
CHAT_HELP_TEXT_LINE12 = "/ckick <频道> <玩家> - 将一个玩家踢出频道";
CHAT_HELP_TEXT_LINE13 = "/ban, /unban <channel> <player> - ban/unban a player from a channel";
CHAT_HELP_TEXT_LINE14 = "/announce, /ann <频道名> - 开启或者关闭加入/离开频道时的提示";
CHAT_HELP_TEXT_LINE15 = "/moderate <频道> - 打开/关闭频道设置";
CHAT_HELP_TEXT_LINE16 = "/away, /busy - 设置为离开或者忙碌状态";
CHAT_IGNORED = "%s已将你屏蔽。";
CHAT_INVALID_NAME_NOTICE = "无效的频道名";
CHAT_INVITE_NOTICE = "%2$s邀请你加入'%1$s'频道。";
CHAT_INVITE_NOTICE_POPUP = "%2$s邀请你加入'%1$s'频道。";
CHAT_INVITE_SEND = "邀请";
CHAT_INVITE_WRONG_FACTION_NOTICE = "%s的目标所属阵营错误。";
CHAT_JOIN = "加入";
CHAT_JOIN_HELP = "输入 /join <频道名称> [密码] 以创建新的频道或加入已有频道。";
CHAT_KICK = "踢出频道";
CHAT_LABEL = "聊天";
CHAT_LEAVE = "离开";
CHAT_LOCKED_TEXT = "锁定聊天设置";
CHAT_MODERATE = "管理";
CHAT_MODERATION_OFF_NOTICE = "|Hchannel:%d|h[%s]|h 频道修改已被%s禁止。";
CHAT_MODERATION_OFF_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 频道修改已被%s关闭。";
CHAT_MODERATION_ON_NOTICE = "|Hchannel:%d|h[%s]|h 频道修改已被%s打开。";
CHAT_MODERATION_ON_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 频道修改已被%s打开。";
CHAT_MONSTER_EMOTE_GET = "";
CHAT_MONSTER_PARTY_GET = "|Hchannel:Party|h[小队]|h %s：\32";
CHAT_MONSTER_SAY_GET = "%s说：\32";
CHAT_MONSTER_WHISPER_GET = "%s悄悄地说：\32";
CHAT_MONSTER_YELL_GET = "%s喊道：\32";
CHAT_MOUSE_WHEEL_SCROLL = "开启鼠标滑轮滚动";
CHAT_MSG_ACHIEVEMENT = "成就通告";
CHAT_MSG_AFK = "离开";
CHAT_MSG_BATTLEGROUND = "战场";
CHAT_MSG_BATTLEGROUND_LEADER = "战场领袖";
CHAT_MSG_BG_SYSTEM_ALLIANCE = "联盟区域信息";
CHAT_MSG_BG_SYSTEM_HORDE = "部落区域信息";
CHAT_MSG_BG_SYSTEM_NEUTRAL = "中立区域信息";
CHAT_MSG_BN_CONVERSATION = "实名对话";
CHAT_MSG_BN_WHISPER = "实名好友密语";
CHAT_MSG_CHANNEL_LIST = "频道列表";
CHAT_MSG_COMBAT_HONOR_GAIN = "荣誉点数";
CHAT_MSG_EMOTE = "表情";
CHAT_MSG_FILTERED = "被过滤聊天信息";
CHAT_MSG_GUILD = "公会";
CHAT_MSG_GUILD_ACHIEVEMENT = "公会通告";
CHAT_MSG_LOOT = "战利品";
CHAT_MSG_MONEY = "钱";
CHAT_MSG_MONSTER_EMOTE = "怪物表情";
CHAT_MSG_MONSTER_PARTY = "怪物小队";
CHAT_MSG_MONSTER_SAY = "怪物说";
CHAT_MSG_MONSTER_WHISPER = "怪物悄悄话";
CHAT_MSG_MONSTER_YELL = "怪物大喊";
CHAT_MSG_OFFICER = "公会官员";
CHAT_MSG_PARTY = "小队";
CHAT_MSG_PARTY_LEADER = "小队队长";
CHAT_MSG_RAID = "团队";
CHAT_MSG_RAID_BOSS_EMOTE = "首领怪物表情";
CHAT_MSG_RAID_LEADER = "团队领袖";
CHAT_MSG_RAID_WARNING = "团队通知";
CHAT_MSG_RESTRICTED = "受限制";
CHAT_MSG_SAY = "说";
CHAT_MSG_SKILL = "技能";
CHAT_MSG_SYSTEM = "系统";
CHAT_MSG_TEXT_EMOTE = "文字表情";
CHAT_MSG_WHISPER = "收到悄悄话";
CHAT_MSG_WHISPER_INFORM = "悄悄话";
CHAT_MSG_YELL = "大喊";
CHAT_MUTED_NOTICE = "|Hchannel:%d|h[%s]|h 你没有发言权限。";
CHAT_MUTED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 你没有发言的权限。";
CHAT_NAME_TEMPLATE = "交谈 %d";
CHAT_NOT_IN_AREA_NOTICE = "[%s] 你不在该频道所要求的区域中。";
CHAT_NOT_MEMBER_NOTICE = "不在%s频道里。";
CHAT_NOT_MODERATED_NOTICE = "%s未修改";
CHAT_NOT_MODERATOR_NOTICE = "不是|Hchannel:%d|h[%s]|h的管理员。";
CHAT_NOT_MODERATOR_NOTICE_BN = "不是|Hchannel:CHANNEL:%d|h[%s]|h.的管理员。";
CHAT_NOT_OWNER_NOTICE = "|Hchannel:%d|h[%s]|h 你不是频道的所有者";
CHAT_NOT_OWNER_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 你不是该频道的所有者。";
CHAT_OFFICER_GET = "|Hchannel:OFFICER|h[官员]|h %s：\32";
CHAT_OFFICER_SEND = "官员：\32";
CHAT_OPTIONS_LABEL = "聊天选项";
CHAT_OVERFLOW_LABEL = "列出所有标签页";
CHAT_OWNER = "指定为所有者";
CHAT_OWNER_CHANGED_NOTICE = "|Hchannel:%d|h[%s]|h 频道所有者被设定为%s。";
CHAT_OWNER_CHANGED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 频道所有者已变更为%s。";
CHAT_PARTY_GET = "|Hchannel:Party|h[小队]|h %s：\32";
CHAT_PARTY_GUIDE_GET = "|Hchannel:PARTY|h[地下城向导]|h %s:\32";
CHAT_PARTY_LEADER_GET = "|Hchannel:PARTY|h[队长]|h %s:\32";
CHAT_PARTY_SEND = "小队：\32";
CHAT_PASSWORD = "设置密码";
CHAT_PASSWORD_CHANGED_NOTICE = "|Hchannel:%d|h[%s]|h 密码已被%s修改。";
CHAT_PASSWORD_CHANGED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 频道密码已被%s更改。";
CHAT_PASSWORD_NOTICE_POPUP = "请输入'%1$s'的密码。";
CHAT_PLAYER_ALREADY_MEMBER_NOTICE = "|Hchannel:%d|h[%s]|h %s已在此频道中。";
CHAT_PLAYER_ALREADY_MEMBER_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 该玩家已在频道中。";
CHAT_PLAYER_BANNED_NOTICE = "|Hchannel:%d|h[%s]|h %s已被%s禁止进入频道。";
CHAT_PLAYER_BANNED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 玩家%s被%s禁止加入频道。";
CHAT_PLAYER_INVITED_NOTICE = "|Hchannel:%d|h[%s]|h 你邀请%s加入频道";
CHAT_PLAYER_INVITED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 你邀请%s加入该频道。";
CHAT_PLAYER_INVITE_BANNED_NOTICE = "|Hchannel:%d|h[%s]|h %s已经被踢出频道。";
CHAT_PLAYER_INVITE_BANNED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s已被禁止进入频道。";
CHAT_PLAYER_KICKED_NOTICE = "|Hchannel:%d|h[%s]|h %s被%s逐出频道。";
CHAT_PLAYER_KICKED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 玩家%s已被%s移出该频道。";
CHAT_PLAYER_NOT_BANNED_NOTICE = "|Hchannel:%d|h[%s]|h 玩家%s未被开除。";
CHAT_PLAYER_NOT_BANNED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 玩家%s不再被禁止进入频道。";
CHAT_PLAYER_NOT_FOUND_NOTICE = "|Hchannel:%d|h[%s]|h 未找到玩家%s。";
CHAT_PLAYER_NOT_FOUND_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 未找到玩家%s。";
CHAT_PLAYER_UNBANNED_NOTICE = "|Hchannel:%d|h[%s]|h %s被%s允许重新进入频道。";
CHAT_PLAYER_UNBANNED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 玩家%s被%s允许重新进入频道。";
CHAT_PROMOTE = "提升";
CHAT_PROMOTE_SEND = "提升";
CHAT_RAID_BOSS_EMOTE_GET = "";
CHAT_RAID_BOSS_WHISPER_GET = "";
CHAT_RAID_GET = "|Hchannel:raid|h[团队]|h %s：\32";
CHAT_RAID_LEADER_GET = "|Hchannel:raid|h[团队领袖]|h %s：\32";
CHAT_RAID_SEND = "团队：\32";
CHAT_RAID_WARNING_GET = "[团队通知] %s：\32";
CHAT_RAID_WARNING_SEND = "团队通知：\32";
CHAT_RESTRICTED = "试玩账号只能对已经将你加入好友列表的玩家发送悄悄话。";
CHAT_SAY_GET = "%s说：\32";
CHAT_SAY_SEND = "说：\32";
CHAT_SAY_UNKNOWN = "胡言乱语了一通。";
CHAT_SET_MODERATOR_NOTICE = "|Hchannel:%d|h[%s]|h %s获得修改权限。";
CHAT_SET_MODERATOR_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s获得了修改权限。";
CHAT_SET_SPEAK_NOTICE = "|Hchannel:%d|h[%s]|h %s获得语音权限。";
CHAT_SET_SPEAK_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s获得语音权限。";
CHAT_SET_VOICE_NOTICE = "|Hchannel:%d|h[%s]|h %s获得发言权限。";
CHAT_SET_VOICE_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s获得了发言权限。";
CHAT_SILENCE = "频道中沉默";
CHAT_STYLE = "聊天风格|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
CHAT_SUSPENDED_NOTICE = "退出频道： |Hchannel:%d|h[%s]|h";
CHAT_SUSPENDED_NOTICE_BN = "离开频道： |Hchannel:CHANNEL:%d|h[%s]|h";
CHAT_THROTTLED_NOTICE = "|Hchannel:%d|h[%s]|h 短时间内发送到此频道的信息条数是受限制的。请稍候再发送新的消息。";
CHAT_THROTTLED_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h 该频道内可发送的信息数量受限，请稍候再发送下一条信息。";
CHAT_UNINVITE_SEND = "开除";
CHAT_UNSET_MODERATOR_NOTICE = "|Hchannel:%d|h[%s]|h %s失去修改权限。";
CHAT_UNSET_MODERATOR_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s失去修改权限。";
CHAT_UNSET_SPEAK_NOTICE = "|Hchannel:%d|h[%s]|h %s失去语音权限。";
CHAT_UNSET_SPEAK_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s失去语音权限。";
CHAT_UNSET_VOICE_NOTICE = "|Hchannel:%d|h[%s]|h %s失去发言权限。";
CHAT_UNSET_VOICE_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s失去发言权限。";
CHAT_UNSILENCE = "频道中解除沉默";
CHAT_VOICE = "语音聊天";
CHAT_VOICE_OFF = "关闭语音";
CHAT_VOICE_OFF_NOTICE = "|Hchannel:%d|h[%s]|h 频道语音聊天已被%s禁用。";
CHAT_VOICE_OFF_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s关闭了频道语音。";
CHAT_VOICE_ON = "开启语音";
CHAT_VOICE_ON_NOTICE = "|Hchannel:%d|h[%s]|h 频道语音聊天已被%s启用。";
CHAT_VOICE_ON_NOTICE_BN = "|Hchannel:CHANNEL:%d|h[%s]|h %s开启了频道语音。";
CHAT_WHISPER_GET = "%s悄悄地说：\32";
CHAT_WHISPER_INFORM_GET = "发送给%s：\32";
CHAT_WHISPER_SEND = "告诉%s：\32";
CHAT_WHOLE_WINDOW_CLICKABLE = "点击窗口以关注";
CHAT_WINDOWS_COUNT = "%d个聊天窗口";
CHAT_WRONG_FACTION_NOTICE = "与%s错误地结盟。";
CHAT_WRONG_PASSWORD_NOTICE = "%s的密码错误。";
CHAT_YELL_GET = "%s喊道：\32";
CHAT_YELL_SEND = "大喊：\32";
CHAT_YELL_UNKNOWN = "向他的队友大喊大叫。";
CHAT_YELL_UNKNOWN_FEMALE = "向她的队友大喊大叫。";
CHAT_YOU_CHANGED_NOTICE = "改变频道：|Hchannel:%d|h[%s]|h";
CHAT_YOU_CHANGED_NOTICE_BN = "变更频道： |Hchannel:CHANNEL:%d|h[%s]|h";
CHAT_YOU_JOINED_NOTICE = "加入频道：|Hchannel:%d|h[%s]|h";
CHAT_YOU_JOINED_NOTICE_BN = "加入频道：|Hchannel:CHANNEL:%d|h[%s]|h";
CHAT_YOU_LEFT_NOTICE = "离开频道：|Hchannel:%d|h[%s]|h";
CHAT_YOU_LEFT_NOTICE_BN = "离开频道： |Hchannel:CHANNEL:%d|h[%s]|h";
CHESTSLOT = "胸部";
CHOOSE_BOX = "选择一个盒子：";
CHOOSE_RAID = "选择团队";
CHOOSE_STATIONERY = "选择信纸";
CHOOSE_YOUR_DUNGEON = "类型：";
CHOSEN_FOR_GMSURVEY = "你被选择填写一份GM调查问卷。";
CINEMATIC_SUBTITLES = "动画字幕";
CLASS = "职业";
CLASSIC_STYLE = "经典风格";
CLASS_COLORS = "职业颜色";
CLASS_SKILLS = "%s技能：";
CLEARED_AFK = "你现在不再处于离开状态";
CLEARED_DND = "你不再处于忙碌状态。";
CLEAR_AFK = "自动清除离开标签";
CLEAR_ALL = "全部清除";
CLEAR_FOCUS = "清除焦点";
CLICK_CAMERA_STYLE = "点击移动视角模式";
CLICK_FOR_ADDITIONAL_QUEST_LOCATIONS = "点击此处查看额外地点。";
CLICK_FOR_DETAILS = "显示细节";
CLICK_HERE_FOR_MORE_INFO = "点击获取更多信息";
CLICK_TO_ENTER_COMMENT = "点击这里输入注释";
CLICK_TO_INVITE_TO_CONVERSATION = "点击此处邀请另一名好友加入本对话。";
CLICK_TO_LEARN = "点击学习技能";
CLICK_TO_MOVE = "点击移动";
CLICK_TO_REMOVE_ADDITIONAL_QUEST_LOCATIONS = "点击此处隐藏额外地点。";
CLICK_TO_START_CONVERSATION = "点击此处以开启与该名玩家及其他好友进行的对话。";
CLIENT_LOGOUT_ALERT = "你的某些设置将在你登出游戏并重新登录之后生效。";
CLIENT_RESTART_ALERT = "你的有些设置需要你重新启动游戏才能够生效。";
CLOSE = "关闭";
CLOSES_IN = "剩余时间";
CLOSE_AND_LEAVE_CHAT_CONVERSATION_WINDOW = "离开对话";
CLOSE_CHAT = "结束交谈";
CLOSE_CHAT_CONVERSATION_WINDOW = "关闭对话窗口";
CLOSE_CHAT_WHISPER_WINDOW = "关闭密语窗口";
CLOSE_CHAT_WINDOW = "关闭窗口";
CLOSE_LOG = "关闭日志";
COD = "付款取信";
COD_AMOUNT = "付款取信邮件的金额";
COD_CONFIRMATION = "收下这件物品将花费：";
COD_INSUFFICIENT_MONEY = "你没有足够的钱来支付付款取信邮件。";
COD_PAYMENT = "付款取信邮件的费用：%s";
COINPICKUP_CANCEL = "取消";
COLOR = "颜色";
COLORBLIND_NAMEWRAPPER_ENEMY = "%s - 敌对";
COLORBLIND_NAMEWRAPPER_FRIENDLY = "%s - 友善";
COLORBLIND_NAMEWRAPPER_NEUTRAL = "%s - 中立";
COLORIZE = "彩色标记：";
COLORS = "颜色";
COLOR_BY_SCHOOL = "按类型区分颜色";
COLOR_PICKER = "颜色选择器";
COMBAT = "战斗";
COMBATLOGDISABLED = "战斗记录已被禁止。";
COMBATLOGENABLED = "战斗记录保存在Logs\\WoWCombatLog.txt中";
COMBATLOG_ARENAPOINTSAWARD = "你获得了%d点竞技场点数奖励。";
COMBATLOG_DEFAULTS = "战斗记录默认";
COMBATLOG_DISHONORGAIN = "%s死亡，非荣誉击杀。";
COMBATLOG_FILTER_STRING_CUSTOM_UNIT = "自定义单位";
COMBATLOG_FILTER_STRING_FRIENDLY_UNITS = "友方";
COMBATLOG_FILTER_STRING_HOSTILE_PLAYERS = "敌方玩家";
COMBATLOG_FILTER_STRING_HOSTILE_UNITS = "敌方单位";
COMBATLOG_FILTER_STRING_ME = "我";
COMBATLOG_FILTER_STRING_MY_PET = "宠物";
COMBATLOG_FILTER_STRING_NEUTRAL_UNITS = "中立";
COMBATLOG_FILTER_STRING_UNKNOWN_UNITS = "未知";
COMBATLOG_HIGHLIGHT_ABILITY = "技能";
COMBATLOG_HIGHLIGHT_DAMAGE = "伤害";
COMBATLOG_HIGHLIGHT_KILL = "杀死";
COMBATLOG_HIGHLIGHT_SCHOOL = "类型";
COMBATLOG_HONORAWARD = "你得到了%d点荣誉的奖励。";
COMBATLOG_HONORGAIN = "%s死亡，荣誉击杀军衔：%s（%d荣誉点数）";
COMBATLOG_HONORGAIN_NO_RANK = "%s死亡，荣誉击杀（%d 荣誉点数）";
COMBATLOG_UNKNOWN_UNIT = "某目标";
COMBATLOG_XPGAIN_EXHAUSTION1 = "%s死亡，你获得了%d点经验值。（%s点经验值的%s奖励）";
COMBATLOG_XPGAIN_EXHAUSTION1_GROUP = "%s死亡，你获得了%d点经验值。（%s点%s奖励，+%d组队奖励）";
COMBATLOG_XPGAIN_EXHAUSTION1_RAID = "%s死亡，你获得了%d点经验值。（%s点%s奖励，-%d点团队惩罚）";
COMBATLOG_XPGAIN_EXHAUSTION2 = "%s死亡，你获得了%d点经验值。（%s点经验值的%s奖励）";
COMBATLOG_XPGAIN_EXHAUSTION2_GROUP = "%s死亡，你获得了%d点经验值。（%s点%s奖励，+%d组队奖励）";
COMBATLOG_XPGAIN_EXHAUSTION2_RAID = "%s死亡，你获得了%d点经验值。（%s点%s奖励，-%d点团队惩罚）";
COMBATLOG_XPGAIN_EXHAUSTION4 = "%s死亡，你获得了%d点经验值。（%s点经验值的%s惩罚）";
COMBATLOG_XPGAIN_EXHAUSTION4_GROUP = "%s死亡，你获得了%d点经验值。（%s点%s惩罚，+%d组队奖励）";
COMBATLOG_XPGAIN_EXHAUSTION4_RAID = "%s死亡，你获得了%d点经验值。（%s点%s惩罚，-%d点团队惩罚）";
COMBATLOG_XPGAIN_EXHAUSTION5 = "%s死亡，你获得了%d点经验值。（%s点经验值的%s惩罚）";
COMBATLOG_XPGAIN_EXHAUSTION5_GROUP = "%s死亡，你获得了%d点经验值。（%s点%s惩罚，+%d组队奖励）";
COMBATLOG_XPGAIN_EXHAUSTION5_RAID = "%s死亡，你获得了%d点经验值。（%s点%s惩罚，-%d点团队惩罚）";
COMBATLOG_XPGAIN_FIRSTPERSON = "%s死亡，你获得%d点经验值。";
COMBATLOG_XPGAIN_FIRSTPERSON_GROUP = "%s死亡，你获得了%d点经验值。（+%d点组队奖励）";
COMBATLOG_XPGAIN_FIRSTPERSON_RAID = "%s死亡，你获得了%d点经验值。（-%d点团队惩罚）";
COMBATLOG_XPGAIN_FIRSTPERSON_UNNAMED = "你获得了%d点经验值。";
COMBATLOG_XPGAIN_FIRSTPERSON_UNNAMED_GROUP = "你获得了%d点经验值。（+%d点组队奖励）";
COMBATLOG_XPGAIN_FIRSTPERSON_UNNAMED_RAID = "你获得%d点经验值。（-%d点团队惩罚）";
COMBATLOG_XPGAIN_QUEST = "你获得了%d点经验值。（%s经验值%s奖励）";
COMBATLOG_XPLOSS_FIRSTPERSON_UNNAMED = "你损失了%d点经验值。";
COMBATTEXT_LABEL = "战斗信息";
COMBATTEXT_SUBTEXT = "这些选项可以让你配置在屏幕中间显示出来的浮动战斗信息文字，以便于你及时了解战况。";
COMBAT_ENEMY = "作战敌人";
COMBAT_ERROR = "作战错误";
COMBAT_FACTION_CHANGE = "声望";
COMBAT_HONOR_GAIN = "荣誉";
COMBAT_LABEL = "战斗";
COMBAT_LOG = "战斗记录";
COMBAT_LOG_MENU_BOTH = "显示所有与%s有关的信息？";
COMBAT_LOG_MENU_EVERYTHING = "全部显示";
COMBAT_LOG_MENU_INCOMING = "%s发生了什么？";
COMBAT_LOG_MENU_OUTGOING = "%s做了些什么？";
COMBAT_LOG_MENU_OUTGOING_ME = "%s对你做了什么？";
COMBAT_LOG_MENU_REVERT = "退回上一个过滤条件";
COMBAT_LOG_MENU_SAVE = "保存为新的过滤条件";
COMBAT_LOG_MENU_SPELL_HIDE = "隐藏与此条类似的信息。";
COMBAT_LOG_MENU_SPELL_LINK = "将%s链接到聊天频道。";
COMBAT_LOG_MENU_SPELL_TYPE_HEADER = "信息类型";
COMBAT_LOG_UNIT_YOU_ENABLED = "1";
COMBAT_MESSAGES = "作战信息";
COMBAT_MISC = "作战其它";
COMBAT_MISC_INFO = "其它信息";
COMBAT_PARTY = "作战小队";
COMBAT_RATING_NAME1 = "武器技能";
COMBAT_RATING_NAME10 = "爆击等级";
COMBAT_RATING_NAME11 = "爆击等级";
COMBAT_RATING_NAME15 = "韧性";
COMBAT_RATING_NAME2 = "防御等级";
COMBAT_RATING_NAME24 = "精准";
COMBAT_RATING_NAME3 = "躲闪等级";
COMBAT_RATING_NAME4 = "招架等级";
COMBAT_RATING_NAME5 = "格挡等级";
COMBAT_RATING_NAME6 = "命中等级";
COMBAT_RATING_NAME7 = "命中等级";
COMBAT_RATING_NAME8 = "命中等级";
COMBAT_RATING_NAME9 = "爆击等级";
COMBAT_SELF = "作战自身";
COMBAT_SUBTEXT = "这些选项会影响到你的角色在战斗中的行为，并且可以供你改变在用户界面中显示战斗信息的方式。";
COMBAT_TEXT_ABSORB = "吸收";
COMBAT_TEXT_ARENA_POINTS_GAINED = "竞技场点数：%s";
COMBAT_TEXT_BLOCK = "格挡";
COMBAT_TEXT_COMBO_POINTS = "<%d连击>";
COMBAT_TEXT_DEFLECT = "偏转";
COMBAT_TEXT_DODGE = "躲闪";
COMBAT_TEXT_EVADE = "闪避";
COMBAT_TEXT_FLOAT_MODE_LABEL = "战斗信息飘浮模式";
COMBAT_TEXT_HONOR_GAINED = "荣誉 %s";
COMBAT_TEXT_IMMUNE = "免疫";
COMBAT_TEXT_LABEL = "浮动战斗信息";
COMBAT_TEXT_MISS = "未命中";
COMBAT_TEXT_NONE = "无";
COMBAT_TEXT_PARRY = "招架";
COMBAT_TEXT_REFLECT = "反射";
COMBAT_TEXT_RESIST = "抵抗";
COMBAT_TEXT_RUNE_BLOOD = "鲜血符文";
COMBAT_TEXT_RUNE_DEATH = "死亡符文";
COMBAT_TEXT_RUNE_FROST = "冰霜符文";
COMBAT_TEXT_RUNE_UNHOLY = "邪恶符文";
COMBAT_TEXT_SCROLL_ARC = "弧形";
COMBAT_TEXT_SCROLL_DOWN = "向下滚动";
COMBAT_TEXT_SCROLL_DOWN_TEXT = "记录向下滚动";
COMBAT_TEXT_SCROLL_UP = "向上滚动";
COMBAT_TEXT_SHOW_AURAS_TEXT = "光环";
COMBAT_TEXT_SHOW_AURA_FADE_TEXT = "光环效果消失";
COMBAT_TEXT_SHOW_COMBAT_STATE_TEXT = "战斗状态";
COMBAT_TEXT_SHOW_COMBO_POINTS_TEXT = "连击点数";
COMBAT_TEXT_SHOW_DODGE_PARRY_MISS_TEXT = "躲闪/招架/未命中";
COMBAT_TEXT_SHOW_ENERGIZE_TEXT = "能量获取";
COMBAT_TEXT_SHOW_FRIENDLY_NAMES_TEXT = "友方治疗者姓名";
COMBAT_TEXT_SHOW_HONOR_GAINED_TEXT = "荣誉获取";
COMBAT_TEXT_SHOW_LOW_HEALTH_MANA_TEXT = "生命/法力过低";
COMBAT_TEXT_SHOW_PERIODIC_ENERGIZE_TEXT = "周期性能量获取";
COMBAT_TEXT_SHOW_REACTIVES_TEXT = "反击法术和技能";
COMBAT_TEXT_SHOW_REPUTATION_TEXT = "声望变化";
COMBAT_TEXT_SHOW_RESISTANCES_TEXT = "伤害减免";
COMBAT_THREAT_DECREASE_0 = "目标改变！";
COMBAT_THREAT_DECREASE_1 = "目标改变！";
COMBAT_THREAT_DECREASE_2 = "威胁值降低";
COMBAT_THREAT_INCREASE_1 = "高威胁值";
COMBAT_THREAT_INCREASE_3 = "正在攻击你！";
COMBAT_XP_GAIN = "经验";
COMBAT_ZONE = "（战斗区域）";
COMMAND = "命令";
COMMENT = "注释";
COMMENTS_COLON = "注释：";
COMPANIONS = "小伙伴";
COMPARE_ACHIEVEMENTS = "比较成就";
COMPLAINT_ADDED = "已提交投诉。";
COMPLETE = "完成";
COMPLETE_QUEST = "完成任务";
CONFIRM_ACCEPT_PVP_QUEST = "接受这个任务之后，你将被标记为PvP状态，直到你放弃或完成此任务。你确定要接受任务吗？";
CONFIRM_ACCEPT_SOCKETS = "镶嵌之后，一颗或多颗宝石将被摧毁。你确定要镶嵌新的宝石吗？";
CONFIRM_BATTLEFIELD_ENTRY = "你现在可以进入%s了，请选择：";
CONFIRM_BINDER = "你想要将%s设为你的新家吗？";
CONFIRM_BUY_BANK_SLOT = "你愿意付钱购买银行空位吗？";
CONFIRM_BUY_GUILDBANK_TAB = "你是否想要购买一个公会银行标签？";
CONFIRM_BUY_STABLE_SLOT = "你确定要支付以下数量的金币来购买一个新的兽栏栏位吗？";
CONFIRM_COMBAT_FILTER_DEFAULTS = "你确定要将过滤条件设定为初始状态吗？";
CONFIRM_COMBAT_FILTER_DELETE = "你确认要删除这个过滤条件？";
CONFIRM_COMPLETE_EXPENSIVE_QUEST = "完成这个任务需要缴纳如下数额的金币。你确定要完成这个任务吗？";
CONFIRM_DELETE_EQUIPMENT_SET = "你确认要删除装备方案%s吗？";
CONFIRM_DELETING_CHARACTER_SPECIFIC_BINDINGS = "确定要切换到通用键位设定吗？所有本角色专用的键位设定都将被永久删除。";
CONFIRM_GLYPH_PLACEMENT = "你确定要嵌入这枚雕文吗？已有的雕文将被摧毁。";
CONFIRM_GUILD_DISBAND = "你真的要解散公会吗？";
CONFIRM_GUILD_LEAVE = "确定要退出%s？";
CONFIRM_GUILD_PROMOTE = "确定要将%s提升为会长？";
CONFIRM_HIGH_COST_ITEM = "你确定要花费如下金额的货币购买%s吗？";
CONFIRM_LEARN_PREVIEW_TALENTS = "你确定要学习这些天赋吗？";
CONFIRM_LEAVE_QUEUE = "你确定要离开这块集合石的队列吗？";
CONFIRM_LOOT_DISTRIBUTION = "你想要将%s分配给%s，确定吗？";
CONFIRM_LOSE_BINDING_CHANGES = "如果你在通用键位设定和角色专用键位设定之间切换，所有未保存的更改都将丢失。";
CONFIRM_OVERWRITE_EQUIPMENT_SET = "你已经有一个名为%s的装备方案了。是否要覆盖已有方案？";
CONFIRM_PET_UNLEARN = "你确定要让宠物遗忘所有技能吗？每次进行这种操作的费用都会递增。";
CONFIRM_PURCHASE_TOKEN_ITEM = "你确定要将%s兑换为下列物品？";
CONFIRM_REFUND_MAX_ARENA_POINTS = "你的竞技场点数已接近上限。出售这件物品会让你损失%d点竞技场点数。确认要继续吗？";
CONFIRM_REFUND_MAX_HONOR = "你的荣誉点数已接近上限。卖掉这件物品会让你损失%d点荣誉点数。确认要继续吗？";
CONFIRM_REFUND_MAX_HONOR_AND_ARENA = "你的荣誉点数和竞技场点数即将达到满值。卖掉此物品会使你损失%1$d的荣誉点数和%2$d的竞技场点数。要继续吗？";
CONFIRM_REFUND_TOKEN_ITEM = "你确定要退还下面这件物品，获得%s的退款吗？";
CONFIRM_REMOVE_GLYPH = "你确定要移除%s吗？该雕文将被永久性地摧毁。";
CONFIRM_RESET_INSTANCES = "你确定想要重置你的所有副本吗？";
CONFIRM_RESET_INTERFACE_SETTINGS = "你想要将所有用户界面和插件设置重置为默认状态，还是只重置这个界面或插件的设置？";
CONFIRM_RESET_SETTINGS = "你想要将所有用户界面和插件设置重置为默认状态吗？将会立即对所有设置生效。";
CONFIRM_SUMMON = "%s想将你召唤到%s去。这个法术将在%d%s后取消。";
CONFIRM_TALENT_WIPE = "你确定要遗忘所有的天赋吗？这将会解散你的宠物，并且每次遗忘天赋的花费都会比上一次更高。";
CONFIRM_TEAM_DISBAND = "你真的要解散你的竞技场战队：%s吗？";
CONFIRM_TEAM_KICK = "确定要从%2$s中移除%1$s？";
CONFIRM_TEAM_LEAVE = "确认要离开%s？";
CONFIRM_TEAM_PROMOTE = "确定要将%s提升为队长？";
CONFIRM_XP_LOSS = "如果你找到你的尸体，那么你可以在没有任何惩罚的情况下复活。现在由我来复活你，那么你的所有物品（包括已装备的和物品栏中的）将损失25%%的耐久度，你也要承受%s的复活虚弱时间。";
CONFIRM_XP_LOSS_AGAIN = "记住，如果你找到你的尸体，那么你可以在没有任何惩罚的情况下复活。你决定现在以承受所有物品（包括已装备的和物品栏中的）损失25%%的耐久度并承受%s的复活虚弱时间的代价来复活吗？";
CONFIRM_XP_LOSS_AGAIN_NO_DURABILITY = "记住，如果你找到自己的尸体的话，将不会受到任何惩罚。你决定现在以承受%s的复活虚弱时间的代价来复活吗？";
CONFIRM_XP_LOSS_AGAIN_NO_SICKNESS = "记住，如果你找到自己的尸体的话，将不会受到任何惩罚。你决定现在以损失所有装备25%耐久度的代价来复活吗？";
CONFIRM_XP_LOSS_NO_DURABILITY = "如果你找到自己的尸体的话，将不会受到任何惩罚。否则，你要承受%s的复活虚弱。";
CONFIRM_XP_LOSS_NO_SICKNESS = "如果你找到你的尸体，你可以复活而且不会受到任何惩罚。如果让我复活你，那么你所有的物品会损失25%%的耐久度（包括装备着的和包中的物品）。";
CONFIRM_XP_LOSS_NO_SICKNESS_NO_DURABILITY = "你可以找到你的尸体并在尸体位置复活。10级或以下的玩家可以在此复活并不受任何惩罚。";
CONFIRM_YOUR_ROLE = "确定你的职责：";
CONSOLIDATE_BUFFS_TEXT = "整理增益效果";
CONTAINER_SLOTS = "%d格%s";
CONTESTED_TERRITORY = "（争夺中的领土）";
CONTINENT = "大陆";
CONTINUE = "继续";
CONTINUED = "……";
CONTROLS_LABEL = "控制";
CONTROLS_SUBTEXT = "这些是基本的游戏相关控制选项，会影响到你的角色与游戏中的物体或其他玩家的互动行为方式。";
CONVERSATION_MODE = "新实名对话|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
CONVERSATION_MODE_INLINE = "一致模式";
CONVERSATION_MODE_POPOUT = "新标签页";
CONVERSATION_NAME = "%d. 对话";
CONVERT_TO_RAID = "转化为团队";
COOLDOWN_ON_LEAVE_COMBAT = "（脱离战斗后开始冷却）";
COOLDOWN_REMAINING = "剩余冷却时间：";
COPPER_AMOUNT = "%d铜币";
COPPER_AMOUNT_SYMBOL = "铜";
COPPER_AMOUNT_TEXTURE = "%d\124TInterface\\MoneyFrame\\UI-CopperIcon:%d:%d:2:0\124t";
COPY_FILTER = "复制过滤";
COPY_NAME = "复制名字";
CORPSE = "尸体";
CORPSE_RED = "尸体";
CORPSE_TOOLTIP = "%s的尸体";
COSTS_LABEL = "花费：";
CRAFT_IS_MAKEABLE = "材料齐备";
CRAFT_IS_MAKEABLE_TOOLTIP = "只显示你已经携带了足够材料的配方。";
CREATE = "制造";
CREATED_ITEM = "%s制造了：%s。";
CREATED_ITEM_MULTIPLE = "%s制造了：%sx%d。";
CREATE_ALL = "全部制造";
CREATE_AUCTION = "开始拍卖";
CREATE_CONVERSATION_WITH = "创建对话";
CREATE_MACROS = "创建宏";
CREATURE = "怪物";
CREATURE_MESSAGES = "怪物信息";
CRIT_ABBR = "Crit";
CRUSHING_TRAILER = "(碾压)";
CR_BLOCK_TOOLTIP = "格挡等级%d使格挡几率提高%.2f%%\n格挡抵消%d点伤害。";
CR_CRIT_MELEE_TOOLTIP = "爆击等级 %d (+%.2f%% 爆击几率)";
CR_CRIT_RANGED_TOOLTIP = "爆击等级 %d (+%.2f%% 爆击几率)";
CR_DODGE_TOOLTIP = "躲闪等级%d使躲闪几率提高%.2f%%|n|cff888888（在效果递减之前）|r";
CR_EXPERTISE_TOOLTIP = "被躲闪或招架的几率降低%s\n精准等级 %d (+%d 精准)";
CR_HASTE_RATING_TOOLTIP = "急速等级 %d (%.2f%% 加速)";
CR_HIT_MELEE_TOOLTIP = "使你的近战攻击命中%d级目标的几率提高%.2f%%\n\n护甲穿透等级%d（敌方目标护甲值降低最多%.2f%%）。";
CR_HIT_RANGED_TOOLTIP = "使你的远程攻击命中%d级目标的几率提高%.2f%%\n\n护甲穿透等级%d（敌方目标护甲值降低最多%.2f%%）。";
CR_HIT_SPELL_TOOLTIP = "使你的法术命中%d级目标的几率提高%.2f%%\n\n法术穿透%d（降低目标抗性%d点）";
CR_PARRY_TOOLTIP = "招架等级%d使招架几率提高%.2f%%|n|cff888888（在效果递减之前）|r";
CTRL_KEY = "CTRL键";
CTRL_KEY_TEXT = "CTRL";
CURRENCY = "货币";
CURRENCY_AMOUNT_REFUND_FORMAT = "%d %s";
CURRENTLY_EQUIPPED = "当前装备";
CURRENT_BID = "当前价格";
CURRENT_PET = "当前宠物：";
CURRENT_QUESTS = "现有任务";
CURRENT_SETTINGS = "这些设置";
CUSTOM = "自定义";
DAILY = "日常";
DAILY_QUESTS_REMAINING = "你今天只能再完成%d个日常任务了。";
DAILY_QUEST_TAG_TEMPLATE = "日常%s";
DAMAGE = "伤害";
DAMAGER = "伤害输出";
DAMAGE_BONUS_TOOLTIP = "增加武器伤害";
DAMAGE_DONE_TOOLTIP = "造成的伤害总量。";
DAMAGE_NUMBER = "伤害数值";
DAMAGE_PER_SECOND = "每秒伤害";
DAMAGE_SCHOOL2 = "神圣";
DAMAGE_SCHOOL3 = "火焰";
DAMAGE_SCHOOL4 = "自然";
DAMAGE_SCHOOL5 = "冰霜";
DAMAGE_SCHOOL6 = "暗影";
DAMAGE_SCHOOL7 = "奥术";
DAMAGE_SCHOOL_TEXT = "伤害类型";
DAMAGE_SHIELD = "伤害护盾";
DAMAGE_SHIELD_COMBATLOG_TOOLTIP = "当法术或技能（如荆棘术）对近战攻击造成反弹伤害时显示信息。";
DAMAGE_TEMPLATE = "%d - %d伤害";
DAMAGE_TEMPLATE_WITH_SCHOOL = "%d - %d 点%s伤害";
DAMAGE_TOOLTIP = "武器伤害";
DATE_COMPLETED = "已完成：%s";
DAYS = "|4天:天;";
DAYS_ABBR = "%d|4天:天;";
DAY_ONELETTER_ABBR = "%d d";
DEAD = "死亡";
DEATHBINDALREADYBOUND = "你已经绑定在这里了！";
DEATHBIND_SUCCESSFUL = "你的灵魂已经绑定在这里了。";
DEATHS = "死亡";
DEATHS_COMBATLOG_TOOLTIP = "当某生物死亡时显示信息。";
DEATHS_TOOLTIP = "你被杀死的次数。";
DEATH_CORPSE_SKINNED = "徽记被取走 - 你只能在墓地复活";
DEATH_EFFECT = "死亡效果";
DEATH_RELEASE = "释放灵魂";
DEATH_RELEASE_NOTIMER = "你死亡了。要释放灵魂到最近的墓地吗？";
DEATH_RELEASE_SPECTATOR = "你死亡了。释放灵魂后将进入观察模式。";
DEATH_RELEASE_TIMER = "%d%s后释放灵魂";
DEBUFF_SYMBOL_CURSE = "诅";
DEBUFF_SYMBOL_DISEASE = "疾";
DEBUFF_SYMBOL_MAGIC = "魔";
DEBUFF_SYMBOL_POISON = "毒";
DEBUG_FRAMESTACK = "框架栈";
DECLENSION_SET = "%s - %s";
DECLINE = "拒绝";
DEDE = "德语";
DEFAULT = "默认";
DEFAULTS = "默认设置";
DEFAULT_AFK_MESSAGE = "离开";
DEFAULT_AGILITY_TOOLTIP = "提高你的远程武器攻击强度。|n提高所有武器的爆击几率。|n提高你的护甲值和躲避攻击的几率。";
DEFAULT_COMBATLOG_FILTER_NAME = "过滤条件%d";
DEFAULT_DND_MESSAGE = "忙碌";
DEFAULT_INTELLECT_TOOLTIP = "提高你的武器技能熟练度提升速度。";
DEFAULT_SPIRIT_TOOLTIP = "提高你的生命值和法力值回复速度。";
DEFAULT_STAMINA_TOOLTIP = "提高你的生命值上限。";
DEFAULT_STAT1_TOOLTIP = "攻击强度提高%d点";
DEFAULT_STAT2_TOOLTIP = "爆击几率提高%.2f%%|n护甲值提高%d点";
DEFAULT_STAT3_TOOLTIP = "生命值提高%d点";
DEFAULT_STAT4_TOOLTIP = "法力值提高%d点|n法术爆击几率提高%.2f%%";
DEFAULT_STAT5_TOOLTIP = "在非战斗状态下，使你的生命值回复速度提高每秒%d点";
DEFAULT_STATARMOR_TOOLTIP = "受到的物理伤害减免%0.2f%%";
DEFAULT_STATDEFENSE_TOOLTIP = "防御等级%d（+%d 防御）|n躲闪、格挡和招架几率提高%.2f%%|n被命中和被爆击的几率降低%.2f%%|n|cff888888（在效果递减之前）|r";
DEFAULT_STATSPELLBONUS_TOOLTIP = "法术攻击的伤害加成。";
DEFENSE = "防御";
DEFENSE_ABBR = "Def";
DEFENSE_TOOLTIP = "防御等级";
DEFLECT = "偏转";
DELETE = "删除";
DELETE_GOOD_ITEM = "你真的要摧毁%s吗？\n\n请在输入框中输入\"DELETE\"以确认。";
DELETE_ITEM = "你确定要摧毁%s？";
DELETE_ITEM_CONFIRM_STRING = "DELETE";
DELETE_MAIL_CONFIRMATION = "删除这封邮件会摧毁%s";
DELETE_MONEY_CONFIRMATION = "删除这封邮件会摧毁：";
DEMOTE = "降职";
DEPOSIT = "存放";
DEPOSIT_COLON = "保管费：";
DEPTH_CONVERGENCE = "屏幕深度";
DESERTER = "逃亡者";
DESKTOP_GAMMA = "使用桌面Gamma";
DESTROY_GEM = "被摧毁的宝石";
DISABLE = "禁用";
DISABLE_ADDONS = "禁用插件";
DISABLE_SPAM_FILTER = "禁用垃圾信息过滤";
DISGUISE = "伪装";
DISHONORABLE_KILLS = "非荣誉击杀";
DISPELS = "驱散";
DISPEL_AURA_COMBATLOG_TOOLTIP = "当光环被移除、破坏或偷取时显示信息。";
DISPLAY = "显示";
DISPLAY_ACTIVE_CHANNEL = "显示活动频道";
DISPLAY_CHANNEL_PULLOUT = "显示聊天拖出列表";
DISPLAY_FREE_BAG_SLOTS = "显示剩余背包空间";
DISPLAY_LABEL = "显示";
DISPLAY_ON_CHARACTER = "试穿效果";
DISPLAY_ON_CHAR_TOOLTIP = "钩选此框可以在你的角色身上显示任何已选中的拍卖物品的装备效果。\n\n按住CTRL并左键点击游戏中的任何可装备的物品均可让你的角色查看试穿效果。";
DISPLAY_OPTIONS = "显示设置";
DISPLAY_SUBTEXT = "这些选项会影响到某些特定的界面元素和游戏角色元素是否显示出来。";
DK = "非荣誉击杀";
DMG = "Dmg";
DND = "忙碌";
DODGE = "躲闪";
DODGE_CHANCE = "躲闪几率";
DONE = "完成";
DONE_BY = "来源为：";
DONE_TO = "目标为：";
DPS_TEMPLATE = "（每秒伤害%.1f）";
DRAINS = "吸取";
DRESSUP_FRAME = "试衣间";
DRESSUP_FRAME_INSTRUCTIONS = "按住CTRL并左键点击额外的物品可以预览该物品的装备效果";
DRUID_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
DRUNK_MESSAGE_ITEM_OTHER1 = "%s喝下%s之后变得清醒了一些。";
DRUNK_MESSAGE_ITEM_OTHER2 = "%s喝下%s之后有点醉醺醺的。";
DRUNK_MESSAGE_ITEM_OTHER3 = "%s灌下%s之后酩酊大醉。";
DRUNK_MESSAGE_ITEM_OTHER4 = "%s灌下%s之后烂醉如泥。";
DRUNK_MESSAGE_ITEM_SELF1 = "你喝下%s之后变得清醒了一些。";
DRUNK_MESSAGE_ITEM_SELF2 = "你喝下%s之后有点醉醺醺的。";
DRUNK_MESSAGE_ITEM_SELF3 = "你灌下%s之后酩酊大醉。";
DRUNK_MESSAGE_ITEM_SELF4 = "你灌下%s之后烂醉如泥。";
DRUNK_MESSAGE_OTHER1 = "%s开始变得冷静下来了。";
DRUNK_MESSAGE_OTHER2 = "%s看上去有些喝醉了。";
DRUNK_MESSAGE_OTHER3 = "%s看上去喝醉了。";
DRUNK_MESSAGE_OTHER4 = "%s看上去完全喝醉了。";
DRUNK_MESSAGE_SELF1 = "你再次感觉清醒。";
DRUNK_MESSAGE_SELF2 = "你感到喝醉了。喔哦！";
DRUNK_MESSAGE_SELF3 = "你感觉喝醉了。喔哦！";
DRUNK_MESSAGE_SELF4 = "你感觉完全喝醉了。";
DUEL = "决斗";
DUEL_COUNTDOWN = "决斗开始：%d";
DUEL_OUTOFBOUNDS_TIMER = "正在离开决斗区域,你将在%d%s内失败。";
DUEL_REQUESTED = "%s向你发出决斗要求。";
DUEL_WINNER_KNOCKOUT = "%1$s在决斗中战胜了%2$s";
DUEL_WINNER_RETREAT = "%2$s在决斗中输给了%1$s";
DUNGEONS_BUTTON = "地下城查找器";
DUNGEON_COMPLETED = "地下城完成！";
DUNGEON_DIFFICULTY = "地下城难度";
DUNGEON_DIFFICULTY1 = "5人";
DUNGEON_DIFFICULTY2 = "5人（英雄）";
DUNGEON_DIFFICULTY3 = "史诗";
DUNGEON_DIFFICULTY_5PLAYER = "5人";
DUNGEON_DIFFICULTY_5PLAYER_HEROIC = "5人（英雄）";
DUNGEON_FLOOR_AHNKAHET1 = "安卡哈特";
DUNGEON_FLOOR_AZJOLNERUB1 = "孵化深渊";
DUNGEON_FLOOR_AZJOLNERUB2 = "哈多诺克斯之巢";
DUNGEON_FLOOR_AZJOLNERUB3 = "镀金之门";
DUNGEON_FLOOR_COTSTRATHOLME0 = "斯坦索姆外围";
DUNGEON_FLOOR_COTSTRATHOLME1 = "斯坦索姆城";
DUNGEON_FLOOR_DALARAN1 = "达拉然城";
DUNGEON_FLOOR_DALARAN2 = "达拉然下水道";
DUNGEON_FLOOR_DRAKTHARONKEEP1 = "达克萨隆前庭";
DUNGEON_FLOOR_DRAKTHARONKEEP2 = "达克萨隆悬崖";
DUNGEON_FLOOR_GUNDRAK1 = "古达克";
DUNGEON_FLOOR_HALLSOFLIGHTNING1 = "坚韧军营";
DUNGEON_FLOOR_HALLSOFLIGHTNING2 = "造物者步道";
DUNGEON_FLOOR_HALLSOFREFLECTION1 = "映像大厅";
DUNGEON_FLOOR_ICECROWNCITADEL1 = "堡垒下层";
DUNGEON_FLOOR_ICECROWNCITADEL2 = "颅骨之墙";
DUNGEON_FLOOR_ICECROWNCITADEL3 = "死亡使者之台";
DUNGEON_FLOOR_ICECROWNCITADEL4 = "冰霜女王的巢穴";
DUNGEON_FLOOR_ICECROWNCITADEL5 = "上层区域";
DUNGEON_FLOOR_ICECROWNCITADEL6 = "皇家区";
DUNGEON_FLOOR_ICECROWNCITADEL7 = "冰封王座";
DUNGEON_FLOOR_ICECROWNCITADEL8 = "霜之哀伤";
DUNGEON_FLOOR_NAXXRAMAS1 = "构造区";
DUNGEON_FLOOR_NAXXRAMAS2 = "蜘蛛区";
DUNGEON_FLOOR_NAXXRAMAS3 = "军事区";
DUNGEON_FLOOR_NAXXRAMAS4 = "瘟疫区";
DUNGEON_FLOOR_NAXXRAMAS5 = "大墓地下层";
DUNGEON_FLOOR_NAXXRAMAS6 = "大墓地上层";
DUNGEON_FLOOR_NEXUS801 = "突变之环";
DUNGEON_FLOOR_NEXUS802 = "加速之环";
DUNGEON_FLOOR_NEXUS803 = "转化之环";
DUNGEON_FLOOR_NEXUS804 = "校准之环";
DUNGEON_FLOOR_PITOFSARON1 = "萨隆矿坑";
DUNGEON_FLOOR_THEARGENTCOLISEUM1 = "银色演武场";
DUNGEON_FLOOR_THEARGENTCOLISEUM2 = "寒冰深渊";
DUNGEON_FLOOR_THEEYEOFETERNITY1 = "永恒之眼";
DUNGEON_FLOOR_THEFORGEOFSOULS1 = "灵魂洪炉";
DUNGEON_FLOOR_THENEXUS1 = "魔枢";
DUNGEON_FLOOR_THEOBSIDIANSANCTUM1 = "黑曜石圣殿";
DUNGEON_FLOOR_ULDUAR0 = "壮阔大道";
DUNGEON_FLOOR_ULDUAR1 = "奥杜尔的前厅";
DUNGEON_FLOOR_ULDUAR2 = "奥杜尔的内部圣殿";
DUNGEON_FLOOR_ULDUAR3 = "尤格-萨隆的监狱";
DUNGEON_FLOOR_ULDUAR4 = "思想火花";
DUNGEON_FLOOR_ULDUAR5 = "心灵之眼";
DUNGEON_FLOOR_ULDUAR771 = "岩石大厅";
DUNGEON_FLOOR_UTGARDEKEEP1 = "诺迪尔备战区";
DUNGEON_FLOOR_UTGARDEKEEP2 = "掠龙氏族高台";
DUNGEON_FLOOR_UTGARDEKEEP3 = "提尔之台";
DUNGEON_FLOOR_UTGARDEPINNACLE1 = "尖塔下层";
DUNGEON_FLOOR_UTGARDEPINNACLE2 = "尖塔上层";
DUNGEON_FLOOR_VAULTOFARCHAVON1 = "阿尔卡冯的宝库";
DUNGEON_FLOOR_VIOLETHOLD1 = "紫罗兰监狱";
DUNGEON_GROUP_FOUND_TOOLTIP = "找到了一个要去地下城的队伍。";
DUNGEON_NAME_WITH_DIFFICULTY = "%1$s（%2$s）";
DURABILITY = "耐久度";
DURABILITYDAMAGE_DEATH = "你所装备的物品遭受了10%的耐久度损失。";
DURABILITY_ABBR = "Dura";
DURABILITY_TEMPLATE = "耐久度 %d / %d";
DYNAMIC = "动态";
D_DAYS = "%d|4天:天;";
D_HOURS = "%d|4小时:小时;";
D_MINUTES = "%d|4分钟:分钟;";
D_SECONDS = "%d|4秒:秒;";
EDIT_TICKET = "保存改动";
EFFECTS_LABEL = "效果";
EFFECTS_SUBTEXT = "这些控制选项可以供你修改游戏中的许多元素和效果的细节层次。";
EJECT_PASSENGER = "弹射乘客";
ELITE = "精英";
EMBLEM_BACKGROUND = "底色";
EMBLEM_BORDER = "镶边";
EMBLEM_BORDER_COLOR = "镶边颜色";
EMBLEM_SYMBOL = "图标";
EMBLEM_SYMBOL_COLOR = "图标颜色";
EMOTE = "表情";
EMOTE100_CMD1 = "/tired";
EMOTE100_CMD2 = "/疲倦";
EMOTE101_CMD = "/victory";
EMOTE101_CMD1 = "/胜利";
EMOTE101_CMD2 = "/victory";
EMOTE101_CMD3 = "/victory";
EMOTE102_CMD1 = "/招手";
EMOTE102_CMD2 = "/wave";
EMOTE103_CMD1 = "/欢迎";
EMOTE103_CMD2 = "/welcome";
EMOTE104_CMD1 = "/哀诉";
EMOTE104_CMD2 = "/whine";
EMOTE105_CMD1 = "/口哨";
EMOTE105_CMD2 = "/whistle";
EMOTE106_CMD1 = "/工作";
EMOTE106_CMD2 = "/work";
EMOTE107_CMD1 = "/yawn";
EMOTE107_CMD2 = "/哈欠";
EMOTE107_CMD3 = "/yawn";
EMOTE108_CMD1 = "/boggle";
EMOTE108_CMD2 = "/犹豫";
EMOTE109_CMD1 = "/冷静";
EMOTE109_CMD2 = "/calm";
EMOTE109_CMD3 = "/冷静";
EMOTE10_CMD1 = "/流血";
EMOTE10_CMD2 = "/流血";
EMOTE10_CMD3 = "/bleed";
EMOTE10_CMD4 = "/blood";
EMOTE110_CMD1 = "/寒冷";
EMOTE110_CMD2 = "/cold";
EMOTE111_CMD1 = "/安慰";
EMOTE111_CMD2 = "/comfort";
EMOTE112_CMD1 = "/拥抱";
EMOTE112_CMD2 = "/spoon";
EMOTE112_CMD3 = "/cuddle";
EMOTE112_CMD4 = "/被拥抱";
EMOTE113_CMD1 = "/闪避";
EMOTE113_CMD2 = "/duck";
EMOTE114_CMD1 = "/insult";
EMOTE114_CMD2 = "/insult";
EMOTE114_CMD3 = "/凌辱";
EMOTE115_CMD1 = "/介绍";
EMOTE115_CMD2 = "/introduce";
EMOTE116_CMD1 = "/玩笑";
EMOTE116_CMD2 = "/jk";
EMOTE117_CMD1 = "/舔";
EMOTE117_CMD2 = "/lick";
EMOTE118_CMD1 = "/聆听";
EMOTE118_CMD2 = "/listen";
EMOTE119_CMD1 = "/失落";
EMOTE119_CMD2 = "/lost";
EMOTE11_CMD1 = "/眨眼";
EMOTE11_CMD2 = "/blink";
EMOTE120_CMD1 = "/嘲笑";
EMOTE120_CMD2 = "/mock";
EMOTE121_CMD1 = "/考虑";
EMOTE121_CMD2 = "/ponder";
EMOTE122_CMD1 = "/冲";
EMOTE122_CMD2 = "/pounce";
EMOTE123_CMD1 = "/表扬";
EMOTE123_CMD2 = "/慷慨";
EMOTE123_CMD3 = "/praise";
EMOTE123_CMD4 = "/lavish";
EMOTE124_CMD1 = "/咕噜";
EMOTE124_CMD2 = "/purr";
EMOTE125_CMD1 = "/迷惑";
EMOTE125_CMD2 = "/puzzled";
EMOTE126_CMD1 = "/伸手";
EMOTE126_CMD2 = "/志愿";
EMOTE126_CMD3 = "/raise";
EMOTE126_CMD4 = "/volunteer";
EMOTE127_CMD1 = "/就绪";
EMOTE127_CMD2 = "/rdy";
EMOTE127_CMD3 = "/ready";
EMOTE127_CMD4 = "/rdy";
EMOTE128_CMD1 = "/摇晃";
EMOTE128_CMD2 = "/shimmy";
EMOTE129_CMD1 = "/颤抖";
EMOTE129_CMD2 = "/shiver";
EMOTE12_CMD1 = "/羞愧";
EMOTE12_CMD2 = "/blush";
EMOTE130_CMD1 = "/嘘声";
EMOTE130_CMD2 = "/pest";
EMOTE130_CMD3 = "/shoo";
EMOTE130_CMD4 = "/pest";
EMOTE131_CMD1 = "/耳光";
EMOTE131_CMD2 = "/slap";
EMOTE132_CMD1 = "/傻笑";
EMOTE132_CMD2 = "/smirk";
EMOTE133_CMD1 = "/吸气";
EMOTE133_CMD2 = "/sniff";
EMOTE134_CMD1 = "/斥责";
EMOTE134_CMD2 = "/snub";
EMOTE135_CMD1 = "/安抚";
EMOTE135_CMD2 = "/soothe";
EMOTE136_CMD1 = "/臭味";
EMOTE136_CMD2 = "/嗅";
EMOTE136_CMD3 = "/stink";
EMOTE136_CMD4 = "/smell";
EMOTE137_CMD1 = "/嘲讽";
EMOTE137_CMD2 = "/taunt";
EMOTE138_CMD1 = "/戏弄";
EMOTE138_CMD2 = "/tease";
EMOTE139_CMD1 = "/干渴";
EMOTE139_CMD2 = "/thirsty";
EMOTE13_CMD1 = "/敲击";
EMOTE13_CMD2 = "/咚";
EMOTE13_CMD3 = "/bonk";
EMOTE13_CMD4 = "/doh";
EMOTE140_CMD1 = "/否决";
EMOTE140_CMD2 = "/veto";
EMOTE141_CMD1 = "/窃笑";
EMOTE141_CMD2 = "/snicker";
EMOTE142_CMD1 = "/胳肢";
EMOTE142_CMD2 = "/tickle";
EMOTE143_CMD1 = "/站立";
EMOTE143_CMD2 = "/stand";
EMOTE144_CMD1 = "/低泣";
EMOTE144_CMD2 = "/violin";
EMOTE145_CMD1 = "/微笑";
EMOTE145_CMD2 = "/smile";
EMOTE146_CMD1 = "/粗鲁";
EMOTE146_CMD2 = "/rasp";
EMOTE147_CMD1 = "/怒号";
EMOTE147_CMD2 = "/growl";
EMOTE148_CMD1 = "/狂哮";
EMOTE148_CMD2 = "/bark";
EMOTE149_CMD1 = "/怜悯";
EMOTE149_CMD2 = "/pity";
EMOTE14_CMD1 = "/无聊";
EMOTE14_CMD2 = "/bored";
EMOTE150_CMD1 = "/恐慌";
EMOTE150_CMD2 = "/scared";
EMOTE151_CMD1 = "/摔落";
EMOTE151_CMD2 = "/flop";
EMOTE152_CMD1 = "/爱";
EMOTE152_CMD2 = "/love";
EMOTE153_CMD1 = "/嗼";
EMOTE153_CMD2 = "/moo";
EMOTE154_CMD1 = "/赞扬";
EMOTE154_CMD2 = "/commend";
EMOTE155_CMD1 = "/火车";
EMOTE155_CMD2 = "/train";
EMOTE156_CMD1 = "/呼救";
EMOTE156_CMD2 = "/helpme";
EMOTE157_CMD1 = "/敌人";
EMOTE157_CMD2 = "/incoming";
EMOTE158_CMD1 = "/开火";
EMOTE158_CMD2 = "/openfire";
EMOTE159_CMD1 = "/冲锋";
EMOTE159_CMD2 = "/charge";
EMOTE15_CMD1 = "/弹跳";
EMOTE15_CMD2 = "/bounce";
EMOTE160_CMD1 = "/逃跑";
EMOTE160_CMD2 = "/flee";
EMOTE161_CMD1 = "/攻击目标";
EMOTE161_CMD2 = "/attacktarget";
EMOTE162_CMD1 = "/魔法耗尽";
EMOTE162_CMD2 = "/oom";
EMOTE163_CMD1 = "/跟着我";
EMOTE163_CMD2 = "/followme";
EMOTE164_CMD1 = "/等等";
EMOTE164_CMD2 = "/wait";
EMOTE165_CMD1 = "/示好";
EMOTE165_CMD2 = "/flirt";
EMOTE166_CMD1 = "/治疗我";
EMOTE166_CMD2 = "/healme";
EMOTE167_CMD1 = "/笑话";
EMOTE167_CMD2 = "/silly";
EMOTE168_CMD1 = "/wink";
EMOTE168_CMD2 = "/wink";
EMOTE169_CMD1 = "/pat";
EMOTE169_CMD2 = "/pat";
EMOTE16_CMD1 = "/马上回来";
EMOTE16_CMD2 = "/brb";
EMOTE170_CMD1 = "/golfclap";
EMOTE170_CMD2 = "/golfclap";
EMOTE171_CMD1 = "/mountspecial";
EMOTE171_CMD2 = "/mountspecial";
EMOTE17_CMD1 = "/鞠躬";
EMOTE17_CMD2 = "/bow";
EMOTE18_CMD1 = "/打嗝";
EMOTE18_CMD2 = "/belch";
EMOTE18_CMD3 = "/burp";
EMOTE18_CMD4 = "/belch";
EMOTE19_CMD1 = "/再见";
EMOTE19_CMD2 = "/goodbye";
EMOTE19_CMD3 = "/再会";
EMOTE19_CMD4 = "/bye";
EMOTE19_CMD5 = "/goodbye";
EMOTE19_CMD6 = "/farewell";
EMOTE1_CMD1 = "/赞同";
EMOTE1_CMD2 = "/agree";
EMOTE20_CMD1 = "/咯咯笑";
EMOTE20_CMD2 = "/cackle";
EMOTE21_CMD1 = "/欢呼";
EMOTE21_CMD2 = "/cheer";
EMOTE21_CMD3 = "/woot";
EMOTE21_CMD4 = "/woot";
EMOTE22_CMD1 = "/小鸡";
EMOTE22_CMD2 = "/flap";
EMOTE22_CMD3 = "/strut";
EMOTE22_CMD4 = "/chicken";
EMOTE22_CMD5 = "/flap";
EMOTE22_CMD6 = "/strut";
EMOTE23_CMD1 = "/chuckle";
EMOTE23_CMD2 = "/chuckle";
EMOTE24_CMD1 = "/拍手";
EMOTE24_CMD2 = "/clap";
EMOTE25_CMD1 = "/糊涂";
EMOTE25_CMD2 = "/confused";
EMOTE26_CMD1 = "/祝贺";
EMOTE26_CMD2 = "/祝贺";
EMOTE26_CMD3 = "/cong";
EMOTE26_CMD4 = "/congratulate";
EMOTE26_CMD5 = "/grats";
EMOTE26_CMD6 = "/cong";
EMOTE27_CMD1 = "/unused";
EMOTE27_CMD2 = "/unused";
EMOTE28_CMD1 = "/cough";
EMOTE28_CMD2 = "/咳嗽";
EMOTE29_CMD1 = "/退缩";
EMOTE29_CMD2 = "/恐惧";
EMOTE29_CMD3 = "/cower";
EMOTE29_CMD4 = "/fear";
EMOTE2_CMD1 = "/惊奇";
EMOTE2_CMD2 = "/amaze";
EMOTE304_CMD1 = "/incoming";
EMOTE304_CMD3 = "/incoming";
EMOTE304_CMD4 = "/inc";
EMOTE306_CMD1 = "/retreat";
EMOTE306_CMD2 = "/retreat";
EMOTE306_CMD3 = "/flee";
EMOTE306_CMD4 = "/flee";
EMOTE30_CMD1 = "/关节";
EMOTE30_CMD2 = "/knuckles";
EMOTE30_CMD3 = "/crack";
EMOTE30_CMD4 = "/knuckles";
EMOTE31_CMD1 = "/畏缩";
EMOTE31_CMD2 = "/cringe";
EMOTE32_CMD1 = "/哭泣";
EMOTE32_CMD2 = "/抽泣";
EMOTE32_CMD3 = "/哭泣";
EMOTE32_CMD4 = "/cry";
EMOTE32_CMD5 = "/sob";
EMOTE32_CMD6 = "/weep";
EMOTE33_CMD1 = "/好奇";
EMOTE33_CMD2 = "/curious";
EMOTE34_CMD1 = "/屈膝";
EMOTE34_CMD2 = "/curtsey";
EMOTE35_CMD1 = "/跳舞";
EMOTE35_CMD2 = "/dance";
EMOTE368_CMD1 = "/blame";
EMOTE368_CMD2 = "/blame";
EMOTE369_CMD1 = "/blank";
EMOTE369_CMD2 = "/blank";
EMOTE36_CMD1 = "/drink";
EMOTE36_CMD2 = "/干杯";
EMOTE36_CMD3 = "/喝酒";
EMOTE36_CMD4 = "/shindig";
EMOTE370_CMD1 = "/brandish";
EMOTE370_CMD2 = "/brandish";
EMOTE371_CMD1 = "/breath";
EMOTE371_CMD2 = "/breath";
EMOTE372_CMD1 = "/disagree";
EMOTE372_CMD2 = "/disagree";
EMOTE373_CMD1 = "/doubt";
EMOTE373_CMD2 = "/doubt";
EMOTE374_CMD1 = "/embarrass";
EMOTE374_CMD2 = "/embarrass";
EMOTE375_CMD1 = "/encourage";
EMOTE375_CMD2 = "/encourage";
EMOTE376_CMD1 = "/enemy";
EMOTE376_CMD2 = "/enemy";
EMOTE377_CMD1 = "/eyebrow";
EMOTE377_CMD2 = "/eyebrow";
EMOTE377_CMD3 = "/brow";
EMOTE377_CMD4 = "/brow";
EMOTE37_CMD1 = "/流口水";
EMOTE37_CMD2 = "/drool";
EMOTE380_CMD1 = "/highfive";
EMOTE380_CMD2 = "/highfive";
EMOTE381_CMD1 = "/absent";
EMOTE381_CMD2 = "/absent";
EMOTE382_CMD1 = "/arm";
EMOTE382_CMD2 = "/arm";
EMOTE383_CMD1 = "/awe";
EMOTE383_CMD2 = "/awe";
EMOTE384_CMD1 = "/backpack";
EMOTE384_CMD2 = "/backpack";
EMOTE384_CMD3 = "/pack";
EMOTE384_CMD4 = "/pack";
EMOTE385_CMD1 = "/badfeeling";
EMOTE385_CMD2 = "/badfeeling";
EMOTE385_CMD3 = "/bad";
EMOTE385_CMD4 = "/bad";
EMOTE386_CMD1 = "/challenge";
EMOTE386_CMD2 = "/challenge";
EMOTE387_CMD1 = "/chug";
EMOTE387_CMD2 = "/chug";
EMOTE389_CMD1 = "/ding";
EMOTE389_CMD2 = "/ding";
EMOTE38_CMD1 = "/吃饭";
EMOTE38_CMD2 = "/吃饭";
EMOTE38_CMD3 = "/feast";
EMOTE38_CMD4 = "/eat";
EMOTE38_CMD5 = "/chew";
EMOTE38_CMD6 = "/feast";
EMOTE390_CMD1 = "/facepalm";
EMOTE390_CMD2 = "/facepalm";
EMOTE390_CMD3 = "/palm";
EMOTE390_CMD4 = "/palm";
EMOTE391_CMD1 = "/faint";
EMOTE391_CMD2 = "/faint";
EMOTE392_CMD1 = "/go";
EMOTE392_CMD2 = "/go";
EMOTE393_CMD1 = "/going";
EMOTE393_CMD2 = "/going";
EMOTE394_CMD1 = "/glower";
EMOTE394_CMD2 = "/glower";
EMOTE395_CMD1 = "/headache";
EMOTE395_CMD2 = "/headache";
EMOTE396_CMD1 = "/hiccup";
EMOTE396_CMD2 = "/hiccup";
EMOTE398_CMD1 = "/hiss";
EMOTE398_CMD2 = "/hiss";
EMOTE399_CMD1 = "/holdhand";
EMOTE399_CMD2 = "/holdhand";
EMOTE39_CMD1 = "/眯眼";
EMOTE39_CMD2 = "/eye";
EMOTE3_CMD1 = "/愤怒";
EMOTE3_CMD2 = "/疯狂";
EMOTE3_CMD3 = "/angry";
EMOTE3_CMD4 = "/mad";
EMOTE401_CMD1 = "/hurry";
EMOTE401_CMD2 = "/hurry";
EMOTE402_CMD1 = "/idea";
EMOTE402_CMD2 = "/idea";
EMOTE403_CMD1 = "/jealous";
EMOTE403_CMD2 = "/jealous";
EMOTE404_CMD1 = "/luck";
EMOTE404_CMD2 = "/luck";
EMOTE405_CMD1 = "/map";
EMOTE405_CMD2 = "/map";
EMOTE406_CMD1 = "/mercy";
EMOTE406_CMD2 = "/mercy";
EMOTE407_CMD1 = "/mutter";
EMOTE407_CMD2 = "/mutter";
EMOTE408_CMD1 = "/nervous";
EMOTE408_CMD2 = "/nervous";
EMOTE409_CMD1 = "/offer";
EMOTE409_CMD2 = "/offer";
EMOTE40_CMD1 = "/放屁";
EMOTE40_CMD2 = "/fart";
EMOTE410_CMD1 = "/pet";
EMOTE410_CMD2 = "/pet";
EMOTE411_CMD1 = "/pinch";
EMOTE411_CMD2 = "/pinch";
EMOTE413_CMD1 = "/proud";
EMOTE413_CMD2 = "/proud";
EMOTE414_CMD1 = "/promise";
EMOTE414_CMD2 = "/promise";
EMOTE415_CMD1 = "/pulse";
EMOTE415_CMD2 = "/pulse";
EMOTE416_CMD1 = "/punch";
EMOTE416_CMD2 = "/punch";
EMOTE417_CMD1 = "/pout";
EMOTE417_CMD2 = "/pout";
EMOTE418_CMD1 = "/regret";
EMOTE418_CMD2 = "/regret";
EMOTE41_CMD1 = "/坐立不安";
EMOTE41_CMD2 = "/不耐烦";
EMOTE41_CMD3 = "/fidget";
EMOTE41_CMD4 = "/impatient";
EMOTE420_CMD1 = "/revenge";
EMOTE420_CMD2 = "/revenge";
EMOTE421_CMD1 = "/rolleyes";
EMOTE421_CMD2 = "/rolleyes";
EMOTE421_CMD3 = "/eyeroll";
EMOTE421_CMD4 = "/eyeroll";
EMOTE422_CMD1 = "/ruffle";
EMOTE422_CMD2 = "/ruffle";
EMOTE423_CMD1 = "/sad";
EMOTE423_CMD2 = "/sad";
EMOTE424_CMD1 = "/scoff";
EMOTE424_CMD2 = "/scoff";
EMOTE425_CMD1 = "/scold";
EMOTE425_CMD2 = "/scold";
EMOTE426_CMD1 = "/scowl";
EMOTE426_CMD2 = "/scowl";
EMOTE427_CMD1 = "/search";
EMOTE427_CMD2 = "/search";
EMOTE428_CMD1 = "/shakefist";
EMOTE428_CMD2 = "/shakefist";
EMOTE428_CMD3 = "/fist";
EMOTE428_CMD4 = "/fist";
EMOTE429_CMD1 = "/shifty";
EMOTE429_CMD2 = "/shifty";
EMOTE42_CMD1 = "/强壮";
EMOTE42_CMD2 = "/strong";
EMOTE42_CMD3 = "/flex";
EMOTE42_CMD4 = "/strong";
EMOTE430_CMD1 = "/shudder";
EMOTE430_CMD2 = "/shudder";
EMOTE431_CMD1 = "/signal";
EMOTE431_CMD2 = "/signal";
EMOTE432_CMD1 = "/silence";
EMOTE432_CMD2 = "/silence";
EMOTE432_CMD3 = "/shush";
EMOTE432_CMD4 = "/shush";
EMOTE433_CMD1 = "/sing";
EMOTE433_CMD2 = "/sing";
EMOTE434_CMD1 = "/smack";
EMOTE434_CMD2 = "/smack";
EMOTE435_CMD1 = "/sneak";
EMOTE435_CMD2 = "/sneak";
EMOTE436_CMD1 = "/sneeze";
EMOTE436_CMD2 = "/sneeze";
EMOTE437_CMD1 = "/snort";
EMOTE437_CMD2 = "/snort";
EMOTE438_CMD1 = "/squeal";
EMOTE438_CMD2 = "/squeal";
EMOTE43_CMD1 = "/皱眉";
EMOTE43_CMD2 = "/失望";
EMOTE43_CMD3 = "/失望";
EMOTE43_CMD4 = "/frown";
EMOTE43_CMD5 = "/disappointed";
EMOTE43_CMD6 = "/disappointment";
EMOTE440_CMD1 = "/suspicious";
EMOTE440_CMD2 = "/suspicious";
EMOTE441_CMD1 = "/think";
EMOTE441_CMD2 = "/think";
EMOTE442_CMD1 = "/truce";
EMOTE442_CMD2 = "/truce";
EMOTE443_CMD1 = "/twiddle";
EMOTE443_CMD2 = "/twiddle";
EMOTE444_CMD1 = "/warn";
EMOTE444_CMD2 = "/warn";
EMOTE445_CMD1 = "/snap";
EMOTE445_CMD2 = "/snap";
EMOTE446_CMD1 = "/charm";
EMOTE446_CMD2 = "/charm";
EMOTE447_CMD1 = "/coverears";
EMOTE447_CMD2 = "/coverears";
EMOTE448_CMD1 = "/crossarms";
EMOTE448_CMD2 = "/crossarms";
EMOTE449_CMD1 = "/look";
EMOTE449_CMD2 = "/look";
EMOTE44_CMD1 = "/gasp";
EMOTE44_CMD2 = "/gasp";
EMOTE44_CMD3 = "/喘气";
EMOTE450_CMD1 = "/object";
EMOTE450_CMD2 = "/object";
EMOTE450_CMD3 = "/objection";
EMOTE450_CMD4 = "/objection";
EMOTE450_CMD5 = "/holdit";
EMOTE450_CMD6 = "/holdit";
EMOTE451_CMD1 = "/sweat";
EMOTE451_CMD2 = "/sweat";
EMOTE452_CMD1 = "/yw";
EMOTE452_CMD2 = "/yw";
EMOTE45_CMD1 = "/gaze";
EMOTE45_CMD2 = "/凝视";
EMOTE46_CMD1 = "/giggle";
EMOTE46_CMD2 = "/giggle";
EMOTE47_CMD1 = "/瞪眼";
EMOTE47_CMD2 = "/glare";
EMOTE48_CMD1 = "/幸灾乐祸";
EMOTE48_CMD2 = "/gloat";
EMOTE49_CMD1 = "/greet";
EMOTE49_CMD2 = "/greetings";
EMOTE49_CMD3 = "/greet";
EMOTE49_CMD4 = "/greetings";
EMOTE4_CMD1 = "/抱歉";
EMOTE4_CMD2 = "/sorry";
EMOTE4_CMD3 = "/apologize";
EMOTE4_CMD4 = "/抱歉";
EMOTE50_CMD1 = "/咧嘴笑";
EMOTE50_CMD2 = "/顽皮";
EMOTE50_CMD3 = "/顽皮";
EMOTE50_CMD4 = "/grin";
EMOTE50_CMD5 = "/wicked";
EMOTE50_CMD6 = "/wickedly";
EMOTE51_CMD1 = "/呻吟";
EMOTE51_CMD2 = "/groan";
EMOTE52_CMD1 = "/grovel";
EMOTE52_CMD2 = "/苦工";
EMOTE52_CMD3 = "/卑微";
EMOTE52_CMD4 = "/peon";
EMOTE53_CMD1 = "/大笑";
EMOTE53_CMD2 = "/guffaw";
EMOTE54_CMD1 = "/hail";
EMOTE54_CMD2 = "/hail";
EMOTE55_CMD1 = "/高兴";
EMOTE55_CMD2 = "/高兴";
EMOTE55_CMD3 = "/高兴";
EMOTE55_CMD4 = "/happy";
EMOTE55_CMD5 = "/glad";
EMOTE55_CMD6 = "/yay";
EMOTE56_CMD1 = "/你好";
EMOTE56_CMD2 = "/hi";
EMOTE56_CMD3 = "/hello";
EMOTE56_CMD4 = "/你好";
EMOTE57_CMD1 = "/拥抱";
EMOTE57_CMD2 = "/hug";
EMOTE58_CMD1 = "/饥饿";
EMOTE58_CMD2 = "/食物";
EMOTE58_CMD3 = "/hungry";
EMOTE58_CMD4 = "/food";
EMOTE58_CMD5 = "/pizza";
EMOTE58_CMD6 = "/pizza";
EMOTE59_CMD1 = "/亲吻";
EMOTE59_CMD2 = "/飞吻";
EMOTE59_CMD3 = "/kiss";
EMOTE59_CMD4 = "/blow";
EMOTE5_CMD1 = "/鼓掌";
EMOTE5_CMD2 = "/bravo";
EMOTE5_CMD3 = "/applause";
EMOTE5_CMD4 = "/applaud";
EMOTE5_CMD5 = "/喝彩";
EMOTE5_CMD6 = "/叫好";
EMOTE60_CMD1 = "/下跪";
EMOTE60_CMD2 = "/kneel";
EMOTE60_CMD3 = "/kneel";
EMOTE61_CMD1 = "/大笑";
EMOTE61_CMD2 = "/lol";
EMOTE61_CMD3 = "/laugh";
EMOTE61_CMD4 = "/lol";
EMOTE62_CMD1 = "/躺下";
EMOTE62_CMD2 = "/liedown";
EMOTE62_CMD3 = "/lay";
EMOTE62_CMD4 = "/lie";
EMOTE62_CMD5 = "/laydown";
EMOTE62_CMD6 = "/liedown";
EMOTE62_CMD7 = "/lay";
EMOTE62_CMD8 = "/lie";
EMOTE63_CMD1 = "/按摩";
EMOTE63_CMD2 = "/massage";
EMOTE64_CMD1 = "/哀号";
EMOTE64_CMD2 = "/moan";
EMOTE65_CMD1 = "/屁股";
EMOTE65_CMD2 = "/moon";
EMOTE66_CMD1 = "/哀悼";
EMOTE66_CMD2 = "/mourn";
EMOTE67_CMD1 = "/不";
EMOTE67_CMD2 = "/no";
EMOTE68_CMD1 = "/点头";
EMOTE68_CMD2 = "/yes";
EMOTE68_CMD3 = "/nod";
EMOTE68_CMD4 = "/yes";
EMOTE69_CMD1 = "/抬鼻";
EMOTE69_CMD2 = "/pick";
EMOTE69_CMD3 = "/nosepick";
EMOTE69_CMD4 = "/挖鼻孔";
EMOTE6_CMD1 = "/害臊";
EMOTE6_CMD2 = "/bashful";
EMOTE70_CMD1 = "/panic";
EMOTE70_CMD2 = "/惊恐";
EMOTE71_CMD1 = "/观察";
EMOTE71_CMD2 = "/peer";
EMOTE72_CMD1 = "/plead";
EMOTE72_CMD2 = "/恳求";
EMOTE73_CMD1 = "/指点";
EMOTE73_CMD2 = "/point";
EMOTE74_CMD1 = "/戳";
EMOTE74_CMD2 = "/poke";
EMOTE75_CMD1 = "/祈祷";
EMOTE75_CMD2 = "/pray";
EMOTE76_CMD1 = "/咆哮";
EMOTE76_CMD2 = "/roar";
EMOTE76_CMD3 = "/rawr";
EMOTE76_CMD4 = "/rawr";
EMOTE77_CMD1 = "/狂笑";
EMOTE77_CMD2 = "/rofl";
EMOTE78_CMD1 = "/粗鲁";
EMOTE78_CMD2 = "/rude";
EMOTE79_CMD1 = "/敬礼";
EMOTE79_CMD2 = "/salute";
EMOTE7_CMD1 = "/beckon";
EMOTE7_CMD2 = "/招手";
EMOTE80_CMD1 = "/瘙痒";
EMOTE80_CMD2 = "/猫咪";
EMOTE80_CMD3 = "/catty";
EMOTE80_CMD4 = "/scratch";
EMOTE80_CMD5 = "/cat";
EMOTE80_CMD6 = "/catty";
EMOTE81_CMD1 = "/sexy";
EMOTE81_CMD2 = "/性感";
EMOTE82_CMD1 = "/摇摆";
EMOTE82_CMD2 = "/rear";
EMOTE82_CMD3 = "/shake";
EMOTE82_CMD4 = "/扭屁股";
EMOTE83_CMD1 = "/shout";
EMOTE83_CMD2 = "/holler";
EMOTE83_CMD3 = "/holler";
EMOTE84_CMD1 = "/耸肩";
EMOTE84_CMD2 = "/shrug";
EMOTE85_CMD1 = "/害羞";
EMOTE85_CMD2 = "/shy";
EMOTE86_CMD1 = "/叹气";
EMOTE86_CMD2 = "/sigh";
EMOTE87_CMD1 = "/坐下";
EMOTE87_CMD2 = "/sit";
EMOTE88_CMD1 = "/睡觉";
EMOTE88_CMD2 = "/sleep";
EMOTE89_CMD1 = "/怒骂";
EMOTE89_CMD2 = "/snarl";
EMOTE8_CMD1 = "/乞求";
EMOTE8_CMD2 = "/beg";
EMOTE90_CMD1 = "/spit";
EMOTE90_CMD2 = "/吐口水";
EMOTE91_CMD1 = "/凝视";
EMOTE91_CMD2 = "/stare";
EMOTE92_CMD1 = "/惊奇";
EMOTE92_CMD2 = "/surprised";
EMOTE93_CMD1 = "/投降";
EMOTE93_CMD2 = "/surrender";
EMOTE94_CMD1 = "/谈话";
EMOTE94_CMD2 = "/talk";
EMOTE95_CMD1 = "/交谈";
EMOTE95_CMD2 = "/激动";
EMOTE95_CMD3 = "/talkex";
EMOTE95_CMD4 = "/excited";
EMOTE96_CMD1 = "/talkq";
EMOTE96_CMD2 = "/疑问";
EMOTE96_CMD3 = "/领悟";
EMOTE96_CMD4 = "/question";
EMOTE97_CMD1 = "/掂脚";
EMOTE97_CMD2 = "/tap";
EMOTE98_CMD1 = "/感谢";
EMOTE98_CMD2 = "/感谢";
EMOTE98_CMD3 = "/ty";
EMOTE98_CMD4 = "/thank";
EMOTE98_CMD5 = "/thanks";
EMOTE98_CMD6 = "/ty";
EMOTE99_CMD1 = "/威胁";
EMOTE99_CMD2 = "/末日";
EMOTE99_CMD3 = "/威胁";
EMOTE99_CMD4 = "/愤怒";
EMOTE99_CMD5 = "/threaten";
EMOTE99_CMD6 = "/doom";
EMOTE99_CMD7 = "/threat";
EMOTE99_CMD8 = "/wrath";
EMOTE9_CMD1 = "/bite";
EMOTE9_CMD2 = "/撕咬";
EMOTE_MESSAGE = "表情";
EMOTE_STATE_KNEEL = "/kneel";
EMPTY = "空";
EMPTY_SOCKET = "等级 %d 插槽";
EMPTY_SOCKET_BLUE = "蓝色插槽";
EMPTY_SOCKET_META = "多彩插槽";
EMPTY_SOCKET_NO_COLOR = "棱彩插槽";
EMPTY_SOCKET_RED = "红色插槽";
EMPTY_SOCKET_YELLOW = "黄色插槽";
EMPTY_STABLE_SLOT = "空的兽栏位置";
ENABLE = "启用";
ENABLE_ALL_SHADERS = "开启所有遮罩效果";
ENABLE_AMBIENCE = "环境音效";
ENABLE_BGSOUND = "背景声音";
ENABLE_DSP_EFFECTS = "死亡骑士语音";
ENABLE_EMOTE_SOUNDS = "表情音效";
ENABLE_ERROR_SPEECH = "错误提示";
ENABLE_GROUP_SPEECH = "开启队伍聊天";
ENABLE_HARDWARE = "使用硬件";
ENABLE_MICROPHONE = "开启麦克风";
ENABLE_MUSIC = "音乐";
ENABLE_MUSIC_LOOPING = "音乐循环";
ENABLE_PET_SOUNDS = "启用宠物音效";
ENABLE_REVERB = "启用混响";
ENABLE_SOFTWARE_HRTF = "耳机模式";
ENABLE_SOUND = "开启声效";
ENABLE_SOUNDFX = "声音效果";
ENABLE_SOUND_AT_CHARACTER = "角色声音";
ENABLE_STEREO_VIDEO = "启用立体画面";
ENABLE_TUTORIAL_TEXT = "自动打开教程";
ENABLE_VOICECHAT = "启用语音聊天";
ENCHANTS = "附魔";
ENCHANT_AURA_COMBATLOG_TOOLTIP = "当武器获得或失去强化效果时显示信息。";
ENCHANT_CONDITION_AND = " 和\32";
ENCHANT_CONDITION_EQUAL_COMPARE = "等量%s宝石和%s宝石";
ENCHANT_CONDITION_EQUAL_VALUE = "%d颗%s宝石";
ENCHANT_CONDITION_LESS_VALUE = "少于%d颗%s宝石";
ENCHANT_CONDITION_MORE_COMPARE = "%s宝石的数量多于%s宝石";
ENCHANT_CONDITION_MORE_EQUAL_COMPARE = "%s宝石的数量不少于%s宝石";
ENCHANT_CONDITION_MORE_VALUE = "至少%d颗%s宝石";
ENCHANT_CONDITION_NOT_EQUAL_COMPARE = "%s宝石和%s宝石的数量不同";
ENCHANT_CONDITION_NOT_EQUAL_VALUE = "除了%d颗%s宝石外的任意宝石";
ENCHANT_CONDITION_REQUIRES = "需要\32";
ENCHANT_ITEM_MIN_SKILL = "附魔要求 %s（%d）";
ENCHANT_ITEM_REQ_LEVEL = "附魔要求等级%d";
ENCHANT_ITEM_REQ_SKILL = "附魔要求 %s";
ENCHANT_SLOT = "附魔/开锁栏";
ENCHSLOT_2HWEAPON = "双手武器";
ENCHSLOT_WEAPON = "武器";
ENCLOSED_MONEY = "已附加的金额";
ENCN = "简体中文（英文语音）";
ENCRYPTED = "已加密";
END_BOUND_TRADEABLE = "执行此项操作会使该物品不可交易。";
END_REFUND = "进行此项操作会使该物品无法退还";
ENEMY = "敌方";
ENERGY = "能量";
ENERGY_COST = "%d能量";
ENERGY_COST_PER_TIME = "%d能量，外加每秒%d";
ENGB = "英语（EU）";
ENSCRIBE = "附魔";
ENTERING_COMBAT = "进入战斗";
ENTER_BATTLE = "进入战斗";
ENTER_CODE = "请输入正确的内容";
ENTER_DUNGEON = "进入地下城";
ENTER_FILTER_NAME = "输入过滤名称：";
ENTER_INVITE_NOTE = "输入好友验证信息（可选）";
ENTER_MACRO_LABEL = "输入宏命令";
ENTER_NAME_OR_EMAIL = "输入电子邮件地址或者角色名";
ENTIRE_LINE = "整条信息";
ENTIRE_LINE_COMBATLOG_TOOLTIP = "按照下面的方式用彩色标记整条信息。";
ENTW = "繁体中文（英文语音）";
ENUS = "英语（US）";
ENVIRONMENTAL_DAMAGE = "环境伤害";
ENVIRONMENTAL_DAMAGE_COMBATLOG_TOOLTIP = "当某人因摔落、溺水或泡在岩浆中而损失生命值时显示信息。";
ENVIRONMENT_DETAIL = "环境细节";
EQUIPMENT_MANAGER = "装备管理";
EQUIPMENT_MANAGER_BAGS_FULL = "装备更换失败。背包已满。";
EQUIPMENT_MANAGER_COMBAT_SWAP = "装备方案%s中的某些物品不能在战斗中切换。";
EQUIPMENT_MANAGER_IGNORE_SLOT = "忽略此栏位";
EQUIPMENT_MANAGER_IS_DISABLED = "装备管理已禁用。";
EQUIPMENT_MANAGER_ITEMS_MISSING_TOOLTIP = "%d %s |cffff0000（缺少%d）";
EQUIPMENT_MANAGER_MISSING_ITEM = "未找到套装方案%s中的一件或多件物品，无法装备。";
EQUIPMENT_MANAGER_PLACE_IN_BAGS = "放入背包";
EQUIPMENT_MANAGER_UNIGNORE_SLOT = "包括此栏位";
EQUIPMENT_SETS = "装备配置方案：|cFFFFFFFF%s|r";
EQUIPMENT_SETS_TOO_MANY = "你不能再创建新的装备方案了。";
EQUIPSET_EQUIP = "装备";
EQUIP_CONTAINER = "装备容器";
EQUIP_NO_DROP = "装备之后，该物品将与你绑定。";
ERRORS = "错误";
ERROR_CANNOT_BIND = "|cffff0000错误！无法绑定该按键！|r";
ERROR_CAPS = "错误";
ERROR_SLASH_CHANGEACTIONBAR = "正确的格式是/changeactionbar x，x是动作条的编号，范围为%d到%d。";
ERROR_SLASH_EQUIP_TO_SLOT = "正确的格式是/equipslot x [物品名]，x是装备栏的编号，范围为%d到%d。";
ERROR_SLASH_LOOT_SETTHRESHOLD = "正确的格式是/threshold [物品质量]，或者/threshold x，其中x是一个介于%d和%d之间的代表物品质量的数字。";
ERROR_SLASH_SWAPACTIONBAR = "正确的格式是/swapactionbar x y，x和y是动作条的编号，范围为%d到%d。";
ERROR_SLASH_TEAM_CAPTAIN = "正确的格式是/teamcaptain [2v2, 3v3, 5v5] [玩家名]";
ERROR_SLASH_TEAM_DISBAND = "正确的格式是/teamdisband [2v2, 3v3, 5v5]";
ERROR_SLASH_TEAM_INVITE = "正确的格式是/teaminvite [2v2, 3v3, 5v5] [玩家名]";
ERROR_SLASH_TEAM_QUIT = "正确的格式是/teamquit [2v2, 3v3, 5v5]";
ERROR_SLASH_TEAM_UNINVITE = "正确的格式是/teamremove [2v2, 3v3, 5v5] [玩家名]";
ERR_2HANDED_EQUIPPED = "不能在使用双手武器的同时装备这件物品。";
ERR_2HSKILLNOTFOUND = "你不能装备双武器";
ERR_ABILITY_COOLDOWN = "技能还没有准备好。";
ERR_ACHIEVEMENT_WATCH_COMPLETED = "这项成就已经完成。";
ERR_ALREADY_INVITED_TO_ARENA_TEAM_S = "%s已经被一支竞技场战队邀请了。";
ERR_ALREADY_INVITED_TO_GUILD_S = "%s已经加入了另一个公会。";
ERR_ALREADY_IN_ARENA_TEAM = "你已经加入了一支该人数规模的竞技场战队。";
ERR_ALREADY_IN_ARENA_TEAM_S = "%s已经加入了一个该规模的竞技场战队。";
ERR_ALREADY_IN_GROUP_S = "%s已经加入了别的队伍";
ERR_ALREADY_IN_GUILD = "你已经加入了一个公会";
ERR_ALREADY_IN_GUILD_S = "%s已经加入了一个公会";
ERR_ALREADY_PICKPOCKETED = "你的目标已经被偷窃过了。";
ERR_ALREADY_QUEUED_FOR_SOMETHING_ELSE = "你已经进入了其它队列。";
ERR_ALREADY_TRADING = "你已经在交易了";
ERR_AMMO_ONLY = "这里只能放置弹药";
ERR_APPROACHING_NO_PLAY_TIME = "%s后进入不健康游戏时间，届时您将无法获得任何经验值和战利品，必须下线累计5小时才能恢复正常状态。";
ERR_APPROACHING_NO_PLAY_TIME_2 = "您已经进入疲劳游戏时间，您的游戏收益将降为正常值的50%%，为了您的健康，请尽快下线休息，做适当身体活动，合理安排学习生活。";
ERR_APPROACHING_PARTIAL_PLAY_TIME = "您的健康游戏时间还剩余%s。届时您的游戏获益将被减半。";
ERR_APPROACHING_PARTIAL_PLAY_TIME_2 = "您累计在线时间已满%s小时。";
ERR_ARENA_EXPIRED_CAIS = "你的队伍中有一名或多名队友处于受限游戏模式下，因此你无法排队。";
ERR_ARENA_NO_TEAM_II = "你不在一个%dv%d的竞技场战队中。";
ERR_ARENA_TEAMS_LOCKED = "竞技场战队已锁定。";
ERR_ARENA_TEAM_CHANGE_FAILED_QUEUED = "无法在排队或比赛时修改竞技场战队。";
ERR_ARENA_TEAM_CREATE_S = "%s已创建。要离开的话，请使用/teamdisband [2v2, 3v3, 5v5]。";
ERR_ARENA_TEAM_DISBANDED_S = "%s解散了%s。";
ERR_ARENA_TEAM_FOUNDER_S = "恭喜，你是%s的创建者之一！要离开的话，请使用/teamquit [2v2, 3v3, 5v5]。";
ERR_ARENA_TEAM_INTERNAL = "内部竞技场战队错误";
ERR_ARENA_TEAM_INVITE_SS = "你邀请%s加入%s。";
ERR_ARENA_TEAM_JOIN_SS = "%s加入了%s。";
ERR_ARENA_TEAM_LEADER_CHANGED_SSS = "%s将%s提升为%s的新队长。";
ERR_ARENA_TEAM_LEADER_IS_SS = "%s是%s的队长。";
ERR_ARENA_TEAM_LEADER_LEAVE_S = "你必须使用/teamcaptain任命一个新的战队指挥官，然后才能离开战队。";
ERR_ARENA_TEAM_LEAVE_SS = "%s离开了%s。";
ERR_ARENA_TEAM_LEVEL_TOO_LOW_I = "你必须达到%d级才能组建竞技场战队。";
ERR_ARENA_TEAM_NAME_EXISTS_S = "竞技场战队名\"%s\"已被使用。";
ERR_ARENA_TEAM_NAME_INVALID = "名字包含无效字符，请输入一个新名字。";
ERR_ARENA_TEAM_NOT_ALLIED = "你无法邀请敌对阵营的玩家。";
ERR_ARENA_TEAM_NOT_FOUND = "该竞技场战队已经离线。";
ERR_ARENA_TEAM_PARTY_SIZE = "队伍人数与该竞技场不符。";
ERR_ARENA_TEAM_PERMISSIONS = "你没有权限那样做。";
ERR_ARENA_TEAM_PLAYER_NOT_FOUND_S = "\"%s\"未找到。";
ERR_ARENA_TEAM_PLAYER_NOT_IN_TEAM = "你尚未加入任何该人数规模的竞技场战队。";
ERR_ARENA_TEAM_PLAYER_NOT_IN_TEAM_SS = "%s不在%s中。";
ERR_ARENA_TEAM_QUIT_S = "你不再是%s的成员了。";
ERR_ARENA_TEAM_REMOVE_SSS = "%1$s被%3$s踢出了%2$s。";
ERR_ARENA_TEAM_TARGET_TOO_HIGH_S = "%s的等级过高，无法加入你的战队。";
ERR_ARENA_TEAM_TARGET_TOO_LOW_S = "%s的等级不够加入你的战队。";
ERR_ARENA_TEAM_TOO_MANY_MEMBERS_S = "%s已满。";
ERR_ARENA_TEAM_YOU_JOIN_S = "你已经加入了%s。要离开这支战队，请使用/teamquit [2v2, 3v3, 5v5]。";
ERR_ATTACK_CHANNEL = "无法在引导时进行攻击。";
ERR_ATTACK_CHARMED = "当被魅惑的时候无法攻击。";
ERR_ATTACK_CONFUSED = "无法在混乱状态下攻击。";
ERR_ATTACK_DEAD = "你不能在死亡状态的时候攻击";
ERR_ATTACK_FLEEING = "无法在逃跑状态下攻击。";
ERR_ATTACK_MOUNTED = "无法在骑乘时攻击。";
ERR_ATTACK_PACIFIED = "无法在平静状态下攻击。";
ERR_ATTACK_PREVENTED_BY_MECHANIC_S = "在%s状态下无法攻击。";
ERR_ATTACK_STUNNED = "无法在昏迷状态下攻击。";
ERR_AUCTION_BAG = "你不能出售非空的背包";
ERR_AUCTION_BID_INCREMENT = "你的提价幅度太小了。";
ERR_AUCTION_BID_OWN = "你不能对自己拍卖的物品竞标";
ERR_AUCTION_BID_PLACED = "竞标已被接受。";
ERR_AUCTION_BOUND_ITEM = "你不能出售灵魂绑定的物品";
ERR_AUCTION_CONJURED_ITEM = "你不能拍卖魔法制造的物品";
ERR_AUCTION_DATABASE_ERROR = "内部拍卖错误";
ERR_AUCTION_ENOUGH_ITEMS = "你的物品数量不足。";
ERR_AUCTION_EXPIRED_S = "你拍卖的%s已经过期。";
ERR_AUCTION_HIGHER_BID = "已经有更高的出价了。";
ERR_AUCTION_HOUSE_DISABLED = "拍卖行目前暂时关闭。|n请稍后再试。";
ERR_AUCTION_LIMITED_DURATION_ITEM = "你不能拍卖带有持续时间的物品。";
ERR_AUCTION_LOOT_ITEM = "你不能拍卖一件可拾取的物品";
ERR_AUCTION_MIN_BID = "你的出价必须不低于最低竞标价格。";
ERR_AUCTION_OUTBID_S = "你对%s的出价被人超过了。";
ERR_AUCTION_QUEST_ITEM = "你不能出售任务物品";
ERR_AUCTION_REMOVED = "拍卖已取消。";
ERR_AUCTION_REMOVED_S = "你竞标的%s被卖家取消。";
ERR_AUCTION_REPAIR_ITEM = "你必须先修理该物品才能将其拍卖。";
ERR_AUCTION_SOLD_S = "你拍卖的%s已经售出。";
ERR_AUCTION_STARTED = "已开始拍卖。";
ERR_AUCTION_USED_CHARGES = "你不能拍卖一件已被使用过的物品";
ERR_AUCTION_WON_S = "你赢得了对%s的竞标";
ERR_AUCTION_WRAPPED_ITEM = "你不能拍卖一件打包的物品";
ERR_AUTOFOLLOW_TOO_FAR = "目标距离太远";
ERR_AUTOLOOT_MONEY_S = "你捡起了%s";
ERR_BADATTACKFACING = "你面朝错误的方向！";
ERR_BADATTACKPOS = "你距离太远！";
ERR_BAD_ON_USE_ENCHANT = "该物品已经拥有一项主动技能了";
ERR_BAD_PLAYER_NAME_S = "无法找到玩家'%s'。";
ERR_BAG_FULL = "这个背包已经满了";
ERR_BAG_IN_BAG = "不能将非空的背包放在别的背包中";
ERR_BANKSLOT_FAILED_TOO_MANY = "你的背包栏位已经全部占满了！";
ERR_BANKSLOT_INSUFFICIENT_FUNDS = "你买不起这个物品。";
ERR_BANKSLOT_NOTBANKER = "目标不是银行职员！";
ERR_BANK_FULL = "你的银行已满";
ERR_BATTLEDGROUND_QUEUED_FOR_RATED = "你不能在排队等待进入竞技场排位赛的时候进入其它战场的队列";
ERR_BATTLEGROUND_ALREADY_IN = "你已经在那个战场里了。";
ERR_BATTLEGROUND_CANNOT_QUEUE_FOR_RATED = "你不能在排队等待进入其它战场的时候进入竞技场排位赛队列";
ERR_BATTLEGROUND_INFO_THROTTLED = "你还不能那么做";
ERR_BATTLEGROUND_JOIN_FAILED = "小队加入失败";
ERR_BATTLEGROUND_JOIN_RANGE_INDEX = "你的队友的等级需要在同一个战场等级限制范围内，否则无法加入队列。";
ERR_BATTLEGROUND_JOIN_TIMED_OUT = "%s无法加入队列";
ERR_BATTLEGROUND_NOT_IN_BATTLEGROUND = "你不能在战场中那么做。";
ERR_BATTLEGROUND_NOT_IN_TEAM = "你的小队不在同一组中";
ERR_BATTLEGROUND_TEAM_LEFT_QUEUE = "你的战队已经离开了竞技场队列";
ERR_BATTLEGROUND_TOO_MANY_QUEUES = "你每次只能进入2个战场的等待序列";
ERR_BG_PLAYER_JOINED_SS = "|Hplayer:%s|h[%s]|h加入了战斗";
ERR_BG_PLAYER_LEFT_S = "%s离开了战斗";
ERR_BN_BROADCAST_THROTTLE = "请稍等几秒再更新你的通告信息。";
ERR_BN_FRIEND_ALREADY = "那个人已经是你的好友了";
ERR_BN_FRIEND_BLOCKED = "那个人在你的屏蔽列表中";
ERR_BN_FRIEND_REQUEST_SENT = "实名好友请求已发送";
ERR_BN_FRIEND_SELF = "你不能加自己为好友";
ERR_BUTTON_LOCKED = "这已经被使用了";
ERR_CANNOTCREATEDIRECTORY = "不能创建目录%s";
ERR_CANNOTCREATEFILE = "不能创建文件%s";
ERR_CANNOT_IGNORE_BN_FRIEND = "你不能屏蔽实名好友。";
ERR_CANTATTACK_NOTSTANDING = "你必须处于站立状态下才能进行攻击！";
ERR_CANT_DO_THAT_IN_A_GROUP = "你不能在队伍中那样做。";
ERR_CANT_DO_THAT_WHILE_LFM = "你不能在招募队员时寻求组队。";
ERR_CANT_EQUIP_EVER = "你永远不能使用这个物品";
ERR_CANT_EQUIP_LEVEL_I = "你必须达到等级%d才能使用这件物品。";
ERR_CANT_EQUIP_NEED_TALENT = "没有所需天赋，无法装备。";
ERR_CANT_EQUIP_RANK = "你还未达到军阶要求";
ERR_CANT_EQUIP_RATING = "你的个人竞技场等级或战队竞技场等级未达到购买该物品的要求";
ERR_CANT_EQUIP_REPUTATION = "你的声望还没有达到可以使用该物品的条件";
ERR_CANT_EQUIP_SKILL = "你现在还没有足够的技能使用这个物品";
ERR_CANT_INTERACT_SHAPESHIFTED = "不能够在变形状态对话";
ERR_CANT_SPEAK_LANGAGE = "你不会说那种语言。";
ERR_CANT_STACK = "这件物品无法堆积";
ERR_CANT_SWAP = "这些物品无法交换";
ERR_CANT_USE_DISARMED = "你不能使用一件已被缴械的物品。";
ERR_CANT_USE_ITEM = "你不能使用这件物品。";
ERR_CANT_USE_ITEM_IN_ARENA = "在竞技场中无法使用该物品。";
ERR_CANT_WRAP_BAGS = "背包无法打包。";
ERR_CANT_WRAP_BOUND = "绑定的物品无法打包";
ERR_CANT_WRAP_EQUIPPED = "已经装备的物品不能被打包";
ERR_CANT_WRAP_STACKABLE = "可以堆积的物品不能被打包";
ERR_CANT_WRAP_UNIQUE = "唯一物品无法打包。";
ERR_CANT_WRAP_WRAPPED = "已经打包的物品不能再被打包";
ERR_CHAT_PLAYER_AMBIGUOUS_S = "%s：找到多个匹配玩家，请输入他们的服务器名以进一步查找";
ERR_CHAT_PLAYER_NOT_FOUND_S = "未找到名为“%s”的在线玩家。";
ERR_CHAT_RESTRICTED = "试玩账号不能频繁发送信息，你必须稍作等待才能再次发言。";
ERR_CHAT_THROTTLED = "可发送的信息数量受限，请稍候再发送下一条信息。";
ERR_CHAT_WHILE_DEAD = "你无法在死亡状态下交谈！";
ERR_CHAT_WRONG_FACTION = "你只能够和你的盟友一方角色进行密语";
ERR_CHEST_IN_USE = "该物品正被使用";
ERR_CLICK_ON_ITEM_TO_FEED = "点击一个物品来喂养你的宠物";
ERR_CLIENT_LOCKED_OUT = "你还不能那样做。";
ERR_COMBAT_DAMAGE_SSI = "%s击中%s造成%d点伤害！";
ERR_COMMAND_NEEDS_TARGET = "你必须指定一个目标：/<命令><目标名称>";
ERR_COMPLAINT_IN_SAME_GUILD = "你不能投诉公会会员。";
ERR_COMSAT_CONNECT_FAIL = "无法连接到语音聊天服务。";
ERR_COMSAT_DISCONNECT = "与语音聊天服务的连接中断。";
ERR_COMSAT_RECONNECT_ATTEMPT = "语音聊天服务已恢复！";
ERR_CORPSE_IS_NOT_IN_INSTANCE = "你的尸体不在那个副本中";
ERR_CURRENCY_FULL = "无法携带这么多的货币。";
ERR_DANCE_CREATE_DUPLICATE = "该舞蹈名已存在";
ERR_DANCE_DELETE_FAILED = "删除舞蹈失败";
ERR_DANCE_SAVE_FAILED = "存储舞蹈失败";
ERR_DEATHBINDALREADYBOUND = "你已经绑定在这里了！";
ERR_DEATHBIND_SUCCESS_S = "%s现在是你的家。";
ERR_DECLINE_GROUP_S = "%s拒绝了你邀请其加入队伍的请求。";
ERR_DESTROY_NONEMPTY_BAG = "只能使用空的背包";
ERR_DIFFICULTY_CHANGE_ALREADY_STARTED = "团队副本难度修改已经在进行中。";
ERR_DIFFICULTY_CHANGE_COMBAT = "当前无法改变团队副本难度。一名玩家正在战斗中。";
ERR_DIFFICULTY_CHANGE_COOLDOWN_S = "团队副本难度近期已更改，%s内无法再次做出修改。";
ERR_DIFFICULTY_CHANGE_ENCOUNTER = "当前无法改变团队副本难度。正在进行首领战。";
ERR_DIFFICULTY_CHANGE_PLAYER_BUSY = "当前无法改变团队副本难度。一名玩家正忙。";
ERR_DIFFICULTY_CHANGE_WORLDSTATE = "当前无法改变团队副本难度。正在进行一个事件。";
ERR_DISMOUNT_NOPET = "内部错误，你没有可以解散的宠物";
ERR_DISMOUNT_NOTMOUNTED = "你现在不处于骑乘状态！";
ERR_DISMOUNT_NOTYOURPET = "内部错误，从一只非宠物身上下来";
ERR_DOOR_LOCKED = "门被锁住了。";
ERR_DROP_BOUND_ITEM = "你不能丢弃一件灵魂绑定的物品";
ERR_DUEL_CANCELLED = "决斗已取消。";
ERR_DUEL_REQUESTED = "你已经发出了决斗要求";
ERR_DUNGEON_DIFFICULTY_CHANGED_S = "地下城难度已设置为%s。";
ERR_DUNGEON_DIFFICULTY_FAILED = "无法改变地下城难度。";
ERR_EAT_WHILE_MOVNG = "你不能在移动中吃东西";
ERR_EMBLEMERROR_NOTABARDGEOSET = "先要恢复到正常状态！";
ERR_EQUIP_TRADE_ITEM = "该物品正在被交易";
ERR_EXHAUSTION_EXHAUSTED = "你现在处于精疲力竭状态。";
ERR_EXHAUSTION_NORMAL = "你进入了正常休息状态。";
ERR_EXHAUSTION_RESTED = "你现在处于精神良好状态。";
ERR_EXHAUSTION_TIRED = "你现在处于疲倦乏力状态。";
ERR_EXHAUSTION_WELLRESTED = "你进入了精力充沛状态。";
ERR_FEIGN_DEATH_RESISTED = "抵抗";
ERR_FILTERING_YOU_S = "无法向%s发送信息，因为你的话语中带有屏蔽词。";
ERR_FISH_ESCAPED = "你的鱼逃走了！";
ERR_FISH_NOT_HOOKED = "没有鱼上钩";
ERR_FOOD_COOLDOWN = "你已经吃得太饱了。";
ERR_FRIEND_ADDED_S = "%s已被加入好友名单";
ERR_FRIEND_ALREADY_S = "%s已经在你的好友名单中了";
ERR_FRIEND_DB_ERROR = "好友搜寻系统数据库错误";
ERR_FRIEND_DELETED = "从列表中移除好友，原因：该角色不存在。";
ERR_FRIEND_ERROR = "来自于服务器中未知好友的回音";
ERR_FRIEND_LIST_FULL = "你的好友列表已满。";
ERR_FRIEND_NOT_FOUND = "没有找到玩家。";
ERR_FRIEND_OFFLINE_S = "%s下线了。";
ERR_FRIEND_ONLINE_SS = "|Hplayer:%s|h[%s]|h上线了。";
ERR_FRIEND_REMOVED_S = "%s已被从好友名单中删除";
ERR_FRIEND_SELF = "你不能把自己加入好友名单中";
ERR_FRIEND_WRONG_FACTION = "好友必须与你属于同一阵营。";
ERR_GENERIC_NO_TARGET = "你没有目标";
ERR_GENERIC_NO_VALID_TARGETS = "没有有效的目标。";
ERR_GENERIC_STUNNED = "你被击昏了";
ERR_GMRESPONSE_DB_ERROR = "获取GM请求错误";
ERR_GROUP_ACTION_THROTTLED = "你在短时间内尝试组队的次数过多。请稍等片刻再作尝试。";
ERR_GROUP_DISBANDED = "你的队伍已经解散。";
ERR_GROUP_FULL = "你的队伍已经满了";
ERR_GROUP_JOIN_BATTLEGROUND_DESERTERS = "你无法加入战场，因为你或你的某个队友被标记为逃亡者。";
ERR_GROUP_JOIN_BATTLEGROUND_FAIL = "你所在的小队加入了一个战场序列，但你不符合条件";
ERR_GROUP_JOIN_BATTLEGROUND_S = "你所在的小队加入了%s的等待队列";
ERR_GROUP_JOIN_BATTLEGROUND_TOO_MANY = "你的小队人数太多，无法加入该战场";
ERR_GROUP_SWAP_FAILED = "正在团队战斗中的玩家无法调换小队";
ERR_GUILDEMBLEM_COLORSPRESENT = "你的公会已经有了徽章！";
ERR_GUILDEMBLEM_INVALIDVENDOR = "这不是一个徽章出售者！";
ERR_GUILDEMBLEM_INVALID_TABARD_COLORS = "无效的公会战袍颜色";
ERR_GUILDEMBLEM_NOGUILD = "你现在不属于任何一个公会！";
ERR_GUILDEMBLEM_NOTENOUGHMONEY = "你没有足够的钱。";
ERR_GUILDEMBLEM_NOTGUILDMASTER = "只有公会首领可以创建徽章";
ERR_GUILDEMBLEM_SAME = "未存储，你的公会战袍没有变更。";
ERR_GUILDEMBLEM_SUCCESS = "公会战袍已经保存。";
ERR_GUILD_ACCEPT = "你已经加入公会";
ERR_GUILD_BANK_BOUND_ITEM = "你不能在公会银行中存放灵魂绑定的物品";
ERR_GUILD_BANK_CONJURED_ITEM = "你不能在公会银行中存放魔法制造的物品";
ERR_GUILD_BANK_EQUIPPED_ITEM = "你必须先卸下那件物品";
ERR_GUILD_BANK_FULL = "该公会银行标签已满";
ERR_GUILD_BANK_QUEST_ITEM = "你不能在公会银行中存放任务物品。";
ERR_GUILD_BANK_WRAPPED_ITEM = "你不能在公会银行中存放包裹起来的物品";
ERR_GUILD_CREATE_S = "%s已经创建";
ERR_GUILD_DECLINE_S = "%s拒绝了你邀请其加入公会的请求。";
ERR_GUILD_DEMOTE_SSS = "%s将%s降职为%s。";
ERR_GUILD_DISBANDED = "公会已经被解散";
ERR_GUILD_DISBAND_S = "%s解散了公会。";
ERR_GUILD_DISBAND_SELF = "你解散了公会";
ERR_GUILD_FOUNDER_S = "恭喜你，你现在是%s的一员了！";
ERR_GUILD_INTERNAL = "内部公会错误";
ERR_GUILD_INVITE_S = "你邀请%s加入你的公会";
ERR_GUILD_JOIN_S = "%s加入了公会。";
ERR_GUILD_LEADER_CHANGED_SS = "%s让%s成为新的公会首领";
ERR_GUILD_LEADER_IS_S = "%s现在是你们公会的首领。";
ERR_GUILD_LEADER_LEAVE = "你必须在离开公会之前使用/gpromote命令指定一个新的公会首领。";
ERR_GUILD_LEADER_S = "%s成为公会首领。";
ERR_GUILD_LEADER_SELF = "你现在是公会首领";
ERR_GUILD_LEAVE_RESULT = "你离开了公会";
ERR_GUILD_LEAVE_S = "%s离开了公会。";
ERR_GUILD_NAME_EXISTS_S = "已经存在名为“\%s\”的公会。";
ERR_GUILD_NOT_ALLIED = "你不能邀请敌对势力的玩家";
ERR_GUILD_NOT_ENOUGH_MONEY = "公会银行储备不足";
ERR_GUILD_PERMISSIONS = "你没有权利这样做";
ERR_GUILD_PLAYER_NOT_FOUND_S = "\"%s\"没有找到";
ERR_GUILD_PLAYER_NOT_IN_GUILD = "你现在没有加入任何一个公会";
ERR_GUILD_PLAYER_NOT_IN_GUILD_S = "%s不在你的公会中";
ERR_GUILD_PROMOTE_SSS = "%s将%s晋升为%s。";
ERR_GUILD_QUIT_S = "现在你不再是%s的一员";
ERR_GUILD_RANKS_LOCKED = "临时性公会错误。请重试！";
ERR_GUILD_RANK_IN_USE = "这个公会等级目前正在使用中";
ERR_GUILD_RANK_TOO_HIGH_S = "%s的级别太高了";
ERR_GUILD_RANK_TOO_LOW_S = "%s的会阶已经是最低了。";
ERR_GUILD_REMOVE_SELF = "你被开除出公会";
ERR_GUILD_REMOVE_SS = "%s被%s开除出公会。";
ERR_GUILD_WITHDRAW_LIMIT = "你今天已经从公会银行提取太多物资了。";
ERR_IGNORE_ADDED_S = "%s现在被加入屏蔽名单了";
ERR_IGNORE_ALREADY_S = "%s已经被你加入屏蔽名单了";
ERR_IGNORE_AMBIGUOUS = "该名字不够明确，请输入该玩家的服务器名。";
ERR_IGNORE_DELETED = "从列表中移除屏蔽，原因：该角色不存在。";
ERR_IGNORE_FULL = "你的屏蔽列表已满";
ERR_IGNORE_NOT_FOUND = "没有找到玩家。";
ERR_IGNORE_REMOVED_S = "%s已被取消屏蔽。";
ERR_IGNORE_SELF = "你不能屏蔽自己";
ERR_IGNORING_YOU_S = "%s已将你屏蔽。";
ERR_INITIATE_TRADE_S = "你对%s发出交易请求";
ERR_INSPECT_S = "%s正在观察你。";
ERR_INTERNAL_BAG_ERROR = "内部包裹错误";
ERR_INVALID_ATTACK_TARGET = "你不能攻击那个目标";
ERR_INVALID_FOLLOW_TARGET = "你不能跟随这个目标";
ERR_INVALID_GLYPH_SLOT = "无效的雕文栏位。";
ERR_INVALID_INSPECT_TARGET = "你无法观察那个单位。";
ERR_INVALID_ITEM_TARGET = "这个物品是个无效的目标";
ERR_INVALID_PROMOTION_CODE = "无法验证号码，请重试。";
ERR_INVALID_RAID_TARGET = "目标无效，为敌方玩家";
ERR_INVALID_TELEPORT_LOCATION = "你没有有效的传送地点。";
ERR_INVITED_ALREADY_IN_GROUP_SS = "|Hplayer:%s|h[%s]|h邀请你加入队伍，但是你无法接受，因为你已经在一个队伍中了。";
ERR_INVITED_TO_ARENA_TEAM = "你已经被邀请加入一支竞技场战队了。";
ERR_INVITED_TO_GROUP_SS = "|Hplayer:%s|h[%s]|h邀请你加入队伍。";
ERR_INVITED_TO_GUILD = "你已经被邀请加入了一个公会";
ERR_INVITED_TO_GUILD_SSS = "|Hplayer:%s|h[%s]|h邀请你加入%s。";
ERR_INVITE_IN_COMBAT = "你不能邀请正在团队战斗中的玩家";
ERR_INVITE_NO_PARTY_SERVER = "无法创建小队";
ERR_INVITE_PARTY_BUSY = "在创建小队之前，无法邀请更多玩家";
ERR_INVITE_PLAYER_S = "你邀请%s加入你的队伍";
ERR_INVITE_RESTRICTED = "试玩账号无法邀请其他角色组队。";
ERR_INVITE_SELF = "你不能邀请自己加入一个队伍";
ERR_INVITE_UNKNOWN_REALM = "你不能邀请来自该服务器的玩家";
ERR_INV_FULL = "物品栏已满。";
ERR_IN_NON_RANDOM_BG = "当你处于另一个战场队列中时，你无法加入随机战场队列。";
ERR_IN_RANDOM_BG = "你不能在随机战场队列中那么做。";
ERR_ITEM_CANT_BE_DESTROYED = "这件物品无法被摧毁。";
ERR_ITEM_COOLDOWN = "物品还没有准备好。";
ERR_ITEM_INVENTORY_FULL_SATCHEL = "你的背包已满。已将包裹发往你的邮箱。";
ERR_ITEM_LOCKED = "物品被锁住了。";
ERR_ITEM_MAX_COUNT = "你不能携带更多此类物品。";
ERR_ITEM_MAX_COUNT_EQUIPPED_SOCKETED = "你在已装备的物品中镶嵌的该类宝石数量已达上限。";
ERR_ITEM_MAX_COUNT_SOCKETED = "你的背包或镶嵌槽中的该类宝石数量已经达到最大值。";
ERR_ITEM_MAX_LIMIT_CATEGORY_COUNT_EXCEEDED_IS = "你只能携带%d个%s";
ERR_ITEM_MAX_LIMIT_CATEGORY_EQUIPPED_EXCEEDED_IS = "你只能装备%d件%s类别中的物品";
ERR_ITEM_MAX_LIMIT_CATEGORY_SOCKETED_EXCEEDED_IS = "你只能装备%d件%s类别中的物品";
ERR_ITEM_NOT_FOUND = "未找到指定物品";
ERR_ITEM_UNIQUE_EQUIPABLE = "你只能装备一件该物品。";
ERR_ITEM_UNIQUE_EQUIPPABLE = "你只能装备一件该物品。";
ERR_ITEM_UNIQUE_EQUIPPABLE_SOCKETED = "你不能在一件镶空物品中镶入多于一颗该类宝石。";
ERR_JOINED_GROUP_S = "%s加入了队伍。";
ERR_KILLED_BY_S = "你被%s杀死了。";
ERR_LEARN_ABILITY_S = "你学会新的技能：%s";
ERR_LEARN_COMPANION_S = "你的收藏中新增一种宠物：%s。";
ERR_LEARN_RECIPE_S = "你已经学会了制作新物品：%s";
ERR_LEARN_SPELL_S = "你学会新的法术：%s";
ERR_LEFT_GROUP_S = "%s离开了队伍。";
ERR_LEFT_GROUP_YOU = "你离开了队伍。";
ERR_LFG_CANT_USE_BATTLEGROUND = "使用地下城系统时无法进入战场或竞技场队列。";
ERR_LFG_CANT_USE_DUNGEONS = "使用战场或竞技场系统时无法进入地下城队列。";
ERR_LFG_DESERTER_PARTY = "有一个或更多的队员带有逃亡者的负面效果。";
ERR_LFG_DESERTER_PLAYER = "在你的逃亡者负面效果消失之前，你无法进入地下城排队系统。";
ERR_LFG_GET_INFO_TIMEOUT = "无法获取某些队员的信息。";
ERR_LFG_GROUP_FULL = "你的队伍已满。";
ERR_LFG_INVALID_SLOT = "一个或更多的地下城暂不可用。";
ERR_LFG_JOINED_LIST = "你现在处于团队浏览器列表中。";
ERR_LFG_JOINED_QUEUE = "你现在处于地下城查找队列中。";
ERR_LFG_LEADER_IS_LFM_S = "在%s寻找更多队员时，你不能那么做。";
ERR_LFG_LEFT_LIST = "你已不再处于团队浏览器的列表中。";
ERR_LFG_LEFT_QUEUE = "你已离开地下城查找队列。";
ERR_LFG_MEMBERS_NOT_PRESENT = "有一个或更多的队员处于待定状态或已经断线。";
ERR_LFG_MISMATCHED_SLOTS = "挑选地下城时你不能将地下城、团队副本和随机副本混合在一起。";
ERR_LFG_NO_LFG_OBJECT = "内部寻找组队报错。";
ERR_LFG_NO_ROLES_SELECTED = "你必须至少选择一个职务。";
ERR_LFG_NO_SLOTS_PARTY = "有一个或更多的队员不符合进入所选地下城的要求。";
ERR_LFG_NO_SLOTS_PLAYER = "你的级别没有达到所选地下城的要求。";
ERR_LFG_NO_SLOTS_SELECTED = "你没有选择任何有效的栏位。";
ERR_LFG_PARTY_PLAYERS_FROM_DIFFERENT_REALMS = "你选择的地下城不支持跨服组队。";
ERR_LFG_PENDING = "你已经在等待进入比赛了。";
ERR_LFG_PLAYER_DECLINED_ROLE_CHECK = "%s没有选择任何职务。";
ERR_LFG_PROPOSAL_DECLINED_PARTY = "你已被移出地下城队列，因为你的小队中的某个成员没有接受邀请。";
ERR_LFG_PROPOSAL_DECLINED_SELF = "你已被移出地下城队列，因为你没有接受邀请。";
ERR_LFG_PROPOSAL_FAILED = "有人拒绝了你的组队邀请。你已被添加到地下城队列的前端。";
ERR_LFG_RANDOM_COOLDOWN_PARTY = "队伍中有一个或更多的队员拥有正在冷却中的随机地下城进度。";
ERR_LFG_RANDOM_COOLDOWN_PLAYER = "当你尚有处于冷却状态的随机地下城进度时，你无法进入随机地下城排队系统。";
ERR_LFG_ROLE_CHECK_ABORTED = "队伍中的队长取消了职责检查。";
ERR_LFG_ROLE_CHECK_FAILED = "职务检查失败。";
ERR_LFG_ROLE_CHECK_FAILED_NOT_VIABLE = "你的队伍不符合要求，职责检查失败。";
ERR_LFG_ROLE_CHECK_FAILED_TIMEOUT = "队员没有响应，职责检查失败。";
ERR_LFG_ROLE_CHECK_INITIATED = "已开始职责检查。当所有人都选择了职责后，队伍将进入队列。";
ERR_LFG_TOO_MANY_MEMBERS = "进入地下城的小队成员人数不能超过5人。";
ERR_LOGGING_OUT = "你正在登出";
ERR_LOGOUT_FAILED = "你不能在无法坐下的情况下登出";
ERR_LOOT_BAD_FACING = "你必须面向尸体才能进行搜索。";
ERR_LOOT_CANT_LOOT_THAT = "你没有达到拾取该物品的要求";
ERR_LOOT_CANT_LOOT_THAT_NOW = "你现在不能拾取那件物品。";
ERR_LOOT_DIDNT_KILL = "你没有被允许搜索这个尸体";
ERR_LOOT_GONE = "物品已经被拾取";
ERR_LOOT_LOCKED = "有人已经正在搜索尸体了";
ERR_LOOT_MASTER_INV_FULL = "该玩家的物品栏已满";
ERR_LOOT_MASTER_OTHER = "无法将物品分配给该玩家";
ERR_LOOT_MASTER_UNIQUE_ITEM = "该玩家已经有太多同类物品了";
ERR_LOOT_NOTSTANDING = "你必须处于站立状态下才能进行搜索！";
ERR_LOOT_NO_UI = "现在不能搜索尸体";
ERR_LOOT_PLAYER_NOT_FOUND = "没有找到玩家";
ERR_LOOT_ROLL_PENDING = "这个物品还在等待掷骰子决定归属。";
ERR_LOOT_STUNNED = "你不能在晕眩状态进行搜索！";
ERR_LOOT_TOO_FAR = "你离尸体太远，无法进行搜索";
ERR_LOOT_WHILE_INVULNERABLE = "不能在无敌状态下搜索尸体";
ERR_MAIL_ATTACHMENT_EXPIRED = "该物品已过期。";
ERR_MAIL_BAG = "你不能邮寄非空的背包";
ERR_MAIL_BOUND_ITEM = "你不能邮寄灵魂绑定的物品。";
ERR_MAIL_CONJURED_ITEM = "你不能邮寄魔法制造的物品";
ERR_MAIL_DATABASE_ERROR = "内部邮件数据库错误";
ERR_MAIL_INVALID_ATTACHMENT = "邮件附件无效。";
ERR_MAIL_INVALID_ATTACHMENT_SLOT = "邮件中最多只能附加12件物品。";
ERR_MAIL_LIMITED_DURATION_ITEM = "你不能邮递带有时限的物品";
ERR_MAIL_QUEST_ITEM = "你不能邮寄任务物品";
ERR_MAIL_REACHED_CAP = "你已经达到了收取邮件的数量上限";
ERR_MAIL_SENT = "邮件已发出。";
ERR_MAIL_TARGET_NOT_FOUND = "找不到收件人。";
ERR_MAIL_TOO_MANY_ATTACHMENTS = "邮件包含太多附件。";
ERR_MAIL_TO_SELF = "你不能给自己发送邮件。";
ERR_MAIL_WRAPPED_COD = "你不能使用货到付款方式发送包裹起来的物品。";
ERR_MAX_SOCKETS = "该物品无法获得更多插槽";
ERR_MEETING_STONE_GROUP_FULL = "你所在的小队已满";
ERR_MEETING_STONE_INVALID_LEVEL = "你没有达到使用这块集合石所需的等级。";
ERR_MEETING_STONE_INVALID_TARGET = "未选择目标";
ERR_MEETING_STONE_IN_PROGRESS = "你仍在使用集合石寻找更多玩家加入队伍。";
ERR_MEETING_STONE_IN_QUEUE_S = "你正在一个等待进入%s的队列中。";
ERR_MEETING_STONE_LEFT_QUEUE_S = "你离开了等待前往%s的队列。";
ERR_MEETING_STONE_MEMBER_ADDED_S = "%s被集合石加入了小队。";
ERR_MEETING_STONE_MEMBER_STILL_IN_QUEUE = "正在集合石队列中寻找新的队友。";
ERR_MEETING_STONE_MUST_BE_LEADER = "只有小队的队长才能与集合石互动";
ERR_MEETING_STONE_NEED_PARTY = "你必须在一个小队中才能使用集合石";
ERR_MEETING_STONE_NOT_FOUND = "未找到玩家。";
ERR_MEETING_STONE_NOT_LEADER = "只有队长才能离开集合石的队列";
ERR_MEETING_STONE_NO_RAID_GROUP = "你不能在团队中使用集合石";
ERR_MEETING_STONE_OTHER_MEMBER_LEFT = "队友已离开。正在集合石队列中寻找新的队伍。";
ERR_MEETING_STONE_SUCCESS = "你的小队已满，你离开了集合石队列。";
ERR_MEETING_STONE_TARGET_INVALID_LEVEL = "你的目标未达到集合石的等级要求。";
ERR_MEETING_STONE_TARGET_NOT_IN_PARTY = "你的目标不在小队中";
ERR_MOUNT_ALREADYMOUNTED = "你已经处于骑乘状态。";
ERR_MOUNT_FORCEDDISMOUNT = "在继续之前先要从坐骑上下来。";
ERR_MOUNT_INVALIDMOUNTEE = "你不能骑上它。";
ERR_MOUNT_LOOTING = "你不能在搜索尸体的时候骑乘";
ERR_MOUNT_NOTMOUNTABLE = "它不能被当作坐骑。";
ERR_MOUNT_NOTYOURPET = "这个坐骑不是你的宠物！";
ERR_MOUNT_OTHER = "未知的骑乘错误";
ERR_MOUNT_RACECANTMOUNT = "由于你的种族限制而无法骑乘。";
ERR_MOUNT_SHAPESHIFTED = "在变形状态下无法骑乘。";
ERR_MOUNT_TOOFARAWAY = "坐骑距离太远。";
ERR_MULTI_CAST_ACTION_TOTEM_S = "只有%s法术可以放入该空位。";
ERR_MUST_EQUIP_ITEM = "你必须先装备这件物品才能使用它。";
ERR_MUST_REPAIR_DURABILITY = "你必须先修理该物品才能使用它。";
ERR_NAME_CONSECUTIVE_SPACES = "无法使用连续的空格。请输入一个新名称。";
ERR_NAME_DECLENSION_DOESNT_MATCH_BASE_NAME = "Your declensions must match your original name.  Enter a new name.";
ERR_NAME_INVALID = "名字包括非法字符，请输入一个新的名字。";
ERR_NAME_INVALID_SPACE = "名称无法以空格开始或结束。请输入一个新名称。";
ERR_NAME_MIXED_LANGUAGES = "名字中只能够包含一种语言。请输入一个新的名字。";
ERR_NAME_NO_NAME = "请输入一个名字。";
ERR_NAME_PROFANE = "这个名字含有不良内容。请输入一个新的名字。";
ERR_NAME_RESERVED = "这个名字已经被保留。请输入一个新的名字。";
ERR_NAME_RUSSIAN_CONSECUTIVE_SILENT_CHARACTERS = "Consecutive silent characters are not allowed. Create a new name.";
ERR_NAME_RUSSIAN_SILENT_CHARACTER_AT_BEGINNING_OR_END = "不能在名字的开头或结尾输入不发音的字母。请输入新名字。";
ERR_NAME_THREE_CONSECUTIVE = "同一个字母不能连续输入3次。请输入新名字。";
ERR_NAME_TOO_LONG = "名字太长。请输入新的名字。";
ERR_NAME_TOO_LONG2 = "名字过长";
ERR_NAME_TOO_SHORT = "这个名字太短了，请输入一个新的名字。";
ERR_NEWTAXIPATH = "发现新的飞行路线！";
ERR_NEW_GUIDE_S = "%s现在是地下城向导。";
ERR_NEW_GUIDE_YOU = "你现在是地下城向导。";
ERR_NEW_LEADER_S = "%s现在是队长";
ERR_NEW_LEADER_YOU = "你现在是队长";
ERR_NEW_LOOT_MASTER_S = "%s现在负责拾取并分配所有战利品";
ERR_NOAMMO_S = "%s";
ERR_NOEMOTEWHILERUNNING = "你不能在移动中实施这个动作！";
ERR_NOTYOURPET = "这不是你的宠物！";
ERR_NOT_A_BAG = "不是背包。";
ERR_NOT_BARBER_SITTING = "你必须坐在理发椅上";
ERR_NOT_DURING_ARENA_MATCH = "你不能在竞技场战斗中那么做";
ERR_NOT_ENOUGH_ARENA_POINTS = "你没有足够的竞技场点数";
ERR_NOT_ENOUGH_GOLD = "金钱不足";
ERR_NOT_ENOUGH_HONOR_POINTS = "你的荣誉点数不足";
ERR_NOT_ENOUGH_MONEY = "你的钱不够。";
ERR_NOT_EQUIPPABLE = "这件物品无法装备";
ERR_NOT_IN_BATTLEGROUND = "你不在战场中";
ERR_NOT_IN_COMBAT = "你无法在战斗中实施那个动作";
ERR_NOT_IN_GROUP = "你现在没有在一个队伍中";
ERR_NOT_IN_RAID = "你不在一个团队中";
ERR_NOT_LEADER = "你现在不是队长";
ERR_NOT_OWNER = "你并未拥有这件物品。";
ERR_NOT_SAME_ACCOUNT = "只能将传家宝邮寄给你自己账号中的角色。";
ERR_NOT_WHILE_DISARMED = "你无法在被缴械时进行该动作";
ERR_NOT_WHILE_FALLING = "你不能在跳跃或摔落时那么做";
ERR_NOT_WHILE_FATIGUED = "你不能在疲劳时那么做";
ERR_NOT_WHILE_MOUNTED = "你不能在骑乘状态下那么做。";
ERR_NOT_WHILE_SHAPESHIFTED = "你不能在变形的时候实施这个动作。";
ERR_NO_ARENA_CHARTER = "你没有竞技场战队登记表。";
ERR_NO_ATTACK_TARGET = "现在没有可以攻击的目标";
ERR_NO_BANK_HERE = "你离银行太远";
ERR_NO_BANK_SLOT = "你没有足够的背包栏位";
ERR_NO_GUILD_CHARTER = "你现在没有公会登记表";
ERR_NO_ITEMS_WHILE_SHAPESHIFTED = "不能在变形状态下使用物品";
ERR_NO_PET = "你现在还没有宠物！";
ERR_NO_REPLY_TARGET = "还没有人回复你";
ERR_NO_SLOT_AVAILABLE = "你没有可以装备这件物品的栏位。";
ERR_NULL_PETNAME = "错误：宠物名称不能为空。";
ERR_OBJECT_IS_BUSY = "目标正在忙碌";
ERR_ONLY_ONE_AMMO = "你只能装备一个弹药袋";
ERR_ONLY_ONE_BOLT = "你只能装备一个箭袋";
ERR_ONLY_ONE_QUIVER = "你只能装备一个箭袋";
ERR_OUT_OF_ENERGY = "能量值不足";
ERR_OUT_OF_FOCUS = "集中值不足";
ERR_OUT_OF_HEALTH = "生命值不足";
ERR_OUT_OF_MANA = "法力值不足";
ERR_OUT_OF_POWER_DISPLAY = "%s不足";
ERR_OUT_OF_RAGE = "怒气值不足";
ERR_OUT_OF_RANGE = "距离太远。";
ERR_OUT_OF_RUNES = "符文不足";
ERR_OUT_OF_RUNIC_POWER = "符文能量不足";
ERR_PARTY_LFG_BOOT_COOLDOWN_S = "你必须等待%s才能发起另一个踢人投票。";
ERR_PARTY_LFG_BOOT_DUNGEON_COMPLETE = "玩家无法在副本完成后被踢出。";
ERR_PARTY_LFG_BOOT_IN_COMBAT = "玩家无法在战斗中和战斗结束后的短时间内被踢出。";
ERR_PARTY_LFG_BOOT_IN_PROGRESS = "另一个踢人投票已在进行中。";
ERR_PARTY_LFG_BOOT_LIMIT = "你不能再发起任何踢人投票了。";
ERR_PARTY_LFG_BOOT_LOOT_ROLLS = "玩家无法在战利品分配掷骰时被踢出。";
ERR_PARTY_LFG_BOOT_NOT_ELIGIBLE_S = "此玩家在随后的%s内尚不能被踢出。";
ERR_PARTY_LFG_BOOT_TOO_FEW_PLAYERS = "人员不足，无法发起投票。";
ERR_PARTY_LFG_BOOT_VOTE_FAILED = "将%s移出队伍的投票失败。";
ERR_PARTY_LFG_BOOT_VOTE_SUCCEEDED = "将%s移出队伍的投票通过。";
ERR_PARTY_LFG_INVITE_RAID_LOCKED = "%s已与另一个副本锁定。";
ERR_PARTY_LFG_TELEPORT_IN_COMBAT = "你不能在战斗中传送出地下城。";
ERR_PARTY_TARGET_AMBIGUOUS = "队伍中存在同名玩家。请将服务器名称也包括在内以便区别。";
ERR_PASSIVE_ABILITY = "你不能在动作条中放置一个被动技能";
ERR_PETITION_ALREADY_SIGNED = "你已经在那张登记表上签名了。";
ERR_PETITION_ALREADY_SIGNED_OTHER = "你已经签署了另一张公会登记表";
ERR_PETITION_CREATOR = "你不能在自己的登记表上签名。";
ERR_PETITION_DECLINED_S = "%s拒绝在你的登记表上签字。";
ERR_PETITION_FULL = "申请表已满";
ERR_PETITION_IN_GUILD = "你已经加入了一个公会";
ERR_PETITION_NOT_ENOUGH_SIGNATURES = "你需要更多的签名。";
ERR_PETITION_NOT_SAME_SERVER = "该玩家与你不在同一个服务器";
ERR_PETITION_OFFERED_S = "你要求%s的签名";
ERR_PETITION_RESTRICTED_ACCOUNT = "试玩账号无法签署公会注册表。";
ERR_PETITION_SIGNED = "已签署登记表。";
ERR_PETITION_SIGNED_S = "%s已经在你的登记表上签名。";
ERR_PET_BROKEN = "你的宠物逃跑了";
ERR_PET_LEARN_ABILITY_S = "你的宠物学会了新的技能：%s。";
ERR_PET_LEARN_SPELL_S = "你的宠物学会了新的法术：%s。";
ERR_PET_NOT_RENAMEABLE = "你的宠物无法被重新命名。";
ERR_PET_SPELL_AFFECTING_COMBAT = "你的宠物正在战斗。";
ERR_PET_SPELL_ALREADY_KNOWN_S = "你的宠物已经学会%s了。";
ERR_PET_SPELL_DEAD = "你的宠物已经死亡。";
ERR_PET_SPELL_NOT_BEHIND = "你的宠物必须在目标的背后。";
ERR_PET_SPELL_OUT_OF_RANGE = "宠物距离目标太远。";
ERR_PET_SPELL_ROOTED = "你的宠物无法移动。";
ERR_PET_SPELL_TARGETS_DEAD = "宠物的目标已经死亡。";
ERR_PET_SPELL_UNLEARNED_S = "你的宠物遗忘了%s。";
ERR_PLAYERLIST_JOINED_BATTLE = "%d名玩家加入了战斗：%s";
ERR_PLAYERLIST_LEFT_BATTLE = "%d名玩家离开了战斗：%s";
ERR_PLAYERS_JOINED_BATTLE_D = "%d名玩家加入了战斗。";
ERR_PLAYERS_LEFT_BATTLE_D = "%d名玩家离开了战斗。";
ERR_PLAYER_BUSY_S = "%s现在很忙";
ERR_PLAYER_DEAD = "你已经死亡，无法实施该动作";
ERR_PLAYER_DIED_S = "%s已经死亡。";
ERR_PLAYER_DIFFICULTY_CHANGED_S = "难度设为%s。";
ERR_PLAYER_JOINED_BATTLE_D = "%s加入了战斗。";
ERR_PLAYER_LEFT_BATTLE_D = "%s离开了战斗。";
ERR_PLAYER_SILENCED = "你的语音权限被一位队长移除了。";
ERR_PLAYER_SILENCED_ECHO = "你移除了%s的语音权限。";
ERR_PLAYER_UNSILENCED = "你的语音权限被一位队长恢复了。";
ERR_PLAYER_UNSILENCED_ECHO = "你恢复了%s的语音权限。";
ERR_PLAYER_WRONG_FACTION = "目标属于对立阵营。";
ERR_PLAY_TIME_EXCEEDED = "已超过最大游戏时间";
ERR_POTION_COOLDOWN = "你已经喝得太饱了。";
ERR_PROFANE_CHAT_NAME = "你不能使用违规词汇创建频道。";
ERR_PROFICIENCY_GAINED_S = "你掌握了%s的使用方法。";
ERR_PROFICIENCY_NEEDED = "你不会使用这种物品。";
ERR_PURCHASE_LEVEL_TOO_LOW = "你必须达到%d级才能购买该物品。";
ERR_PVP_TOGGLE_OFF = "关闭PvP战斗";
ERR_PVP_TOGGLE_ON = "开启PvP战斗";
ERR_QUEST_ACCEPTED_S = "接受任务：%s";
ERR_QUEST_ADD_FOUND_SII = "%s：%d/%d";
ERR_QUEST_ADD_ITEM_SII = "%s：%d/%d";
ERR_QUEST_ADD_KILL_SII = "已杀死%s：%d/%d";
ERR_QUEST_ADD_PLAYER_KILL_SII = "已杀死玩家：%d/%d";
ERR_QUEST_ALREADY_DONE = "你已经完成了那个任务。";
ERR_QUEST_ALREADY_DONE_DAILY = "你今天已经完成这个日常任务了。";
ERR_QUEST_ALREADY_ON = "你已经接到了该任务";
ERR_QUEST_COMPLETE_S = "%s完成。";
ERR_QUEST_FAILED_BAG_FULL_S = "%s失败：背包已满。";
ERR_QUEST_FAILED_CAIS = "你在疲惫状态下不能完成任务。";
ERR_QUEST_FAILED_EXPANSION = "这项任务要求一个开启了资料片权限的账号。";
ERR_QUEST_FAILED_LOW_LEVEL = "你现在的等级没有达到任务的等级要求";
ERR_QUEST_FAILED_MAX_COUNT_S = "%s失败：发现重复的物品。";
ERR_QUEST_FAILED_MISSING_ITEMS = "你没有所需要的物品。请查看自己的物品栏。";
ERR_QUEST_FAILED_NOT_ENOUGH_MONEY = "你没有足够的钱去完成这个任务";
ERR_QUEST_FAILED_S = "%s失败。";
ERR_QUEST_FAILED_TOO_MANY_DAILY_QUESTS_I = "你今天已经完成了%d个日常任务";
ERR_QUEST_FAILED_WRONG_RACE = "你的种族不能接受这个任务。";
ERR_QUEST_FORCE_REMOVED_S = "已将任务：%s从你的任务日志中移除";
ERR_QUEST_LOG_FULL = "你的任务日志已经满了。";
ERR_QUEST_MUST_CHOOSE = "你必须选择一件奖励品。";
ERR_QUEST_NEED_PREREQS = "你还未达到接受该任务的前提条件";
ERR_QUEST_OBJECTIVE_COMPLETE_S = "%s（完成）";
ERR_QUEST_ONLY_ONE_TIMED = "你不能同时做多个限时任务";
ERR_QUEST_PUSH_ACCEPTED_S = "%s接受了你的任务。";
ERR_QUEST_PUSH_ALREADY_DONE_S = "%s已经完成这个任务了";
ERR_QUEST_PUSH_BUSY_S = "%s很忙。";
ERR_QUEST_PUSH_DECLINED_S = "%s拒绝了你的任务。";
ERR_QUEST_PUSH_DIFFERENT_SERVER_DAILY_S = "%s今天不能接受这个任务";
ERR_QUEST_PUSH_INVALID_S = "无法与%s共享任务";
ERR_QUEST_PUSH_LOG_FULL_S = "%s的任务记录已满";
ERR_QUEST_PUSH_NOT_DAILY_S = "今天无法共享该任务";
ERR_QUEST_PUSH_NOT_IN_PARTY_S = "你不在一个小队中";
ERR_QUEST_PUSH_ONQUEST_S = "%s已经有这个任务了";
ERR_QUEST_PUSH_SUCCESS_S = "已与%s共享任务";
ERR_QUEST_PUSH_TIMER_EXPIRED_S = "任务共享已过期";
ERR_QUEST_REWARD_EXP_I = "获得经验值：%d";
ERR_QUEST_REWARD_ITEM_MULT_IS = "收取物品：%2$s（%1$d）。";
ERR_QUEST_REWARD_ITEM_S = "获得物品：%s。";
ERR_QUEST_REWARD_MONEY_S = "获得%s。";
ERR_QUEST_UNKNOWN_COMPLETE = "目标达成";
ERR_RAID_DIFFICULTY_CHANGED_S = "团队副本难度设置为%s。";
ERR_RAID_DIFFICULTY_FAILED = "无法更改团队副本难度";
ERR_RAID_DISALLOWED_BY_LEVEL = "角色等级过低，无法参加团队副本。";
ERR_RAID_GROUP_FULL = "此副本已满。";
ERR_RAID_GROUP_LOWLEVEL = "你的等级太低，无法进入此副本。";
ERR_RAID_GROUP_ONLY = "你必须在一个团队中才能进入这个副本。";
ERR_RAID_GROUP_REQUIREMENTS_UNMATCH = "你没有达到进入该副本的前提条件。";
ERR_RAID_LEADER_READY_CHECK_START_S = "%s开始进行就位确认。";
ERR_RAID_LOCKOUT_CHANGED_S = "团队副本锁定设置为%s。";
ERR_RAID_MEMBER_ADDED_S = "%s加入了团队。";
ERR_RAID_MEMBER_REMOVED_S = "%s离开了团队。";
ERR_RAID_YOU_JOINED = "你加入了一个团队。";
ERR_RAID_YOU_LEFT = "你已经离开了这个团队。";
ERR_READY_CHECK_IN_PROGRESS = "你已经在进行就位确认了";
ERR_READY_CHECK_THROTTLED = "你还不能那么做";
ERR_RECEIVE_ITEM_S = "获得%s。";
ERR_REFER_A_FRIEND_DIFFERENT_FACTION = "你不能为对立阵营的角色提升等级";
ERR_REFER_A_FRIEND_GRANT_LEVEL_MAX_I = "你不能为等级高于%d级的玩家提升等级";
ERR_REFER_A_FRIEND_INSUFFICIENT_GRANTABLE_LEVELS = "你的等级还不足以为战友提升更多等级";
ERR_REFER_A_FRIEND_INSUF_EXPAN_LVL = "该玩家没有安装相应的资料片，因此无法进入本区域";
ERR_REFER_A_FRIEND_NOT_NOW = "目前你还不能为该玩家提升等级。";
ERR_REFER_A_FRIEND_NOT_REFERRED_BY = "你没有被该玩家标记为战友";
ERR_REFER_A_FRIEND_SUMMON_COOLDOWN = "你每小时只能召唤一次战友";
ERR_REFER_A_FRIEND_SUMMON_LEVEL_MAX_I = "你不能召唤等级高于%d级的玩家";
ERR_REFER_A_FRIEND_SUMMON_OFFLINE_S = "%s已离线，无法被召唤";
ERR_REFER_A_FRIEND_TARGET_TOO_HIGH = "该玩家等级太高";
ERR_REFER_A_FRIEND_TOO_FAR = "目标玩家距离太远";
ERR_REMOVE_FROM_PVP_QUEUE_FACTION_CHANGE_NONE = "你因为进行了阵营修改而从PVP队列中被移出。";
ERR_REMOVE_FROM_PVP_QUEUE_GRANT_LEVEL = "你因另一名玩家使你提升一级而被移出PVP队列";
ERR_REMOVE_FROM_PVP_QUEUE_XP_GAIN = "你因为修改了经验值获取设定而被移出PVP队列";
ERR_RESTRICTED_ACCOUNT = "试玩账号无法进行该动作";
ERR_SCALING_STAT_ITEM_LEVEL_EXCEEDED = "你的等级太高，无法使用该物品";
ERR_SET_LOOT_FREEFORALL = "分配方式设定为自由拾取。";
ERR_SET_LOOT_GROUP = "分配方式设定为队伍分配。";
ERR_SET_LOOT_MASTER = "分配方式设定为队长分配。";
ERR_SET_LOOT_NBG = "分配方式设定为需求优先。";
ERR_SET_LOOT_ROUNDROBIN = "分配方式设定为轮流拾取。";
ERR_SET_LOOT_THRESHOLD_S = "分配物品界限设定为%s。";
ERR_SHAPESHIFT_FORM_CANNOT_EQUIP = "在该形态下无法装备此物品";
ERR_SKILL_GAINED_S = "你学会了%s技能。";
ERR_SKILL_UP_SI = "你的%s技能提高到了%d。";
ERR_SLOT_EMPTY = "这个栏位是空的";
ERR_SOCKETING_META_GEM_ONLY_IN_METASLOT = "多彩宝石只能镶嵌在多彩宝石插槽中";
ERR_SOCKETING_REQUIRES_META_GEM = "该插槽需要多彩宝石";
ERR_SPECIFY_MASTER_LOOTER = "你必须指定一个物品分配人员。";
ERR_SPELL_ALREADY_KNOWN_S = "你已经学会%s了。";
ERR_SPELL_COOLDOWN = "法术还没有准备好。";
ERR_SPELL_FAILED_ALREADY_AT_FULL_HEALTH = "你的生命值已满";
ERR_SPELL_FAILED_ALREADY_AT_FULL_MANA = "你的法力值已经全满了";
ERR_SPELL_FAILED_ALREADY_AT_FULL_POWER_S = "你的%s已满";
ERR_SPELL_FAILED_EQUIPPED_ITEM = "必须装备正确的物品";
ERR_SPELL_FAILED_EQUIPPED_ITEM_CLASS_S = "%s";
ERR_SPELL_FAILED_NOTUNSHEATHED = "你必须拔出武器来实施这个动作！";
ERR_SPELL_FAILED_REAGENTS = "%s";
ERR_SPELL_FAILED_REAGENTS_GENERIC = "缺少材料";
ERR_SPELL_FAILED_S = "%s";
ERR_SPELL_FAILED_SHAPESHIFT_FORM_S = "%s";
ERR_SPELL_FAILED_TOTEMS = "%s";
ERR_SPELL_OUT_OF_RANGE = "距离太远。";
ERR_SPELL_UNLEARNED_S = "你忘却了%s";
ERR_SPLIT_FAILED = "无法分开这些物品";
ERR_SYSTEM_DISABLED = "该系统目前已被禁用。";
ERR_TALENT_WIPE_ERROR = "你还没有分配任何天赋点数。";
ERR_TAME_FAILED = "%s。";
ERR_TARGET_LOGGING_OUT = "该玩家已经登出。";
ERR_TARGET_NOT_IN_GROUP_S = "%s不在你的队伍中";
ERR_TARGET_NOT_IN_INSTANCE_S = "%s不在你的副本中。";
ERR_TARGET_STUNNED = "目标被晕眩";
ERR_TAXINOPATH = "你选择的路径不存在！";
ERR_TAXINOPATHS = "你还未发现任何连接到这里的飞行点。";
ERR_TAXINOSUCHPATH = "没有到该地点的直接路径！";
ERR_TAXINOTENOUGHMONEY = "你没有足够的钱！";
ERR_TAXINOTSTANDING = "你必须先站起来。";
ERR_TAXINOTVISITED = "你还没有步行到达过该空运站！";
ERR_TAXINOVENDORNEARBY = "附近没有空运站！";
ERR_TAXIPLAYERALREADYMOUNTED = "你已经上了坐骑！请先下来。";
ERR_TAXIPLAYERBUSY = "你太忙了，无法使用空中运输服务。";
ERR_TAXIPLAYERMOVING = "你正在移动。";
ERR_TAXIPLAYERSHAPESHIFTED = "你不能在变形状态下使用空中运输服务！";
ERR_TAXISAMENODE = "你已经在那里了！";
ERR_TAXITOOFARAWAY = "你离空运站太远了。";
ERR_TAXIUNSPECIFIEDSERVERERROR = "未知的服务器错误";
ERR_TICKET_ALREADY_EXISTS = "你已经有一张GM请求了。";
ERR_TICKET_CREATE_ERROR = "创建GM请求错误";
ERR_TICKET_DB_ERROR = "获取GM请求错误";
ERR_TICKET_NO_TEXT = "你必须输入正文的内容。";
ERR_TICKET_TEXT_TOO_LONG = "你的表单内容过长。";
ERR_TICKET_UPDATE_ERROR = "更新GM请求错误";
ERR_TOOBUSYTOFOLLOW = "你现在很忙，无法跟随！";
ERR_TOO_FAR_TO_ATTACK = "你离你的攻击目标太远了！";
ERR_TOO_FAR_TO_INTERACT = "你必须更加靠近该目标才能与之互动。";
ERR_TOO_FEW_TO_SPLIT = "你试图分开的数量大于物品原有的数量。";
ERR_TOO_MANY_CHAT_CHANNELS = "你最多同时进入10个频道。";
ERR_TOO_MANY_SOCKETS = "该物品插槽过多。";
ERR_TOO_MANY_SPECIAL_BAGS = "你只能装备一个该类型的背包";
ERR_TOO_MUCH_GOLD = "已达到金币数量上限";
ERR_TRADE_BAG = "你不能交易装有物品的包";
ERR_TRADE_BAG_FULL = "交易失败，你没有足够的物品栏空间。";
ERR_TRADE_BLOCKED_S = "%s要求进行交易，你拒绝了。";
ERR_TRADE_BOUND_ITEM = "你不能交易一件灵魂绑定物品";
ERR_TRADE_CANCELLED = "交易取消";
ERR_TRADE_COMPLETE = "交易完成";
ERR_TRADE_EQUIPPED_BAG = "你无法交易已经装备的包裹。";
ERR_TRADE_GROUND_ITEM = "你不能交易一件还放在地上的物品。";
ERR_TRADE_MAX_COUNT_EXCEEDED = "你拥有超过一件";
ERR_TRADE_NOT_ON_TAPLIST = "你只能将绑定物品交易给拥有该物品起始拾取资格的玩家。";
ERR_TRADE_QUEST_ITEM = "你不能交易一件任务物品";
ERR_TRADE_REQUEST_S = "%s想要和你进行交易。";
ERR_TRADE_SELF = "你不能与自己交易。";
ERR_TRADE_TARGET_BAG_FULL = "交易失败，交易目标没有足够的物品栏空间。";
ERR_TRADE_TARGET_DEAD = "你不能和已死亡的玩家交易";
ERR_TRADE_TARGET_MAX_COUNT_EXCEEDED = "交易对象已经拥有该类唯一物品。";
ERR_TRADE_TARGET_MAX_LIMIT_CATEGORY_COUNT_EXCEEDED_IS = "你的交易对象只能携带%d个%s";
ERR_TRADE_TEMP_ENCHANT_BOUND = "你不能交易带有临时附魔的物品。";
ERR_TRADE_TOO_FAR = "交易目标太远";
ERR_TRADE_WRONG_REALM = "你只能与来自其它服务器的玩家交易魔法制造的物品";
ERR_UNHEALTHY_TIME = "您已进入不健康游戏时间，为了您的健康，请您立即下线休息。如不下线，您的身体将受到损害，您的收益已降为零，直到您的累计下线时间满5小时后，才能恢复正常。";
ERR_UNINVITE_YOU = "你已经被移出队伍";
ERR_UNIT_NOT_FOUND = "未知的单位";
ERR_UNKNOWN_MACRO_OPTION_S = "未知的宏设置：%s";
ERR_USER_SQUELCHED = "您的聊天和邮件权限已被暂时禁用，以便GM检查。";
ERR_USE_BAD_ANGLE = "你面朝的角度不对！";
ERR_USE_CANT_IMMUNE = "无法在免疫状态下那样做。";
ERR_USE_CANT_OPEN = "你不能打开这个";
ERR_USE_DESTROYED = "已经被摧毁。";
ERR_USE_LOCKED = "物品被锁住了。";
ERR_USE_LOCKED_WITH_ITEM_S = "需要%s";
ERR_USE_LOCKED_WITH_SPELL_KNOWN_SI = "需要%s %d";
ERR_USE_LOCKED_WITH_SPELL_S = "需要%s";
ERR_USE_OBJECT_MOVING = "目标在移动中";
ERR_USE_PREVENTED_BY_MECHANIC_S = "在%s时无法使用。";
ERR_USE_SPELL_FOCUS = "目标是个法术焦点。";
ERR_USE_TOO_FAR = "你距离太远了。";
ERR_VENDOR_DOESNT_BUY = "你不能将物品卖给这个商人";
ERR_VENDOR_HATES_YOU = "商人不喜欢你。";
ERR_VENDOR_MISSING_TURNINS = "你没有购买该物品所需的物品";
ERR_VENDOR_NOT_INTERESTED = "商人不要这件物品";
ERR_VENDOR_SOLD_OUT = "这件物品已经售完";
ERR_VENDOR_TOO_FAR = "你距离太远了。";
ERR_VOICESESSION_FULL = "你试图加入的语音频道已满。";
ERR_VOICE_CHAT_PARENTAL_DISABLE_ALL = "语音聊天已被家长控制系统禁用。";
ERR_VOICE_CHAT_PARENTAL_DISABLE_MIC = "麦克风已被家长控制系统禁用。";
ERR_VOICE_IGNORE_ADDED_S = "%s被禁声了。";
ERR_VOICE_IGNORE_ALREADY_S = "%s已被禁声。";
ERR_VOICE_IGNORE_AMBIGUOUS = "搜索条件模糊，请输入该玩家的服务器名。";
ERR_VOICE_IGNORE_DELETED = "移除语音禁声，因为该玩家角色不存在。";
ERR_VOICE_IGNORE_FULL = "你不能再禁声更多玩家了。";
ERR_VOICE_IGNORE_NOT_FOUND = "未找到玩家。";
ERR_VOICE_IGNORE_REMOVED_S = "%s被解除禁声了。";
ERR_VOICE_IGNORE_SELF = "你无法对自己实行禁声。";
ERR_WRONG_BAG_TYPE = "该物品无法装入这个包中。";
ERR_WRONG_BAG_TYPE_SUBCLASS = "只有%s能放在那个位置。";
ERR_WRONG_DIRECTION_FOR_ATTACK = "你必须面对正确的方向进行攻击！";
ERR_WRONG_SLOT = "物品无法放入该空位。";
ERR_YELL_RESTRICTED = "试玩账号不能呼喊（/y）";
ERR_ZONE_EXPLORED = "发现：%s";
ERR_ZONE_EXPLORED_XP = "发现%s：获得%d点经验值";
ESES = "西班牙语（欧洲）";
ESMX = "西班牙语（拉丁美洲）";
EVADE = "闪避";
EVENTS_LABEL = "事件";
EXAMPLE_SPELL_FIREBALL = "火球术";
EXAMPLE_SPELL_FROSTBOLT = "寒冰箭";
EXAMPLE_TARGET_MONSTER = "怪物";
EXAMPLE_TEXT = "范例文字：";
EXHAUSTION_LABEL = "疲倦";
EXHAUST_TOOLTIP1 = "|cffffd200%s|r\n|cffffffff从怪物身上获得正常经验值的%d%%。|r";
EXHAUST_TOOLTIP2 = "\n|cffff0000你应该找一家旅店休息。|r";
EXHAUST_TOOLTIP3 = "\n\n在当前的状态下，你可以得到\n比基础经验值多出%d的经验值\n，直到进入下一个休息状态阶段。";
EXHAUST_TOOLTIP4 = "\n\n|cffffd200正在休息|r\n|cffffffff你必须再休息%d\n分钟才能恢复到|r |cffffd200精力充沛状态|r|cffffffff。|r";
EXIT = "退出";
EXIT_GAME = "退出游戏";
EXOTICS = "异种武器";
EXPANSION_NAME0 = "经典旧世";
EXPANSION_NAME1 = "燃烧的远征";
EXPANSION_NAME2 = "巫妖王之怒";
EXPERIENCE_COLON = "经验值：";
EXPERTISE_ABBR = "Expr";
EXTENDED = "|cff00ff00已延长|r";
EXTEND_RAID_LOCK = "延长副本锁定";
EXTRA_ATTACKS = "额外攻击";
EYE_SEPARATION = "深度值";
English = "";
FACIAL_HAIR_EARRINGS = "耳环";
FACIAL_HAIR_FEATURES = "特色";
FACIAL_HAIR_HAIR = "头发";
FACIAL_HAIR_HORNS = "犄角形状";
FACIAL_HAIR_MARKINGS = "面纹";
FACIAL_HAIR_NORMAL = "胡须";
FACIAL_HAIR_PIERCINGS = "刺环";
FACIAL_HAIR_TUSKS = "獠牙";
FACING_WRONG_DIRECTION = "你没有面向正确的方向！";
FACTION = "阵营";
FACTION_ALLIANCE = "联盟";
FACTION_CONTROLLED_TERRITORY = "（%s领地）";
FACTION_HORDE = "部落";
FACTION_INACTIVE = "隐藏";
FACTION_OTHER = "其它";
FACTION_STANDING_CHANGED = "你在%2$s中的声望达到了%1$s。";
FACTION_STANDING_DECREASED = "你在%s中的声望值降低了%d点。";
FACTION_STANDING_DECREASED_GENERIC = "在%s中的声望降低了。";
FACTION_STANDING_INCREASED = "你在%s中的声望值提高了%d点。";
FACTION_STANDING_INCREASED_BONUS = "在%s中的声望提高%d点。（+%.1f战友招募奖励）";
FACTION_STANDING_INCREASED_GENERIC = "在%s中的声望提升了。";
FACTION_STANDING_LABEL1 = "仇恨";
FACTION_STANDING_LABEL1_FEMALE = "仇恨";
FACTION_STANDING_LABEL2 = "敌对";
FACTION_STANDING_LABEL2_FEMALE = "敌对";
FACTION_STANDING_LABEL3 = "冷淡";
FACTION_STANDING_LABEL3_FEMALE = "冷淡";
FACTION_STANDING_LABEL4 = "中立";
FACTION_STANDING_LABEL4_FEMALE = "中立";
FACTION_STANDING_LABEL5 = "友善";
FACTION_STANDING_LABEL5_FEMALE = "友善";
FACTION_STANDING_LABEL6 = "尊敬";
FACTION_STANDING_LABEL6_FEMALE = "尊敬";
FACTION_STANDING_LABEL7 = "崇敬";
FACTION_STANDING_LABEL7_FEMALE = "崇敬";
FACTION_STANDING_LABEL8 = "崇拜";
FACTION_STANDING_LABEL8_FEMALE = "崇拜";
FAILED = "失败";
FAILURES = "失败";
FAR = "远";
FARCLIP = "视野距离";
FEATURES_LABEL = "特色";
FEATURES_SUBTEXT = "使你可以开启或关闭游戏中的全部特色选项。";
FEATURE_BECOMES_AVAILABLE_AT_LEVEL = "该功能将在%d级开启。";
FEAT_OF_STRENGTH_DESCRIPTION = "对于许多玩家来说，“光辉事迹”中的成就几乎不可能完成，至少是极端困难的。它们并不奖励成就点数，而是你在艾泽拉斯世界曾经创下的丰功伟绩的纪录。";
FEEDPET_LOG_FIRSTPERSON = "你的宠物开始食用%s。";
FEEDPET_LOG_THIRDPERSON = "%s的宠物开始食用%s。";
FEETSLOT = "脚";
FEMALE = "女性";
FERAL_DRUID_ITEM_AP = "在猎豹、熊、巨熊和枭兽形态下的攻击强度提高%d点。";
FILTER = "过滤器";
FILTERS = "过滤器";
FILTER_BY_ENEMIES_COMBATLOG_TOOLTIP = "敌方怪物和宠物进行的动作。";
FILTER_BY_FRIENDS_COMBATLOG_TOOLTIP = "由友方玩家、宠物和怪物进行的动作。";
FILTER_BY_HOSTILE_PLAYERS_COMBATLOG_TOOLTIP = "敌方玩家进行的动作。";
FILTER_BY_ME_COMBATLOG_TOOLTIP = "由你、你的陷阱和你的法术进行的动作。";
FILTER_BY_NEUTRAL_COMBATLOG_TOOLTIP = "由中立生物进行的动作。";
FILTER_BY_PET_COMBATLOG_TOOLTIP = "由你的宠物、召唤生物和被魅惑玩家进行的动作。";
FILTER_BY_UNKNOWN_COMBATLOG_TOOLTIP = "由掉落、酸液或岩浆之类的源头产生的动作。";
FILTER_NAME = "过滤名称";
FILTER_TO_FRIENDS_COMBATLOG_TOOLTIP = "对友方玩家、宠物和怪物进行的动作。";
FILTER_TO_HOSTILE_COMBATLOG_TOOLTIP = "对敌方宠物和怪物进行的动作。";
FILTER_TO_HOSTILE_PLAYERS_COMBATLOG_TOOLTIP = "对敌方玩家进行的动作。";
FILTER_TO_ME_COMBATLOG_TOOLTIP = "对你进行的动作。";
FILTER_TO_NEUTRAL_COMBATLOG_TOOLTIP = "对中立生物进行的动作。";
FILTER_TO_PET_COMBATLOG_TOOLTIP = "对你的宠物、召唤生物和被魅惑玩家进行的动作。";
FILTER_TO_UNKNOWN_COMBATLOG_TOOLTIP = "对隐形目标进行的动作。";
FIND_A_GROUP = "寻找组队";
FIND_DUNGEON = "寻找地下城";
FINGER0SLOT = "手指";
FINGER0SLOT_UNIQUE = "戒指1";
FINGER1SLOT = "手指";
FINGER1SLOT_UNIQUE = "戒指2";
FIRST_AVAILABLE = "首先可用";
FIRST_AVAILABLE_TOOLTIP = "加入首先可用的战场等待序列。如果你选择了“首先可用”战场，而另一个队友已经进入了“首先可用”战场，那么你的默认战场副本也会被更改为该战场。";
FIRST_NUMBER_CAP = " K";
FIX_LAG = "降低输入延迟";
FLAG_COUNT_TEMPLATE = "x %d";
FLOOR = "区域";
FLOOR_NUMBER = "区域 %d";
FOCUS = "集中";
FOCUSTARGET = "设定焦点目标";
FOCUS_CAST_KEY_TEXT = "焦点施法按键";
FOCUS_COST = "%d集中";
FOCUS_COST_PER_TIME = "%d集中，外加每秒%d";
FOCUS_TOKEN_NOT_FOUND = "<无焦点>";
FOLLOW = "跟随";
FOLLOW_TERRAIN = "跟随地形";
FONT_SIZE = "字体大小";
FONT_SIZE_TEMPLATE = "%d pt";
FOOD_TIMER = "你已经吃得太饱了。";
FOREIGN_SERVER_LABEL = " (*)";
FORMATED_HOURS = "%d小时";
FORMATTING = "格式";
FPS_ABBR = "fps";
FRAMERATE_LABEL = "每秒帧数:";
FREE_FOR_ALL_TERRITORY = "（PvP区域）";
FRFR = "法语";
FRIEND = "好友";
FRIENDLY = "友方";
FRIENDS = "好友";
FRIENDS_FRIENDS_CHOICE_EVERYONE = "所有人";
FRIENDS_FRIENDS_CHOICE_MUTUAL = "共同好友";
FRIENDS_FRIENDS_CHOICE_POTENTIAL = "潜在好友";
FRIENDS_FRIENDS_HEADER = "%s的好友";
FRIENDS_FRIENDS_MUTUAL_TEXT = "（共同的）";
FRIENDS_FRIENDS_REQUESTED_TEXT = "（请求已发送）";
FRIENDS_FRIENDS_WAITING = "搜寻中……";
FRIENDS_LEVEL_TEMPLATE = "等级%d %s";
FRIENDS_LIST = "好友名单";
FRIENDS_LIST_AVAILABLE = "有空";
FRIENDS_LIST_AWAY = "离开";
FRIENDS_LIST_BUSY = "忙碌";
FRIENDS_LIST_ENTER_TEXT = "告诉你的好友你正在干什么";
FRIENDS_LIST_NOTE_OFFLINE_TEMPLATE = "|cff999999(%s)|r";
FRIENDS_LIST_NOTE_TEMPLATE = "(%s)";
FRIENDS_LIST_OFFLINE = "离线";
FRIENDS_LIST_OFFLINE_TEMPLATE = "|cff999999%s - 离线|r";
FRIENDS_LIST_ONLINE = "在线";
FRIENDS_LIST_PLAYING = "其他在线角色：";
FRIENDS_LIST_REALM = "服务器：";
FRIENDS_LIST_STATUS_TOOLTIP = "状态：|cffffffff%s";
FRIENDS_LIST_TEMPLATE = "|cffffffff- %s|r %s";
FRIENDS_LIST_WOW_TEMPLATE = "%1$s, %2$d %3$s";
FRIENDS_LIST_ZONE = "区域：";
FRIENDS_TOOLTIP_TOO_MANY_CHARACTERS = "(%d个更多的角色)";
FRIENDS_TOOLTIP_WOW_TOON_TEMPLATE = "%1$s, %2$s %3$s %4$s";
FROM = "来自：";
FUEL = "燃料";
FULLDATE = "%1$s，%4$d年%2$s%3$d日";
FULLDATE_AND_TIME = "%1$s，%2$s";
FULLDATE_MONTH_APRIL = "4月";
FULLDATE_MONTH_AUGUST = "8月";
FULLDATE_MONTH_DECEMBER = "12月";
FULLDATE_MONTH_FEBRUARY = "2月";
FULLDATE_MONTH_JANUARY = "1月";
FULLDATE_MONTH_JULY = "7月";
FULLDATE_MONTH_JUNE = "6月";
FULLDATE_MONTH_MARCH = "3月";
FULLDATE_MONTH_MAY = "5月";
FULLDATE_MONTH_NOVEMBER = "11月";
FULLDATE_MONTH_OCTOBER = "10月";
FULLDATE_MONTH_SEPTEMBER = "9月";
FULL_SCREEN_GLOW = "全屏幕泛光效果";
FULL_SIZE_FOCUS_FRAME_TEXT = "扩大焦点框架";
FULL_TEXT_COMBATLOG_TOOLTIP = "整句显示战斗记录信息。";
GAIN_EXPERIENCE = "|cffffffff%d|r 经验值";
GAME = "游戏";
GAMEFIELD_DESELECT_TEXT = "目标锁定";
GAMEOPTIONS_MENU = "设置";
GAMES = "比赛";
GAMETIME_TOOLTIP_CALENDAR_INVITES = "你有未回复的日程邀请。";
GAMETIME_TOOLTIP_TOGGLE_CALENDAR = "点击这里显示日历。";
GAMETIME_TOOLTIP_TOGGLE_CLOCK = "点击这里显示时钟设置选项。";
GAME_SOUND_OUTPUT = "游戏声音输出";
GAME_VERSION_LABEL = "版本";
GAMMA = "Gamma";
GEARSETS_POPUP_TEXT = "输入方案名称（最多16个字符）：";
GEARSETS_TITLE = "装备管理";
GENERAL = "综合";
GENERAL_LABEL = "综合";
GENERAL_MACROS = "通用宏";
GENERAL_SPELLS = "综合";
GENERAL_SUBTEXT = "这些选项控制着你的显示硬件渲染游戏画面的尺寸和细节层次。";
GIVE_LOOT = "将尸体上的物品给予：";
GLANCING_TRAILER = "(偏斜)";
GLOBAL_CHANNELS = "通用频道";
GLYPHS = "雕文";
GLYPH_EMPTY = "空余";
GLYPH_EMPTY_DESC = "将你的物品栏中的雕文嵌入你的法术书中。";
GLYPH_FILLED = "已填充";
GLYPH_INACTIVE = "空";
GLYPH_LOCKED = "已锁定";
GLYPH_SLOT_REMOVE_TOOLTIP = "<按住Shift并右键点击以移除>";
GLYPH_SLOT_TOOLTIP1 = "达到15级后解锁。";
GLYPH_SLOT_TOOLTIP2 = "达到15级后解锁。";
GLYPH_SLOT_TOOLTIP3 = "达到50级后解锁。";
GLYPH_SLOT_TOOLTIP4 = "达到30级后解锁。";
GLYPH_SLOT_TOOLTIP5 = "达到70级后解锁。";
GLYPH_SLOT_TOOLTIP6 = "达到80级后解锁。";
GMSURVEYRATING1 = "极差";
GMSURVEYRATING2 = "较差";
GMSURVEYRATING3 = "普通";
GMSURVEYRATING4 = "优秀";
GMSURVEYRATING5 = "出色";
GMSURVEY_BLOCK_TEXT = "与我所经历过的其它客户服务相比：";
GMSURVEY_EXCELLENT = "5 (卓越)";
GMSURVEY_POOR = "1 (极差)";
GMSURVEY_REQUEST_TEXT = "请回答下面的问题：";
GMSURVEY_SUBMITTED = "你填写的问卷已经提交";
GMSURVEY_TITLE = "GM调查问卷";
GM_CHAT = "与GM交谈";
GM_CHAT_LAST_SESSION = "最后一次与你对话的GM是%s。";
GM_CHAT_OPEN = "打开GM交谈记录";
GM_CHAT_STATUS_READY = "GM交谈请求";
GM_CHAT_STATUS_READY_DESCRIPTION = "有一名GM想要与你交谈。请点击这里开始交谈。";
GM_EMAIL_NAME = "客户支持";
GM_RESPONSE_ALERT = "你收到了一条请求回复。点击这里进行阅读。";
GM_RESPONSE_FRAME_HEADER = "GM响应：";
GM_RESPONSE_ISSUE_HEADER = "问题：";
GM_RESPONSE_MESSAGE_HEADER = "GM回复：";
GM_RESPONSE_MORE_HELP = "需要更多帮助";
GM_RESPONSE_POPUP_MUST_RESOLVE_RESPONSE = "请先阅读当前GM回复。";
GM_RESPONSE_POPUP_NEED_MORE_HELP_WARNING = "如果你寻求更多帮助则无法再查看你的响应信息。你确定要新开一个跟进请求吗？";
GM_RESPONSE_POPUP_RESOLVE_CONFIRM = "如果标为已读则无法再查看你的响应信息。你确定要将此响应信息标为已读吗？";
GM_RESPONSE_POPUP_VIEW_RESPONSE = "查看回复";
GM_RESPONSE_RESOLVE = "已读";
GM_SURVEY_NOT_APPLICABLE = "N/A";
GM_TICKET_ESCALATED = "你的请求升级了。";
GM_TICKET_HIGH_VOLUME = "我们目前正在处理大量请求。";
GM_TICKET_SERVICE_SOON = "你的请求将很快得到答复。";
GM_TICKET_UNAVAILABLE = "等待时间不可预估。";
GM_TICKET_WAIT_TIME = "剩余时间：\n%s";
GOLD_AMOUNT = "%d金币";
GOLD_AMOUNT_SYMBOL = "金";
GOLD_AMOUNT_TEXTURE = "%d\124TInterface\\MoneyFrame\\UI-GoldIcon:%d:%d:2:0\124t";
GOLD_PER_DAY = "每日金币限额";
GOODBYE = "再见";
GOSSIP_OPTIONS = "闲谈选项";
GREED = "贪婪";
GREED_NEWBIE = "如果没人想获得该物品，你可以拿走该物品。";
GROUND_DENSITY = "地表景观密度";
GROUND_RADIUS = "地表景观范围";
GROUP = "小队";
GROUPS = "组队";
GROUP_INVITE = "组队邀请";
GUIDE = "向导";
GUIDE_TOOLTIP = "表示你拥有在地下城中战斗的经验，并愿意指导队伍攻克难关。";
GUILD = "公会";
GUILDADDRANK_BUTTON_TOOLTIP = "点击这里增加新会阶";
GUILDBANK_AVAILABLE_MONEY = "可用数量：";
GUILDBANK_BUYTAB_MONEY_FORMAT = "%s花费%s购买了一个公会银行标签";
GUILDBANK_DEPOSIT = "存放数量：";
GUILDBANK_DEPOSIT_FORMAT = "%s 存放了 %s";
GUILDBANK_DEPOSIT_MONEY_FORMAT = "%s 存放了 %s";
GUILDBANK_INFO_TITLE_FORMAT = "%s 信息";
GUILDBANK_LOG_QUANTITY = " x %d";
GUILDBANK_LOG_TITLE_FORMAT = "%s 记录";
GUILDBANK_MOVE_FORMAT = "%s将%sx%d从%s移动到了%s";
GUILDBANK_NAME_CONFIG = "%s配置：";
GUILDBANK_POPUP_TEXT = "输入公会银行标签名称：";
GUILDBANK_REMAINING_MONEY = "%s的每日提取额度剩余：|cffffffff%s|r";
GUILDBANK_REPAIR = "今日公会银行修理余额：";
GUILDBANK_REPAIR_MONEY_FORMAT = "%s 提取了 %s 用作修理";
GUILDBANK_TAB_COLON = "公会银行标签：";
GUILDBANK_TAB_DEPOSIT_ONLY = "只能存放";
GUILDBANK_TAB_FULL_ACCESS = "全部权限";
GUILDBANK_TAB_LOCKED = "锁定";
GUILDBANK_TAB_NUMBER = "标签%d";
GUILDBANK_TAB_WITHDRAW_ONLY = "只能提取";
GUILDBANK_WITHDRAW = "提取数量：";
GUILDBANK_WITHDRAWFORTAB_MONEY_FORMAT = "%s提取了%s以购买公会银行标签";
GUILDBANK_WITHDRAW_FORMAT = "%s |cffff2020提取了|r %s";
GUILDBANK_WITHDRAW_MONEY_FORMAT = "%s |cffff2020提取了|r %s";
GUILDCONTROL = "公会控制";
GUILDCONTROL_ALLOWRANK = "此会阶的权限：";
GUILDCONTROL_DEPOSIT_ITEMS = "存放物品";
GUILDCONTROL_OPTION1 = "听取公会聊天";
GUILDCONTROL_OPTION10 = "编辑公共信息";
GUILDCONTROL_OPTION11 = "查看官员信息";
GUILDCONTROL_OPTION12 = "编辑官员信息";
GUILDCONTROL_OPTION13 = "修改公会信息";
GUILDCONTROL_OPTION14 = "创建公会活动";
GUILDCONTROL_OPTION15 = "修理";
GUILDCONTROL_OPTION15_TOOLTIP = "使用公会资金修理";
GUILDCONTROL_OPTION16 = "金币";
GUILDCONTROL_OPTION16_TOOLTIP = "从公会银行提取金币";
GUILDCONTROL_OPTION17 = "设置公会活动";
GUILDCONTROL_OPTION2 = "公会聊天讲话";
GUILDCONTROL_OPTION3 = "听取官员聊天";
GUILDCONTROL_OPTION4 = "官员聊天讲话";
GUILDCONTROL_OPTION5 = "提升";
GUILDCONTROL_OPTION6 = "降职";
GUILDCONTROL_OPTION7 = "邀请成员";
GUILDCONTROL_OPTION8 = "移除成员";
GUILDCONTROL_OPTION9 = "设置今日信息";
GUILDCONTROL_RANKLABEL = "会阶名称：";
GUILDCONTROL_SELECTRANK = "选择要修改的会阶：";
GUILDCONTROL_UPDATE_TEXT = "更新标签文字";
GUILDCONTROL_VIEW_TAB = "浏览标签";
GUILDCONTROL_WITHDRAW_GOLD = "提取：";
GUILDCONTROL_WITHDRAW_ITEMS = "提取物品（组/天）";
GUILDEVENT_TYPE_DEMOTE = "%s将%s降级为%s";
GUILDEVENT_TYPE_INVITE = "%s邀请了%s";
GUILDEVENT_TYPE_JOIN = "%s加入了公会。";
GUILDEVENT_TYPE_PROMOTE = "%s将%s提升为%s";
GUILDEVENT_TYPE_QUIT = "%s离开了公会";
GUILDEVENT_TYPE_REMOVE = "%s将%s从公会中开除了";
GUILDMEMBER_ALERT = "公会成员提示";
GUILDMOTD_BUTTON_TOOLTIP = "点击这里查看/编辑本日公会信息。";
GUILDNOTE_BUTTON_TOOLTIP = "点击这里查看/编辑已选中玩家的公共信息。";
GUILDOFFICERNOTE_BUTTON_TOOLTIP = "点击这里查看/编辑已选中玩家的公会官员信息。";
GUILDREMOVERANK_BUTTON_TOOLTIP = "点击这里移除这个会阶";
GUILD_ACHIEVEMENT = "公会通告";
GUILD_BANK = "公会银行";
GUILD_BANK_LOG = "记录";
GUILD_BANK_LOG_TIME = "( %s以前 )";
GUILD_BANK_MONEY_LOG = "金币记录";
GUILD_BANK_TAB_INFO = "信息";
GUILD_CHARTER = "公会登记表";
GUILD_CHARTER_CREATOR = "公会首领：%s";
GUILD_CHARTER_PURCHASE = "购买公会申请表";
GUILD_CHARTER_REGISTER = "注册公会登记表";
GUILD_CHARTER_TEMPLATE = "%s公会登记表";
GUILD_CHARTER_TITLE = "公会登记表：%s";
GUILD_CHAT = "公会聊天";
GUILD_CREST_DESIGN = "设计公会战袍";
GUILD_EVENT_LOG = "记录";
GUILD_FRAME_TITLE = "公会名册";
GUILD_HELP_TEXT_LINE1 = "要创建一个公会，请在控制台中输入'guildcreate <公会名称>'";
GUILD_HELP_TEXT_LINE2 = "'/ginfo' 显示公会的基本信息";
GUILD_HELP_TEXT_LINE3 = "'/g <信息>' 向公会内的所有成员发送一条信息";
GUILD_HELP_TEXT_LINE4 = "'/o <信息>' 向公会中所有的管理人员发送一条信息";
GUILD_HELP_TEXT_LINE5 = "'/ginvite <玩家>' 邀请玩家加入你的公会";
GUILD_HELP_TEXT_LINE6 = "'/gremove <玩家>' 从公会中开除玩家";
GUILD_HELP_TEXT_LINE7 = "'/gpromote <玩家>' 提升一个玩家在公会内的会阶";
GUILD_HELP_TEXT_LINE8 = "'/gdemote <玩家>' 降低一个玩家在公会中的会阶";
GUILD_HELP_TEXT_LINE9 = "'/gmotd <信息>' 设置公会通知";
GUILD_HELP_TEXT_LINE10 = "'/gquit' removes you from your guild";
GUILD_HELP_TEXT_LINE11 = "'/groster' 列出公会成员名单";
GUILD_HELP_TEXT_LINE12 = "'/gleader <玩家>' 指定另一个玩家担任公会首领";
GUILD_HELP_TEXT_LINE13 = "'/gdisband' 解散公会";
GUILD_INFORMATION = "公会信息";
GUILD_INFO_EDITLABEL = "点击这里设置消息";
GUILD_INFO_TEMPLATE = "公会创立于%1$d-%2$d-%3$d，%4$d名成员，%5$d个账号";
GUILD_INVITATION = "%s邀请你加入公会：%s";
GUILD_LEAVE = "退出公会";
GUILD_MEMBER_OPTIONS = "会员设置";
GUILD_MEMBER_TEMPLATE = "%s，%s";
GUILD_MESSAGE = "公会聊天";
GUILD_MOTD = "公会今日信息";
GUILD_MOTD_EDITLABEL = "点击这里设置本日公会信息。";
GUILD_MOTD_LABEL = "今日公会信息：";
GUILD_MOTD_LABEL2 = "公会今日信息";
GUILD_MOTD_TEMPLATE = "今日公会信息：%s";
GUILD_NAME = "公会名称";
GUILD_NAME_TEMPLATE = "公会：%s";
GUILD_NOTES_LABEL = "玩家记录：";
GUILD_NOTE_EDITLABEL = "点击这里设置公共信息。";
GUILD_NOT_ALLIED_S = "%s不是你的盟友";
GUILD_OFFICERNOTES_LABEL = "公会官员信息";
GUILD_OFFICERNOTE_EDITLABEL = "点击这里设置公会官员信息。";
GUILD_OFFICER_NOTE = "公会官员注释";
GUILD_ONLINE_LABEL = "在线";
GUILD_PETITION_LEADER_INSTRUCTIONS = "选择一个你想要邀请的玩家然后点击<要求签名>。当你完成了所有签名之后将登记表交给公会注册者。";
GUILD_PETITION_MEMBER_INSTRUCTIONS = "点击<签署登记表>注册成为此公会的一员。";
GUILD_PROMOTE = "提升为会长";
GUILD_RANK0_DESC = "公会首领";
GUILD_RANK1_DESC = "公会官员";
GUILD_RANK2_DESC = "资深成员";
GUILD_RANK3_DESC = "普通成员";
GUILD_RANK4_DESC = "见习成员";
GUILD_REGISTRAR_PURCHASE_TEXT = "要创建一个公会，你必须购买一张公会登记表，在获得9个不同玩家的签名之后将其交还给我。请输入你想要成立的公会的名称。";
GUILD_ROSTER_TEMPLATE = "%d玩家，%d账号";
GUILD_STATUS = "公会状态";
GUILD_TEMPLATE = "%2$s - %1$s";
GUILD_TITLE_TEMPLATE = "%2$s - %1$s";
GUILD_TOTAL = "|cffffffff%d|r位公会成员";
GUILD_TOTALONLINE = "(|cffffffff%d|r |cff00ff00在线|r)";
HAIR_HORNS_COLOR = "犄角颜色";
HAIR_HORNS_STYLE = "犄角形状";
HAIR_NORMAL_COLOR = "发色";
HAIR_NORMAL_STYLE = "发型";
HANDSSLOT = "手";
HAPPINESS = "快乐";
HARASSMENT = "骚扰";
HARASSMENT_POLICY_TEXT = "想要了解更多关于骚扰的条例，请访问：\nhttp://www.warcraftchina.com/cs/policy/harassment";
HARASSMENT_TEXT = "请选择以下的选项：";
HARDWARE = "硬件";
HARDWARE_CURSOR = "硬件指针";
HARMFUL_AURA_COMBATLOG_TOOLTIP = "当你获得或失去有害光环时显示信息。";
HATRED = "仇恨";
HAVE_MAIL = "你有未阅读的邮件";
HAVE_MAIL_FROM = "未读邮件来自：";
HEADSLOT = "头部";
HEAD_BOB = "摆头";
HEALER = "治疗者";
HEALING_DONE_TOOLTIP = "施放的治疗总量。";
HEALS = "治疗";
HEALTH = "生命值";
HEALTH_COLON = "生命值：";
HEALTH_COST = "%d生命值";
HEALTH_COST_PER_TIME = "%d生命值，每秒增加%d";
HEALTH_LOW = "生命值过低";
HELPFRAME_ACCOUNT_BULLET1 = "创建账号出现问题";
HELPFRAME_ACCOUNT_BULLET2 = "关于支付方式的问题";
HELPFRAME_ACCOUNT_BULLET3 = "账号信息修改的问题";
HELPFRAME_ACCOUNT_BULLET4 = "付费选择";
HELPFRAME_ACCOUNT_BULLET_TITLE1 = "支付和账号服务和以下事务相关：";
HELPFRAME_ACCOUNT_BUTTON_TEXT = "报告账号/付费问题";
HELPFRAME_ACCOUNT_ENDTEXT = "想要联系任何此类问题，请联系支付和帐户服务：|n|n通过网站：|cffffd200http://www.warcraftchina.com/support|r|n电子邮件：|<EMAIL>|r|n|n我们还建议您访问账号管理页面：|n|n|cffffd200www.battlenet.com.cn|r |n|n在账号管理页面，您可以查看您的缴费信息、进行充卡和使用其他重要的账号功能。";
HELPFRAME_ACCOUNT_TEXT = "创建账号的时候遇到问题或者支付问题";
HELPFRAME_ACCOUNT_TITLE = "支付和账号服务";
HELPFRAME_BUG_BUTTON_DESCRIPTION = "提交游戏中的一个错误或者Bug";
HELPFRAME_BUG_BUTTON_TEXT = "提交一个Bug：";
HELPFRAME_CHARACTER_BULLET1 = "技能/属性缺失或者无法正常发挥效力";
HELPFRAME_CHARACTER_BULLET2 = "无法登录魔兽世界";
HELPFRAME_CHARACTER_BULLET3 = "技能等级显示为负";
HELPFRAME_CHARACTER_BULLET4 = "专业技能无法列出";
HELPFRAME_CHARACTER_BULLET5 = "天赋不正常工作或者出现缺失";
HELPFRAME_CHARACTER_BULLET_TITLE1 = "以下是角色相关问题的例子：";
HELPFRAME_CHARACTER_BUTTON_TEXT = "报告角色问题";
HELPFRAME_CHARACTER_TEXT = "关于技能，职业，声望和天赋的问题";
HELPFRAME_CHARACTER_TITLE = "角色";
HELPFRAME_ENVIRONMENTAL_BULLET1 = "无法使用一个熔炉";
HELPFRAME_ENVIRONMENTAL_BULLET2 = "能够走过/跳过一堵墙";
HELPFRAME_ENVIRONMENTAL_BULLET3 = "被传送到错误的墓地";
HELPFRAME_ENVIRONMENTAL_BULLET4 = "掉落穿越世界";
HELPFRAME_ENVIRONMENTAL_BULLET5 = "玩家死亡后希望被传送到原来的位置";
HELPFRAME_ENVIRONMENTAL_BULLET6 = "询问一个很难找到的地方";
HELPFRAME_ENVIRONMENTAL_BULLET_TITLE1 = "以下是环境相关问题的例子：";
HELPFRAME_ENVIRONMENTAL_BULLET_TITLE2 = "以下不被认为是环境问题：";
HELPFRAME_ENVIRONMENTAL_BUTTON_TEXT = "报告环境问题";
HELPFRAME_ENVIRONMENTAL_TEXT = "关于一个角色和游戏环境进行交互或者无法在游戏环境中实施动作的问题";
HELPFRAME_ENVIRONMENTAL_TITLE = "环境";
HELPFRAME_GENERAL_BUTTON_DESCRIPTION = "基本游戏性问题/信息";
HELPFRAME_GENERAL_BUTTON_TEXT = "综合性游戏问题：";
HELPFRAME_GMTALK_ISSUE1 = "所有任务、非玩家角色（NPC）、物品信息、地点信息或者任何有关游戏中的世界的信息，玩家都可以通过与世界或其他用户互动来获得。";
HELPFRAME_GMTALK_ISSUE1_HEADER = "游戏提示";
HELPFRAME_GMTALK_ISSUE2 = "包括下列信息：下一次补丁的内容、发布时间和发布方式，即将增加的内容，游戏性变动和未来的规则改动。";
HELPFRAME_GMTALK_ISSUE2_HEADER = "关于游戏的提示";
HELPFRAME_GMTALK_ISSUE3 = "大多数PvP问题都可以通过游戏内的机制解决。GM在大多数情况下不会介入此类问题，除非涉及到《魔兽世界》骚扰条款中列举的情况。有关PvP的特殊规则，请访问：";
HELPFRAME_GMTALK_ISSUE3_HEADER = "PvP";
HELPFRAME_GMTALK_TEXT1 = "GM一天24小时应该都能提供帮助。无论你现在使用的是什么角色，GM都可以帮助你。GM|cffffd200不会|r帮助你做的事情包括但|cffffd200不限于|r以下这些：";
HELPFRAME_GMTALK_TEXT2 = "另外，我们鼓励所有的玩家首先选择论坛和官方网站来寻找相关问题的解决信息，网址是|cffffd200http://www.warcraftchina.com/|r。并请仔细阅读我们的游戏条款，网址是：|cffffd200https://www.battlenet.com.cn/support/index.xml?locale=zh_CN&gameId=11&categoryId=3883&rootCategoryId=1749|r";
HELPFRAME_GMTALK_TITLE = "联系GM";
HELPFRAME_GM_BUTTON_DESCRIPTION = "联系一个GM来得到帮助";
HELPFRAME_GM_BUTTON_TEXT = "呼叫GM：";
HELPFRAME_GUILD_BULLET1 = "无法增加/移除公会会员";
HELPFRAME_GUILD_BULLET2 = "无法创建一个公会";
HELPFRAME_GUILD_BULLET3 = "无法重新设置和命名你在公会的等级";
HELPFRAME_GUILD_BULLET_TITLE1 = "以下是公会相关问题的例子：";
HELPFRAME_GUILD_BUTTON_TEXT = "报告公会问题";
HELPFRAME_GUILD_TEXT = "任何公会创建或者公会相关功能的问题";
HELPFRAME_GUILD_TITLE = "公会";
HELPFRAME_HARASSMENT_BUTTON_DESCRIPTION = "行为或者语言行为如果冒犯了其他玩家可以被归类在这个选项";
HELPFRAME_HARASSMENT_BUTTON_TEXT = "骚扰：";
HELPFRAME_HOME_TEXT = "请选择能够最好地描述你的帮助请求的选项：";
HELPFRAME_ITEM_BULLET1 = "崩溃之后物品损失";
HELPFRAME_ITEM_BULLET2 = "武器效果没有激活";
HELPFRAME_ITEM_BULLET3 = "附魔效果无法正常生效";
HELPFRAME_ITEM_BULLET4 = "其它附加效果无法正常生效";
HELPFRAME_ITEM_BULLET5 = "更多细节请访问官方网站";
HELPFRAME_ITEM_BULLET6 = "关于如何获得一件物品的询问";
HELPFRAME_ITEM_BULLET7 = "需要物品的请求";
HELPFRAME_ITEM_BULLET_TITLE1 = "以下是物品相关问题的例子：";
HELPFRAME_ITEM_BULLET_TITLE2 = "不包括以下类型的问题：";
HELPFRAME_ITEM_BUTTON_TEXT = "报告物品问题";
HELPFRAME_ITEM_TEXT = "关于任何物品功能性或者装备的问题。";
HELPFRAME_ITEM_TITLE = "物品";
HELPFRAME_LAG_TEXT1 = "通常导致延迟的原因是一个地区内的玩家数量过多，如大城市或战场。从你的计算机到游戏服务器的连接延迟过高也会导致这一问题。发送此报告将帮助我们侦测并有可能解决你所遇到的延迟问题。\n\n你现在遇到的是哪种延迟？请点击下方按钮，发送相关报告。";
HELPFRAME_LAG_TITLE = "报告延迟";
HELPFRAME_NONQUEST_BULLET1 = "野生生物一直保持躲避状态";
HELPFRAME_NONQUEST_BULLET2 = "NPC行走路径不正确";
HELPFRAME_NONQUEST_BULLET3 = "野生生物重生速度过快或者过慢";
HELPFRAME_NONQUEST_BULLET4 = "NPC商人没有物品出售或者没有反映";
HELPFRAME_NONQUEST_BULLET5 = "需要关于非任务NPC/野生怪物重生或者移除的请求";
HELPFRAME_NONQUEST_BULLET6 = "需要关于非任务NPC/野生怪物信息的请求";
HELPFRAME_NONQUEST_BULLET7 = "和任务相关的NPC或者怪物的问题";
HELPFRAME_NONQUEST_BULLET_TITLE1 = "以下是非任务NPC/怪物的例子：";
HELPFRAME_NONQUEST_BULLET_TITLE2 = "以下不被认为是非任务NPC/怪物的问题：";
HELPFRAME_NONQUEST_BUTTON_TEXT = "报告非任务NPC/野生怪物问题";
HELPFRAME_NONQUEST_TEXT = "与NPC和野生怪物交流的问题或者别的交流问题";
HELPFRAME_NONQUEST_TITLE = "非任务NPC/怪物";
HELPFRAME_OPENTICKET_EDITTEXT = "你现在的问题：";
HELPFRAME_OPENTICKET_FOLLOWUPTEXT = "描述你的跟进问题：";
HELPFRAME_OPENTICKET_TEXT = "描述你的问题：";
HELPFRAME_OTHER_BUTTON_DESCRIPTION = "任何需要GM协助的事件";
HELPFRAME_OTHER_BUTTON_TEXT = "其他事件：";
HELPFRAME_QUEST_BULLET1 = "任务相关NPC或者物体无法正常工作";
HELPFRAME_QUEST_BULLET2 = "无法获取任务相关物品";
HELPFRAME_QUEST_BULLET3 = "任务要求所杀的怪物不掉落任务所需要的物品";
HELPFRAME_QUEST_BULLET4 = "需要任务NPC/野生怪物信息的请求";
HELPFRAME_QUEST_BULLET5 = "需要关于简化一个任务的信息请求";
HELPFRAME_QUEST_BULLET_TITLE1 = "以下是任务/任务NPC的例子：";
HELPFRAME_QUEST_BULLET_TITLE2 = "以下不被认为是任务/任务NPC问题：";
HELPFRAME_QUEST_BUTTON_TEXT = "报告任务/NPC问题";
HELPFRAME_QUEST_TEXT = "关于无法实施或者开始或者完成任务的问题";
HELPFRAME_QUEST_TITLE = "任务/任务NPC";
HELPFRAME_REPORTISSUE_BULLET1 = "被其他玩家骚扰";
HELPFRAME_REPORTISSUE_BULLET2 = "图像问题之类的小错误";
HELPFRAME_REPORTISSUE_BULLET_TITLE1 = "主题包括：";
HELPFRAME_REPORTISSUE_TEXT1 = "请使用本页面报告在游戏中遇到的无需GM立刻处理的问题。";
HELPFRAME_REPORTISSUE_TEXT2 = "如需了解更多信息，我们会同您联系。同时，我们将尽全力解决该问题。";
HELPFRAME_REPORTISSUE_TITLE = "报告问题";
HELPFRAME_REPORTLAG_TEXT1 = "你的延迟报告已经成功提交。";
HELPFRAME_STUCK_BUTTON_DESCRIPTION = "在地图的某处卡住";
HELPFRAME_STUCK_BUTTON_TEXT = "卡死：";
HELPFRAME_STUCK_TEXT1 = "如果你被卡住而无法移动，请首先尝试自动脱离卡死功能，然后再联络GM寻求帮助。此功能在大部份的情况下都能帮助你解决困难。自动脱离卡死功能首先会尝试使用你的炉石将你送回绑定的旅店。若无法使用炉石，则它会尝试将你的角色从卡住的地点移开。\n\n请注意，这个功能每5分钟只能使用一次。使用自动脱离卡死功能将记录你的角色所在的位置，以便我们今后修正这个位置。";
HELPFRAME_STUCK_TITLE = "角色卡死";
HELPFRAME_SUGGESTION_BUTTON_DESCRIPTION = "所有关于这个游戏的综合性建议和反馈";
HELPFRAME_SUGGESTION_BUTTON_TEXT = "发送一个建议：";
HELPFRAME_TECHNICAL_BULLET1 = "游戏运行表现降低（游戏速度变慢或者出现停顿）";
HELPFRAME_TECHNICAL_BULLET2 = "显示错误（黑色方形物体，物体显示闪烁，显示扭曲等等）";
HELPFRAME_TECHNICAL_BULLET3 = "过场动画或者别的游戏动画无法正常播放";
HELPFRAME_TECHNICAL_BULLET4 = "声音问题（没有声音或者音乐，杂音，循环音效等等）";
HELPFRAME_TECHNICAL_BULLET5 = "连接/断线问题";
HELPFRAME_TECHNICAL_BULLET6 = "游戏中出现的崩溃或者错误信息";
HELPFRAME_TECHNICAL_BULLET7 = "任何其它的技术问题";
HELPFRAME_TECHNICAL_BULLET_TITLE1 = "以下是技术问题的例子：";
HELPFRAME_TECHNICAL_BULLET_TITLE2 = "您可以在技术支持论坛中找到各种常见问题的解决方法，请访问魔兽世界官方网站：\n\nwww.warcraftchina.com\n\n如果您的问题无法通过该方法找到解决方案的话，请和我们的技术支持部门联系：\n\n电话：(021)-61626168\n网页：http://www.warcraftchina.com/support\n电子邮件：<EMAIL>";
HELPFRAME_TECHNICAL_BUTTON_TEXT = "报告技术问题";
HELPFRAME_TECHNICAL_TEXT = "我们的技术团队会解决您在魔兽世界游戏中遇到的任何技术问题。";
HELPFRAME_TECHNICAL_TITLE = "技术支持";
HELPFRAME_WELCOME_TEXT1 = "如果您需要联系GM、与其沟通以获取在线帮助或问题解答，请使用|cffffd200联系GM|r按钮；如果您只是需要提交建议或意见而不需要GM回答，请使用|cffffd200报告问题|r按钮；如果您遇到了角色卡在某处无法脱离的问题，请使用|cffffd200角色卡死|r按钮。";
HELPFRAME_WELCOME_TITLE = "用户支持";
HELP_BUTTON = "帮助请求";
HELP_FRAME_TITLE = "用户帮助";
HELP_LABEL = "帮助";
HELP_SUBTEXT = "这些选项可以让你调整游戏中的帮助系统设置。";
HELP_TEXT_LINE1 = "魔兽世界帮助：";
HELP_TEXT_LINE2 = "- 'x' 坐下/起立";
HELP_TEXT_LINE3 = "- 组队/观察/交易：点击目标玩家，然后右键点击其头像";
HELP_TEXT_LINE4 = "- 'F1-F5' 选定自己/队友为目标";
HELP_TEXT_LINE5 = "- 'Shift-1 到 Shift-6' 切换动作条。Shift 上/下 和 Shift-鼠标滚轮也有同样功能。";
HELP_TEXT_LINE6 = "- 按下'Numlock'自动奔跑";
HELP_TEXT_LINE7 = "- 'z' 拿出/收起武器";
HELP_TEXT_LINE8 = "- 'v' 查看附近目标，也可用于选择目标";
HELP_TEXT_LINE9 = "- Tab 切换最近敌人为目标";
HELP_TEXT_LINE10 = "- '1' 或 't' 攻击当前目标（如无当前目标则攻击最近目标）";
HELP_TEXT_LINE11 = "- 'PageUp/PageDown' 翻动聊天记录";
HELP_TEXT_LINE12 = "- 'r' 或 '/r' 回复最后一个悄悄话";
HELP_TEXT_LINE13 = "- 输入/who 获取玩家列表";
HELP_TEXT_LINE14 = "- 输入/chat可以显示聊天命令列表";
HELP_TEXT_LINE15 = "- 输入/ghelp 获取公会命令列表";
HELP_TEXT_SIMPLE = "输入 '/help' 以获得命令列表";
HELP_TICKET_ABANDON = "取消请求";
HELP_TICKET_ABANDON_CONFIRM = "确定要取消当前的GM请求？";
HELP_TICKET_EDIT = "编辑请求";
HELP_TICKET_EDIT_ABANDON = "你现在已经有一个正在等待处理的请求了。请选择操作。";
HELP_TICKET_OPEN = "填写申请";
HELP_TICKET_QUEUE_DISABLED = "GM帮助请求暂时不可用。";
HEROIC_PREFIX = "英雄难度：%s";
HERTZ = "Hz";
HIDE = "隐藏";
HIDE_OUTDOOR_WORLD_STATE_TEXT = "隐藏区域目标追踪";
HIDE_PARTY_INTERFACE_TEXT = "隐藏小队界面";
HIDE_PULLOUT_BG = "隐藏背景";
HIGH = "高";
HIGHLIGHTING = "高亮显示：";
HIGHLIGHT_ABILITY_COMBATLOG_TOOLTIP = "在战斗信息中高亮显示技能名。";
HIGHLIGHT_DAMAGE_COMBATLOG_TOOLTIP = "在战斗信息中高亮显示伤害数值。";
HIGHLIGHT_KILL_COMBATLOG_TOOLTIP = "高亮显示你或你的队友杀死敌人时所显示的信息。";
HIGHLIGHT_SCHOOL_COMBATLOG_TOOLTIP = "在战斗信息中高亮显示法术的类型。";
HIGH_BIDDER = "最高出价";
HIT = "Hit";
HK = "荣誉击杀";
HOME = "Home";
HOME_INN = "你所处的旅店";
HONOR = "荣誉";
HONORABLE_KILLS = "荣誉击杀";
HONORABLE_KILLS_TOOLTIP = "在死亡之前被你（或你所在的队伍）击伤的敌方玩家";
HONOR_CONTRIBUTION_POINTS = "荣誉";
HONOR_ESTIMATED_TOOLTIP = "本日荣誉获取";
HONOR_GAINED = "获得荣誉";
HONOR_GAINED_TOOLTIP = "获取的荣誉总值。";
HONOR_HIGHEST_RANK = "最高级别";
HONOR_LASTWEEK = "上周";
HONOR_LIFETIME = "总计";
HONOR_POINTS = "荣誉点数";
HONOR_STANDING = "排名";
HONOR_THIS_SESSION = "今日";
HONOR_TODAY = "本日";
HONOR_YESTERDAY = "昨天";
HOSTILE = "敌对";
HOURS = "|4小时:小时;";
HOURS_ABBR = "%d|4小时:小时;";
HOUR_ONELETTER_ABBR = "%d h";
HP = "生命值";
HP_TEMPLATE = "%d生命值";
HUNTER_AGILITY_TOOLTIP = "提高你的近战武器和远程武器的攻击强度，并提高所有武器的爆击几率。|n提高你的护甲值和躲避攻击的几率。";
HUNTER_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
ICON_TAG_RAID_TARGET_CIRCLE1 = "rt2";
ICON_TAG_RAID_TARGET_CIRCLE2 = "rt2";
ICON_TAG_RAID_TARGET_CROSS1 = "rt7";
ICON_TAG_RAID_TARGET_CROSS2 = "X";
ICON_TAG_RAID_TARGET_DIAMOND1 = "rt3";
ICON_TAG_RAID_TARGET_DIAMOND2 = "rt3";
ICON_TAG_RAID_TARGET_MOON1 = "rt5";
ICON_TAG_RAID_TARGET_MOON2 = "rt5";
ICON_TAG_RAID_TARGET_SKULL1 = "rt8";
ICON_TAG_RAID_TARGET_SKULL2 = "rt8";
ICON_TAG_RAID_TARGET_SQUARE1 = "rt6";
ICON_TAG_RAID_TARGET_SQUARE2 = "rt6";
ICON_TAG_RAID_TARGET_STAR1 = "rt1";
ICON_TAG_RAID_TARGET_STAR2 = "rt1";
ICON_TAG_RAID_TARGET_TRIANGLE1 = "rt4";
ICON_TAG_RAID_TARGET_TRIANGLE2 = "rt4";
ID = "ID";
IDLE_MESSAGE = "因长时间没有动作，将自动退出游戏。如果你希望继续游戏，请点击取消键。";
IGNORE = "屏蔽";
IGNORED = "已屏蔽";
IGNORE_DIALOG = "忽略";
IGNORE_ERRORS = "忽略";
IGNORE_LIST = "屏蔽列表";
IGNORE_PLAYER = "屏蔽玩家";
IGR_BILLING_NAG_DIALOG = "你的IGR游戏时间即将用尽，你很快会被断开连接。";
IMMUNE = "免疫";
IMPORTANT_PEOPLE_IN_GROUP = "队伍中的玩家：";
IM_STYLE = "即时通讯风格";
INBOX = "收件箱";
INBOX_TOO_MUCH_MAIL = "你的收件箱已满。";
INBOX_TOO_MUCH_MAIL_TOOLTIP = "无法显示全部邮件。|n请删除部分邮件以释放邮箱空间。";
INCOMPLETE = "未完成";
INCREASE_POTENTIAL = "将|cffffffff%s|r的上限提升|cffffffff%d|r点";
INDIVIDUALS = "个人";
INPUT_CHINESE = "中";
INPUT_JAPANESE = "日";
INPUT_KOREAN = "韩";
INPUT_ROMAN = "英";
INSCRIPTION = "铭文";
INSPECT = "观察";
INSPECT_NOTIFY = "%s正在观察你。";
INSTANCE = "副本";
INSTANCE_BOOT_TIMER = "你现在不在这个副本的队伍里。你将在%d%s内被传送到最近的墓地中。";
INSTANCE_DIFFICULTY_FORMAT = "（%s）";
INSTANCE_ID = "副本ID：%d";
INSTANCE_LEAVE = "离开副本";
INSTANCE_LOCK_SEPARATOR = "%s|n|n%s";
INSTANCE_LOCK_TIMER = "你进入了一个已经保存进度的副本！你将在%2$s内被保存到%1$s的副本进度中！";
INSTANCE_LOCK_TIMER_PREVIOUSLY_SAVED = "你进入了一个|cffffd200已延长|r的副本。你将在%2$s!内被保存到%1$s的副本进度中！";
INSTANCE_RESET_FAILED = "无法重置%s，该副本中仍有玩家。";
INSTANCE_RESET_FAILED_OFFLINE = "无法重置%s，你的队伍中有人离线。";
INSTANCE_RESET_FAILED_ZONING = "无法重置%s，你的队伍中有人正在试图进入某个副本。";
INSTANCE_RESET_SUCCESS = "%s 已被重置。";
INSTANCE_SAVED = "你在此副本中的进度已保存";
INSTANCE_SHUTDOWN_MESSAGE = "没有足够的玩家。服务器将在%s内关闭";
INSTANCE_UNAVAILABLE_OTHER_EXPANSION_TOO_LOW = "%s没有安装正确的《魔兽世界》资料片。";
INSTANCE_UNAVAILABLE_OTHER_GEAR_TOO_HIGH = "%s的装备太强了。";
INSTANCE_UNAVAILABLE_OTHER_GEAR_TOO_LOW = "%s必须有更好的装备。";
INSTANCE_UNAVAILABLE_OTHER_LEVEL_TOO_HIGH = "%s的级别太高了。";
INSTANCE_UNAVAILABLE_OTHER_LEVEL_TOO_LOW = "%s的级别不够。";
INSTANCE_UNAVAILABLE_OTHER_MISSING_ITEM = "%s没有所需的物品。";
INSTANCE_UNAVAILABLE_OTHER_OTHER = "%s没有满足进入该地下城的要求。";
INSTANCE_UNAVAILABLE_OTHER_QUEST_NOT_COMPLETED = "%s没有完成所需的任务。";
INSTANCE_UNAVAILABLE_OTHER_RAID_LOCKED = "%s已与该副本锁定。";
INSTANCE_UNAVAILABLE_SELF_EXPANSION_TOO_LOW = "你没有安装正确的《魔兽世界》资料片。";
INSTANCE_UNAVAILABLE_SELF_GEAR_TOO_HIGH = "你的装备太强了。";
INSTANCE_UNAVAILABLE_SELF_GEAR_TOO_LOW = "你必须有更好的装备。";
INSTANCE_UNAVAILABLE_SELF_LEVEL_TOO_HIGH = "你的级别太高了。";
INSTANCE_UNAVAILABLE_SELF_LEVEL_TOO_LOW = "你的级别不够。";
INSTANCE_UNAVAILABLE_SELF_MISSING_ITEM = "你没有所需的物品。";
INSTANCE_UNAVAILABLE_SELF_OTHER = "你的级别没有达到该地下城的要求。";
INSTANCE_UNAVAILABLE_SELF_QUEST_NOT_COMPLETED = "你没有完成所需的任务。";
INSTANCE_UNAVAILABLE_SELF_RAID_LOCKED = "你已与该副本锁定。";
INT = "Int";
INTELLECT_COLON = "智力：";
INTELLECT_TOOLTIP = "提高法力值上限，并且提高法术的爆击\n几率。智力还能让你更快地提升\n武器技能。（但不会影响专业技能的\n提升速度。）";
INTERFACE_ACTION_BLOCKED = "插件导致界面行为失效";
INTERFACE_OPTIONS = "界面选项";
INTERNAL_STRING_ERROR = "内部命令行错误%d";
INTERRUPT = "中断";
INTERRUPTED = "被打断";
INTERRUPTS = "打断";
INT_GENERAL_DURATION_DAYS = "%d天";
INT_GENERAL_DURATION_HOURS = "%d小时";
INT_GENERAL_DURATION_MIN = "%d分钟";
INT_GENERAL_DURATION_SEC = "%d秒";
INT_SPELL_DURATION_DAYS = "%d天";
INT_SPELL_DURATION_HOURS = "%d小时";
INT_SPELL_DURATION_MIN = "%d分钟";
INT_SPELL_DURATION_SEC = "%d秒";
INT_SPELL_POINTS_SPREAD_TEMPLATE = "%d到%d";
INVENTORY_FULL = "物品栏已满。";
INVENTORY_TOOLTIP = "物品栏";
INVERT_MOUSE = "反转鼠标";
INVITATION = "%s邀请你加入队伍";
INVITE = "邀请";
INVITE_CONVERSATION_INSTRUCTIONS = "选择最多|cffffffff%d|r名好友加入本次对话。";
INVITE_FRIEND_TO_CONVERSATION = "邀请好友";
INVITE_TO_CONVERSATION = "邀请加入对话";
INVTYPE_2HWEAPON = "双手";
INVTYPE_AMMO = "弹药";
INVTYPE_BAG = "背包";
INVTYPE_BODY = "衬衣";
INVTYPE_CHEST = "胸部";
INVTYPE_CLOAK = "背部";
INVTYPE_FEET = "脚";
INVTYPE_FINGER = "手指";
INVTYPE_HAND = "手";
INVTYPE_HEAD = "头部";
INVTYPE_HOLDABLE = "副手物品";
INVTYPE_LEGS = "腿部";
INVTYPE_NECK = "颈部";
INVTYPE_QUIVER = "箭袋";
INVTYPE_RANGED = "远程";
INVTYPE_RANGEDRIGHT = "远程";
INVTYPE_RELIC = "圣物";
INVTYPE_ROBE = "胸部";
INVTYPE_SHIELD = "副手";
INVTYPE_SHOULDER = "肩部";
INVTYPE_TABARD = "战袍";
INVTYPE_THROWN = "投掷";
INVTYPE_TRINKET = "饰品";
INVTYPE_WAIST = "腰部";
INVTYPE_WEAPON = "单手";
INVTYPE_WEAPONMAINHAND = "主手";
INVTYPE_WEAPONMAINHAND_PET = "主要攻击";
INVTYPE_WEAPONOFFHAND = "副手";
INVTYPE_WRIST = "手腕";
ITEMPRESENTINOFFHAND = "不能装备，因为你的副手已持有物品！";
ITEMS = "物品";
ITEMSLOTTEXT = "物品栏位";
ITEMS_EQUIPPED = "已装备%d件物品";
ITEMS_IN_INVENTORY = "背包中有%d件物品";
ITEMS_NOT_IN_INVENTORY = "缺少%d件物品";
ITEMS_VARIABLE_QUANTITY = "%d件物品";
ITEM_ACCOUNTBOUND = "账号绑定";
ITEM_BIND_ON_EQUIP = "装备后绑定";
ITEM_BIND_ON_PICKUP = "拾取后绑定";
ITEM_BIND_ON_USE = "使用后绑定";
ITEM_BIND_QUEST = "任务物品";
ITEM_BIND_TO_ACCOUNT = "账号绑定";
ITEM_CANT_BE_DESTROYED = "这件物品无法被摧毁。";
ITEM_CLASSES_ALLOWED = "职业：%s";
ITEM_CONJURED = "魔法制造的物品";
ITEM_COOLDOWN_TIME = "冷却时间剩余：%s";
ITEM_COOLDOWN_TIME_DAYS = "剩余冷却时间：%d天";
ITEM_COOLDOWN_TIME_HOURS = "剩余冷却时间：%d小时";
ITEM_COOLDOWN_TIME_MIN = "冷却时间剩余：%d 分钟";
ITEM_COOLDOWN_TIME_SEC = "冷却时间剩余：%d秒";
ITEM_COOLDOWN_TOTAL = "（%s冷却）";
ITEM_COOLDOWN_TOTAL_DAYS = "(%d天冷却时间)";
ITEM_COOLDOWN_TOTAL_HOURS = "(%d小时冷却时间)";
ITEM_COOLDOWN_TOTAL_MIN = "(%d分钟冷却时间)";
ITEM_COOLDOWN_TOTAL_SEC = "(%d秒冷却时间)";
ITEM_CREATED_BY = "|cff00ff00<由%s制造>|r";
ITEM_DELTA_DESCRIPTION = "如果你替换该物品，将会产生以下的属性变更：";
ITEM_DISENCHANT_ANY_SKILL = "可分解";
ITEM_DISENCHANT_MIN_SKILL = "分解需要%s (%d)";
ITEM_DISENCHANT_NOT_DISENCHANTABLE = "无法分解";
ITEM_DURATION_DAYS = "持续时间：%d天";
ITEM_DURATION_HOURS = "持续时间：%d小时";
ITEM_DURATION_MIN = "持续时间：%d分钟";
ITEM_DURATION_SEC = "持续时间：%d秒";
ITEM_ENCHANT_DISCLAIMER = "物品将不会被交易！";
ITEM_ENCHANT_TIME_LEFT_DAYS = "%s（%d天）";
ITEM_ENCHANT_TIME_LEFT_HOURS = "%s（%d小时）";
ITEM_ENCHANT_TIME_LEFT_MIN = "%s（%d分钟）";
ITEM_ENCHANT_TIME_LEFT_SEC = "%s（%d秒）";
ITEM_HEROIC = "英雄级别";
ITEM_HEROIC_EPIC = "英雄级别史诗品质";
ITEM_LEVEL = "物品等级%d";
ITEM_LEVEL_AND_MIN = "等级 %d （最小 %d）";
ITEM_LEVEL_RANGE = "需要等级%d到%d";
ITEM_LEVEL_RANGE_CURRENT = "需要等级 %d到%d （%d）";
ITEM_LIMIT_CATEGORY = "唯一：%s（%d）";
ITEM_LIMIT_CATEGORY_MULTIPLE = "装备唯一：%s （%d）";
ITEM_LOOT = "物品拾取";
ITEM_MILLABLE = "可研磨";
ITEM_MIN_LEVEL = "需要等级 %d";
ITEM_MIN_SKILL = "需要%s（%d）";
ITEM_MISSING = "%s缺失";
ITEM_MOD_AGILITY = "%c%d 敏捷";
ITEM_MOD_AGILITY_SHORT = "敏捷";
ITEM_MOD_ARMOR_PENETRATION_RATING = "使你的护甲穿透等级提高%d。";
ITEM_MOD_ARMOR_PENETRATION_RATING_SHORT = "护甲穿透等级";
ITEM_MOD_ATTACK_POWER = "攻击强度提高%d点。";
ITEM_MOD_ATTACK_POWER_SHORT = "攻击强度";
ITEM_MOD_BLOCK_RATING = "使你的盾牌格挡等级提高%d。";
ITEM_MOD_BLOCK_RATING_SHORT = "格挡等级";
ITEM_MOD_BLOCK_VALUE = "使你的盾牌格挡值提高%d。";
ITEM_MOD_BLOCK_VALUE_SHORT = "格挡值";
ITEM_MOD_CRIT_MELEE_RATING = "近战爆击等级提高%d。";
ITEM_MOD_CRIT_MELEE_RATING_SHORT = "爆击等级（近战）";
ITEM_MOD_CRIT_RANGED_RATING = "远程爆击等级提高%d。";
ITEM_MOD_CRIT_RANGED_RATING_SHORT = "爆击等级（远程）";
ITEM_MOD_CRIT_RATING = "爆击等级提高%d。";
ITEM_MOD_CRIT_RATING_SHORT = "爆击等级";
ITEM_MOD_CRIT_SPELL_RATING = "法术爆击等级提高%d。";
ITEM_MOD_CRIT_SPELL_RATING_SHORT = "爆击等级（法术）";
ITEM_MOD_CRIT_TAKEN_MELEE_RATING = "近战爆击躲闪等级提高%d。";
ITEM_MOD_CRIT_TAKEN_MELEE_RATING_SHORT = "爆击躲闪等级（近战）";
ITEM_MOD_CRIT_TAKEN_RANGED_RATING = "远程爆击躲闪等级提高%d。";
ITEM_MOD_CRIT_TAKEN_RANGED_RATING_SHORT = "爆击躲闪等级（远程）";
ITEM_MOD_CRIT_TAKEN_RATING = "爆击躲闪等级提高%d。";
ITEM_MOD_CRIT_TAKEN_RATING_SHORT = "爆击躲闪等级";
ITEM_MOD_CRIT_TAKEN_SPELL_RATING = "法术爆击躲闪等级提高%d。";
ITEM_MOD_CRIT_TAKEN_SPELL_RATING_SHORT = "爆击躲闪等级（法术）";
ITEM_MOD_DAMAGE_PER_SECOND_SHORT = "每秒伤害";
ITEM_MOD_DEFENSE_SKILL_RATING = "防御等级提高%d。";
ITEM_MOD_DEFENSE_SKILL_RATING_SHORT = "防御等级";
ITEM_MOD_DODGE_RATING = "使你的躲闪等级提高%d。";
ITEM_MOD_DODGE_RATING_SHORT = "躲闪等级";
ITEM_MOD_EXPERTISE_RATING = "使你的精准等级提高%d。";
ITEM_MOD_EXPERTISE_RATING_SHORT = "精准等级";
ITEM_MOD_FERAL_ATTACK_POWER = "在猎豹、熊、巨熊和枭兽形态下的攻击强度提高%d点。";
ITEM_MOD_FERAL_ATTACK_POWER_SHORT = "变形形态下的攻击强度";
ITEM_MOD_HASTE_MELEE_RATING = "近战急速等级提高%d。";
ITEM_MOD_HASTE_MELEE_RATING_SHORT = "急速等级（近战）";
ITEM_MOD_HASTE_RANGED_RATING = "远程急速等级提高%d。";
ITEM_MOD_HASTE_RANGED_RATING_SHORT = "急速等级（远程）";
ITEM_MOD_HASTE_RATING = "急速等级提高%d。";
ITEM_MOD_HASTE_RATING_SHORT = "急速等级";
ITEM_MOD_HASTE_SPELL_RATING = "法术急速等级提高%d。";
ITEM_MOD_HASTE_SPELL_RATING_SHORT = "急速等级（法术）";
ITEM_MOD_HEALTH = "%c%d 生命值";
ITEM_MOD_HEALTH_REGEN = "每5秒恢复%d点生命值。";
ITEM_MOD_HEALTH_REGENERATION = "每5秒恢复%d点生命值。";
ITEM_MOD_HEALTH_REGENERATION_SHORT = "生命值恢复";
ITEM_MOD_HEALTH_REGEN_SHORT = "每5秒的生命值恢复";
ITEM_MOD_HEALTH_SHORT = "生命值";
ITEM_MOD_HIT_MELEE_RATING = "近战命中等级提高%d。";
ITEM_MOD_HIT_MELEE_RATING_SHORT = "命中等级（近战）";
ITEM_MOD_HIT_RANGED_RATING = "远程命中等级提高%d。";
ITEM_MOD_HIT_RANGED_RATING_SHORT = "命中等级（远程）";
ITEM_MOD_HIT_RATING = "命中等级提高%d。";
ITEM_MOD_HIT_RATING_SHORT = "命中等级";
ITEM_MOD_HIT_SPELL_RATING = "法术命中等级提高%d。";
ITEM_MOD_HIT_SPELL_RATING_SHORT = "命中等级（法术）";
ITEM_MOD_HIT_TAKEN_MELEE_RATING = "近战命中躲闪等级提高%d。";
ITEM_MOD_HIT_TAKEN_MELEE_RATING_SHORT = "命中躲闪等级（近战）";
ITEM_MOD_HIT_TAKEN_RANGED_RATING = "远程命中躲闪等级提高%d。";
ITEM_MOD_HIT_TAKEN_RANGED_RATING_SHORT = "命中躲闪等级（远程）";
ITEM_MOD_HIT_TAKEN_RATING = "命中躲闪等级提高%d。";
ITEM_MOD_HIT_TAKEN_RATING_SHORT = "命中躲闪等级";
ITEM_MOD_HIT_TAKEN_SPELL_RATING = "法术命中躲闪等级提高%d。";
ITEM_MOD_HIT_TAKEN_SPELL_RATING_SHORT = "命中躲闪等级（法术）";
ITEM_MOD_INTELLECT = "%c%d 智力";
ITEM_MOD_INTELLECT_SHORT = "智力";
ITEM_MOD_MANA = "%c%d 法力值";
ITEM_MOD_MANA_REGENERATION = "每5秒回复%d点法力值。";
ITEM_MOD_MANA_REGENERATION_SHORT = "法力回复";
ITEM_MOD_MANA_SHORT = "法力值";
ITEM_MOD_MELEE_ATTACK_POWER_SHORT = "近战攻击强度";
ITEM_MOD_PARRY_RATING = "使你的招架等级提高%d。";
ITEM_MOD_PARRY_RATING_SHORT = "招架等级";
ITEM_MOD_POWER_REGEN0_SHORT = "每5秒的法力值恢复";
ITEM_MOD_POWER_REGEN1_SHORT = "每5秒的怒气增长";
ITEM_MOD_POWER_REGEN2_SHORT = "每5秒的专注获得";
ITEM_MOD_POWER_REGEN3_SHORT = "每5秒的能量恢复";
ITEM_MOD_POWER_REGEN4_SHORT = "每5秒的快乐值获得";
ITEM_MOD_POWER_REGEN5_SHORT = "每5秒的符文恢复";
ITEM_MOD_POWER_REGEN6_SHORT = "每5秒的符文能量恢复";
ITEM_MOD_RANGED_ATTACK_POWER = "远程攻击强度提高%d点。";
ITEM_MOD_RANGED_ATTACK_POWER_SHORT = "远程攻击强度";
ITEM_MOD_RESILIENCE_RATING = "韧性等级提高%d。";
ITEM_MOD_RESILIENCE_RATING_SHORT = "韧性等级";
ITEM_MOD_SPELL_DAMAGE_DONE = "法术和魔法效果的伤害量提高最多%d点。";
ITEM_MOD_SPELL_DAMAGE_DONE_SHORT = "伤害加成";
ITEM_MOD_SPELL_HEALING_DONE = "法术和魔法效果的治疗量提高最多%d点。";
ITEM_MOD_SPELL_HEALING_DONE_SHORT = "治疗加成";
ITEM_MOD_SPELL_PENETRATION = "法术穿透提高%d。";
ITEM_MOD_SPELL_PENETRATION_SHORT = "法术穿透";
ITEM_MOD_SPELL_POWER = "法术强度提高%d点。";
ITEM_MOD_SPELL_POWER_SHORT = "法术强度";
ITEM_MOD_SPIRIT = "%c%d 精神";
ITEM_MOD_SPIRIT_SHORT = "精神";
ITEM_MOD_STAMINA = "%c%d 耐力";
ITEM_MOD_STAMINA_SHORT = "耐力";
ITEM_MOD_STRENGTH = "%c%d 力量";
ITEM_MOD_STRENGTH_SHORT = "力量";
ITEM_MOUSE_OVER = "将鼠标移动到图标上可以获得更多的信息";
ITEM_NAMES = "物品名";
ITEM_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP = "在物品名称外显示括号。";
ITEM_NO_DROP = "无法丢弃";
ITEM_OPENABLE = "<右键点击打开>";
ITEM_PROPOSED_ENCHANT = "将获得%s的效果。";
ITEM_PROSPECTABLE = "可选矿";
ITEM_PURCHASED_COLON = "物品购入：";
ITEM_QUALITY0_DESC = "粗糙";
ITEM_QUALITY1_DESC = "普通";
ITEM_QUALITY2_DESC = "优秀";
ITEM_QUALITY3_DESC = "精良";
ITEM_QUALITY4_DESC = "史诗";
ITEM_QUALITY5_DESC = "传说";
ITEM_QUALITY6_DESC = "神器";
ITEM_QUALITY7_DESC = "传家宝";
ITEM_QUANTITY_TEMPLATE = "%d %s";
ITEM_RACES_ALLOWED = "种族：%s";
ITEM_RANDOM_ENCHANT = "<随机附魔>";
ITEM_READABLE = "<右键点击阅读>";
ITEM_REFUND_MSG = "物品已退还。获得退款：";
ITEM_REQ_ARENA_RATING = "需要个人竞技场等级和战队竞技场等级达到%d";
ITEM_REQ_ARENA_RATING_3V3 = "需要3v3或5v5的个人竞技场等级和战队竞技场等级达到%d|n";
ITEM_REQ_ARENA_RATING_5V5 = "需要5v5的个人竞技场等级和战队竞技场等级达到%d|n";
ITEM_REQ_PURCHASE_GROUP = "需要 %s";
ITEM_REQ_REPUTATION = "需要 %s - %s";
ITEM_REQ_SKILL = "需要%s";
ITEM_RESIST_ALL = "%c%d 所有抗性";
ITEM_RESIST_SINGLE = "%c%d %s抗性";
ITEM_SET_BONUS = "套装：%s";
ITEM_SET_BONUS_GRAY = "(%d) 套装：%s";
ITEM_SET_NAME = "%s（%d/%d）";
ITEM_SIGNABLE = "<右键点击以了解详情>";
ITEM_SLOTS_IGNORED = "略过%d个插槽";
ITEM_SOCKETABLE = "<Shift+右键点击打开镶嵌界面>";
ITEM_SOCKETING = "物品镶嵌";
ITEM_SOCKET_BONUS = "镶孔奖励：%s";
ITEM_SOLD_COLON = "物品售出：";
ITEM_SOULBOUND = "已绑定";
ITEM_SPELL_CHARGES = "%d次";
ITEM_SPELL_CHARGES_NONE = "耗尽次数";
ITEM_SPELL_EFFECT = "效果：%s";
ITEM_SPELL_KNOWN = "已经学会";
ITEM_SPELL_TRIGGER_ONEQUIP = "装备：";
ITEM_SPELL_TRIGGER_ONPROC = "击中时可能：";
ITEM_SPELL_TRIGGER_ONUSE = "使用：";
ITEM_STARTS_QUEST = "该物品将触发一个任务";
ITEM_SUFFIX_TEMPLATE = "%2$s%1$s";
ITEM_TEXT_FROM = "发信人，";
ITEM_UNIQUE = "唯一";
ITEM_UNIQUE_EQUIPPABLE = "装备唯一";
ITEM_UNIQUE_MULTIPLE = "唯一（%d）";
ITEM_UNSELLABLE = "无法出售";
ITEM_WRAPPED_BY = "|cff00ff00<%s的礼物>|r";
ITEM_WRITTEN_BY = "由%s撰写";
ITEM_WRONG_CLASS = "你的职业无法使用这件物品！";
ITEM_WRONG_RACE = "你的种族无法使用这件物品！";
ITUNES_SHOW_ALL_TRACK_CHANGES = "显示所有iTunes音轨改变";
ITUNES_SHOW_ALL_TRACK_CHANGES_TOOLTIP = "当iTunes切换播放音轨时，自动在弹出窗口中显示音轨名。";
ITUNES_SHOW_FEEDBACK = "显示iTunes控制器反馈";
ITUNES_SHOW_FEEDBACK_TOOLTIP = "当你使用iTunes控制器切换音轨时，在弹出窗口中显示音轨名。";
JOIN = "加入";
JOINED_PARTY = "%s加入了队伍。";
JOIN_AS_GROUP = "小队加入";
JOIN_AS_GROUP_TOOLTIP = "小队的队长可以选择将自己的整个小队加入战场等待序列。你的小队成员将同时进入战场。";
JOIN_AS_PARTY = "小队加入";
JOIN_NEW_CHANNEL = "加入新频道";
KBASE_ARTICLE_COUNT = "第%d至%d篇（总共%d篇）";
KBASE_ARTICLE_ID = "文章编号：%d";
KBASE_CHARSTUCK = "角色卡死";
KBASE_DEFAULT_SEARCH_TEXT = "在这里输入关键字。";
KBASE_ERROR_LOAD_FAILURE = "知识库目前不可用。请访问http://us.blizzard.com/support/index.xml?gameId=11以寻求支援方面的帮助，或者使用下面的按钮寻求在线帮助。";
KBASE_ERROR_NO_RESULTS = "没有符合你所指定的搜索条件的文章。";
KBASE_GMTALK = "联系GM";
KBASE_HOT_ISSUE = "热门问题";
KBASE_LAG = "报告延迟";
KBASE_RECENTLY_UPDATED = "最近更新";
KBASE_REPORTISSUE = "报告问题";
KBASE_SEARCH_RESULTS = "搜索结果";
KBASE_TOP_ISSUES = "重要问题";
KEY1 = "按键设置1";
KEY2 = "按键设置2";
KEYBINDINGFRAME_MOUSEWHEEL_ERROR = "无法将鼠标滚轮的上下滚动状态绑定在动作条上";
KEYRING = "钥匙链";
KEY_APOSTROPHE = "'";
KEY_BACKSLASH = "\\";
KEY_BACKSPACE = "退格";
KEY_BACKSPACE_MAC = "删除";
KEY_BINDING = "按键设置";
KEY_BINDINGS = "按键设置";
KEY_BINDINGS_MAC = "绑定";
KEY_BOUND = "按键设置成功";
KEY_BUTTON1 = "鼠标左键";
KEY_BUTTON10 = "鼠标按键10";
KEY_BUTTON11 = "鼠标按键11";
KEY_BUTTON12 = "鼠标按键12";
KEY_BUTTON13 = "鼠标按键13";
KEY_BUTTON14 = "鼠标按键14";
KEY_BUTTON15 = "鼠标按键15";
KEY_BUTTON16 = "鼠标按键16";
KEY_BUTTON17 = "鼠标按键17";
KEY_BUTTON18 = "鼠标按键18";
KEY_BUTTON19 = "鼠标按键19";
KEY_BUTTON2 = "鼠标右键";
KEY_BUTTON20 = "鼠标按键20";
KEY_BUTTON21 = "鼠标按键21";
KEY_BUTTON22 = "鼠标按键22";
KEY_BUTTON23 = "鼠标按键23";
KEY_BUTTON24 = "鼠标按键24";
KEY_BUTTON25 = "鼠标按键25";
KEY_BUTTON26 = "鼠标按键26";
KEY_BUTTON27 = "鼠标按键27";
KEY_BUTTON28 = "鼠标按键28";
KEY_BUTTON29 = "鼠标按键29";
KEY_BUTTON3 = "鼠标中键";
KEY_BUTTON30 = "鼠标按键30";
KEY_BUTTON31 = "鼠标按键31";
KEY_BUTTON4 = "鼠标按键4";
KEY_BUTTON5 = "鼠标按键5";
KEY_BUTTON6 = "鼠标按键6";
KEY_BUTTON7 = "鼠标按键7";
KEY_BUTTON8 = "鼠标按键8";
KEY_BUTTON9 = "鼠标按键9";
KEY_COMMA = ",";
KEY_DELETE = "Delete";
KEY_DELETE_MAC = "Del";
KEY_DOWN = "方向键下";
KEY_END = "End";
KEY_ENTER = "回车";
KEY_ENTER_MAC = "返回";
KEY_ESCAPE = "Escape";
KEY_HOME = "Home";
KEY_INSERT = "Insert";
KEY_INSERT_MAC = "帮助";
KEY_LEFT = "方向键左";
KEY_LEFTBRACKET = "[";
KEY_MINUS = "-";
KEY_MOUSEWHEELDOWN = "鼠标滚轮向下滚动";
KEY_MOUSEWHEELUP = "鼠标滚轮向上滚动";
KEY_NUMLOCK = "Num Lock";
KEY_NUMLOCK_MAC = "清除";
KEY_NUMPAD0 = "数字键盘0";
KEY_NUMPAD1 = "数字键盘1";
KEY_NUMPAD2 = "数字键盘2";
KEY_NUMPAD3 = "数字键盘3";
KEY_NUMPAD4 = "数字键盘4";
KEY_NUMPAD5 = "数字键盘5";
KEY_NUMPAD6 = "数字键盘6";
KEY_NUMPAD7 = "数字键盘7";
KEY_NUMPAD8 = "数字键盘8";
KEY_NUMPAD9 = "数字键盘9";
KEY_NUMPADDECIMAL = "数字键盘.";
KEY_NUMPADDIVIDE = "数字键盘/";
KEY_NUMPADMINUS = "数字键盘-";
KEY_NUMPADMULTIPLY = "数字键盘*";
KEY_NUMPADPLUS = "数字键盘+";
KEY_PAGEDOWN = "Page Down";
KEY_PAGEUP = "Page Up";
KEY_PAUSE = "Pause";
KEY_PAUSE_MAC = "F15";
KEY_PERIOD = ".";
KEY_PLUS = "+";
KEY_PRINTSCREEN = "Print Screen";
KEY_PRINTSCREEN_MAC = "F13";
KEY_RIGHT = "方向键右";
KEY_RIGHTBRACKET = "]";
KEY_SCROLLLOCK = "Scroll Lock";
KEY_SCROLLLOCK_MAC = "F14";
KEY_SEMICOLON = ";";
KEY_SLASH = "/";
KEY_SPACE = "空格键";
KEY_TAB = "Tab";
KEY_TILDE = "~";
KEY_UNBOUND_ERROR = "|cffff0000%s功能未设置！|r";
KEY_UP = "方向键上";
KILLING_BLOWS = "杀敌";
KILLING_BLOW_TOOLTIP = "你亲手击杀的敌人";
KILLS = "杀敌";
KILLS_COMBATLOG_TOOLTIP = "当你的队友杀死某目标时显示信息。";
KILLS_PVP = "杀敌";
KNOWLEDGEBASE_FRAME_TITLE = "知识库";
KNOWLEDGE_BASE = "知识库";
KNOWN_TALENTS_HEADER = "我的天赋";
KOKR = "韩语";
LABEL_NOTE = "注释";
LALT_KEY_TEXT = "左ALT";
LANGUAGE = "语言";
LANGUAGES_LABEL = "语言";
LANGUAGES_SUBTEXT = "这些选项可以让你修改游戏的语言设置。";
LASTONLINE = "最后上线";
LASTONLINE_DAYS = "%d天";
LASTONLINE_HOURS = "%d小时";
LASTONLINE_MINS = "<1个小时";
LASTONLINE_MINUTES = "%d分钟";
LASTONLINE_MONTHS = "%d月";
LASTONLINE_SECS = "< 一分钟";
LASTONLINE_YEARS = "%d年";
LAST_ONLINE_COLON = "最后上线：";
LATEST_UNLOCKED_ACHIEVEMENTS = "近期成就";
LATEST_UPDATED_STATS = "最近更新的统计数据";
LAUGH_WORD1 = "lol";
LAUGH_WORD2 = "rofl";
LAUGH_WORD3 = "hehe";
LAUGH_WORD4 = "哈哈";
LAUGH_WORD5 = "哈哈";
LAUGH_WORD6 = "哈哈";
LAUGH_WORD7 = "哈哈";
LAUGH_WORD8 = "哈哈";
LAUGH_WORD9 = "rofl";
LCTRL_KEY_TEXT = "左CTRL";
LEADER = "领袖";
LEADER_TOOLTIP = "表示你愿意带领一支队伍。";
LEARN = "学习";
LEARN_SKILL_TEMPLATE = "学习%s";
LEAVE_ALL = "全部退出";
LEAVE_ARENA = "离开竞技场";
LEAVE_BATTLEGROUND = "离开战场";
LEAVE_CONVERSATION = "离开对话";
LEAVE_QUEUE = "离开队列";
LEAVE_VEHICLE = "离开载具";
LEAVE_ZONE = "离开%s";
LEAVING_COMBAT = "脱离战斗";
LEFT_PARTY = "%s离开了队伍。";
LEGSSLOT = "腿部";
LESS_THAN_ONE_MINUTE = "< 1分钟";
LEVEL = "等级";
LEVEL_ABBR = "Lv";
LEVEL_GAINED = "等级 %d";
LEVEL_GRANT = "%s想要为你提升一个等级";
LEVEL_RANGE = "等级范围";
LEVEL_REQUIRED = "需要等级%d";
LEVEL_TOO_LOW = "你需要达到等级%d才能装备这件物品。";
LEVEL_UP = "祝贺你，你升到了%d级！";
LEVEL_UP_CHAR_POINTS = "你获得了%d个天赋点数。";
LEVEL_UP_HEALTH = "你获得%d的生命值。";
LEVEL_UP_HEALTH_MANA = "你获得%d的生命值和%d的法力值。";
LEVEL_UP_SKILL_POINTS = "你还可以再学习%d个专业。";
LEVEL_UP_STAT = "你的%s提高了%d点。";
LFD_HOLIDAY_REWARD_EXPLANATION1 = "你每天取得的首次胜利将为你赢得：";
LFD_HOLIDAY_REWARD_EXPLANATION2 = "每天继首次胜利之后的每次胜利将为你赢得：";
LFD_LEVEL_FORMAT_RANGE = "(%d - %d)";
LFD_LEVEL_FORMAT_SINGLE = "(%d)";
LFD_RANDOM_EXPLANATION = "使用地下城查找器前往随机地下城，会有额外奖励哦！";
LFD_RANDOM_REWARD_EXPLANATION1 = "每天完成第一个随机地下城后会获得：";
LFD_RANDOM_REWARD_EXPLANATION2 = "每日完成的第一个随机地下城之后地下城会奖励你：";
LFD_RANDOM_REWARD_PUG_EXPLANATION = "你还将得到以下奖励。具体奖励内容视队伍中随机组队玩家的数量：";
LFD_REWARDS = "奖励";
LFGWIZARD_TITLE = "选择一个动作";
LFG_DESERTER_OTHER = "你的一名队友刚刚逃离了随机副本队伍，在接下来的时间内无法再度排队。";
LFG_DESERTER_YOU = "你刚刚逃离了随机副本队伍，在接下来的时间内无法再度排队：";
LFG_DISABLED_LFM_TOOLTIP = "你不能在招募队员时使用寻求组队功能。";
LFG_DISABLED_PARTY_TOOLTIP = "你不能在小队中使用寻求组队功能。";
LFG_LABEL = "我希望组队";
LFG_OFFER_CONTINUE = "一名玩家离开了你的队伍。是否寻找另一名玩家以完成%s？";
LFG_RANDOM_COOLDOWN_OTHER = "你的一名队友近期加入过一个随机地下城队列，暂时无法加入另一个。";
LFG_RANDOM_COOLDOWN_YOU = "你近期加入过一个随机地下城队列。\n需要过一段时间才可加入另一个，等待时间为：";
LFG_ROLES_TITLE = "我可以担当以下职责：";
LFG_ROLE_CHECK_ROLE_CHOSEN = "%s选择了：%s";
LFG_STATISTIC_AVERAGE_WAIT = "平均等待时间为：%s";
LFG_STATISTIC_AVERAGE_WAIT_UNKNOWN = "%1$s的等待时间未知。";
LFG_STATISTIC_MATCHES_MADE = "上一小时中有%2$d个%1$s的成功组队匹配。";
LFG_STATISTIC_PARTIES_IN_QUEUE = "%2$d个队伍正在寻找同去%1$s的队友";
LFG_STATISTIC_PLAYERS_IN_QUEUE = "%2$d个玩家在同去%1$s的队列中";
LFG_TITLE = "寻求组队";
LFG_TOOLTIP_ROLES = "职责：";
LFG_TYPE_ANY_DUNGEON = "任何地下城";
LFG_TYPE_ANY_HEROIC_DUNGEON = "任何英雄地下城";
LFG_TYPE_BATTLEGROUND = "战场";
LFG_TYPE_DAILY_DUNGEON = "日常地下城";
LFG_TYPE_DAILY_HEROIC_DUNGEON = "日常英雄地下城";
LFG_TYPE_DUNGEON = "地下城";
LFG_TYPE_HEROIC_DUNGEON = "英雄副本";
LFG_TYPE_NONE = "无";
LFG_TYPE_QUEST = "任务（组队）";
LFG_TYPE_RAID = "团队";
LFG_TYPE_RANDOM_DUNGEON = "随机地下城";
LFG_TYPE_ZONE = "地区";
LFM_DISABLED_LFG_TOOLTIP = "你必须在一支队伍中，或者正在寻求组队，才能加入招募队员的队列。";
LFM_NAME_TEMPLATE = "%s - 等级 %s %s";
LFM_NUM_RAID_MEMBER_TEMPLATE = "团队中有%d名成员";
LFM_TITLE = "招募队员";
LINK_TRADESKILL_TOOLTIP = "点击这里以创建一个链接到你的专业的快捷方式。";
LIST_ME = "列出我的名字";
LIST_MY_GROUP = "列出我的队伍";
LOCALE_INFORMATION = "语音选择";
LOCATION_COLON = "所在地区：";
LOCK = "锁定";
LOCKED = "已锁";
LOCKED_WITH_ITEM = "需要%s";
LOCKED_WITH_SPELL = "需要%s";
LOCKED_WITH_SPELL_KNOWN = "需要%s";
LOCK_ACTIONBAR_TEXT = "锁定动作条";
LOCK_BATTLEFIELDMINIMAP = "锁定区域地图";
LOCK_CHANNELPULLOUT_LABEL = "锁定频道名单";
LOCK_EXPIRE = "锁定过期";
LOCK_FOCUS_FRAME = "锁定框体位置";
LOCK_WINDOW = "锁定窗口";
LOGOUT = "返回角色选择";
LOG_PERIODIC_EFFECTS = "周期性伤害";
LOOKING = "正在寻找";
LOOKING_FOR = "寻找：";
LOOKING_FOR_DUNGEON = "地下城查找器";
LOOKING_FOR_GROUP_LABEL = "我要加入一支队伍并前往：";
LOOKING_FOR_GROUP_LABEL2 = "另外我还要加入一支队伍并前往：";
LOOKING_FOR_GROUP_TEXT = "让其他玩家知道你对哪些地下城、团队、任务或地区感兴趣。";
LOOKING_FOR_MORE = "招募队员";
LOOKING_FOR_MORE_TEXT = "为地下城、团队、任务或地区探险寻找更多队员。";
LOOKING_FOR_RAID = "团队浏览器";
LOOK_FOR_GROUP = "寻求组队";
LOOK_FOR_MORE = "招募队员";
LOOT = "拾取";
LOOTER = "拾取者";
LOOT_FREE_FOR_ALL = "分配方式：自由拾取";
LOOT_GONE = "物品已经被搜刮";
LOOT_GROUP_LOOT = "分配方式：队伍分配";
LOOT_ITEM = "%s获得了物品：%s。";
LOOT_ITEM_CREATED_SELF = "你制造了：%s。";
LOOT_ITEM_CREATED_SELF_MULTIPLE = "你制造了：%sx%d。";
LOOT_ITEM_MULTIPLE = "%s获得了物品：%sx%d。";
LOOT_ITEM_PUSHED_SELF = "你获得了物品：%s。";
LOOT_ITEM_PUSHED_SELF_MULTIPLE = "你获得了：%sx%d。";
LOOT_ITEM_SELF = "你获得了物品：%s。";
LOOT_ITEM_SELF_MULTIPLE = "你得到了物品：%sx%d。";
LOOT_KEY_TEXT = "拾取键";
LOOT_MASTER_LOOTER = "分配方式：队长分配";
LOOT_METHOD = "物品分配方式";
LOOT_MONEY = "%s拾取了%s。";
LOOT_MONEY_SPLIT = "你分到%s。";
LOOT_NEED_BEFORE_GREED = "分配方式：需求优先";
LOOT_NEXT_PAGE = "切换页面";
LOOT_NO_DROP = "拾取%s后，该物品将与你绑定。";
LOOT_NO_DROP_DISENCHANT = "分解%s会将其摧毁。";
LOOT_PROMOTE = "提升为物品分配者";
LOOT_ROLL_ALL_PASSED = "所有人都放弃了：%s";
LOOT_ROLL_DISENCHANT = "%s选择了分解取向：%s";
LOOT_ROLL_DISENCHANT_SELF = "你选择了分解取向：%s";
LOOT_ROLL_GREED = "%s选择了贪婪取向：%s";
LOOT_ROLL_GREED_SELF = "你选择了贪婪取向：%s";
LOOT_ROLL_INELIGIBLE_REASON1 = "你的职业无法对这件物品掷出需求骰子。";
LOOT_ROLL_INELIGIBLE_REASON2 = "你已达到获取该物品的数量上限。";
LOOT_ROLL_INELIGIBLE_REASON3 = "该物品无法被分解。";
LOOT_ROLL_INELIGIBLE_REASON4 = "你所在的队伍中没有附魔技能达到%d的附魔师。";
LOOT_ROLL_INELIGIBLE_REASON5 = "无法对这件物品掷出需求骰子。";
LOOT_ROLL_NEED = "%s选择了需求取向：%s";
LOOT_ROLL_NEED_SELF = "你选择了需求取向：%s";
LOOT_ROLL_PASSED = "%s放弃了：%s";
LOOT_ROLL_PASSED_AUTO = "%s自动放弃了%s，因为他无法拾取该物品。";
LOOT_ROLL_PASSED_AUTO_FEMALE = "%s自动放弃了%s，因为她无法拾取该物品。";
LOOT_ROLL_PASSED_SELF = "你放弃了：%s";
LOOT_ROLL_PASSED_SELF_AUTO = "你自动放弃了%s，因为你无法拾取该物品。";
LOOT_ROLL_ROLLED_DE = "（分解）%d点：%s（%s）";
LOOT_ROLL_ROLLED_GREED = "（贪婪）%d点：%s（%s）";
LOOT_ROLL_ROLLED_NEED = "（需求）%d点：%s（%s）";
LOOT_ROLL_WON = "%s赢得了：%s";
LOOT_ROLL_WON_NO_SPAM_DE = "%1$s赢得了：%3$s |cff818181(分解 - %2$d)|r";
LOOT_ROLL_WON_NO_SPAM_GREED = "%1$s赢得了：%3$s |cff818181(贪婪 - %2$d)|r";
LOOT_ROLL_WON_NO_SPAM_NEED = "%1$s赢得了：%3$s |cff818181(需求 - %2$d)|r";
LOOT_ROLL_YOU_WON = "你赢得了：%s";
LOOT_ROLL_YOU_WON_NO_SPAM_DE = "你赢得了：%2$s |cff818181(分解 - %1$d)|r";
LOOT_ROLL_YOU_WON_NO_SPAM_GREED = "你赢得了：%2$s |cff818181(贪婪 - %1$d)|r";
LOOT_ROLL_YOU_WON_NO_SPAM_NEED = "你赢得了：%2$s |cff818181(需求 - %1$d)|r";
LOOT_ROUND_ROBIN = "分配方式：轮流拾取";
LOOT_THRESHOLD = "物品分配界限";
LOOT_UNDER_MOUSE_TEXT = "鼠标位置打开拾取窗口";
LOSS = "负";
LOW = "低";
LSHIFT_KEY_TEXT = "左SHIFT";
LUA_ERROR = "Lua错误";
MACRO = "宏";
MACROFRAME_CHAR_LIMIT = "已使用%d个字符，最多255个";
MACROS = "宏命令设置";
MACRO_ACTION_FORBIDDEN = "一段宏代码已被禁止，因为其功能只对暴雪UI开放。";
MACRO_HELP_TEXT_LINE1 = "宏帮助：";
MACRO_HELP_TEXT_LINE2 = "- 输入 /macro 可打开宏UI，或从聊天命令菜单中选择";
MACRO_HELP_TEXT_LINE3 = "- 使用宏UI可以编制聊天文字，表情和动作命令";
MACRO_HELP_TEXT_LINE4 = "- 在宏中施放法术： /cast <法术名称> (<台词>)";
MACRO_HELP_TEXT_LINE5 = "- 在已打开宏UI的情况下按住Shift点击法术书中的法术可将此法术添加到你的宏里";
MACRO_POPUP_CHOOSE_ICON = "选择一个图标：";
MACRO_POPUP_TEXT = "输入宏的名字（最多16个字符）";
MAC_OPTIONS = "Mac设置";
MAGE_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
MAGIC_RESISTANCES_COLON = "魔法抗性：";
MAIL_COD_ERROR = "付费取信邮件的金额不得超过%d";
MAIL_COD_ERROR_COLORBLIND = "付款取信必须小于等于%1$d%2$s";
MAIL_LABEL = "信件";
MAIL_LETTER_TOOLTIP = "点击这里来获得一份这封信\n永久性的副本。";
MAIL_LOOT_KEY_TEXT = "收取邮件按键";
MAIL_MULTIPLE_ITEMS = "多物品";
MAIL_REPLY_PREFIX = "回复：";
MAIL_RETURN = "退信";
MAIL_SUBJECT_LABEL = "主题：";
MAIL_TO_LABEL = "收件人：";
MAINASSIST = "主助理";
MAINHANDSLOT = "主手";
MAINMENUBAR_FPS_LABEL = "帧数：%.0f fps";
MAINMENUBAR_LATENCY_LABEL = "延迟：%.0f ms";
MAINMENU_BUTTON = "主菜单";
MAINTANK = "主坦克";
MAIN_ASSIST = "主助理";
MAIN_MENU = "设置";
MAIN_TANK = "主坦克";
MAJOR_GLYPH = "大型雕文";
MAKE_INTERACTABLE = "使其可交互";
MAKE_MODERATOR = "指定管理员";
MAKE_UNINTERACTABLE = "使其不可交互";
MALE = "男性";
MANA = "法力值";
MANAGE_ACCOUNT = "帐户管理";
MANAGE_ACCOUNT_URL = "http://www.battlenet.com.cn";
MANA_COLON = "法力值：";
MANA_COST = "%d法力值";
MANA_COST_PER_TIME = "%d点法力值，外加每秒%d点";
MANA_LOW = "法力值过低";
MANA_REGEN = "法力回复";
MANA_REGEN_ABBR = "Regen";
MANA_REGEN_FROM_SPIRIT = "在非施法状态下时，每5秒的法力值恢复量提高%d点";
MANA_REGEN_TOOLTIP = "非施法状态下每5秒恢复%d点法力值\n施法状态下每5秒恢复%d点法力值";
MAP_QUEST_DIFFICULTY_TEXT = "任务难度颜色";
MARKED_AFK = "你现在处于离开状态";
MARKED_AFK_MESSAGE = "你现在处于离开状态：%s";
MARKED_DND = "你现在处于忙碌状态：%s。";
MASTERY_POINTS_SPENT = "%1$s天赋：%2$s";
MASTER_LOOTER = "队长分配";
MASTER_VOLUME = "主音量";
MATCHMAKING_MATCH_S = "你加入了一支去往%s的队伍。";
MATCHMAKING_PENDING = "等待完成配组……";
MAXIMUM = "最大";
MAX_FOLLOW_DIST = "最大镜头距离";
MAX_HP_TEMPLATE = "%d 最大生命值";
MEETINGSTONE_LEVEL = "等级 %d-%d";
MEETINGSTONE_TOOLTIP = "寻找更多队友去%s";
MELEE = "近战";
MELEE_ATTACK = "近战攻击";
MELEE_ATTACK_POWER = "近战攻击强度";
MELEE_ATTACK_POWER_TOOLTIP = "使近战武器的每秒伤害提高%.1f。";
MELEE_COMBATLOG_TOOLTIP = "显示普通的近战攻击。";
MELEE_CRIT_CHANCE = "爆击率";
MELEE_RANGE = "近战范围";
MEMBERS = "成员";
MERCHANT = "商人";
MERCHANT_ARENA_POINTS = "%d点竞技场点数";
MERCHANT_BUYBACK = "从商人处购回";
MERCHANT_HONOR_POINTS = "%d点荣誉点数";
MERCHANT_PAGE_NUMBER = "页数 %s/%s";
MERCHANT_STOCK = "(%d)";
MESSAGE_SOURCES = "信息来源";
MESSAGE_TYPES = "信息类型";
META_GEM = "多彩";
MILLISECONDS_ABBR = "毫秒";
MINIMAP_LABEL = "微缩地图";
MINIMAP_TRACKING_AUCTIONEER = "拍卖师";
MINIMAP_TRACKING_BANKER = "银行职员";
MINIMAP_TRACKING_BATTLEMASTER = "战场军官";
MINIMAP_TRACKING_FLIGHTMASTER = "飞行管理员";
MINIMAP_TRACKING_INNKEEPER = "旅店老板";
MINIMAP_TRACKING_MAILBOX = "邮箱";
MINIMAP_TRACKING_REPAIR = "修理";
MINIMAP_TRACKING_STABLEMASTER = "兽栏管理员";
MINIMAP_TRACKING_TOOLTIP_NONE = "点击以选择追踪类型";
MINIMAP_TRACKING_TRAINER_CLASS = "职业训练师";
MINIMAP_TRACKING_TRAINER_PROFESSION = "专业训练师";
MINIMAP_TRACKING_TRIVIAL_QUESTS = "低等级任务";
MINIMAP_TRACKING_VENDOR_AMMO = "弹药";
MINIMAP_TRACKING_VENDOR_FOOD = "食物和饮料";
MINIMAP_TRACKING_VENDOR_POISON = "毒药";
MINIMAP_TRACKING_VENDOR_REAGENT = "材料";
MINIMIZE = "最小化";
MINIMUM = "最小";
MINOR_GLYPH = "小型雕文";
MINS_ABBR = "分钟";
MINUTES = "|4分钟:分钟;";
MINUTES_ABBR = "%d|4分钟:分钟;";
MINUTE_ONELETTER_ABBR = "%d m";
MISCELLANEOUS = "杂项";
MISS = "未命中";
MISSES = "未命中";
MODE = "模式";
MODIFIERS_COLON = "管理员：";
MONEY = "钱";
MONEY_COLON = "金钱：";
MONEY_LOOT = "金钱拾取";
MONSTER_BOSS_EMOTE = "首领台词";
MONSTER_BOSS_WHISPER = "首领密语";
MONTH_APRIL = "4月";
MONTH_AUGUST = "8月";
MONTH_DECEMBER = "12月";
MONTH_FEBRUARY = "2月";
MONTH_JANUARY = "1月";
MONTH_JULY = "7月";
MONTH_JUNE = "6月";
MONTH_MARCH = "3月";
MONTH_MAY = "5月";
MONTH_NOVEMBER = "11月";
MONTH_OCTOBER = "10月";
MONTH_SEPTEMBER = "9月";
MORE_REAGENTS = "更多材料";
MOTD_COLON = "今日信息：";
MOUNT = "召唤";
MOUNTS = "坐骑";
MOUSE_LABEL = "鼠标";
MOUSE_LOOK_SPEED = "鼠标观察速度";
MOUSE_SENSITIVITY = "鼠标灵敏度";
MOUSE_SUBTEXT = "这些选项可以让你调整游戏中的鼠标设置。";
MOVE_FILTER_DOWN = "过滤条件下移";
MOVE_FILTER_UP = "过滤上移";
MOVE_TO_CONVERSATION_WINDOW = "移至对话窗口";
MOVE_TO_INACTIVE = "隐藏";
MOVE_TO_NEW_WINDOW = "移至一个新窗口";
MOVE_TO_WHISPER_WINDOW = "移至密语窗口";
MOVIE_RECORDING_AIC = "Apple Intermediate";
MOVIE_RECORDING_AIC_TOOLTIP = "Exclusive to MacOS X. This codec is the fastest to compress.";
MOVIE_RECORDING_CANCEL_CONFIRMATION = "你确定要取消此视频吗？这将会删除未压缩的部分，如果你目前正在录制，则会删除整部视频。";
MOVIE_RECORDING_CODEC_TOOLTIP = "改变压缩方式。";
MOVIE_RECORDING_COMPRESSBUTTON = "压缩";
MOVIE_RECORDING_COMPRESSDIALOG = "压缩…";
MOVIE_RECORDING_COMPRESSING = "正在压缩";
MOVIE_RECORDING_COMPRESSING_CANCEL_NEWBIE_TOOLTIP = "提前结束压缩。你会失去未被压缩的部分，而已经压缩的部分则会保存在你的硬盘上。";
MOVIE_RECORDING_COMPRESSING_CANCEL_TOOLTIP = "取消压缩";
MOVIE_RECORDING_COMPRESSION = "压缩";
MOVIE_RECORDING_COMPRESSION_STARTED = "已开始压缩视频\"%s\"。";
MOVIE_RECORDING_COMPRESS_TOOLTIP = "This will search for uncompressed movies and ask you if you want to compress, delete or ignore them.";
MOVIE_RECORDING_DATA_RATE = "数据录制速度：";
MOVIE_RECORDING_DATA_RATE_TOOLTIP = "每秒钟写入你的硬盘的数据量。如果在录制视频时游戏速度变得非常迟缓，你可能需要调低这个数值以免给硬盘造成太大的负担。在写入速度太慢时，游戏会自动停止录制视频。";
MOVIE_RECORDING_DV = "DV";
MOVIE_RECORDING_DV_TOOLTIP = "DV is the primary codec for most camcorders and iMovie.";
MOVIE_RECORDING_ENABLE_COMPRESSION = "录制后压缩";
MOVIE_RECORDING_ENABLE_COMPRESSION_TOOLTIP = "If this is checked, the movie will be compressed when you stop recording. Otherwise, you will need to use the compress button to finish processing the movies.";
MOVIE_RECORDING_ENABLE_CURSOR = "录制鼠标";
MOVIE_RECORDING_ENABLE_CURSOR_TOOLTIP = "钩选此项之后，你将在自己录制的视频中看到鼠标指针。（无论是否钩选此项，游戏中都会正常显示你的鼠标指针。）";
MOVIE_RECORDING_ENABLE_GUI = "录制用户界面";
MOVIE_RECORDING_ENABLE_GUI_TOOLTIP = "钩选此项之后，你将在自己录制的视频中看到用户界面。（无论是否钩选此项，游戏中都会正常显示你的用户界面。）";
MOVIE_RECORDING_ENABLE_ICON = "显示录制图标";
MOVIE_RECORDING_ENABLE_ICON_TOOLTIP = "钩选此项之后，你将在录制视频时看到微缩地图旁出现一个摄像机图标。如果你选择了录制用户界面，则该图标也会出现在视频中。";
MOVIE_RECORDING_ENABLE_RECOVER = "登录时压缩";
MOVIE_RECORDING_ENABLE_RECOVER_TOOLTIP = "钩选此项之后，当你登录游戏时，游戏会询问你是否要压缩/忽略或删除未处理的视频文件。";
MOVIE_RECORDING_ENABLE_SOUND = "录制声音";
MOVIE_RECORDING_ENABLE_SOUND_TOOLTIP = "钩选此项之后，声音将被录制在视频中。";
MOVIE_RECORDING_FPS_FOURTH = "1/4 游戏帧数";
MOVIE_RECORDING_FPS_HALF = "1/2 游戏帧数";
MOVIE_RECORDING_FPS_THIRD = "1/3 游戏帧数";
MOVIE_RECORDING_FRAMERATE = "画面帧数";
MOVIE_RECORDING_FRAMERATE_TOOLTIP = "改变视频的每秒帧数，降低这个数值可以提高游戏运行速度。游戏实际运行时的帧数也会被限定在这个数值以下。";
MOVIE_RECORDING_FULL_RESOLUTION = "全分辨率";
MOVIE_RECORDING_GUI_OFF = "不录制用户界面。";
MOVIE_RECORDING_GUI_ON = "录制用户界面。";
MOVIE_RECORDING_H264 = "H.264";
MOVIE_RECORDING_H264_TOOLTIP = "This codec is supported natively by Apple devices like the iPod, iPhone and AppleTV. This codec has the best ratio quality/size but it is also the slowest to compress.";
MOVIE_RECORDING_MJPEG = "动态JPEG";
MOVIE_RECORDING_MJPEG_TOOLTIP = "This codec is faster to compress than H.264 but it will generate a bigger file.";
MOVIE_RECORDING_MPEG4 = "MPEG-4";
MOVIE_RECORDING_MPEG4_TOOLTIP = "MPEG-4 is supported by many digital cameras and iMovie.";
MOVIE_RECORDING_PIXLET = "Pixlet";
MOVIE_RECORDING_QUALITY_TOOLTIP = "控制视频的质量。这个选项会对录制结束之后的最终压缩成品产生影响。提高质量也会令视频所占用的磁盘空间增加并使得压缩时间延长。";
MOVIE_RECORDING_RECORDING = "录制时间：";
MOVIE_RECORDING_RECORDING_STARTED = "已开始录制视频\"%s\"。";
MOVIE_RECORDING_RECORDING_STOPPED = "已停止录制视频\"%s\"。";
MOVIE_RECORDING_RECOVERING = "正在恢复帧数#";
MOVIE_RECORDING_RESOLUTION_TOOLTIP = "改变视频的分辨率。较低的分辨率令你可以录制较长时间的视频。";
MOVIE_RECORDING_TIME = "可用压缩时间：";
MOVIE_RECORDING_TIME_TOOLTIP = "在你的磁盘空间耗尽之前可以录制的视频时长。游戏会在达到该时长之后自动停止录制。";
MOVIE_RECORDING_UNCOMPRESSED_RGB = "未压缩RGB";
MOVIE_RECORDING_WARNING_COMPRESSING = "你不能在压缩视频的过程中开始录制视频。";
MOVIE_RECORDING_WARNING_DISK_FULL = "你的硬盘上没有足够的空间来录制视频。";
MOVIE_RECORDING_WARNING_NO_MOVIE = "没有视频需要压缩。";
MOVIE_RECORDING_WARNING_PERF = "录制停止 - 视频录制设置对于本机来说可能过高。";
MOVIE_RECORDING_WARNING_REQUIREMENTS = "你的系统未达到录制视频所需的最低配置要求。需要MacOS X 10.4.9、Quicktime 7.1.6和一块支持遮罩功能的显卡。";
MP = "法力值";
MULTIPLE_DUNGEONS = "多个地下城";
MULTISAMPLE = "多重采样";
MULTISAMPLING_FORMAT_STRING = "%d位色 %d位色深 %dx采样";
MULTI_CAST_TOOLTIP_NO_TOTEM = "没有图腾";
MUSIC_DISABLED = "禁用音乐";
MUSIC_ENABLED = "启用音乐";
MUSIC_VOLUME = "音乐";
MUTE = "禁声";
MUTED = "禁声";
MUTED_LIST = "禁声列表";
MUTE_PLAYER = "禁声玩家";
NAME = "名字";
NAMES_LABEL = "名字";
NAMES_SUBTEXT = "这些选项可以让你控制在游戏中显示哪一类名字。";
NAME_CHAT_WINDOW = "输入对话窗口名称";
NEAR = "近";
NECKSLOT = "颈部";
NEED = "需求";
NEED_NEWBIE = "你十分需要这件物品。";
NET_PROMOTER_HIGH = "我很乐意";
NET_PROMOTER_LOW = "不大可能";
NEVER = "从不";
NEW = "新建";
NEWBIE_TOOLTIP_ABANDONQUEST = "放弃选定的任务，将其从任务日志中移除。任何被放弃的任务都可以重新获得。放弃一个任务不会给你带来任何惩罚。";
NEWBIE_TOOLTIP_ACHIEVEMENT = "浏览有关你的成就和统计数据的信息。";
NEWBIE_TOOLTIP_ADDFRIEND = "将一个玩家添加到你的好友名单中。当该玩家登录或者退出游戏的时候，你会收到相应的提示。其他玩家不会知道他们是否在你的好友名单之上。";
NEWBIE_TOOLTIP_ADDMEMBER = "添加新的公会成员。";
NEWBIE_TOOLTIP_ADDTEAMMEMBER = "将新成员加入战队。";
NEWBIE_TOOLTIP_ALLIANCE = "一名联盟的成员，和部落的成员相敌对（兽人，巨魔，牛头人，亡灵，血精灵）。";
NEWBIE_TOOLTIP_AUTO_JOIN_VOICE = "在没有加入语音聊天频道时自动加入。";
NEWBIE_TOOLTIP_BATTLEFIELDMINIMAP_OPTIONS = "鼠标右键点击以打开此窗口的自定义设置列表。可以用鼠标左键点击并拖拽此窗口。";
NEWBIE_TOOLTIP_BATTLEFIELD_GROUP_JOIN = "如果你是一个小队的队长，这个按钮可以将你的整个小队一起加入战场等待序列，并保证你的小队同时进入同一个战场副本。但是，如果你在此之后添加新的队员，或者某个队员正在其它战场的等待队列中，那么他们也许无法和你进入同一个战场。";
NEWBIE_TOOLTIP_CHANNELPULLOUT_OPTIONS = "右键点击以打开该窗口的设置选项列表。左键点击并拖拽以移动该窗口。";
NEWBIE_TOOLTIP_CHANNELTAB = "浏览或编辑你的文本和语音聊天频道。";
NEWBIE_TOOLTIP_CHARACTER = "关于你的角色的各种信息，包括装备、战斗属性、技能和声望。";
NEWBIE_TOOLTIP_CHATMENU = "与别人联系的命令。使用聊天菜单，你可以和周围的玩家聊天，可以发送悄悄话，或者和队伍成员聊天，和一个朋友挥手致意或者创建一个宏。";
NEWBIE_TOOLTIP_CHATOPTIONS = "右键点击获得这个窗口的自定义选项列表。左键点击拖拉来移动这个窗口。";
NEWBIE_TOOLTIP_CHAT_OVERFLOW = "部分聊天标签页未显示。左键点击以查看所有的聊天标签页。";
NEWBIE_TOOLTIP_DEMOTE = "降低所选择玩家在公会中的等级。";
NEWBIE_TOOLTIP_DISHONORABLE_KILLS = "每当你在PvP中协助或主动杀死了一个等级过低（对你来说是灰色）的平民，则你获得一个非荣誉击杀。每次非荣誉击杀都会立刻稍稍降低你的总体排名，而非荣誉击杀的次数越多，每次遭受的惩罚也越严重。非荣誉击杀直接影响你的排名。";
NEWBIE_TOOLTIP_DISPLAY_CHANNEL_PULLOUT = "点击并拖拽以显示在该频道中启用了语音聊天的玩家名单。";
NEWBIE_TOOLTIP_ENCHANTSLOT = "将物品放入这个空格中可以让其他玩家进行开锁、附魔或淬毒。放在这里的物品不会被交易，在交易结束之后，它会回到原拥有者的物品栏中。";
NEWBIE_TOOLTIP_ENTER_BATTLEGROUND = "当战场中有可用的空位时再加入战场。这样会比选择加入首先可用的战场的等待时间稍长。";
NEWBIE_TOOLTIP_EQUIPMENT_MANAGER = "使用装备管理功能保存装备配置方案，并快速在多种方案之间切换。";
NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_IGNORE_SLOT = "将你装备在此栏中的物品排除在装备方案之外。";
NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_PLACE_IN_BAGS = "脱下这件装备，将它放在你的背包中。";
NEWBIE_TOOLTIP_EQUIPMENT_MANAGER_UNIGNORE_SLOT = "不再将你装备在此栏中的物品排除在装备方案之外。";
NEWBIE_TOOLTIP_FIRST_AVAILABLE = "加入首先可用的战场等待序列。如果你选择了“首先可用”，而另一个队友已经进入了“首先可用”的战场，那么你的首选战场将被改为这个战场。";
NEWBIE_TOOLTIP_FRAMERATE = "游戏画面显示的流畅程度。如果画面帧数较低，可以通过改变视频选项来提高。";
NEWBIE_TOOLTIP_FRIENDSTAB = "编辑你比较乐意一起游戏的玩家的名单。你可以收到这些玩家上线和下线的提示。";
NEWBIE_TOOLTIP_GROUPINVITE = "邀请选择的玩家加入队伍。";
NEWBIE_TOOLTIP_GUILDCONTROL = "自定义公会等级的名称和它们的权限。";
NEWBIE_TOOLTIP_GUILDGROUPINVITE = "邀请选择的公会成员加入队伍。";
NEWBIE_TOOLTIP_GUILDPUBLICNOTE = "点击察看你的公共信息。这个信息是别人通过公会用户界面可以查看到的。如果你点击这条信息，那你就可以对其进行编辑。";
NEWBIE_TOOLTIP_GUILDREMOVE = "将选定的玩家移除公会。";
NEWBIE_TOOLTIP_GUILDTAB = "查看关于你所在的公会及其会员的信息。如果你是公会的管理人员，还可以在这个窗口中进行公会管理工作。";
NEWBIE_TOOLTIP_GUILD_INFORMATION = "点击这里浏览更多有关你的公会的信息。如果你是一名官员或会长，还可以在这里输入信息以供会员阅读。";
NEWBIE_TOOLTIP_GUILD_MEMBER_OPTIONS = "右键点击一名会员可以看到更多设置选项。";
NEWBIE_TOOLTIP_HEALTHBAR = "你现在所拥有的生命值。如果你的生命值为0的话，那你就会死亡。当你脱离战斗的时候，生命值会自动恢复。";
NEWBIE_TOOLTIP_HELP = "你可以通过这个菜单给我们发送建议和报告错误。如果你有什么问题或者需要帮助的话，你还可以使用帮助请求菜单来与我们的GM在游戏中对话。";
NEWBIE_TOOLTIP_HONORABLE_KILLS = "每当你或你所在的队伍在PvP中对一个敌方玩家造成伤害，并且该玩家随后被杀死，则你可以获得一次荣誉击杀。只有在目标等级对你来说是绿色或更高（即杀死与该玩家等级相当的怪物可以获得经验值）的情况下才能获得荣誉击杀。只有敌方玩家会提供荣誉击杀。";
NEWBIE_TOOLTIP_HONOR_CONTRIBUTION_POINTS = "荣誉点数是通过在PvP战斗中进行荣誉击杀获得的。杀死敌人可以获得多少点数取决于你（或你所在的队伍）对其造成了多少伤害，并由你所在的小队或团队均分。如果你在短时间内反复杀死同一个敌方玩家，则每次杀死他所获得的荣誉点数也会越来越少，因此，在数次杀死同一个敌方玩家之后，你将获得极少的荣誉点数，甚至不再获得任何荣誉点数。";
NEWBIE_TOOLTIP_HONOR_STANDING = "排名是根据你与其他同阵营玩家（联盟或部落）的对比得出的。如果你的排名是150，那就是说有149名玩家在上一阶段中获得的荣誉点数比你多。你每周必须获得至少25个荣誉击杀点数才能参与军阶评定。";
NEWBIE_TOOLTIP_HORDE = "一名部落的成员，和联盟的成员相敌对（暗夜精灵，矮人，人类，侏儒，德莱尼）。";
NEWBIE_TOOLTIP_IGNOREPLAYER = "将一个玩家添加到你的屏蔽名单中。你将不会收到屏蔽名单上玩家的信息或者别的文字，被屏蔽的玩家也无法使用任何方式与你联络。";
NEWBIE_TOOLTIP_IGNORETAB = "编辑你想要屏蔽的玩家的名单。";
NEWBIE_TOOLTIP_LATENCY = "和游戏服务器通讯的平均时间。如果延迟一直很高（红色）的话，那么你的互联网连接可能有问题。";
NEWBIE_TOOLTIP_LFGPARENT = "这个工具可以帮助你找到一支队伍以供加入，也可以帮助你找到新的玩家来组建队伍或补充队伍空缺。";
NEWBIE_TOOLTIP_LFMTAB = "点击这里以寻找可以加入你的队伍的玩家。";
NEWBIE_TOOLTIP_MAINMENU = "你可以在这里修改你的视频、音频和界面设置，或者自定义快捷键。你还可以在这里选择返回角色选择画面或者退出游戏。";
NEWBIE_TOOLTIP_MANABAR0 = "你现在所拥有的法力值。玩家需要法力值来施放法术。如果你不进行施法动作的时间超过5秒，法力值就会开始自动回复。";
NEWBIE_TOOLTIP_MANABAR1 = "战士（以及德鲁伊的熊形态和巨熊形态）使用技能需要怒气。怒气可以通过制造伤害或者承受伤害获得。";
NEWBIE_TOOLTIP_MANABAR2 = "已废弃";
NEWBIE_TOOLTIP_MANABAR3 = "潜行者（以及德鲁伊的猎豹形态）需要能量来使用他们的技能。能量会不断地自动恢复。";
NEWBIE_TOOLTIP_MANABAR4 = "你现在宠物的喜悦程度。让你的宠物感到满足会让它对敌人造成普通伤害，而高兴状态下的宠物会制造更多的伤害，而不高兴状态下的宠物制造的伤害会减少。当你的宠物死亡或者饥饿状态的时候会降低喜悦程度，而喂食你的宠物能增加它的喜悦程度。";
NEWBIE_TOOLTIP_MEMORY = "你的插件所占用的内存。你可以通过角色选择画面进入插件管理页面。";
NEWBIE_TOOLTIP_MINIMAPTOGGLE = "开启/关闭小地图显示";
NEWBIE_TOOLTIP_MUTEPLAYER = "将一个玩家添加到你的禁声列表。你将无法听到禁声列表中的玩家讲话。";
NEWBIE_TOOLTIP_PARTYOPTIONS = "如果你在一个小队中，你可以右键点击你的头像来打开小队选项菜单。这个菜单可以让你退出队伍或者查看现在使用的物品捡拾方式。小队的领袖可以增加或者移除队员，并且还可以改变物品捡拾方式。";
NEWBIE_TOOLTIP_PLAYEROPTIONS = "右键点击可以实施和别的玩家交互的特殊命令。你可以查看他们的装备，发出组队邀请，开启交易窗口或者和他进行对决。一个队伍的领导可以提升队伍中队员的作用或者将其移除队伍。";
NEWBIE_TOOLTIP_PROMOTE = "提升所选择玩家在公会中的等级。";
NEWBIE_TOOLTIP_PVP = "浏览你的荣誉和竞技场战队信息。";
NEWBIE_TOOLTIP_PVPFFA = "你可以在游戏中攻击和被任何玩家攻击。";
NEWBIE_TOOLTIP_QUESTLOG = "你现在所拥有的任务。你最多可以同时拥有25条任务记录。";
NEWBIE_TOOLTIP_RAF_SUMMON_LINKED = "点击这里召唤一名已链接的队友。";
NEWBIE_TOOLTIP_RAIDTAB = "查看或者编辑你的大型团队。团队是一支由超过5个玩家组成的队伍，可以用来击败高等级的非常强大的敌人。";
NEWBIE_TOOLTIP_RANK = "荣誉系统的计算结果将以军阶的形式表现。根据你的本周排名和当前军阶级别的对比，你的军阶级别可能会提高、降低或保持不变。高级的玩家提升军阶的速度更快（下降也更慢）。另外，低级军阶提升的速度比高级军阶提升的速度更快。";
NEWBIE_TOOLTIP_RANK_POSITION = "你在排名体系中的位置。它会告诉你距离晋级或降级还有多远，但是只在每周更新排名时才会随之更新。";
NEWBIE_TOOLTIP_REMOVEFRIEND = "将选定的玩家从你的好友名单之中移除。";
NEWBIE_TOOLTIP_REMOVEPLAYER = "将选中的玩家移出你的屏蔽名单。";
NEWBIE_TOOLTIP_SENDMESSAGE = "给选定的玩家发送信息。";
NEWBIE_TOOLTIP_SHAREQUEST = "和符合条件的队伍队员共享任务。有些任务（比如通过物品触发的任务）无法共享。";
NEWBIE_TOOLTIP_SOCIAL = "关于游戏中的其他玩家人物的信息。你可以使用社交窗口来设置你的好友名单和屏蔽名单，以及查看有哪些玩家在线。";
NEWBIE_TOOLTIP_SPELLBOOK = "你的所有法术和技能。如果你想要将法术或技能图标拖到动作条上，就打开法术和技能窗口，左键点击你想要拖拉的法术或者技能，然后将其拖到动作条上。";
NEWBIE_TOOLTIP_STOPIGNORE = "将选定的玩家从你的屏蔽名单之中移除。";
NEWBIE_TOOLTIP_STOPWATCH_PLAYPAUSEBUTTON = "播放/暂停";
NEWBIE_TOOLTIP_STOPWATCH_RESETBUTTON = "重置";
NEWBIE_TOOLTIP_TALENTS = "天赋的各种组合选择能够强化你的角色，并使你的角色与众不同。";
NEWBIE_TOOLTIP_TRACKQUEST = "从你的任务查看列表中添加或删除所选任务。你可以在任务查看列表中点击任务来查看相关细节。";
NEWBIE_TOOLTIP_UNIT_DUEL = "向选定的玩家发出挑战，进行对决。生命值首先降到零的玩家将被判负。";
NEWBIE_TOOLTIP_UNIT_FOLLOW = "让你开始跟随选定的玩家。你将自动跟随这个玩家，直到自动跟随模式被取消。";
NEWBIE_TOOLTIP_UNIT_FREE_FOR_ALL = "在自由拾取规则下，所有的队伍队员都可以捡拾任何杀死怪物掉下的物品。金钱自动平分。";
NEWBIE_TOOLTIP_UNIT_GROUP_LOOT = "在队伍分配规则下，玩家通常使用轮流拾取规则来获得战利品，但是品质高于预设级别的物品会经过掷骰子决定归属。";
NEWBIE_TOOLTIP_UNIT_INSPECT = "观察选定的玩家所装备的护甲和武器。";
NEWBIE_TOOLTIP_UNIT_INVITE = "邀请选择的玩家加入队伍。";
NEWBIE_TOOLTIP_UNIT_LEAVE_PARTY = "从玩家目前所在的小队中开除该玩家。";
NEWBIE_TOOLTIP_UNIT_LOOT_THRESHOLD = "从怪物身上拾取战利品时，品质不低于此级别的物品将经过掷骰子决定归属。";
NEWBIE_TOOLTIP_UNIT_MASTER_LOOTER = "在队长分配规则下，只有队伍领袖才能够捡拾品质等于或高于预设级别的物品。在该级别之下的物品和金钱采用轮流拾取方式分配。";
NEWBIE_TOOLTIP_UNIT_NEED_BEFORE_GREED = "在需求优先规则下，玩家通常使用轮流拾取规则来获得战利品，但是品质高于预设级别的物品会由能使用该物品的队友掷骰子决定归属。";
NEWBIE_TOOLTIP_UNIT_OPT_OUT_LOOT = "开启此选项之后，你只能拾取任务物品、所有人都放弃的物品，以及由物品分配人员分配给你的物品。";
NEWBIE_TOOLTIP_UNIT_PET_ABANDON = "放弃你的宠物，让它回归自然。一旦你放弃了宠物，它将永远不再回来。";
NEWBIE_TOOLTIP_UNIT_PET_DISMISS = "解散由你控制的宠物。";
NEWBIE_TOOLTIP_UNIT_PET_PAPERDOLL = "关于你宠物的信息，包括它的战斗属性、法术抗性和食谱。";
NEWBIE_TOOLTIP_UNIT_PET_RENAME = "给你的宠物设定一个新名字。一个宠物只能被改名一次。";
NEWBIE_TOOLTIP_UNIT_PROMOTE = "交出队伍的领导权，选定的玩家将继续行使这一职责。";
NEWBIE_TOOLTIP_UNIT_ROUND_ROBIN = "在轮流拾取规则下，队伍队员按照顺序捡拾怪物掉落物品。金钱自动平分。";
NEWBIE_TOOLTIP_UNIT_TRADE = "与选择的玩家进行交易。";
NEWBIE_TOOLTIP_UNIT_UNINVITE = "将选定的玩家移出队伍。";
NEWBIE_TOOLTIP_UNIT_VOTE_TO_KICK = "发起一个将被选中的玩家从队伍中移出的投票。";
NEWBIE_TOOLTIP_UNMUTE = "从你的禁声列表中移除选定的玩家。";
NEWBIE_TOOLTIP_VOICE_CHAT_SELECTOR = "这个选单可以让你选择想要加入的语音聊天频道。右键点击以显示可用的语音聊天频道列表。";
NEWBIE_TOOLTIP_WHOTAB = "找到其他玩家在这个世界中的位置。";
NEWBIE_TOOLTIP_WORLDMAP = "在你探索一个地区时，它附近的地区会显示在世界地图上。你可以使用左键放大地图，右键缩小地图。";
NEWBIE_TOOLTIP_XPBAR = "你所得到的经验值（XP）。你的经验槽的颜色代表了你的休息状态，蓝色代表精力充沛，紫色代表正常。精力充沛的玩家通过杀死怪物所得的经验值是正常状态下的两倍，你可以在城市或者旅馆中逗留或者下线以获得更多的休息良好状态时间。";
NEW_ACHIEVEMENT_EARNED = "你获得了成就“%s”。";
NEW_CHAT_WINDOW = "创建新窗口";
NEW_CONVERSATION_INSTRUCTIONS = "选择|cffffffff2|r名好友以发起对话。";
NEW_LEADER = "新的领袖";
NEW_TITLE_EARNED = "你获得了'%s'的头衔。";
NEXT = "下一页";
NEXT_ABILITY = "下一个技能";
NEXT_BATTLE = "下一场战斗：%1$02d:%2$02d:%3$02d";
NO = "否";
NONE = "无";
NONEQUIPSLOT = "已制造物品";
NONE_CAPS = "无";
NONE_KEY = "无";
NORMAL_QUEST_DISPLAY = "|cff000000%s|r";
NOTE = "公共信息";
NOTE_COLON = "备注：";
NOTE_SUBMITTED = "信件已经提交";
NOTE_SUBMIT_FAILED = "信件提交失败";
NOT_APPLICABLE = "无";
NOT_BOUND = "未设置";
NOT_ENOUGH_MANA = "你没有足够的法力值来施放%s。";
NOT_IN_GROUP = "你没有加入任何队伍";
NOT_TAMEABLE = "不可驯服";
NOT_YET_SIGNED = "<未被签名>";
NO_ATTACHMENTS = "无附件";
NO_BIDS = "无竞价";
NO_COMPLETED_ACHIEVEMENTS = "你最近没有获得任何成就";
NO_DAILY_QUESTS_REMAINING = "你今天不能再完成任何日常任务了。";
NO_EMPTY_KEYRING_SLOTS_ERROR = "你的钥匙链已经满了。";
NO_EQUIPMENT_SLOTS_AVAILABLE = "你没有可以装备这件物品的栏位。";
NO_FRIEND_REQUESTS = "当前你没有好友请求";
NO_GUILDBANK_TABS = "你的公会尚未购买任何公会银行空间。";
NO_LFD_WHILE_LFR = "处于团队浏览器列表时无法加入地下城队列。";
NO_LFR_WHILE_LFD = "处于地下城队列时无法加入团队浏览器列表。";
NO_RAIDS_AVAILABLE = "你当前的等级无法进入任何团队副本。";
NO_RAID_INSTANCES_SAVED = "你在任何副本中的进度都未保存";
NO_RESPONSE = "无回复";
NO_UPDATED_STATS_TEXT = "你没有最近更新的统计数据";
NO_VIEWABLE_GUILDBANK_LOGS = "你没有浏览任何公会银行记录的权限。";
NO_VIEWABLE_GUILDBANK_TABS = "你没有浏览任何公会银行标签的权限。";
NO_VOICE_SESSIONS = "无频道";
NUMBER_OF_RESULTS_TEMPLATE = "物品%d - %d（总数%d）";
NUM_FREE_SLOTS = "%d个剩余栏位（总共）";
NUM_GUILDBANK_TABS_PURCHASED = "（已购买标签 %d/%d）";
NUM_RAID_MEMBERS = "%d团队成员";
OBJECTIVES_IGNORE_CURSOR_TEXT = "禁用鼠标悬停";
OBJECTIVES_LABEL = "任务目标|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
OBJECTIVES_SHOW_QUEST_MAP = "显示任务地图";
OBJECTIVES_STOP_TRACKING = "停止追踪";
OBJECTIVES_SUBTEXT = "这些选项可以帮助你自定义在用户界面上显示游戏目标（比如任务和成就）的方式。";
OBJECTIVES_TRACKER_LABEL = "目标";
OBJECTIVES_VIEW_ACHIEVEMENT = "打开成就";
OBJECTIVES_VIEW_IN_ACHIEVEMENTS = "打开成就";
OBJECTIVES_VIEW_IN_QUESTLOG = "打开任务细节";
OBJECTIVES_WATCH_QUESTS_ARENA = "你不能在竞技场中追踪任务。";
OBJECTIVES_WATCH_TOO_MANY = "你没有足够的空间追踪更多目标了。请先取消对一些目标的追踪。";
OBJECT_ALPHA = "物体半透明效果";
OFF = "关";
OFFICER = "官员";
OFFICER_CHAT = "官员聊天";
OFFICER_NOTE_COLON = "官员备注";
OKAY = "确定";
OLD_TITLE_LOST = "你失去了'%s'的头衔。";
ONLY_EMPTY_BAGS = "只能捡起空的背包！";
ON_COOLDOWN = "处于冷却状态";
OPACITY = "不透明";
OPENING = "正在打开";
OPENMAIL = "打开邮件";
OPEN_LOCK_OTHER = "%1$s对%3$s使用%2$s。";
OPEN_LOCK_SELF = "你对%2$s使用%1$s。";
OPEN_RAID_BROWSER = "打开团队浏览器";
OPTIONAL = "可选";
OPTIONAL_PARENS = "（可选）";
OPTIONS_BRIGHTNESS = "亮度";
OPTIONS_MENU = "选项菜单";
OPTIONS_SHADERS = "遮罩";
OPTION_CHAT_STYLE_CLASSIC = "聊天文本输入框与以前的功能相同。主聊天框体总是有一个单独的文本输入框与其同步。";
OPTION_CHAT_STYLE_IM = "点击聊天框体中的标签页时，文本输入栏会与该框体同步。每个聊天窗口的文本输入栏都会独立保留其自有的状态及历史。";
OPTION_CONVERSATION_MODE_INLINE = "实名对话的显示方式与频道类似。在对话名上点击右键可以前往新的聊天窗口。";
OPTION_CONVERSATION_MODE_POPOUT = "新的实名好友对话将显示在一个新的标签页中。";
OPTION_LOGOUT_REQUIREMENT = "需要重登录才能生效。";
OPTION_PREVIEW_TALENT_CHANGES_DESCRIPTION = "启用后，你可以在分配天赋点数前在天赋界面检查你的天赋是否分配正确。";
OPTION_RESTART_REQUIREMENT = "需要重新启动游戏才能生效。";
OPTION_STEREO_CONVERGENCE = "改变屏幕的3D深度。";
OPTION_STEREO_SEPARATION = "改变3D深度造成的双眼视觉差异。";
OPTION_TOOLTIP_ADVANCED_OBJECTIVES = "使你可以对任务框体进行缩放、拖动、最小化和展开等操作。";
OPTION_TOOLTIP_ADVANCED_WORLD_MAP = "启用该选项将允许你右键点击小世界地图的标题栏对其进行移动。";
OPTION_TOOLTIP_AGGRO_WARNING_DISPLAY1 = "从不显示仇恨警报系统。";
OPTION_TOOLTIP_AGGRO_WARNING_DISPLAY2 = "仅当你在副本中时显示仇恨警报系统。";
OPTION_TOOLTIP_AGGRO_WARNING_DISPLAY3 = "仅当你在小队或团队中时显示仇恨警报系统。";
OPTION_TOOLTIP_AGGRO_WARNING_DISPLAY4 = "总是显示仇恨警报系统。";
OPTION_TOOLTIP_ALWAYS_SHOW_MULTIBARS = "勾选此选项后，额外的动作条将总是被显示出来。";
OPTION_TOOLTIP_AMBIENCE_VOLUME = "调整环境音效的音量。";
OPTION_TOOLTIP_ANIMATION = "PLACE_HOLDER";
OPTION_TOOLTIP_ANISOTROPIC = "提高材质的锐度，尤其是以倾斜视角观察的材质。降低此项可以提高运行速度。";
OPTION_TOOLTIP_ASSIST_ATTACK = "自动攻击使用\"/assist\"指令选定的目标。";
OPTION_TOOLTIP_AUTO_DISMOUNT_FLYING = "钩选此项之后，你的角色在施放法术前会自动解散飞行坐骑。";
OPTION_TOOLTIP_AUTO_FOLLOW_SPEED = "调节在总是跟随和智能跟随模式下的镜头移动速度。";
OPTION_TOOLTIP_AUTO_JOIN_GUILD_CHANNEL = "钩选这个选项可以让你自动加入公会招募频道（取决于你是否已经加入了某个公会）。";
OPTION_TOOLTIP_AUTO_LOOT_ALT_KEY = "打开“自动拾取”选项之后，按ALT键可以拾取物品。未打开“自动拾取”选项则按住ALT键可以自动拾取物品。";
OPTION_TOOLTIP_AUTO_LOOT_CTRL_KEY = "打开“自动拾取”选项之后，按CTRL键可以拾取物品。未打开“自动拾取”选项则按住CTRL键可以自动拾取物品。";
OPTION_TOOLTIP_AUTO_LOOT_DEFAULT = "将自动拾取设置为点击一个可拾取物品的目标时的默认动作。（通过按住拾取键/自动拾取键，可以在钩选此项时手动拾取，或在未钩选此项时自动拾取。）";
OPTION_TOOLTIP_AUTO_LOOT_KEY_TEXT = "用来自动拾取的按键";
OPTION_TOOLTIP_AUTO_LOOT_NONE_KEY = "未绑定按键。";
OPTION_TOOLTIP_AUTO_LOOT_SHIFT_KEY = "打开“自动拾取”选项之后，按SHIFT键可以拾取物品。未打开“自动拾取”选项则按住SHIFT键可以自动拾取物品。";
OPTION_TOOLTIP_AUTO_QUEST_PROGRESS = "当你达到一项任务目标时，该任务会被自动追踪5分钟。";
OPTION_TOOLTIP_AUTO_QUEST_WATCH = "在你接到一个带有可追踪目标的任务时，自动开始追踪这个任务。";
OPTION_TOOLTIP_AUTO_RANGED_COMBAT = "钩选之后，你的角色会自动在自动攻击和自动射击之间切换。";
OPTION_TOOLTIP_AUTO_SELF_CAST = "开启此选项后，如果你的当前目标为非友方目标或没有目标，则可以对友方目标施放的法术会自动对你本人施放。";
OPTION_TOOLTIP_AUTO_SELF_CAST_ALT_KEY = "按住ALT键以对自己施放有益法术，即便你当前选中的目标是敌对的，甚至没有目标。";
OPTION_TOOLTIP_AUTO_SELF_CAST_CTRL_KEY = "按住CTRL键以对自己施放有益法术，即便你当前选中的目标是敌对的，甚至没有目标。";
OPTION_TOOLTIP_AUTO_SELF_CAST_KEY_TEXT = "按住这个键之后施法目标会变成自己，即使你当前锁定的目标是某个敌对怪物或玩家。";
OPTION_TOOLTIP_AUTO_SELF_CAST_NONE_KEY = "未绑定按键。";
OPTION_TOOLTIP_AUTO_SELF_CAST_SHIFT_KEY = "按住SHIFT键以对自己施放有益法术，即便你当前选中的目标是敌对的，甚至没有目标。";
OPTION_TOOLTIP_BLOCK_TRADES = "阻止所有的交易要求。";
OPTION_TOOLTIP_CAMERA1 = "将视角固定在你所设置的角度，但你的角色移动时则恢复到跟踪视角。（只调整水平角度）";
OPTION_TOOLTIP_CAMERA2 = "设定视角，使视角总是处于你的角色后方。";
OPTION_TOOLTIP_CAMERA3 = "设定视角，使其固定在一点，永远不自动调节。";
OPTION_TOOLTIP_CAMERA4 = "将视角固定在你所设置的角度，但你的角色移动时则恢复到跟踪视角。";
OPTION_TOOLTIP_CAMERA_ALWAYS = "设定视角，使视角总是处于你的角色后方。";
OPTION_TOOLTIP_CAMERA_NEVER = "设定视角，使其固定在一点，永远不自动调节。";
OPTION_TOOLTIP_CAMERA_SMART = "将视角固定在你所设置的角度，但你的角色移动时则恢复到跟踪视角。（只调整水平角度）";
OPTION_TOOLTIP_CAMERA_SMARTER = "将视角固定在你所设置的角度，但你的角色移动时则恢复到跟踪视角。";
OPTION_TOOLTIP_CHARACTER_SHADOWS = "显示游戏中所有角色的影子。禁用此选项有时可以提高游戏运行速度。";
OPTION_TOOLTIP_CHAT_BUBBLES = "在角色头顶上显示“/说”指令和“/喊”指令的文字泡泡。";
OPTION_TOOLTIP_CHAT_LOCKED = "锁定所有聊天窗口，这样它们就不会被误修改了。";
OPTION_TOOLTIP_CHAT_MOUSE_WHEEL_SCROLL = "勾选以使鼠标在悬停于聊天窗口时可使用滑轮滚动聊天文本。";
OPTION_TOOLTIP_CHAT_WHOLE_WINDOW_CLICKABLE = "点击聊天框体的任意位置以关注该聊天。";
OPTION_TOOLTIP_CINEMATIC_SUBTITLES = "开启开场动画字幕。";
OPTION_TOOLTIP_CLEAR_AFK = "移动或者说话时\n自动解除离开状态。";
OPTION_TOOLTIP_CLICKCAMERA_LOCKED = "设置视角，使其在你使用鼠标点击移动的同时，视角保持与你行进方向的一致。";
OPTION_TOOLTIP_CLICKCAMERA_NEVER = "设置视角，使其在使用鼠标点击移动的时候不改变视角。";
OPTION_TOOLTIP_CLICKCAMERA_SMART = "设置视角，使其在你使用点击鼠标移动改变方向的时候稍作延时，然后跟随你的角色角色。（推荐模式）";
OPTION_TOOLTIP_CLICK_CAMERA1 = "设置视角，使其在你使用点击鼠标移动改变方向的时候稍作延时，然后跟随你的角色角色。（推荐模式）";
OPTION_TOOLTIP_CLICK_CAMERA2 = "设置视角，使其在你使用鼠标点击移动的同时，视角保持与你行进方向的一致。";
OPTION_TOOLTIP_CLICK_CAMERA3 = "设置视角，使其在使用鼠标点击移动的时候不改变视角。";
OPTION_TOOLTIP_CLICK_CAMERA_STYLE = "决定点击移动方式的时候视角跟随方式。";
OPTION_TOOLTIP_CLICK_TO_MOVE = "使用鼠标点击来把你的角色移动到指定的地点。";
OPTION_TOOLTIP_COMBAT_TEXT_MODE = "设置战斗信息滚动的方向。";
OPTION_TOOLTIP_COMBAT_TEXT_SCROLL_DOWN = "战斗记录向下滚动，而非向上滚动。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_AURAS = "当玩家获得或失去光环效果时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_AURA_FADE = "当魔法效果从玩家身上消失时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_COMBAT_STATE = "当你进入或脱离战斗时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_COMBO_POINTS = "当你获得新的连击点时，显示连击点的数量。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_DODGE_PARRY_MISS = "当敌人的攻击被你躲闪、招架或未命中你时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_ENERGIZE = "显示所有立即获取的法力值、怒气值和能量值。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_FRIENDLY_NAMES = "当一个友方施法者对你施放治疗法术时，显示他/她的名字。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_HONOR_GAINED = "显示你通过杀死其他玩家所得到的荣誉值。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_LOW_HEALTH_MANA = "当你的生命值或法力值低于20%的时候显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_PERIODIC_ENERGIZE = "显示所有周期性获得的法力值、怒气值和能量值。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_REACTIVES = "当特定的反击型职业技能和法术（如战士的压制）可以使用时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_REPUTATION = "当玩家在某个阵营中的声望提高或降低时显示信息。";
OPTION_TOOLTIP_COMBAT_TEXT_SHOW_RESISTANCES = "当你抵抗攻击或法术时显示信息。";
OPTION_TOOLTIP_CONSOLIDATE_BUFFS = "将一部分增益效果划分至一个增益效果盒。短时间增益效果在该盒中显示。持续时间很长的增益效果显示在该盒中，直到效果即将消失。";
OPTION_TOOLTIP_DEATH_EFFECT = "关闭此选项可以禁止鬼魂状态下的泛光效果，可略微提高运行速度。";
OPTION_TOOLTIP_DESKTOP_GAMMA = "使用桌面Gamma设定。";
OPTION_TOOLTIP_DISABLE_SPAM_FILTER = "禁用聊天文字中的垃圾信息过滤功能。";
OPTION_TOOLTIP_DISPLAY_FREE_BAG_SLOTS = "在背包图标上显示剩余背包空间。";
OPTION_TOOLTIP_ENABLE_ALL_SHADERS = "开启所有像素遮罩。";
OPTION_TOOLTIP_ENABLE_AMBIENCE = "开启环境音效。";
OPTION_TOOLTIP_ENABLE_BGSOUND = "开启此选项可以让《魔兽世界》的声音在游戏处于后台运行状态时继续播放。";
OPTION_TOOLTIP_ENABLE_DSP_EFFECTS = "为死亡骑士的语音开启特殊效果。禁用此项可以提高运行速度。";
OPTION_TOOLTIP_ENABLE_EMOTE_SOUNDS = "开启/关闭表情指令的音效。";
OPTION_TOOLTIP_ENABLE_ERROR_SPEECH = "开启错误提示语音（比如“目标太远”）。";
OPTION_TOOLTIP_ENABLE_GROUP_SPEECH = "开启队友语音。";
OPTION_TOOLTIP_ENABLE_HARDWARE = "开启此选项以使用3D声音加速。可能会改变你的声音表现效果。";
OPTION_TOOLTIP_ENABLE_MICROPHONE = "启用你的麦克风。";
OPTION_TOOLTIP_ENABLE_MUSIC = "开启背景音乐。";
OPTION_TOOLTIP_ENABLE_MUSIC_LOOPING = "循环播放背景音乐。";
OPTION_TOOLTIP_ENABLE_PET_SOUNDS = "打开或关闭宠物空闲和攻击音效。";
OPTION_TOOLTIP_ENABLE_REVERB = "启用声音混响效果。会降低游戏运行速度。";
OPTION_TOOLTIP_ENABLE_SOFTWARE_HRTF = "为耳机环绕声效开启软件模拟。";
OPTION_TOOLTIP_ENABLE_SOUND = "开启或禁止所有声音。";
OPTION_TOOLTIP_ENABLE_SOUNDFX = "开启或禁止游戏声音效果。";
OPTION_TOOLTIP_ENABLE_SOUND_AT_CHARACTER = "将声源接收位置设定为来自玩家角色所在位置，而非镜头位置。";
OPTION_TOOLTIP_ENABLE_STEREO_VIDEO = "启用3D立体眼镜。";
OPTION_TOOLTIP_ENABLE_VOICECHAT = "语音聊天并未受到暴雪娱乐的监控，您的游戏体验可能会在与其他玩家进行互动时发生相应的改变。";
OPTION_TOOLTIP_ENVIRONMENT_DETAIL = "控制你所能看见物体的远近。降低这个数值可以提高运行速度。";
OPTION_TOOLTIP_FARCLIP = "改变你的视野范围。降低这个数值可以极大地提高运行速度。";
OPTION_TOOLTIP_FIX_LAG = "启用此项可以降低用户界面延迟，但是也有可能明显降低画面帧数。";
OPTION_TOOLTIP_FOCUS_CAST_ALT_KEY = "按住ALT键以对你的焦点目标施放需要指定目标的法术。";
OPTION_TOOLTIP_FOCUS_CAST_CTRL_KEY = "按住CTRL键以对你的焦点目标施放需要指定目标的法术。";
OPTION_TOOLTIP_FOCUS_CAST_NONE_KEY = "未绑定按键。";
OPTION_TOOLTIP_FOCUS_CAST_SHIFT_KEY = "按住SHIFT键以对你的焦点目标施放需要指定目标的法术。";
OPTION_TOOLTIP_FOLLOW_TERRAIN = "根据地形自动调节视角。如果你的角色在爬坡，视角会向上旋转；如果你的角色在往下走，那视角会向下旋转。";
OPTION_TOOLTIP_FULL_SCREEN_GLOW = "开启全屏幕柔化和光影效果。关闭这个选项有时可以提高运行速度。";
OPTION_TOOLTIP_FULL_SIZE_FOCUS_FRAME = "将焦点框体的尺寸扩大到与目标框架相同。";
OPTION_TOOLTIP_GAMEFIELD_DESELECT = "钩选此框可以让你避免因为点击画面上的空白区域而取消对当前目标的选择。你只能通过按下ESC键或点击另一个目标来切换你所选择的目标。";
OPTION_TOOLTIP_GAMMA = "控制游戏画面的亮度。增加亮度直到你清晰的辨别下方的21级灰度条。";
OPTION_TOOLTIP_GROUND_DENSITY = "控制地表景观物体（比如草和其它植被）的数量。降低这个数值可以提高游戏运行速度。";
OPTION_TOOLTIP_GROUND_RADIUS = "控制地表景观物体（比如草和其它植被）的显示范围。降低这个数值可以提高游戏运行速度。";
OPTION_TOOLTIP_GUILDMEMBER_ALERT = "当你的公会会员登录或者退出游戏时显示提示信息。";
OPTION_TOOLTIP_HARDWARE_CURSOR = "启用此项以获得更灵敏的鼠标反应速度，除非你的鼠标指针出现问题。";
OPTION_TOOLTIP_HEAD_BOB = "在第一人称视角时候，模拟头部晃动。";
OPTION_TOOLTIP_HIDE_OUTDOOR_WORLD_STATE = "钩选此框将隐藏界面上的所有本区域范围内的目标指示。";
OPTION_TOOLTIP_HIDE_PARTY_INTERFACE = "点击这里隐藏你的队员的头像和生命槽。";
OPTION_TOOLTIP_INVERT_MOUSE = "鼠标移动方向与改变视角方向相反。";
OPTION_TOOLTIP_LOCALE = "选择你要使用的语言版本。";
OPTION_TOOLTIP_LOCK_ACTIONBAR = "使玩家无法拖动动作条上的快捷技能图标。这项功能可以通过按键设置界面设置相应的快捷键。";
OPTION_TOOLTIP_LOG_PERIODIC_EFFECTS = "显示周期性伤害效果数值，比如撕裂和暗言术：痛。";
OPTION_TOOLTIP_LONG_RANGE_NAMEPLATE = "调整这个选项可以增加显示姓名板的范围。";
OPTION_TOOLTIP_LOOT_KEY_TEXT = "用来手动拾取物品的按键";
OPTION_TOOLTIP_LOOT_UNDER_MOUSE = "钩选此项后，拾取窗口将在当前鼠标位置打开。";
OPTION_TOOLTIP_MAP_QUEST_DIFFICULTY = "将任务名称按照任务难度以不同颜色显示在世界地图上。";
OPTION_TOOLTIP_MAP_TRACK_QUEST = "从你的任务查看列表中添加或删除所选任务。|n你也可以按住SHIFT键点击某任务或其图标。";
OPTION_TOOLTIP_MASTER_VOLUME = "调整主音量。";
OPTION_TOOLTIP_MAX_FOLLOW_DIST = "调节最大跟随距离。";
OPTION_TOOLTIP_MOUSE_LOOK_SPEED = "调节使用鼠标改变视角时的移动速度。";
OPTION_TOOLTIP_MOUSE_SENSITIVITY = "调节鼠标指针移动速度。";
OPTION_TOOLTIP_MULTISAMPLING = "提高多重采样级别可以使模型边缘变得更平滑，但是会明显降低游戏运行速度。";
OPTION_TOOLTIP_MUSIC_VOLUME = "调整背景音乐的音量。";
OPTION_TOOLTIP_OBJECTIVES_IGNORE_CURSOR = "锁定目标框体，鼠标悬停在框体上时不会显示出框体来。";
OPTION_TOOLTIP_OBJECT_ALPHA = "PLACE_HOLDER";
OPTION_TOOLTIP_PARTICLE_DENSITY = "控制法术、火焰等效果中使用的粒子数量。降低此项可以提高游戏运行速度。";
OPTION_TOOLTIP_PARTY_CHAT_BUBBLES = "在队友头顶上显示组队聊天的文字泡泡。";
OPTION_TOOLTIP_PET_NAMEPLATES = "开启/关闭你的宠物、图腾和守护者的名字显示。";
OPTION_TOOLTIP_PET_SPELL_DAMAGE = "显示你的宠物造成的法术伤害。";
OPTION_TOOLTIP_PHONG_SHADING = "开启这个选项可以平滑角色光线表现。";
OPTION_TOOLTIP_PLAYER_DETAIL = "调整玩家角色材质的分辨率。降低此项可以略微提高游戏运行速度。";
OPTION_TOOLTIP_PLAY_AGGRO_SOUNDS = "启用当仇恨过高时的警报音效。";
OPTION_TOOLTIP_PROFANITY_FILTER = "开启不良语句过滤。";
OPTION_TOOLTIP_PROFANITY_FILTER_WITH_WARNING = "开启不良语言过滤器。\n\n|cffff0000警告：|r战网不可用，本次设置改动也许不会被保存。";
OPTION_TOOLTIP_PROJECTED_TEXTURES = "启用将贴图材质投射到环境中的功能。禁用此项可以明显提高游戏运行速度。";
OPTION_TOOLTIP_PUSHTOTALK_SOUND = "当你激活了按键发言时会有声音提示。";
OPTION_TOOLTIP_REMOVE_CHAT_DELAY = "选择后当鼠标移动到聊天窗口之后窗口立即显示。";
OPTION_TOOLTIP_ROTATE_MINIMAP = "选中此项以旋转微缩地图，而非旋转玩家指示箭头。";
OPTION_TOOLTIP_SCROLL_ARC = "战斗信息文字以弧线路径远离玩家。";
OPTION_TOOLTIP_SCROLL_DOWN = "战斗信息文字向屏幕下方滚动。";
OPTION_TOOLTIP_SCROLL_UP = "战斗信息文字向屏幕上方滚动。";
OPTION_TOOLTIP_SECURE_ABILITY_TOGGLE = "钩选此项之后，你将不会因为在短时间内偶然多次点击快捷键而意外取消你的技能。";
OPTION_TOOLTIP_SHADOW_QUALITY = "控制游戏角色和环境的阴影渲染质量。降低此项可以提高游戏运行速度。";
OPTION_TOOLTIP_SHOW_ARENA_ENEMY_CASTBAR = "显示对方的竞技场战队中的玩家正在施放的法术。";
OPTION_TOOLTIP_SHOW_ARENA_ENEMY_FRAMES = "在竞技场中显示对手成员框体。";
OPTION_TOOLTIP_SHOW_ARENA_ENEMY_PETS = "显示对方的竞技场战队中的宠物头像。";
OPTION_TOOLTIP_SHOW_BATTLENET_TOASTS = "开启该选项以使来自战网系统的信息以弹窗方式显示。";
OPTION_TOOLTIP_SHOW_BUFF_DURATION = "显示/隐藏增益效果的持续时间。";
OPTION_TOOLTIP_SHOW_CASTABLE_BUFFS = "只显示可以对友方目标施放的增益法术。只在团队中有效。";
OPTION_TOOLTIP_SHOW_CASTABLE_DEBUFFS = "仅显示你对敌方目标施放的负面效果。";
OPTION_TOOLTIP_SHOW_CHAT_ICONS = "设定是否在自动加入的聊天频道（如交易频道）中以图标形式显示图标链接。";
OPTION_TOOLTIP_SHOW_CLASS_COLOR_IN_V_KEY = "在用以显示敌方玩家生命值的姓名板背景上标示出敌对玩家的职业颜色。";
OPTION_TOOLTIP_SHOW_CLOAK = "取消这个选项来隐藏你的角色的披风。";
OPTION_TOOLTIP_SHOW_CLOCK = "在微缩地图上显示时钟按钮。";
OPTION_TOOLTIP_SHOW_COMBAT_HEALING = "显示你对目标的治疗量。";
OPTION_TOOLTIP_SHOW_COMBAT_TEXT = "钩选此项将在屏幕上直观显示额外的战斗信息。";
OPTION_TOOLTIP_SHOW_DAMAGE = "在敌对生物身上显示所造成的伤害。";
OPTION_TOOLTIP_SHOW_DISPELLABLE_DEBUFFS = "只显示友方目标身上可以驱散的负面效果。只在团队中有效。";
OPTION_TOOLTIP_SHOW_FULLSCREEN_STATUS = "当你在使用全屏幕UI的情况下，并且在战斗中时，在屏幕边缘显示闪光效果。";
OPTION_TOOLTIP_SHOW_GUILD_NAMES = "总是显示游戏世界中的玩家公会名字。";
OPTION_TOOLTIP_SHOW_HELM = "取消这个选项来隐藏你的角色的头盔。";
OPTION_TOOLTIP_SHOW_ITEM_LEVEL = "在提示信息中显示特定物品的物品等级。";
OPTION_TOOLTIP_SHOW_LOOT_SPAM = "不选中此项可以隐藏各人掷骰子的信息，只显示获胜者的掷骰子信息。";
OPTION_TOOLTIP_SHOW_LUA_ERRORS = "显示与UI功能相关的错误信息。";
OPTION_TOOLTIP_SHOW_MULTIBAR1 = "打开/关闭屏幕左下角的可选动作条。";
OPTION_TOOLTIP_SHOW_MULTIBAR2 = "打开/关闭屏幕右下角的可选动作条。";
OPTION_TOOLTIP_SHOW_MULTIBAR3 = "打开/关闭屏幕右边的可选动作条。";
OPTION_TOOLTIP_SHOW_MULTIBAR4 = "打开/关闭屏幕右边的第二个可选动作条。";
OPTION_TOOLTIP_SHOW_NEWBIE_TIPS = "显示不同玩家界面元素的详细信息。高级玩家可以将这个选项关闭。";
OPTION_TOOLTIP_SHOW_NPC_NAMES = "总是显示游戏世界中的NPC名字。";
OPTION_TOOLTIP_SHOW_NUMERIC_THREAT = "钩选此项，以百分比形式显示威胁值。";
OPTION_TOOLTIP_SHOW_OTHER_TARGET_EFFECTS = "显示其他玩家的目标身上的效果，比如沉默和诱捕。";
OPTION_TOOLTIP_SHOW_OWN_NAME = "显示你的角色的名字。";
OPTION_TOOLTIP_SHOW_PARTY_BACKGROUND = "在小队成员和敌方竞技场队员后面显示背景。";
OPTION_TOOLTIP_SHOW_PARTY_PETS = "显示队友宠物的头像。";
OPTION_TOOLTIP_SHOW_PARTY_TEXT = "总是以数字形式在队友的状态条上显示其生命值、法力值、怒气值或能量值。";
OPTION_TOOLTIP_SHOW_PET_MELEE_DAMAGE = "显示你的宠物造成的伤害。";
OPTION_TOOLTIP_SHOW_PLAYER_NAMES = "总是显示游戏世界中的玩家姓名。";
OPTION_TOOLTIP_SHOW_PLAYER_TITLES = "总是显示游戏世界中的玩家头衔。";
OPTION_TOOLTIP_SHOW_QUEST_FADING = "钩选此框以立刻显示所有任务文本。";
OPTION_TOOLTIP_SHOW_QUEST_OBJECTIVES_ON_MAP = "启用后你可以在地图上看到特定任务目标的位置。";
OPTION_TOOLTIP_SHOW_RAID_RANGE = "当团队成员距离太远时，通过使他们的生命条变淡来标示距离。";
OPTION_TOOLTIP_SHOW_TARGET_CASTBAR = "显示你的当前目标正在施放的法术。";
OPTION_TOOLTIP_SHOW_TARGET_CASTBAR_IN_V_KEY = "在你的当前目标姓名板被显示出来的情况下，在游戏界面中显示敌方施法条和生命值。";
OPTION_TOOLTIP_SHOW_TARGET_EFFECTS = "显示你的目标身上的效果，比如沉默和诱捕。";
OPTION_TOOLTIP_SHOW_TARGET_OF_TARGET = "显示或隐藏你的目标所选定的目标。";
OPTION_TOOLTIP_SHOW_TIPOFTHEDAY = "在游戏读取界面上显示或隐藏每日小窍门。";
OPTION_TOOLTIP_SHOW_TOAST_BROADCAST = "实名好友更新通告时显示战网信息。";
OPTION_TOOLTIP_SHOW_TOAST_CONVERSATION = "加入实名好友对话时显示战网信息。";
OPTION_TOOLTIP_SHOW_TOAST_FRIEND_REQUEST = "收到实名好友请求时显示战网信息。";
OPTION_TOOLTIP_SHOW_TOAST_OFFLINE = "实名好友下线时显示战网信息。";
OPTION_TOOLTIP_SHOW_TOAST_ONLINE = "实名好友上线时显示战网信息。";
OPTION_TOOLTIP_SHOW_TOAST_WINDOW = "开启该选项将使战网信息显示在一个浮动信息窗口中。";
OPTION_TOOLTIP_SHOW_TUTORIALS = "显示介绍你如何进行魔兽世界游戏的教程。";
OPTION_TOOLTIP_SHOW_UNIT_NAMES = "在游戏中显示单位名称。";
OPTION_TOOLTIP_SIMPLE_CHAT = "自动设置无法修改设定的主对话窗口和战斗日志窗口。";
OPTION_TOOLTIP_SIMPLE_QUEST_WATCH_TEXT = "自动管理目标追踪框体的尺寸和位置。";
OPTION_TOOLTIP_SMART_PIVOT = "当镜头固定在地面上时，使你可以自由观察。";
OPTION_TOOLTIP_SOUND_CHANNELS = "调整有效软件声道的数量。";
OPTION_TOOLTIP_SOUND_OUTPUT = "选择游戏声音输出设备。";
OPTION_TOOLTIP_SOUND_QUALITY = "调整游戏声音的品质。\n降低此标准可以提高游戏运行速度。";
OPTION_TOOLTIP_SOUND_VOLUME = "调整音效的音量。";
OPTION_TOOLTIP_SPELL_DETAIL = "控制法术效果的细节级别。调低此选项可以提高运行速度。";
OPTION_TOOLTIP_STATUS_BAR = "总是用数字表示经验值、生命值、法力值、怒气值、能量值或快乐值。";
OPTION_TOOLTIP_STATUS_TEXT_PARTY = "总是在你的小队成员的生命条、法力条、怒气条或能量条上显示文字。";
OPTION_TOOLTIP_STATUS_TEXT_PERCENT = "总是以百分比显示状态文字。";
OPTION_TOOLTIP_STATUS_TEXT_PET = "总是在你的宠物的生命条、法力条、或集中条上显示文字。";
OPTION_TOOLTIP_STATUS_TEXT_PLAYER = "总是在你的角色的生命条、法力条、怒气条或能量条上显示文字。";
OPTION_TOOLTIP_STATUS_TEXT_TARGET = "总是在你的目标的生命条、法力条、怒气条、能量条或集中条上显示文字。";
OPTION_TOOLTIP_STEREO_HARDWARE_CURSOR = "勾选此项可以提高游戏运行速度，但是将无法使用3D鼠标功能。";
OPTION_TOOLTIP_STOP_AUTO_ATTACK = "当你切换目标时停止自动攻击。";
OPTION_TOOLTIP_TARGETOFTARGET1 = "当你在一个团队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET2 = "当你在一个小队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET3 = "当你独自冒险时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET4 = "当你在一个团队或小队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET5 = "总是显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET_ALWAYS = "总是显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET_PARTY = "当你在一个小队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET_RAID = "当你在团队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET_RAID_AND_PARTY = "当你在一个团队或小队中时显示目标的目标。";
OPTION_TOOLTIP_TARGETOFTARGET_SOLO = "当你独自冒险时显示目标的目标。";
OPTION_TOOLTIP_TERRAIN_HIGHLIGHTS = "为地形使用镜面高光。禁用此项有时可以提高运行速度。";
OPTION_TOOLTIP_TERRAIN_TEXTURE = "设置两种地貌的混合比率。";
OPTION_TOOLTIP_TEXTURE_DETAIL = "调节所有材质的细节等级。降低此项可以略微提高运行速度。";
OPTION_TOOLTIP_TIMESTAMPS = "选择聊天信息的时间戳格式。";
OPTION_TOOLTIP_TOAST_DURATION = "调整浮动信息窗口的持续时间。";
OPTION_TOOLTIP_TRILINEAR = "开启高质量材质过滤。关闭此选项可以提高运行速度。";
OPTION_TOOLTIP_TRIPLE_BUFFER = "在启用垂直同步时打开三倍缓冲。启用此项可以使画面帧数平均化，但也可能造成轻微的输入延迟。";
OPTION_TOOLTIP_UI_SCALE = "改变用户游戏界面尺寸。";
OPTION_TOOLTIP_UNIT_NAMEPLATES_ALLOW_OVERLAP = "单位姓名板会因为视野而重叠在一起。关掉此选项可以使姓名板相互隔开。";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_ENEMIES = "打开此选项可以显示敌人的姓名板。";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_ENEMY_GUARDIANS = "选中该选项以显示敌方守护者的姓名板";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_ENEMY_PETS = "打开此选项可以显示敌人宠物的姓名板。";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_ENEMY_TOTEMS = "打开此选项可以显示敌人图腾的姓名板";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_FRIENDLY_GUARDIANS = "选中该选项以显示友方守护者的姓名板";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_FRIENDLY_PETS = "打开此选项可以显示友方宠物的姓名板。";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_FRIENDLY_TOTEMS = "选中该选项以显示友方图腾的姓名板";
OPTION_TOOLTIP_UNIT_NAMEPLATES_SHOW_FRIENDS = "打开此选项可以显示友方单位的姓名板。";
OPTION_TOOLTIP_UNIT_NAME_ENEMY = "在游戏中显示敌方玩家的名字。";
OPTION_TOOLTIP_UNIT_NAME_ENEMY_GUARDIANS = "在游戏中显示敌方守护者的名字。";
OPTION_TOOLTIP_UNIT_NAME_ENEMY_PETS = "在游戏中显示敌方宠物的名字。";
OPTION_TOOLTIP_UNIT_NAME_ENEMY_TOTEMS = "在游戏中显示敌方图腾的名字。";
OPTION_TOOLTIP_UNIT_NAME_FRIENDLY = "在游戏中显示友方玩家的名字。";
OPTION_TOOLTIP_UNIT_NAME_FRIENDLY_GUARDIANS = "在游戏中显示友方守护者的名字。";
OPTION_TOOLTIP_UNIT_NAME_FRIENDLY_PETS = "在游戏中显示友方宠物的名字。";
OPTION_TOOLTIP_UNIT_NAME_FRIENDLY_TOTEMS = "在游戏中显示友方图腾的名字。";
OPTION_TOOLTIP_UNIT_NAME_GUILD = "在游戏中显示玩家的公会名。";
OPTION_TOOLTIP_UNIT_NAME_NONCOMBAT_CREATURE = "在游戏中显示小伙伴的名字。";
OPTION_TOOLTIP_UNIT_NAME_NPC = "在游戏中显示NPC的名字。";
OPTION_TOOLTIP_UNIT_NAME_OWN = "在游戏中显示你的角色的名字。";
OPTION_TOOLTIP_UNIT_NAME_PLAYER_TITLE = "在游戏中显示玩家的头衔。";
OPTION_TOOLTIP_USE_COLORBLIND_MODE = "在提示信息中显示物品品质以及目标对你的反应，并在其它一些界面中显示额外的信息。";
OPTION_TOOLTIP_USE_ENGLISH_AUDIO = "勾选此项以覆盖你的语言设置并使用英文语音。";
OPTION_TOOLTIP_USE_REFRESH = "改变显示器刷新率。更高的刷新率下，画面闪烁更少。";
OPTION_TOOLTIP_USE_RESOLUTION = "改变游戏分辨率。降低分辨率可以提高游戏运行速度。";
OPTION_TOOLTIP_USE_UBERTOOLTIPS = "开启屏幕右下角的具体信息提示。";
OPTION_TOOLTIP_USE_UISCALE = "选择则可以使用用户界面比例调节，不选择的话就使用系统默认比例。";
OPTION_TOOLTIP_USE_WEATHER_SHADER = "如果天气效果会导致你的计算机崩溃，就不要开启这一选项。";
OPTION_TOOLTIP_VERTEX_ANIMATION_SHADERS = "开启顶点着色器加速动画。开启这个选项将最大化画面表现。";
OPTION_TOOLTIP_VERTICAL_SYNC = "将你的游戏帧数与显示器刷新率同步。可以解决游戏中的图像无法正常显示的问题。";
OPTION_TOOLTIP_VOICE_ACTIVATION_SENSITIVITY = "调节麦克风的敏感度。";
OPTION_TOOLTIP_VOICE_AMBIENCE = "调整使用语音聊天时的环境音量。";
OPTION_TOOLTIP_VOICE_INPUT = "选择你的麦克风，或者它所连接到的控制器。";
OPTION_TOOLTIP_VOICE_INPUT_VOLUME = "调整你的语音音量。";
OPTION_TOOLTIP_VOICE_MUSIC = "调节使用语音聊天时的游戏音乐音量。";
OPTION_TOOLTIP_VOICE_OUTPUT = "选择语音聊天的发声位置。";
OPTION_TOOLTIP_VOICE_OUTPUT_VOLUME = "调整其他人的语音音量。";
OPTION_TOOLTIP_VOICE_SOUND = "调整使用语音聊天时的游戏音量。";
OPTION_TOOLTIP_VOICE_TYPE1 = "必须先按下指定的按键才能在语音聊天中发言。";
OPTION_TOOLTIP_VOICE_TYPE2 = "只需说话即可进行语音传输。";
OPTION_TOOLTIP_WATCH_FRAME_WIDTH = "增加任务目标追踪器的宽度。";
OPTION_TOOLTIP_WATER_COLLISION = "设置视角，使其在你角色处于水面之上的时候镜头在水面之上，而当你在水下的时候，视角在水面之下。";
OPTION_TOOLTIP_WEATHER_DETAIL = "调节天气效果的表现强度。降低这个效果可以提高运行速度。";
OPTION_TOOLTIP_WINDOWED_MAXIMIZED = "钩选此项以最大化窗口并去除边框。";
OPTION_TOOLTIP_WINDOWED_MODE = "钩选此框，在非全屏幕的窗口模式下游戏。\n\n在窗口模式下，游戏将使用桌面Gamma值，并无法通过下面的滑杆调节。";
OPTION_TOOLTIP_WINDOW_LOCK = "开启此选项后可以使游戏窗口无法被缩放。";
OPTION_TOOLTIP_WORLD_LOD = "开启地形多边型细节动态缩减，打开此选项可以提高运行速度。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY1 = "当你在PvP区域中时显示世界PvP目标。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY2 = "当你在PvP地点附近时显示世界PvP目标。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY3 = "关闭世界PvP目标显示。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY_ALWAYS = "当你在PvP区域中时显示世界PvP目标。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY_DYNAMIC = "当你在PvP地点附近时显示世界PvP目标。";
OPTION_TOOLTIP_WORLD_PVP_DISPLAY_NEVER = "关闭世界PvP目标显示。";
OPTION_TOOLTIP_WOW_MOUSE = "启用此项以启用魔兽世界专用鼠标并对其进行额外的键位绑定。";
OPTION_TOOLTIP_XP_BAR = "总是在你的经验条上显示文字。";
OPTION_UI_DEPTH = "改变UI的基础深度。";
OPTION_USE_EQUIPMENT_MANAGER_DESCRIPTION = "你可以使用该功能来保存装备配置方案。配置完成后点击图标即可实现一键换装。启用后，可以在人物窗口进行装备管理。";
OPT_OUT_LOOT_TITLE = "放弃物品：%s";
OPT_OUT_LOOT_TOGGLE_OFF = "你可以拾取普通物品。";
OPT_OUT_LOOT_TOGGLE_ON = "你放弃所有普通物品。";
OR_CAPS = "或";
OTHER = "其它";
OTHER_MESSAGES = "其它信息";
OUTBID = "开价被压过";
OUTBID_BY = "更高的出价";
OUT_OF_ENERGY = "能量值不足";
OUT_OF_FOCUS = "集中值不足";
OUT_OF_HEALTH = "生命值不足";
OUT_OF_MANA = "法力值不足";
OUT_OF_POWER_DISPLAY = "%s不足";
OUT_OF_RAGE = "怒气值不足";
PAGE_NUMBER = "第%d页";
PALADIN_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
PALADIN_STRENGTH_TOOLTIP = "提高你的近战武器的攻击强度。\n使你用盾牌格挡攻击时减免更多的伤害。";
PAPERDOLLFRAME_TOOLTIP_FORMAT = "%s";
PAPERDOLL_SELECT_TITLE = "选择一个头衔";
PARENS_TEMPLATE = "（%s）";
PARRIED = "招架";
PARRY = "招架";
PARRY_CHANCE = "招架几率";
PARTICLE_DENSITY = "粒子密度";
PARTY = "队伍";
PARTYRAID_LABEL = "小队与团队";
PARTYRAID_SUBTEXT = "这些选项可以帮你调整游戏界面中的小队和团队界面的细节。";
PARTY_CHAT_BUBBLES_TEXT = "组队聊天泡泡";
PARTY_INVITE = "邀请";
PARTY_LEADER = "小队队长";
PARTY_LEAVE = "离开队伍";
PARTY_MESSAGE = "队伍聊天";
PARTY_OPTIONS_LABEL = "组队选项";
PARTY_PROMOTE = "提升为队长";
PARTY_PROMOTE_GUIDE = "提升为向导";
PARTY_QUEST_STATUS_NONE = "你附近没有正在做该任务的队友。";
PARTY_QUEST_STATUS_ON = "附近正在做该任务的队友：";
PARTY_SILENCE = "小队中沉默";
PARTY_UNINVITE = "取消邀请";
PARTY_UNSILENCE = "小队中解除沉默";
PASS = "放弃";
PASSIVE_PARENS = "（被动技能）";
PASSWORD = "密码";
PENDING_INVITE = "等待确认中";
PENDING_INVITE_LIST = "等待列表";
PERCENT_SYMBOL = "%%";
PERIODIC = "周期";
PERIODIC_MESSAGES = "周期效果";
PET = "宠物";
PETITION_CREATOR = "由%s制造";
PETITION_NUM_SIGNATURES = "%d签名";
PETITION_TITLE = "请愿：%s";
PETS = "宠物";
PETTAME_ANOTHERSUMMONACTIVE = "你已经有一个召唤生物了";
PETTAME_CANTCONTROLEXOTIC = "你无法控制特殊生物";
PETTAME_CREATUREALREADYOWNED = "目标生物已经被控制";
PETTAME_DEAD = "你的宠物已经死亡";
PETTAME_INTERNALERROR = "内部宠物错误";
PETTAME_INVALIDCREATURE = "没有找到生物";
PETTAME_NOPETAVAILABLE = "你无法召唤宠物";
PETTAME_NOTDEAD = "你的宠物还未死亡";
PETTAME_NOTTAMEABLE = "目标生物无法被驯服";
PETTAME_TOOHIGHLEVEL = "生物等级太高，无法驯服";
PETTAME_TOOMANY = "你的宠物太多了";
PETTAME_UNITSCANTTAME = "你不能驯服生物";
PETTAME_UNKNOWNERROR = "未知的驯服错误";
PET_ABANDON = "放弃";
PET_ACTION_ATTACK = "攻击";
PET_ACTION_DISMISS = "解散";
PET_ACTION_FOLLOW = "跟随";
PET_ACTION_WAIT = "停留";
PET_AGGRESSIVE = "攻击型";
PET_ATTACK = "攻击";
PET_BONUS_TOOLTIP_ARMOR = "使你的宠物的护甲值提高%d点";
PET_BONUS_TOOLTIP_INTELLECT = "使你的宠物的智力值提高%d点";
PET_BONUS_TOOLTIP_RANGED_ATTACK_POWER = "使你的宠物的攻击强度提高%d点";
PET_BONUS_TOOLTIP_RESISTANCE = "使你的宠物的抗性提高%d点";
PET_BONUS_TOOLTIP_SPELLDAMAGE = "使你的宠物的法术伤害提高%d点";
PET_BONUS_TOOLTIP_STAMINA = "使你的宠物的耐力提高%d点";
PET_BONUS_TOOLTIP_WARLOCK_SPELLDMG_FIRE = "你的火焰伤害可以令你的宠物的攻击强度提高%d，法术伤害提高%d";
PET_BONUS_TOOLTIP_WARLOCK_SPELLDMG_SHADOW = "你的暗影伤害可以令你的宠物的攻击强度提高%d，法术伤害提高%d";
PET_DAMAGE_PERCENTAGE = "造成%d%%的普通伤害";
PET_DEFENSIVE = "防御型";
PET_DIET_TEMPLATE = "食物：%s";
PET_DISMISS = "解散";
PET_FOLLOW = "跟随";
PET_HAPPINESS1 = "不高兴状态";
PET_HAPPINESS2 = "满足";
PET_HAPPINESS3 = "快乐";
PET_INFO = "宠物信息";
PET_MODE_AGGRESSIVE = "攻击型";
PET_MODE_DEFENSIVE = "防御型";
PET_MODE_PASSIVE = "被动型";
PET_PAPERDOLL = "宠物具体信息";
PET_PASSIVE = "被动型";
PET_RENAME = "重命名";
PET_RENAME_CONFIRMATION = "你确定要将宠物命名为'%s'吗？";
PET_RENAME_LABEL = "输入你想要给宠物起的名字：";
PET_SPELLS_TEMPLATE = "驯兽能力：%s";
PET_SPELL_NOPATH = "无路径可供你的宠物使用";
PET_TIME_LEFT_MINUTES = "剩余%d分钟";
PET_TIME_LEFT_SECONDS = "剩余%d秒";
PET_TYPE_DEMON = "恶魔";
PET_TYPE_PET = "宠物";
PET_WAIT = "停留";
PHONG_SHADING = "阴影平滑";
PHYSICAL_HARASSMENT = "行为骚扰";
PHYSICAL_HARASSMENT_DESCRIPTION = "任何个人采取行动不断使别的玩家沮丧，心情恶化或者别的招惹行为的话，此人将被认为在采取行为骚扰或者“消极游戏”。然而，请确定这种行为是在PvP准则范围之外的。";
PHYSICAL_HARASSMENT_TEXT1 = "以下行为不被认为是行为骚扰：";
PHYSICAL_HARASSMENT_TEXT2 = "攻击其他玩家正在攻击的怪物或NPC";
PHYSICAL_HARASSMENT_TEXT3 = "反复攻击和/或杀死NPC";
PHYSICAL_HARASSMENT_TEXT4 = "“守怪物”（为了重复杀死某一种怪物而占据一处怪物刷新的地点）";
PHYSICAL_HARASSMENT_TEXT5 = "“守尸体”（在一名玩家的尸体旁等待，并在他/她复活后对其进行攻击）";
PHYSICAL_HARASSMENT_TEXT6 = "“拉火车”（将敌人引到另一个玩家面前，强迫其作战）";
PIXEL_SHADERS = "特殊效果";
PLAYBACK = "回放";
PLAYED = "场次";
PLAYER = "玩家";
PLAYERSTAT_BASE_STATS = "基础属性";
PLAYERSTAT_DEFENSES = "防御";
PLAYERSTAT_MELEE_COMBAT = "近战";
PLAYERSTAT_RANGED_COMBAT = "远程";
PLAYERSTAT_SPELL_COMBAT = "法术";
PLAYERS_IN_GROUP = "队员";
PLAYER_COUNT_ALLIANCE = "%d联盟玩家";
PLAYER_COUNT_HORDE = "%d部落玩家";
PLAYER_DETAIL = "玩家角色材质";
PLAYER_DIFFICULTY1 = "普通";
PLAYER_DIFFICULTY2 = "英雄";
PLAYER_IS_PVP_AFK = "|cffff64ff%s（隐藏）|r";
PLAYER_LEVEL = "等级%s %s%s";
PLAYER_LEVEL_UP = "升级";
PLAYER_LIST_DELIMITER = "，";
PLAYER_LOGOUT_FAILED = "登出失败";
PLAYER_LOGOUT_FAILED_ERROR = "由于你现在无法坐下，所以无法登出。";
PLAYER_MESSAGES = "玩家信息";
PLAYER_NOT_FOUND = "无法找到指定玩家";
PLAYER_OFFLINE = "离线";
PLAYER_OPTIONS_LABEL = "玩家选项";
PLAYER_SERVER_FIRST_ACHIEVEMENT = "|Hplayer:%s|h[%s]|h获得了成就$a！";
PLAYER_STATUS = "玩家状态";
PLAYER_V_PLAYER = "PvP";
PLAYTIME_TIRED = "您已经进入疲劳游戏时间，您的游戏收益将降为正常值的50%%，为了您的健康，请尽快下线休息，做适当身体活动，合理安排学习生活。";
PLAYTIME_TIRED_ABILITY = "你的在线时间已经超过3小时。在目前阶段下，你不能这么做。在下线休息%d小时后，你的防沉迷时间将会清零。请退出游戏下线休息。";
PLAYTIME_UNHEALTHY = "您已进入不健康游戏时间，为了您的健康，请您立即下线休息。如不下线，您的身体将受到损害，您的收益已降为零，直到您的累计下线时间满5小时后，才能恢复正常。";
PLAYTIME_UNHEALTHY_ABILITY = "你的在线时间已经超过5小时。在目前阶段下，你不能这么做。在下线休息%d小时后，你的防沉迷时间将会清零。请退出游戏，下线休息和运动。";
PLAY_AGGRO_SOUNDS = "播放激怒音效";
PLUS_AMMO_DAMAGE_TEMPLATE = "每秒伤害+%g";
PLUS_AMMO_SCHOOL_DAMAGE_TEMPLATE = "每秒+%g点%s伤害";
PLUS_DAMAGE_TEMPLATE = "+ %d - %d伤害";
PLUS_DAMAGE_TEMPLATE_WITH_SCHOOL = "+%d - %d 点%s伤害";
PLUS_SINGLE_DAMAGE_TEMPLATE = "+ %d伤害";
PLUS_SINGLE_DAMAGE_TEMPLATE_WITH_SCHOOL = "+%d %s伤害";
POP_IN_CHAT = "加入聊天";
POP_OUT_CHAT = "弹出聊天窗口";
POTION_TIMER = "你已经喝得太饱了。";
POWER_ABBR = "PWR";
POWER_DISPLAY_COST = "%d %s";
POWER_DISPLAY_COST_PER_TIME = "%d %s，再加上每秒%d";
POWER_GAINS = "获得能量";
POWER_GAINS_COMBATLOG_TOOLTIP = "当法术或技能恢复法力、能量、怒气、集中或快乐值时显示信息。";
POWER_TYPE_BLOOD_POWER = "鲜血能量";
POWER_TYPE_HEAT = "热能";
POWER_TYPE_OOZE = "软泥";
POWER_TYPE_PYRITE = "蓝铁";
POWER_TYPE_STEAM = "蒸汽动力";
POWER_TYPE_WRATH = "愤怒";
PREFERENCES = "偏好选择";
PRESS_TAB = "按下Tab键";
PREV = "上一页";
PREVIEW_TALENT_CHANGES = "预览天赋改变";
PREVIOUS = "返回";
PRIEST_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
PRIMARY = "主要";
PRIMARY_SKILLS = "主要技能：";
PROC_EVENT0_DESC = "No Proc Trigger Assigned. Tell Kevin.";
PROC_EVENT1024_DESC = "当被法术击中的时候";
PROC_EVENT128_DESC = "挥舞时";
PROC_EVENT16_DESC = "躲闪时";
PROC_EVENT1_DESC = "击中目标时";
PROC_EVENT2048_DESC = "当被%s法术击中的时候";
PROC_EVENT256_DESC = "施放时";
PROC_EVENT2_DESC = "被击中时";
PROC_EVENT32_DESC = "招架时";
PROC_EVENT3_DESC = "击中目标或被击中时";
PROC_EVENT4_DESC = "杀死目标时";
PROC_EVENT512_DESC = "施放%s时";
PROC_EVENT64_DESC = "格挡时";
PROC_EVENT8_DESC = "每个tick";
PROFANITY_FILTER = "语言过滤器";
PROFESSION_CONFIRMATION1 = "你只能学习两个专业。你要学习|cffffd200%s|r作为你的第一个专业吗？";
PROFESSION_CONFIRMATION2 = "你只能学习两个专业。你要学习|cffffd200%s|r作为你的第二个专业吗？";
PROFFESSION_CONFIRMATION2 = "你只能学习两个专业。你要把%s作为你的第二个专业吗？";
PROFICIENCIES = "精通：";
PROFICIENCIES_COLON = "精通：";
PROFICIENCY_NEEDED = "你不会使用这种物品。";
PROJECTED_TEXTURES = "材质投射";
PTT_BOUND = "按键绑定";
PUBLICNOTE_BUTTON_TOOLTIP = "点击浏览你的公共信息";
PUBLIC_NOTE = "公共信息";
PURCHASE = "购买";
PURCHASED_BY_COLON = "购买者：";
PURCHASE_TAB_TEXT = "你想要购买这个标签吗？";
PUSHTOTALK_SOUND_TEXT = "按键发言声音";
PUSH_TO_TALK = "按键发言";
PVP = "PvP";
PVPBATTLEGROUND_WINTERGRASPTIMER = "冬拥湖|n|cffffffff%s|r";
PVPBATTLEGROUND_WINTERGRASPTIMER_CANNOT_QUEUE = "|cffffffff你现在不能进入冬拥湖的队列。|n战斗即将开始时队列会对你开放。|r";
PVPBATTLEGROUND_WINTERGRASPTIMER_CAN_QUEUE = "|cffffffff你现在可以进入冬拥湖的队列了！与|n任何主城中的冬拥湖战场军官交谈即|n可加入战斗。|r";
PVPBATTLEGROUND_WINTERGRASPTIMER_TOOLTIP = "下一场冬拥湖战斗：|cffffffff%s|r";
PVPFFA = "自由PvP";
PVP_DISABLED = "PvP未开启";
PVP_ENABLED = "PvP";
PVP_FLAG = "PvP";
PVP_LABEL_ARENA = "竞技场：";
PVP_LABEL_HONOR = "荣誉：";
PVP_MEDAL1 = "暴风城守卫者";
PVP_MEDAL2 = "奥格瑞玛的领主";
PVP_MEDAL3 = "铁炉堡领主";
PVP_MEDAL4 = "达纳苏斯高阶哨兵";
PVP_MEDAL5 = "幽暗城的死亡领主";
PVP_MEDAL6 = "雷霆崖酋长";
PVP_MEDAL7 = "诺莫瑞根复仇者";
PVP_MEDAL8 = "巫毒领袖森金";
PVP_MINIMAP = "显示PvP小地图";
PVP_OPTIONS = "PvP";
PVP_POLICY_URL = "|cffffd200https://www.battlenet.com.cn/support/article.xml?locale=zh_CN&articleId=27424|r";
PVP_RANK_0_0 = "斥候";
PVP_RANK_0_0_FEMALE = "斥候";
PVP_RANK_0_1 = "下士";
PVP_RANK_0_1_FEMALE = "下士";
PVP_RANK_10_0 = "石头守卫";
PVP_RANK_10_0_FEMALE = "石头守卫";
PVP_RANK_10_1 = "骑士";
PVP_RANK_10_1_FEMALE = "骑士";
PVP_RANK_11_0 = "血卫士";
PVP_RANK_11_0_FEMALE = "血卫士";
PVP_RANK_11_1 = "骑士中尉";
PVP_RANK_11_1_FEMALE = "骑士中尉";
PVP_RANK_12_0 = "军团士兵";
PVP_RANK_12_0_FEMALE = "军团士兵";
PVP_RANK_12_1 = "骑士队长";
PVP_RANK_12_1_FEMALE = "骑士队长";
PVP_RANK_13_0 = "百夫长";
PVP_RANK_13_0_FEMALE = "百夫长";
PVP_RANK_13_1 = "护卫骑士";
PVP_RANK_13_1_FEMALE = "护卫骑士";
PVP_RANK_14_0 = "勇士";
PVP_RANK_14_0_FEMALE = "勇士";
PVP_RANK_14_1 = "少校";
PVP_RANK_14_1_FEMALE = "少校";
PVP_RANK_15_0 = "中将";
PVP_RANK_15_0_FEMALE = "中将";
PVP_RANK_15_1 = "司令";
PVP_RANK_15_1_FEMALE = "司令";
PVP_RANK_16_0 = "将军";
PVP_RANK_16_0_FEMALE = "将军";
PVP_RANK_16_1 = "统帅";
PVP_RANK_16_1_FEMALE = "统帅";
PVP_RANK_17_0 = "督军";
PVP_RANK_17_0_FEMALE = "督军";
PVP_RANK_17_1 = "元帅";
PVP_RANK_17_1_FEMALE = "元帅";
PVP_RANK_18_0 = "高阶督军";
PVP_RANK_18_0_FEMALE = "高阶督军";
PVP_RANK_18_1 = "大元帅";
PVP_RANK_18_1_FEMALE = "大元帅";
PVP_RANK_19_0 = "领袖";
PVP_RANK_19_0_FEMALE = "领袖";
PVP_RANK_19_1 = "领袖";
PVP_RANK_19_1_FEMALE = "领袖";
PVP_RANK_1_0 = "无赖";
PVP_RANK_1_0_FEMALE = "无赖";
PVP_RANK_1_1 = "无赖";
PVP_RANK_1_1_FEMALE = "无赖";
PVP_RANK_2_0 = "逃犯";
PVP_RANK_2_0_FEMALE = "逃犯";
PVP_RANK_2_1 = "逃犯";
PVP_RANK_2_1_FEMALE = "逃犯";
PVP_RANK_3_0 = "流亡者";
PVP_RANK_3_0_FEMALE = "流亡者";
PVP_RANK_3_1 = "流亡者";
PVP_RANK_3_1_FEMALE = "流亡者";
PVP_RANK_4_0 = "耻辱者";
PVP_RANK_4_0_FEMALE = "耻辱者";
PVP_RANK_4_1 = "耻辱者";
PVP_RANK_4_1_FEMALE = "耻辱者";
PVP_RANK_5_0 = "侦察兵";
PVP_RANK_5_0_FEMALE = "侦察兵";
PVP_RANK_5_1 = "列兵";
PVP_RANK_5_1_FEMALE = "列兵";
PVP_RANK_6_0 = "步兵";
PVP_RANK_6_0_FEMALE = "步兵";
PVP_RANK_6_1 = "下士";
PVP_RANK_6_1_FEMALE = "下士";
PVP_RANK_7_0 = "中士";
PVP_RANK_7_0_FEMALE = "中士";
PVP_RANK_7_1 = "中士";
PVP_RANK_7_1_FEMALE = "中士";
PVP_RANK_8_0 = "高阶军士";
PVP_RANK_8_0_FEMALE = "高阶军士";
PVP_RANK_8_1 = "军士长";
PVP_RANK_8_1_FEMALE = "军士长";
PVP_RANK_9_0 = "一等军士长";
PVP_RANK_9_0_FEMALE = "一等军士长";
PVP_RANK_9_1 = "士官长";
PVP_RANK_9_1_FEMALE = "士官长";
PVP_RANK_LEADER = "阵营领袖";
PVP_RATING = "等级：";
PVP_REPORT_AFK = "举报离开玩家";
PVP_REPORT_AFK_ALL = "全部举报";
PVP_REPORT_AFK_ALREADY_NOTIFIED = "举报离开玩家：你已经举报了该玩家。";
PVP_REPORT_AFK_GENERIC_FAILURE = "举报离开玩家失败。";
PVP_REPORT_AFK_NOT_SAME_TEAM = "举报离开玩家失败：你不在该玩家的队伍中。";
PVP_REPORT_AFK_PLAYER_NOT_VALID = "举报离开玩家失败：目标无效。";
PVP_REPORT_AFK_SUCCEEDED = "已成功举报暂离玩家。";
PVP_REPORT_AFK_SYSTEM_DISABLED = "通知系统已被禁用。";
PVP_REPORT_AFK_SYSTEM_ENABLED = "通知系统已被启用。";
PVP_REQUIRED_FOR_CAPTURE = "你必须开启PvP才能夺取目标。";
PVP_TEAMSIZE = "(%dv%d)";
PVP_TEAMTYPE = "%dv%d";
PVP_TOGGLE_OFF_VERBOSE = "当你身处友方领地，并且在5分钟内不进行任何PvP动作之后，你将离开PvP状态。";
PVP_TOGGLE_ON_VERBOSE = "你现在被标记为可以进行PvP战斗，该状态将一直持续到你关闭PvP战斗标记。";
PVP_YOUR_RATING = "你的等级";
PVP_ZONE_OBJECTIVES = "显示PvP区域目标";
QUALITY = "质量";
QUESTLOG_BUTTON = "任务日志";
QUESTLOG_NO_QUESTS_TEXT = "没有正在进行的任务";
QUESTS_COLON = "任务：";
QUESTS_LABEL = "任务";
QUESTS_SUBTEXT = "这些选项可以令你自定义游戏中的任务界面细节。";
QUEST_ACCEPT = "%s%s\n";
QUEST_ACCEPT_LOG_FULL = "%s正在开始%s任务\n你的任务纪录已满。如果你设法在任务纪录中\n空出位置，你也可以参与此任务。";
QUEST_COMPLETE = "任务完成";
QUEST_DASH = "-";
QUEST_DESCRIPTION = "描述";
QUEST_DETAILS = "任务细节";
QUEST_FACTION_NEEDED = "%s：%s / %s";
QUEST_FACTION_NEEDED_NOPROGRESS = "%s：%s";
QUEST_FAILED = "任务失败";
QUEST_FAILED_TAG = "- 失败";
QUEST_HARD = "（困难）";
QUEST_INTERMEDIATE_ITEMS_NEEDED = "%s：(%d)";
QUEST_ITEMS_NEEDED = "%s：%d/%d";
QUEST_ITEMS_NEEDED_NOPROGRESS = "%s x %d";
QUEST_LOG = "任务日志";
QUEST_LOG_COUNT_TEMPLATE = "任务：|cffffffff%d/%d|r";
QUEST_LOG_DAILY_COUNT_TEMPLATE = "日常：|cffffffff%d/%d|r";
QUEST_LOG_DAILY_TOOLTIP = "你每天只能完成%d个日常任务。\n新的一天将在%s后开始。";
QUEST_MONSTERS_KILLED = "已杀死%s：%d/%d";
QUEST_MONSTERS_KILLED_NOPROGRESS = "%s x %d";
QUEST_OBJECTIVES = "任务目标";
QUEST_OBJECTS_FOUND = "%s：%d/%d";
QUEST_OBJECTS_FOUND_NOPROGRESS = "%s x %d";
QUEST_PLAYERS_KILLED = "已杀死玩家：%d/%d";
QUEST_PLAYERS_KILLED_NOPROGRESS = "玩家 x %d";
QUEST_REWARDS = "奖励";
QUEST_SUGGESTED_GROUP_NUM = "建议玩家人数：[%d]";
QUEST_SUGGESTED_GROUP_NUM_TAG = "队伍：%d";
QUEST_TIMERS = "任务计时";
QUEST_TOOLTIP_ACTIVE = "你已经在做这个任务了";
QUEST_TOOLTIP_REQUIREMENTS = "要求：";
QUEST_WATCH_NO_OBJECTIVES = "这项任务没有可以追踪的任务目标";
QUEST_WATCH_TOOLTIP = "按住Shift点击一个任务可以将其加入你的任务追踪列表，或者从任务追踪列表中删除。";
QUEST_WATCH_TOO_MANY = "你最多同时追踪%d个任务。";
QUEUED_FOR = "已在%s的队列中";
QUEUED_FOR_SHORT = "已在队列中：";
QUEUE_TIME_UNAVAILABLE = "不可预估";
QUICKBUTTON_NAME_DEFAULT = "默认";
QUICKBUTTON_NAME_EVERYTHING = "所有";
QUICKBUTTON_NAME_EVERYTHING_TOOLTIP = "显示所有战斗信息。";
QUICKBUTTON_NAME_FRIENDS = "友方";
QUICKBUTTON_NAME_KILLS = "杀敌";
QUICKBUTTON_NAME_KILLS_TOOLTIP = "显示所有死亡和杀敌信息。";
QUICKBUTTON_NAME_ME = "我发生了什么？";
QUICKBUTTON_NAME_ME_TOOLTIP = "显示我所接收的所有行为信息。";
QUICKBUTTON_NAME_SELF = "自己";
QUICKBUTTON_NAME_SELF_TOOLTIP = "显示你所进行的动作和你受到的动作的信息。";
QUICK_BUTTON_COMBATLOG_TOOLTIP = "在聊天窗口中放置一个该过滤条件的快捷方式。";
QUIT = "退出";
QUIT_NOW = "立刻退出";
QUIT_TIMER = "%d%s后退出游戏";
RACE = "种族";
RACE_CLASS_ONLY = "%s专用";
RACIAL_SKILLS = "%s技能：";
RAF_GRANT_LEVEL = "提升等级";
RAF_SUMMON = "召唤战友";
RAF_SUMMON_LINKED = "召唤已链接的战友";
RAF_SUMMON_WITH_COOLDOWN = "召唤好友(%s)";
RAGE = "怒气";
RAGE_COST = "%d怒气";
RAGE_COST_PER_TIME = "%d怒气，外加每秒%d";
RAID = "团队";
RAIDOPTIONS_MENU = "团队设置";
RAID_AND_PARTY = "团队&小队";
RAID_ASSISTANT = "团队助理";
RAID_ASSISTANT_TOKEN = "A";
RAID_BOSS_MESSAGE = "首领怪物";
RAID_BROWSER_DESCRIPTION = "寻找一个团队或组建一个团队";
RAID_CONTROL = "团队管理";
RAID_DESCRIPTION = "团队是超过5个人的队伍，这是为了击败高等级的特定挑战而准备的大型队伍模式。\n\n|cffffffff- 团队成员无法获得非团队任务所需的物品或者杀死怪物的纪录。\n\n- 在团队中，你通过杀死怪物获得的经验值相对普通小队要少。\n\n- 团队让你可以赢得用其它方法根本无法通过的挑战。|r";
RAID_DIFFICULTY = "团队副本难度";
RAID_DIFFICULTY1 = "10人";
RAID_DIFFICULTY2 = "25人";
RAID_DIFFICULTY3 = "10人（英雄）";
RAID_DIFFICULTY4 = "25人（英雄）";
RAID_DIFFICULTY_10PLAYER = "10人";
RAID_DIFFICULTY_10PLAYER_HEROIC = "10人（英雄）";
RAID_DIFFICULTY_20PLAYER = "20人";
RAID_DIFFICULTY_25PLAYER = "25人";
RAID_DIFFICULTY_25PLAYER_HEROIC = "25人（英雄）";
RAID_DIFFICULTY_40PLAYER = "40人";
RAID_GROUPS = "团队";
RAID_INFO = "团队信息";
RAID_INFORMATION = "团队信息";
RAID_INFO_DESC = "你的已保存的团队副本信息。";
RAID_INSTANCE_EXPIRED = "你在副本%s中的锁定状态结束了。";
RAID_INSTANCE_EXPIRES = "%1$s内结束";
RAID_INSTANCE_EXPIRES_EXPIRED = "已过期";
RAID_INSTANCE_EXPIRES_EXTENDED = "%1$s内结束（已延长）";
RAID_INSTANCE_INFO_FMT = "%s (ID=%lx)：%s";
RAID_INSTANCE_INFO_HDR = "团队副本倒计时：";
RAID_INSTANCE_LOCK_EXTENDED = "你在副本%s中的锁定状态持续时间延长了。";
RAID_INSTANCE_LOCK_NOT_EXTENDED = "你在副本%s中的锁定状态不再延长了。";
RAID_INSTANCE_WARNING_HOURS = "你在副本%s中的锁定状态将在%d小时后结束。";
RAID_INSTANCE_WARNING_MIN = "你在副本%s中的锁定状态将在%d分钟后结束！";
RAID_INSTANCE_WARNING_MIN_SOON = "你在副本%s中的锁定状态将在%d小时后结束。";
RAID_INSTANCE_WELCOME = "欢迎来到%s。这个副本的锁定状态预计在%s后结束。";
RAID_INSTANCE_WELCOME_DH = "%s将在%d天%d小时后结束。";
RAID_INSTANCE_WELCOME_EXTENDED = "欢迎来到%s。你在这个副本中的锁定状态持续时间延长了。";
RAID_INSTANCE_WELCOME_HM = "%s将在%d小时%d分钟后结束。";
RAID_INSTANCE_WELCOME_LOCKED = "欢迎来到%s。你在这个副本中的锁定状态预计在%s后结束。";
RAID_INSTANCE_WELCOME_LOCKED_EXTENDED = "欢迎来到%s。你在这个副本中的锁定状态预计在%s后结束（已延长）。";
RAID_LEADER = "团队领袖";
RAID_LEADER_TOKEN = "L";
RAID_MEMBERS_AFK = "下列玩家正处于离开状态：%s";
RAID_MEMBER_NOT_READY = "%s 还未准备就绪";
RAID_MESSAGE = "团队";
RAID_SILENCE = "团队中沉默";
RAID_TARGET_1 = "星形";
RAID_TARGET_2 = "圆形";
RAID_TARGET_3 = "菱形";
RAID_TARGET_4 = "三角";
RAID_TARGET_5 = "月亮";
RAID_TARGET_6 = "方块";
RAID_TARGET_7 = "十字";
RAID_TARGET_8 = "骷髅";
RAID_TARGET_ICON = "团队目标图标";
RAID_TARGET_NONE = "无";
RAID_UNSILENCE = "团队中解除沉默";
RAID_WARNING = "团队通知";
RAID_WARNING_MESSAGE = "团队通知";
RALT_KEY_TEXT = "右ALT";
RANDOM_BATTLEGROUND = "随机战场";
RANDOM_BATTLEGROUND_EXPLANATION = "完成随机战场可使你获得额外奖励。";
RANDOM_DUNGEON_IS_READY = "你的随机地下城小队已经整装待发！";
RANDOM_ROLL_RESULT = "%s掷出%d（%d-%d）";
RANGED = "远程";
RANGEDSLOT = "远程";
RANGED_ATTACK = "远程攻击";
RANGED_ATTACK_POWER = "远程攻击强度";
RANGED_ATTACK_POWER_TOOLTIP = "使远程武器的每秒伤害提高%.1f。";
RANGED_ATTACK_TOOLTIP = "远程攻击等级";
RANGED_COMBATLOG_TOOLTIP = "显示弓箭、枪械、投掷武器和魔杖的攻击。";
RANGED_CRIT_CHANCE = "爆击率";
RANGED_DAMAGE_TOOLTIP = "远程伤害";
RANGE_DAMAGE_COMBATLOG_TOOLTIP = "显示造成了全额或部分伤害的远程射击。";
RANGE_MISSED_COMBATLOG_TOOLTIP = "显示未能造成伤害的远程射击。";
RANK = "级别";
RANK_COLON = "级别：";
RANK_POSITION = "排名";
RARITY = "稀有程度";
RATING = "等级";
RATINGS_MENU = "等级";
RATINGS_TEXT = "韩国评级";
RATING_CHANGE_TOOLTIP = "总等级改变";
RCTRL_KEY_TEXT = "右CTRL";
REACTIVATE_RAID_LOCK = "重新激活副本锁定";
READY = "就绪";
READY_CHECK = "就位确认";
READY_CHECK_ALL_READY = "所有人都已准备就绪";
READY_CHECK_FINISHED = "就位检查完成";
READY_CHECK_MESSAGE = "%s正在进行就位确认。\n|cffffffff你准备好了吗？|r";
READY_CHECK_NO_AFK = "无人处于离开状态";
READY_CHECK_START = "开始就位确认…";
READY_CHECK_YOU_WERE_AFK = "你在就位确认时处于离开状态";
RECOVER_CORPSE = "现在复活吗？";
RECOVER_CORPSE_INSTANCE = "你必须进入副本才能捡回你的尸体。";
RECOVER_CORPSE_TIMER = "%d%s后复活";
RED_GEM = "红色";
REFLECT = "反射";
REFRESH = "刷新";
REFRESH_RATE = "刷新";
REFUND_TIME_REMAINING = "在%s内将这件物品卖给商人可以获得全额退款";
RELICSLOT = "圣物";
REMOVE = "移除";
REMOVE_BLOCK = "移除";
REMOVE_CHAT_DELAY_TEXT = "移除聊天标签弹出延迟";
REMOVE_FRIEND = "删除好友";
REMOVE_FRIEND_CONFIRMATION = "你确定要将%s移出实名好友名单？";
REMOVE_GUILDMEMBER_LABEL = "你确定想要从公会中开除%s吗？";
REMOVE_IGNORE = "移除";
REMOVE_MODERATOR = "移除管理员";
REMOVE_MUTE = "移除";
REMOVE_PLAYER = "移除玩家";
RENAME_ARENA_TEAM = "战队重命名";
RENAME_ARENA_TEAM_LABEL = "输入一个新的竞技场战队名：";
RENAME_CHAT_WINDOW = "重命名窗口";
RENAME_GUILD = "公会重命名";
RENAME_GUILD_LABEL = "输入新的公会名：";
REPAIR_ALL_ITEMS = "修理所有物品";
REPAIR_AN_ITEM = "修理一件物品";
REPAIR_COST = "修理花费：";
REPAIR_ITEMS = "修理物品";
REPLACE_ENCHANT = "你要将\"%s\"替换为\"%s\"吗？";
REPLY_MESSAGE = "回复";
REPORT_MULTIPLE_PVP_AFK_SENT = "已为所有选定玩家发送通知。";
REPORT_PHYSICAL_HARASSMENT = "报告行为骚扰";
REPORT_PVP_AFK_SENT = "已为%s发送通知。";
REPORT_SPAM = "举报骚扰";
REPORT_SPAM_CONFIRMATION = "你确定要举报%s为骚扰者吗？";
REPORT_VERBAL_HARASSMENT = "报告语言骚扰";
REPUTATION = "声望";
REPUTATION_ABBR = "声望";
REPUTATION_AT_WAR_DESCRIPTION = "决定该派别的成员对你的反应。如果你钩选了交战状态框，那么你就可以攻击他们。如果你没有钩选交战状态框，则不会对他们进行攻击。";
REPUTATION_FACTION_DESCRIPTION = "这里列出的是你与各个阵营派别的声望。";
REPUTATION_MOVE_TO_INACTIVE = "将声望条移动到你的声望列表最底部的隐藏分类中。对于归类你不再关心的声望非常有用。";
REPUTATION_SHOW_AS_XP = "在你的技能栏上方显示声望栏。";
REPUTATION_STANDING_DESCRIPTION = "声望条越偏右，你在该阵营派别中的声望就越高。声望从低到高分为多个级别：仇恨、敌对、冷淡、中立、友善、尊敬、崇敬、崇拜。";
REPUTATION_STATUS_AT_PEACE = "反选此框即进入和平状态。";
REPUTATION_STATUS_AT_WAR = "你正在与该派别处于交战状态。";
REPUTATION_STATUS_NOT_AT_PEACE = "钩选此框即进入交战状态。";
REPUTATION_STATUS_PERMANENT_AT_PEACE = "你不能与己方派别开战！";
REPUTATION_STATUS_PERMANENT_AT_WAR = "你无法与%s保持和平关系。";
REQUEST_SIGNATURE = "要求签名";
REQUIRED_MONEY = "需要金钱：";
REQUIRES_LABEL = "需要：";
REQUIRES_RUNIC_POWER = "需要符文能量";
RESET = "重置";
RESETS_IN = "结束时间";
RESET_ALL_WINDOWS = "重置聊天窗口";
RESET_CHAT_WINDOW = "将你的聊天窗口重置为默认设置。\n你会失去所有自定义设置。";
RESET_FAILED_NOTIFY = "小队的队长试图重置你所在的副本。请退出该副本以重置。";
RESET_INSTANCES = "重置所有副本";
RESET_TO_DEFAULT = "恢复默认设置";
RESET_TUTORIALS = "重置教程";
RESILIENCE = "韧性";
RESILIENCE_ABBR = "Resil";
RESILIENCE_TOOLTIP = "使敌人对你造成爆击的几率降低%.2f%%。\n使法力吸取效果和受到爆击时承受的伤害降低%.2f%%。\n使玩家和他们的宠物或奴仆对你造成的所有伤害降低额外%.2f%%。";
RESIST = "抵抗";
RESISTANCE0_NAME = "护甲";
RESISTANCE1_NAME = "神圣抗性";
RESISTANCE2_NAME = "火焰抗性";
RESISTANCE3_NAME = "自然抗性";
RESISTANCE4_NAME = "冰霜抗性";
RESISTANCE5_NAME = "暗影抗性";
RESISTANCE6_NAME = "奥术抗性";
RESISTANCE_EXCELLENT = "极高";
RESISTANCE_FAIR = "一般";
RESISTANCE_GOOD = "较高";
RESISTANCE_LABEL = "抗性";
RESISTANCE_NONE = "无";
RESISTANCE_POOR = "较低";
RESISTANCE_TEMPLATE = "%d%s";
RESISTANCE_TOOLTIP_SUBTEXT = "提高你对%s系攻击、法术和技能的抵抗能力。\n对%d级敌人的抗性：|cffffffff%s|r";
RESISTANCE_TYPE0 = "护甲";
RESISTANCE_TYPE1 = "神圣";
RESISTANCE_TYPE2 = "火焰";
RESISTANCE_TYPE3 = "自然";
RESISTANCE_TYPE4 = "冰霜";
RESISTANCE_TYPE5 = "暗影";
RESISTANCE_TYPE6 = "奥术";
RESISTANCE_VERYGOOD = "很高";
RESIST_TRAILER = "(%d点被抵抗)";
RESOLUTION = "分辨率";
RESOLUTION_LABEL = "分辨率";
RESOLUTION_SUBTEXT = "你可以通过这些选项修改显示硬件渲染游戏画面的尺寸和细节层次。";
RESURRECT = "复活";
RESURRECTABLE = "可复活";
RESURRECT_REQUEST = "%s想要复活你。一旦这样复活，你将会进入复活虚弱状态";
RESURRECT_REQUEST_NO_SICKNESS = "%s想要复活你";
RESURRECT_REQUEST_NO_SICKNESS_TIMER = "%s要复活你，%d%s内生效";
RESURRECT_REQUEST_TIMER = "%s要复活你，%d%s内生效。一旦这样复活，你将会进入复活虚弱状态。";
RETRIEVING_ITEM_INFO = "正在获取物品信息";
RETURN_TO_GAME = "返回游戏";
RETURN_TO_WORLD = "离开战场";
REWARD_AURA = "该法术将被施放在你身上：";
REWARD_CHOICES = "你可以从这些奖励品中选择一件：";
REWARD_CHOOSE = "选择你的奖励品：";
REWARD_ITEMS = "你还将得到：";
REWARD_ITEMS_ONLY = "你将得到：";
REWARD_REPUTATION = "  %s：";
REWARD_REPUTATION_TEXT = "声望奖励：";
REWARD_SPELL = "你将学会：";
REWARD_TITLE = "你将获得头衔：";
REWARD_TRADESKILL_SPELL = "你将学会如何制造：";
RID_FRIEND_REQUEST_INFO = "这应该是你在现实中认识并信任的朋友。你能通过任何暴雪游戏与他进行聊天。如果你接受了他们的好友请求，他们的所有好友都将能够看到你的名字。";
RIGHT_CLICK_MESSAGE = "|cffffffff<右键点击> 设置PvP选项|r";
ROGUE_AGILITY_TOOLTIP = "提高你的近战武器和远程武器的攻击强度，并提高所有武器的爆击几率。|n提高你的护甲值和躲避攻击的几率。";
ROLE = "职责";
ROLE_CHECK_IN_PROGRESS_TOOLTIP = "等候所有玩家确认职责中……";
ROLE_DESCRIPTION1 = "表示你愿意担当对敌人输出伤害的职责。";
ROLE_DESCRIPTION2 = "表示你愿意通过使敌人攻击自己，保护队友不受攻击。";
ROLE_DESCRIPTION3 = "表示你愿意在队友受到伤害时为他们提供治疗。";
ROLL_DISENCHANT = "分解";
ROLL_DISENCHANT_NEWBIE = "你想选择贪婪取向，但你会在赢得该物品后将其分解。";
ROTATE_MINIMAP = "旋转微缩地图";
RSHIFT_KEY_TEXT = "右SHIFT";
RUNES = "符文";
RUNE_COST_BLOOD = "%d鲜血";
RUNE_COST_FROST = "%d冰霜";
RUNE_COST_ONGOING = "进行中";
RUNE_COST_UNHOLY = "%d邪恶";
RUNIC_POWER = "符文能量";
RUNIC_POWER_COST = "%d符文能量";
RUNIC_POWER_COST_PER_TIME = "%d点符文能量，外加每秒%d点";
RURU = "俄语";
RUSSIAN_DECLENSION = "Declensions";
RUSSIAN_DECLENSION_1 = "Genitive Case";
RUSSIAN_DECLENSION_2 = "Dative Case";
RUSSIAN_DECLENSION_3 = "Accusative Case";
RUSSIAN_DECLENSION_4 = "Instrumental Case";
RUSSIAN_DECLENSION_5 = "Prepositional Case";
RUSSIAN_DECLENSION_EXAMPLE_1 = "我总是与%s一起旅行。";
RUSSIAN_DECLENSION_EXAMPLE_2 = "昨天我敲了%s的脑袋。";
RUSSIAN_DECLENSION_EXAMPLE_3 = "今天我又见到了%s。";
RUSSIAN_DECLENSION_EXAMPLE_4 = "现在我与%s是朋友了。";
RUSSIAN_DECLENSION_EXAMPLE_5 = "我对%s一无所知。";
SALE_PRICE_COLON = "售价：";
SANCTUARY_TERRITORY = "（安全区域）";
SAVE = "保存";
SAVE_CHANGES = "保存改变";
SAY = "说";
SAY_MESSAGE = "说";
SCORE_DAMAGE_DONE = "伤害量";
SCORE_FLAGS_CAPTURED = "军旗\n夺取";
SCORE_FLAGS_RETURNED = "军旗\n归还";
SCORE_HEALING_DONE = "治疗量";
SCORE_HONORABLE_KILLS = "击杀";
SCORE_HONOR_GAINED = "荣誉\n获取";
SCORE_KILLING_BLOWS = "杀敌";
SCORE_POWER_UPS = "增强物品";
SCORE_RATING_CHANGE = "等级\n改变";
SCORE_TEAM_SKILL = "匹配值";
SCREENSHOT_FAILURE = "截图失败";
SCREENSHOT_SUCCESS = "截图完成";
SEARCH = "搜索";
SEARCHING_FOR_GROUPS_NEEDS = "你在的队列中的角色：";
SEARCHING_FOR_ITEMS = "搜索物品";
SECONDARY = "次要";
SECONDARYHANDSLOT = "副手";
SECONDARY_SKILLS = "次要技能：";
SECONDS = "|4秒:秒;";
SECONDS_ABBR = "%d|4秒:秒;";
SECOND_NUMBER_CAP = " M";
SECOND_ONELETTER_ABBR = "%d s";
SECURE_ABILITY_TOGGLE = "技能锁定";
SELECT_CATEGORY = "——> 选择一个类别";
SELFMUTED = "|cffff0000（自我禁声）|r";
SELL_PRICE = "卖价";
SENDMAIL = "发件箱";
SENDMAIL_TEXT = "在这里放置物品和/或钱币";
SEND_BUG = "提交Bug";
SEND_LABEL = "发送";
SEND_MAIL_COST = "邮资：";
SEND_MESSAGE = "发送信息";
SEND_MONEY = "发送钱币";
SEND_MONEY_CONFIRMATION = "确定要将下列金额发送给%s吗？";
SEND_REQUEST = "发送请求";
SEND_SUGGEST = "发送建议";
SERVER_CHANNELS = "服务器频道";
SERVER_FIRST_ACHIEVEMENT = "%s获得了成就$a！";
SERVER_MESSAGE_COLON = "提醒：";
SERVER_MESSAGE_PREFIX = "[服务器]";
SETTINGS = "设置";
SET_COMMENT_LABEL = "设置注释：";
SET_FOCUS = "设置焦点";
SET_FRIENDNOTE_LABEL = "为%s设置提示：";
SET_GUILDMOTD_LABEL = "设置今日公会通知：";
SET_GUILDOFFICERNOTE_LABEL = "设置公会官员信息";
SET_GUILDPLAYERNOTE_LABEL = "设置玩家信息";
SET_MAIN_ASSIST = "提升为主助理";
SET_MAIN_TANK = "提升为主坦克";
SET_NOTE = "设置备注";
SET_RAID_ASSISTANT = "提升为助理";
SET_RAID_LEADER = "提升为团队领袖";
SHADOW_QUALITY = "阴影质量";
SHAMAN_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
SHAMAN_STRENGTH_TOOLTIP = "提高你的近战武器的攻击强度。\n使你用盾牌格挡攻击时减免更多的伤害。";
SHARDS = "Shards";
SHARE_QUEST = "共享任务";
SHARE_QUEST_ABBREV = "共享";
SHARE_QUEST_TEXT = "如果你队伍的成员符合条件的话\n你可以和他分享一个任务。\n有些任务，比如由物品激发的\n任务，无法进行分享。";
SHIELDSLOT = "盾牌";
SHIELD_BLOCK_TEMPLATE = "%d格挡";
SHIFT_KEY = "SHIFT键";
SHIFT_KEY_TEXT = "SHIFT";
SHIRTSLOT = "衬衣";
SHORTDATE = "%2$d/%1$02d/%3$02d";
SHOULDERSLOT = "肩部";
SHOW_ALL_SPELL_RANKS = "显示所有法术等级";
SHOW_ARENA_ENEMY_CASTBAR_TEXT = "显示施法条";
SHOW_ARENA_ENEMY_FRAMES_TEXT = "竞技场对手框体";
SHOW_ARENA_ENEMY_PETS_TEXT = "显示宠物";
SHOW_BATTLEFIELDMINIMAP_PLAYERS = "显示队友";
SHOW_BATTLENET_TOASTS = "战网浮窗";
SHOW_BRACES = "显示括号";
SHOW_BRACES_COMBATLOG_TOOLTIP = "在战斗记录信息中的超链接外显示括号。";
SHOW_BUFFS = "显示增益效果";
SHOW_BUFF_DURATION_TEXT = "增益持续时间";
SHOW_CASTABLE_BUFFS_TEXT = "可施放增益";
SHOW_CASTABLE_DEBUFFS_TEXT = "可施放的负面效果";
SHOW_CHAT_ICONS = "显示图标";
SHOW_CLASS_COLOR = "显示职业颜色";
SHOW_CLASS_COLOR_IN_V_KEY = "在姓名板上标示职业颜色";
SHOW_CLOAK = "显示披风";
SHOW_CLOCK = "显示时钟";
SHOW_COMBAT_HEALING = "治疗";
SHOW_COMBAT_TEXT_TEXT = "开启浮动战斗信息";
SHOW_DAMAGE_TEXT = "目标伤害";
SHOW_DEBUFFS = "显示负面效果";
SHOW_DISPELLABLE_DEBUFFS_TEXT = "可驱散效果";
SHOW_ENEMY_CAST = "施法条";
SHOW_FACTION_ON_MAINSCREEN = "显示为经验条";
SHOW_FREE_BAG_SLOTS_TEXT = "显示剩余背包空间";
SHOW_FRIENDS_LIST = "显示好友列表";
SHOW_FULLSCREEN_STATUS_TEXT = "屏幕边缘闪光";
SHOW_GUILD_NAMES = "玩家公会名";
SHOW_HELM = "显示头盔";
SHOW_IGNORE_LIST = "显示屏蔽名单";
SHOW_ITEM_LEVEL = "显示物品等级";
SHOW_LOOT_SPAM = "详细拾取信息";
SHOW_LUA_ERRORS = "显示Lua错误";
SHOW_MAP = "显示地图";
SHOW_MULTIBAR1_TEXT = "左下方动作条";
SHOW_MULTIBAR2_TEXT = "右下方动作条";
SHOW_MULTIBAR3_TEXT = "右边动作条";
SHOW_MULTIBAR4_TEXT = "右边动作条2";
SHOW_NEWBIE_TIPS_TEXT = "新手提示";
SHOW_NPC_NAMES = "NPC姓名";
SHOW_NUMERIC_THREAT = "显示威胁百分比";
SHOW_OFFLINE_MEMBERS = "显示离线成员";
SHOW_ON_BACKPACK = "在行囊上显示";
SHOW_OTHER_TARGET_EFFECTS = "其他玩家目标的效果";
SHOW_OWN_NAME = "显示本人名字";
SHOW_PARTY_BACKGROUND_TEXT = "小队/竞技场背景";
SHOW_PARTY_PETS_TEXT = "队友宠物";
SHOW_PARTY_TEXT_TEXT = "队友生命值数字";
SHOW_PET_MELEE_DAMAGE = "宠物伤害";
SHOW_PET_NAMEPLATES = "宠物姓名栏";
SHOW_PET_SPELL_DAMAGE = "显示宠物法术伤害";
SHOW_PLAYER_NAMES = "玩家名字";
SHOW_PLAYER_TITLES = "玩家头衔";
SHOW_QUEST_FADING_TEXT = "立即显示任务文本";
SHOW_QUEST_OBJECTIVES_ON_MAP_TEXT = "显示任务目标";
SHOW_QUICK_BUTTON = "显示快捷按钮";
SHOW_RAID_RANGE_TEXT = "团队界面中的距离";
SHOW_TARGET = "显示目标";
SHOW_TARGET_CASTBAR = "目标";
SHOW_TARGET_CASTBAR_IN_V_KEY = "姓名板";
SHOW_TARGET_EFFECTS = "目标效果";
SHOW_TARGET_OF_TARGET_TEXT = "目标的目标";
SHOW_TIMESTAMP = "显示时间戳";
SHOW_TIPOFTHEDAY_TEXT = "读盘画面小窍门";
SHOW_TOAST_BROADCAST_TEXT = "通告更新";
SHOW_TOAST_CONVERSATION_TEXT = "对话通知";
SHOW_TOAST_FRIEND_REQUEST_TEXT = "实名好友请求";
SHOW_TOAST_OFFLINE_TEXT = "好友下线";
SHOW_TOAST_ONLINE_TEXT = "好友上线";
SHOW_TOAST_WINDOW_TEXT = "显示浮窗";
SHOW_TUTORIALS = "教程";
SHOW_UNIT_NAMES = "显示单位名称";
SIGN_CHARTER = "签名";
SILVER_AMOUNT = "%d银币";
SILVER_AMOUNT_SYMBOL = "银";
SILVER_AMOUNT_TEXTURE = "%d\124TInterface\\MoneyFrame\\UI-SilverIcon:%d:%d:2:0\124t";
SIMPLE_CHAT_OPTION_ENABLE_INTERRUPT = "启用简易聊天模式会导致你的聊天窗口设置失效。你确定要启用简易聊天模式吗？";
SIMPLE_CHAT_TEXT = "简易聊天模式";
SIMPLE_QUEST_WATCH_TEXT = "简易目标追踪";
SINGLE_DAMAGE_TEMPLATE = "%d点伤害";
SINGLE_DAMAGE_TEMPLATE_WITH_SCHOOL = "%d点%s伤害";
SINGLE_PAGE_RESULTS_TEMPLATE = "%d件物品";
SKILL = "技能";
SKILLS = "技能";
SKILLS_ABBR = "技能";
SKILLUPS = "技能提升";
SKILL_DESCRIPTION = "|cffffffff%s|r %s";
SKILL_INCREMENT_COST = "消耗%s%d|r技能点来升级。";
SKILL_INCREMENT_COST_SINGULAR = "消耗%s%d|r技能点来升级。";
SKILL_LEARNING_COST = "学习该技能：%s%d|r 技能点。";
SKILL_LEARNING_COST_SINGULAR = "学习该技能：%s%d|r 技能点。";
SKILL_LEVEL = "技能等级";
SKILL_POINTS_TOOLTIP = "技能点数是用来在世界各地的训练师处\n学习技能的。";
SKILL_RANK_UP = "你的%s技能提高到了%d。";
SKIN_COLOR = "肤色";
SLASH_ACHIEVEMENTUI1 = "/ach";
SLASH_ACHIEVEMENTUI2 = "/ach";
SLASH_ACHIEVEMENTUI3 = "/achieve";
SLASH_ACHIEVEMENTUI4 = "/achieve";
SLASH_ACHIEVEMENTUI5 = "/achievement";
SLASH_ACHIEVEMENTUI6 = "/achievement";
SLASH_ACHIEVEMENTUI7 = "/achievements";
SLASH_ACHIEVEMENTUI8 = "/achievements";
SLASH_ASSIST1 = "/a";
SLASH_ASSIST2 = "/协助";
SLASH_ASSIST3 = "/a";
SLASH_ASSIST4 = "/assist";
SLASH_BATTLEGROUND1 = "/bg";
SLASH_BATTLEGROUND2 = "/battleground";
SLASH_BATTLEGROUND3 = "/bg";
SLASH_BATTLEGROUND4 = "/battleground";
SLASH_BENCHMARK1 = "/timetest";
SLASH_BENCHMARK2 = "/timetest";
SLASH_CALENDAR1 = "/calendar";
SLASH_CALENDAR2 = "/calendar";
SLASH_CANCELAURA1 = "/cancelaura";
SLASH_CANCELAURA2 = "/cancelaura";
SLASH_CANCELFORM1 = "/cancelform";
SLASH_CANCELFORM2 = "/cancelform";
SLASH_CAST1 = "/施放";
SLASH_CAST2 = "/spell";
SLASH_CAST3 = "/cast";
SLASH_CAST4 = "/法术";
SLASH_CASTRANDOM1 = "/castrandom";
SLASH_CASTRANDOM2 = "/castrandom";
SLASH_CASTSEQUENCE1 = "/castsequence";
SLASH_CASTSEQUENCE2 = "/castsequence";
SLASH_CHANGEACTIONBAR1 = "/changeactionbar";
SLASH_CHANGEACTIONBAR2 = "/changeactionbar";
SLASH_CHANNEL1 = "/c";
SLASH_CHANNEL2 = "/csay";
SLASH_CHANNEL3 = "/c";
SLASH_CHANNEL4 = "/csay";
SLASH_CHATLOG1 = "/chatlog";
SLASH_CHATLOG2 = "/聊天记录";
SLASH_CHAT_AFK1 = "/afk";
SLASH_CHAT_AFK2 = "/暂离";
SLASH_CHAT_AFK3 = "/离开";
SLASH_CHAT_AFK4 = "/离开";
SLASH_CHAT_ANNOUNCE1 = "/announce";
SLASH_CHAT_ANNOUNCE2 = "/禁止公告";
SLASH_CHAT_ANNOUNCE3 = "/宣布";
SLASH_CHAT_ANNOUNCE4 = "/ann";
SLASH_CHAT_BAN1 = "/ban";
SLASH_CHAT_BAN2 = "/ban";
SLASH_CHAT_CINVITE1 = "/cinvite";
SLASH_CHAT_CINVITE2 = "/聊天邀请";
SLASH_CHAT_CINVITE3 = "/cinvite";
SLASH_CHAT_CINVITE4 = "/chatinvite";
SLASH_CHAT_DND1 = "/dnd";
SLASH_CHAT_DND2 = "/请勿打扰";
SLASH_CHAT_DND3 = "/请勿打扰";
SLASH_CHAT_DND4 = "/忙碌";
SLASH_CHAT_DND5 = "/忙碌";
SLASH_CHAT_DND6 = "/忙碌";
SLASH_CHAT_HELP1 = "/chat";
SLASH_CHAT_HELP2 = "/频道帮助";
SLASH_CHAT_HELP3 = "/聊天帮助";
SLASH_CHAT_HELP4 = "/聊天帮助";
SLASH_CHAT_HELP5 = "/chathelp";
SLASH_CHAT_KICK1 = "/ckick";
SLASH_CHAT_KICK2 = "/频道剔除";
SLASH_CHAT_MODERATE1 = "/moderate";
SLASH_CHAT_MODERATE2 = "/修改";
SLASH_CHAT_MODERATOR1 = "/mod";
SLASH_CHAT_MODERATOR2 = "/缓和器";
SLASH_CHAT_MODERATOR3 = "/mod";
SLASH_CHAT_MODERATOR4 = "/moderator";
SLASH_CHAT_MUTE1 = "/mute";
SLASH_CHAT_MUTE2 = "/压制";
SLASH_CHAT_MUTE3 = "/unvoice";
SLASH_CHAT_MUTE4 = "/静声";
SLASH_CHAT_MUTE5 = "/squelch";
SLASH_CHAT_MUTE6 = "/禁声";
SLASH_CHAT_OWNER1 = "/owner";
SLASH_CHAT_OWNER2 = "/owner";
SLASH_CHAT_PASSWORD1 = "/password";
SLASH_CHAT_PASSWORD2 = "/pass";
SLASH_CHAT_PASSWORD3 = "/密码";
SLASH_CHAT_PASSWORD4 = "/密码";
SLASH_CHAT_PASSWORD5 = "/pass";
SLASH_CHAT_UNBAN1 = "/unban";
SLASH_CHAT_UNBAN2 = "/unban";
SLASH_CHAT_UNMODERATOR1 = "/unmod";
SLASH_CHAT_UNMODERATOR2 = "/解除缓和";
SLASH_CHAT_UNMODERATOR3 = "/unmod";
SLASH_CHAT_UNMODERATOR4 = "/unmoderator";
SLASH_CHAT_UNMUTE1 = "/unmute";
SLASH_CHAT_UNMUTE2 = "/unsquelch";
SLASH_CHAT_UNMUTE3 = "/声音";
SLASH_CHAT_UNMUTE4 = "/开声";
SLASH_CHAT_UNMUTE5 = "/unsquelch";
SLASH_CHAT_UNMUTE6 = "/voice";
SLASH_CLEAR1 = "/clear";
SLASH_CLEAR2 = "/clear";
SLASH_CLEARFOCUS1 = "/clearfocus";
SLASH_CLEARFOCUS2 = "/clearfocus";
SLASH_CLEARMAINASSIST1 = "/clearma";
SLASH_CLEARMAINASSIST2 = "/clearmainassist";
SLASH_CLEARMAINASSIST3 = "/clearma";
SLASH_CLEARMAINASSIST4 = "/clearmainassist";
SLASH_CLEARMAINTANK1 = "/clearmt";
SLASH_CLEARMAINTANK2 = "/clearmaintank";
SLASH_CLEARMAINTANK3 = "/clearmt";
SLASH_CLEARMAINTANK4 = "/clearmaintank";
SLASH_CLEARTARGET1 = "/cleartarget";
SLASH_CLEARTARGET2 = "/cleartarget";
SLASH_CLICK1 = "/click";
SLASH_CLICK2 = "/click";
SLASH_COMBATLOG1 = "/战斗日志";
SLASH_COMBATLOG2 = "/combatlog";
SLASH_CONSOLE1 = "/console";
SLASH_CONSOLE2 = "/控制台";
SLASH_DISABLE_ADDONS1 = "/disableaddons";
SLASH_DISMOUNT1 = "/dismount";
SLASH_DISMOUNT2 = "/dismount";
SLASH_DUEL1 = "/对决";
SLASH_DUEL2 = "/duel";
SLASH_DUEL_CANCEL1 = "/yield";
SLASH_DUEL_CANCEL2 = "/认输";
SLASH_DUEL_CANCEL3 = "/forfeit";
SLASH_DUEL_CANCEL4 = "/yield";
SLASH_DUEL_CANCEL5 = "/concede";
SLASH_DUEL_CANCEL6 = "/forfeit";
SLASH_DUMP1 = "/dump";
SLASH_DUMP2 = "/dump";
SLASH_DUNGEONS1 = "/dungeonfinder";
SLASH_DUNGEONS2 = "/dungeonfinder";
SLASH_DUNGEONS3 = "/lfd";
SLASH_DUNGEONS4 = "/lfd";
SLASH_DUNGEONS5 = "/df";
SLASH_DUNGEONS6 = "/df";
SLASH_EMOTE1 = "/e";
SLASH_EMOTE2 = "/表情";
SLASH_EMOTE3 = "/emote";
SLASH_EMOTE4 = "/emote";
SLASH_EMOTE5 = "/表情";
SLASH_EMOTE6 = "/em";
SLASH_EMOTE7 = "/emote";
SLASH_EMOTE8 = "/me";
SLASH_ENABLE_ADDONS1 = "/enableaddons";
SLASH_EQUIP1 = "/equip";
SLASH_EQUIP2 = "/eq";
SLASH_EQUIP3 = "/equip";
SLASH_EQUIP4 = "/eq";
SLASH_EQUIP_SET1 = "/equipset";
SLASH_EQUIP_SET2 = "/equipset";
SLASH_EQUIP_TO_SLOT1 = "/equipslot";
SLASH_EQUIP_TO_SLOT2 = "/equipslot";
SLASH_EVENTTRACE1 = "/eventtrace";
SLASH_EVENTTRACE2 = "/eventtrace";
SLASH_EVENTTRACE3 = "/etrace";
SLASH_EVENTTRACE4 = "/etrace";
SLASH_FOCUS1 = "/focus";
SLASH_FOCUS2 = "/focus";
SLASH_FOLLOW1 = "/f";
SLASH_FOLLOW2 = "/跟随";
SLASH_FOLLOW3 = "/fol";
SLASH_FOLLOW4 = "/f";
SLASH_FOLLOW5 = "/follow";
SLASH_FOLLOW6 = "/fol";
SLASH_FOLLOW7 = "/f";
SLASH_FRAMESTACK1 = "/framestack";
SLASH_FRAMESTACK2 = "/framestack";
SLASH_FRAMESTACK3 = "/fstack";
SLASH_FRAMESTACK4 = "/fstack";
SLASH_FRIENDS1 = "/friends";
SLASH_FRIENDS2 = "/朋友";
SLASH_FRIENDS3 = "/friends";
SLASH_FRIENDS4 = "/friend";
SLASH_GUILD1 = "/g";
SLASH_GUILD2 = "/gc";
SLASH_GUILD3 = "/gu";
SLASH_GUILD4 = "/公会";
SLASH_GUILD5 = "/公会";
SLASH_GUILD6 = "/gc";
SLASH_GUILD7 = "/gu";
SLASH_GUILD8 = "/guild";
SLASH_GUILD9 = "/g";
SLASH_GUILD_DEMOTE1 = "/gdemote";
SLASH_GUILD_DEMOTE2 = "/公会降职";
SLASH_GUILD_DEMOTE3 = "/公会降级";
SLASH_GUILD_DEMOTE4 = "/guilddemote";
SLASH_GUILD_DISBAND1 = "/gdisband";
SLASH_GUILD_DISBAND2 = "/guilddisband";
SLASH_GUILD_DISBAND3 = "/公会解散";
SLASH_GUILD_DISBAND4 = "/guilddisband";
SLASH_GUILD_HELP1 = "/ghelp";
SLASH_GUILD_HELP2 = "/guildhelp";
SLASH_GUILD_HELP3 = "/公会帮助";
SLASH_GUILD_HELP4 = "/公会帮助";
SLASH_GUILD_HELP5 = "/ghelp";
SLASH_GUILD_INFO1 = "/ginfo";
SLASH_GUILD_INFO2 = "/guildinfo";
SLASH_GUILD_INFO3 = "/公会信息";
SLASH_GUILD_INFO4 = "/guildinfo";
SLASH_GUILD_INVITE1 = "/ginvite";
SLASH_GUILD_INVITE2 = "/guildinvite";
SLASH_GUILD_INVITE3 = "/公会邀请";
SLASH_GUILD_INVITE4 = "/guildinvite";
SLASH_GUILD_LEADER1 = "/gleader";
SLASH_GUILD_LEADER2 = "/公会领袖";
SLASH_GUILD_LEADER3 = "/公会领袖";
SLASH_GUILD_LEADER4 = "/guildleader";
SLASH_GUILD_LEADER_REPLACE = "/greplace";
SLASH_GUILD_LEAVE1 = "/gquit";
SLASH_GUILD_LEAVE2 = "/退出公会";
SLASH_GUILD_LEAVE3 = "/gquit";
SLASH_GUILD_LEAVE4 = "/guildquit";
SLASH_GUILD_MOTD1 = "/gmotd";
SLASH_GUILD_MOTD2 = "/guildmotd";
SLASH_GUILD_MOTD3 = "/公会信息";
SLASH_GUILD_MOTD4 = "/guildmotd";
SLASH_GUILD_PROMOTE1 = "/gpromote";
SLASH_GUILD_PROMOTE2 = "/guildpromote";
SLASH_GUILD_PROMOTE3 = "/公会提升";
SLASH_GUILD_PROMOTE4 = "/guildpromote";
SLASH_GUILD_ROSTER1 = "/groster";
SLASH_GUILD_ROSTER2 = "/guildroster";
SLASH_GUILD_ROSTER3 = "/公会名册";
SLASH_GUILD_ROSTER4 = "/guildroster";
SLASH_GUILD_UNINVITE1 = "/gremove";
SLASH_GUILD_UNINVITE2 = "/公会删除";
SLASH_GUILD_UNINVITE3 = "/公会剔除";
SLASH_GUILD_UNINVITE4 = "/guildremove";
SLASH_GUILD_WHO1 = "/glist";
SLASH_GUILD_WHO2 = "/gwho";
SLASH_GUILD_WHO3 = "/whoguild";
SLASH_GUILD_WHO4 = "/glist";
SLASH_GUILD_WHO5 = "/gwho";
SLASH_GUILD_WHO6 = "/whoguild";
SLASH_HELP1 = "/h";
SLASH_HELP2 = "/帮助";
SLASH_HELP3 = "/?";
SLASH_HELP4 = "/h";
SLASH_HELP5 = "/h";
SLASH_HELP6 = "/help";
SLASH_IGNORE1 = "/屏蔽";
SLASH_IGNORE2 = "/ignore";
SLASH_INSPECT1 = "/ins";
SLASH_INSPECT2 = "/观察";
SLASH_INSPECT3 = "/ins";
SLASH_INSPECT4 = "/inspect";
SLASH_INVITE1 = "/i";
SLASH_INVITE2 = "/inv";
SLASH_INVITE3 = "/邀请";
SLASH_INVITE4 = "/i";
SLASH_INVITE5 = "/inv";
SLASH_INVITE6 = "/invite";
SLASH_INVITE7 = "/i";
SLASH_JOIN1 = "/join";
SLASH_JOIN2 = "/频道";
SLASH_JOIN3 = "/频道";
SLASH_JOIN4 = "/加入";
SLASH_JOIN5 = "/加入";
SLASH_JOIN6 = "/channel";
SLASH_JOIN7 = "/chan";
SLASH_LEAVE1 = "/leave";
SLASH_LEAVE2 = "/离开频道";
SLASH_LEAVE3 = "/退出频道";
SLASH_LEAVE4 = "/离开";
SLASH_LEAVE5 = "/离开";
SLASH_LEAVE6 = "/chatleave";
SLASH_LEAVE7 = "/chatexit";
SLASH_LEAVEVEHICLE1 = "/leavevehicle";
SLASH_LEAVEVEHICLE2 = "/leavevehicle";
SLASH_LIST_CHANNEL1 = "/chatlist";
SLASH_LIST_CHANNEL2 = "/频道查询";
SLASH_LIST_CHANNEL3 = "/频道信息";
SLASH_LIST_CHANNEL4 = "/聊天列表";
SLASH_LIST_CHANNEL5 = "/聊天列表";
SLASH_LIST_CHANNEL6 = "/chatwho";
SLASH_LIST_CHANNEL7 = "/chatinfo";
SLASH_LOGOUT1 = "/登出";
SLASH_LOGOUT2 = "/camp";
SLASH_LOGOUT3 = "/logout";
SLASH_LOGOUT4 = "/camp";
SLASH_LOOT_FFA1 = "/随意拾取";
SLASH_LOOT_FFA2 = "/ffa";
SLASH_LOOT_GROUP1 = "/group";
SLASH_LOOT_GROUP2 = "/group";
SLASH_LOOT_MASTER1 = "/队长分配";
SLASH_LOOT_MASTER2 = "/master";
SLASH_LOOT_NEEDBEFOREGREED1 = "/needbeforegreed";
SLASH_LOOT_NEEDBEFOREGREED2 = "/needbeforegreed";
SLASH_LOOT_ROUNDROBIN1 = "/轮流拾取";
SLASH_LOOT_ROUNDROBIN2 = "/roundrobin";
SLASH_LOOT_SETTHRESHOLD1 = "/threshold";
SLASH_LOOT_SETTHRESHOLD2 = "/threshold";
SLASH_MACRO1 = "/宏";
SLASH_MACRO2 = "/m";
SLASH_MACRO3 = "/m";
SLASH_MACRO4 = "/macro";
SLASH_MACROHELP1 = "/宏帮助";
SLASH_MACROHELP2 = "/macrohelp";
SLASH_MACROHELP3 = "/宏帮助";
SLASH_MAINASSISTOFF1 = "/maoff";
SLASH_MAINASSISTOFF2 = "/mainassistoff";
SLASH_MAINASSISTOFF3 = "/maoff";
SLASH_MAINASSISTOFF4 = "/mainassistoff";
SLASH_MAINASSISTON1 = "/ma";
SLASH_MAINASSISTON2 = "/mainassist";
SLASH_MAINASSISTON3 = "/ma";
SLASH_MAINASSISTON4 = "/mainassist";
SLASH_MAINTANKOFF1 = "/mtoff";
SLASH_MAINTANKOFF2 = "/maintankoff";
SLASH_MAINTANKOFF3 = "/mtoff";
SLASH_MAINTANKOFF4 = "/maintankoff";
SLASH_MAINTANKON1 = "/mt";
SLASH_MAINTANKON2 = "/maintank";
SLASH_MAINTANKON3 = "/mt";
SLASH_MAINTANKON4 = "/maintank";
SLASH_OFFICER1 = "/o";
SLASH_OFFICER2 = "/官员发言";
SLASH_OFFICER3 = "/o";
SLASH_OFFICER4 = "/osay";
SLASH_OFFICER5 = "/officer";
SLASH_OFFICER6 = "/officer";
SLASH_PARTY1 = "/p";
SLASH_PARTY2 = "/party";
SLASH_PARTY3 = "/p";
SLASH_PARTY4 = "/party";
SLASH_PARTY5 = "/p";
SLASH_PET_AGGRESSIVE1 = "/petaggressive";
SLASH_PET_AGGRESSIVE2 = "/petaggressive";
SLASH_PET_ATTACK1 = "/petattack";
SLASH_PET_ATTACK2 = "/petattack";
SLASH_PET_AUTOCASTOFF1 = "/petautocastoff";
SLASH_PET_AUTOCASTOFF2 = "/petautocastoff";
SLASH_PET_AUTOCASTON1 = "/petautocaston";
SLASH_PET_AUTOCASTON2 = "/petautocaston";
SLASH_PET_AUTOCASTTOGGLE1 = "/petautocasttoggle";
SLASH_PET_AUTOCASTTOGGLE2 = "/petautocasttoggle";
SLASH_PET_DEFENSIVE1 = "/petdefensive";
SLASH_PET_DEFENSIVE2 = "/petdefensive";
SLASH_PET_FOLLOW1 = "/petfollow";
SLASH_PET_FOLLOW2 = "/petfollow";
SLASH_PET_PASSIVE1 = "/petpassive";
SLASH_PET_PASSIVE2 = "/petpassive";
SLASH_PET_STAY1 = "/petstay";
SLASH_PET_STAY2 = "/petstay";
SLASH_PLAYED1 = "/played";
SLASH_PLAYED2 = "/游戏时间";
SLASH_PROMOTE1 = "/pr";
SLASH_PROMOTE2 = "/升职";
SLASH_PROMOTE3 = "/pr";
SLASH_PROMOTE4 = "/promote";
SLASH_PVP1 = "/pvp";
SLASH_PVP2 = "/pvp";
SLASH_QUIT1 = "/离开";
SLASH_QUIT2 = "/退出";
SLASH_QUIT3 = "/quit";
SLASH_QUIT4 = "/exit";
SLASH_RAID1 = "/团队";
SLASH_RAID2 = "/raid";
SLASH_RAID3 = "/ra";
SLASH_RAID4 = "/rsay";
SLASH_RAID5 = "/ra";
SLASH_RAID6 = "/rsay";
SLASH_RAIDBROWSER1 = "/raidbrowser";
SLASH_RAIDBROWSER2 = "/lfr";
SLASH_RAIDBROWSER3 = "/rb";
SLASH_RAIDBROWSER4 = "/raidbrowser";
SLASH_RAIDBROWSER5 = "/lfr";
SLASH_RAIDBROWSER6 = "/rb";
SLASH_RAID_INFO1 = "/raidinfo";
SLASH_RAID_INFO2 = "/raidinfo";
SLASH_RAID_WARNING1 = "/rw";
SLASH_RAID_WARNING2 = "/rw";
SLASH_RANDOM1 = "/random";
SLASH_RANDOM2 = "/rand";
SLASH_RANDOM3 = "/随机";
SLASH_RANDOM4 = "/random";
SLASH_RANDOM5 = "/rand";
SLASH_RANDOM6 = "/rnd";
SLASH_RANDOM7 = "/roll";
SLASH_READYCHECK1 = "/readycheck";
SLASH_READYCHECK2 = "/readycheck";
SLASH_RELOAD1 = "/reload";
SLASH_RELOAD2 = "/reload";
SLASH_REMOVEFRIEND1 = "/删除好友";
SLASH_REMOVEFRIEND2 = "/remfriend";
SLASH_REMOVEFRIEND3 = "/removefriend";
SLASH_REMOVEFRIEND4 = "/remfriend";
SLASH_REPLY1 = "/r";
SLASH_REPLY2 = "/回复";
SLASH_REPLY3 = "/回复";
SLASH_REPLY4 = "/reply";
SLASH_RESETCHAT1 = "/resetchat";
SLASH_RESETCHAT2 = "/resetchat";
SLASH_SAVEGUILDROSTER1 = "/saveguildroster";
SLASH_SAVEGUILDROSTER2 = "/saveguildroster";
SLASH_SAY1 = "/s";
SLASH_SAY2 = "/说";
SLASH_SAY3 = "/s";
SLASH_SAY4 = "/say";
SLASH_SCRIPT1 = "/脚本";
SLASH_SCRIPT2 = "/run";
SLASH_SCRIPT3 = "/script";
SLASH_SCRIPT4 = "/run";
SLASH_SET_TITLE1 = "/settitle";
SLASH_SET_TITLE2 = "/settitle";
SLASH_STARTATTACK1 = "/startattack";
SLASH_STARTATTACK2 = "/startattack";
SLASH_STOPATTACK1 = "/stopattack";
SLASH_STOPATTACK2 = "/stopattack";
SLASH_STOPCASTING1 = "/stopcasting";
SLASH_STOPCASTING2 = "/stopcasting";
SLASH_STOPMACRO1 = "/stopmacro";
SLASH_STOPMACRO2 = "/stopmacro";
SLASH_STOPWATCH1 = "/stopwatch";
SLASH_STOPWATCH2 = "/timer";
SLASH_STOPWATCH3 = "/sw";
SLASH_STOPWATCH4 = "/stopwatch";
SLASH_STOPWATCH5 = "/timer";
SLASH_STOPWATCH6 = "/sw";
SLASH_STOPWATCH_PARAM_PAUSE1 = "暂停";
SLASH_STOPWATCH_PARAM_PAUSE2 = "暂停";
SLASH_STOPWATCH_PARAM_PLAY1 = "播放";
SLASH_STOPWATCH_PARAM_PLAY2 = "播放";
SLASH_STOPWATCH_PARAM_STOP1 = "停止";
SLASH_STOPWATCH_PARAM_STOP2 = "清除";
SLASH_STOPWATCH_PARAM_STOP3 = "重置";
SLASH_STOPWATCH_PARAM_STOP4 = "停止";
SLASH_STOPWATCH_PARAM_STOP5 = "清除";
SLASH_STOPWATCH_PARAM_STOP6 = "重置";
SLASH_SWAPACTIONBAR1 = "/swapactionbar";
SLASH_SWAPACTIONBAR2 = "/swapactionbar";
SLASH_TARGET1 = "/目标";
SLASH_TARGET2 = "/tar";
SLASH_TARGET3 = "/target";
SLASH_TARGET4 = "/tar";
SLASH_TARGET_EXACT1 = "/targetexact";
SLASH_TARGET_EXACT2 = "/targetexact";
SLASH_TARGET_LAST_ENEMY1 = "/targetlastenemy";
SLASH_TARGET_LAST_ENEMY2 = "/targetlastenemy";
SLASH_TARGET_LAST_FRIEND1 = "/targetlastfriend";
SLASH_TARGET_LAST_FRIEND2 = "/targetlastfriend";
SLASH_TARGET_LAST_TARGET1 = "/targetlasttarget";
SLASH_TARGET_LAST_TARGET2 = "/targetlasttarget";
SLASH_TARGET_NEAREST_ENEMY1 = "/targetenemy";
SLASH_TARGET_NEAREST_ENEMY2 = "/targetenemy";
SLASH_TARGET_NEAREST_ENEMY_PLAYER1 = "/targetenemyplayer";
SLASH_TARGET_NEAREST_ENEMY_PLAYER2 = "/targetenemyplayer";
SLASH_TARGET_NEAREST_FRIEND1 = "/targetfriend";
SLASH_TARGET_NEAREST_FRIEND2 = "/targetfriend";
SLASH_TARGET_NEAREST_FRIEND_PLAYER1 = "/targetfriendplayer";
SLASH_TARGET_NEAREST_FRIEND_PLAYER2 = "/targetfriendplayer";
SLASH_TARGET_NEAREST_PARTY1 = "/targetparty";
SLASH_TARGET_NEAREST_PARTY2 = "/targetparty";
SLASH_TARGET_NEAREST_RAID1 = "/targetraid";
SLASH_TARGET_NEAREST_RAID2 = "/targetraid";
SLASH_TEAM_CAPTAIN1 = "/teamcaptain";
SLASH_TEAM_CAPTAIN2 = "/tcaptain";
SLASH_TEAM_CAPTAIN3 = "/teamcaptain";
SLASH_TEAM_CAPTAIN4 = "/tcaptain";
SLASH_TEAM_DISBAND1 = "/teamdisband";
SLASH_TEAM_DISBAND2 = "/tdisband";
SLASH_TEAM_DISBAND3 = "/teamdisband";
SLASH_TEAM_DISBAND4 = "/tdisband";
SLASH_TEAM_INVITE1 = "/teaminvite";
SLASH_TEAM_INVITE2 = "/tinvite";
SLASH_TEAM_INVITE3 = "/teaminvite";
SLASH_TEAM_INVITE4 = "/tinvite";
SLASH_TEAM_QUIT1 = "/teamquit";
SLASH_TEAM_QUIT2 = "/tquit";
SLASH_TEAM_QUIT3 = "/teamquit";
SLASH_TEAM_QUIT4 = "/tquit";
SLASH_TEAM_UNINVITE1 = "/teamremove";
SLASH_TEAM_UNINVITE2 = "/tremove";
SLASH_TEAM_UNINVITE3 = "/teamremove";
SLASH_TEAM_UNINVITE4 = "/tremove";
SLASH_TIME1 = "/时间";
SLASH_TIME2 = "/time";
SLASH_TOKEN1 = "/token";
SLASH_TOKEN2 = "/token";
SLASH_TOKEN3 = "/tk";
SLASH_TOKEN4 = "/tk";
SLASH_TRADE1 = "/tr";
SLASH_TRADE2 = "/交易";
SLASH_TRADE3 = "/tr";
SLASH_TRADE4 = "/trade";
SLASH_UNIGNORE1 = "/取消屏蔽";
SLASH_UNIGNORE2 = "/unignore";
SLASH_UNINVITE1 = "/u";
SLASH_UNINVITE2 = "/un";
SLASH_UNINVITE3 = "/踢除";
SLASH_UNINVITE4 = "/移除";
SLASH_UNINVITE5 = "/u";
SLASH_UNINVITE6 = "/un";
SLASH_UNINVITE7 = "/uninvite";
SLASH_UNINVITE8 = "/kick";
SLASH_UNINVITE9 = "/votekick";
SLASH_UNINVITE10 = "/votekick";
SLASH_USE1 = "/use";
SLASH_USE2 = "/use";
SLASH_USERANDOM1 = "/userandom";
SLASH_USERANDOM2 = "/userandom";
SLASH_USE_TALENT_SPEC1 = "/usetalents";
SLASH_USE_TALENT_SPEC2 = "/usetalents";
SLASH_VOICEMACRO1 = "/v";
SLASH_VOICEMACRO2 = "/v";
SLASH_WHISPER1 = "/w";
SLASH_WHISPER2 = "/密语";
SLASH_WHISPER3 = "/t";
SLASH_WHISPER4 = "/告诉";
SLASH_WHISPER5 = "/发送";
SLASH_WHISPER6 = "/w";
SLASH_WHISPER7 = "/whisper";
SLASH_WHISPER8 = "/t";
SLASH_WHISPER9 = "/tell";
SLASH_WHISPER10 = "/send";
SLASH_WHO1 = "/查询";
SLASH_WHO2 = "/who";
SLASH_YELL1 = "/y";
SLASH_YELL2 = "/大喊";
SLASH_YELL3 = "/sh";
SLASH_YELL4 = "/大叫";
SLASH_YELL5 = "/大喊";
SLASH_YELL6 = "/yell";
SLASH_YELL7 = "/sh";
SLASH_YELL8 = "/shout";
SLURRED_SPEECH = "%s ……嗝儿！";
SMART_PIVOT = "智能调节";
SOCIALS = "社交";
SOCIAL_BUTTON = "社交";
SOCIAL_LABEL = "社交";
SOCIAL_SUBTEXT = "这些选项可以帮你控制并编辑你与其他玩家的社交信息。";
SOCKET_GEMS = "镶嵌宝石";
SOCKET_ITEM_MIN_SKILL = "插槽要求 %s（%d）";
SOCKET_ITEM_REQ_LEVEL = "插槽要求等级%d";
SOCKET_ITEM_REQ_SKILL = "插槽要求 %s";
SOLD_BY_COLON = "销售者：";
SOLO = "独身";
SORT_QUEST = "任务分类";
SOUNDOPTIONS_MENU = "声音";
SOUND_CHANNELS = "声道";
SOUND_DISABLED = "所有音效已关闭";
SOUND_EFFECTS_DISABLED = "禁用声效";
SOUND_EFFECTS_ENABLED = "启用声效";
SOUND_LABEL = "声音";
SOUND_OPTIONS = "声音设置";
SOUND_QUALITY = "声音品质";
SOUND_SUBTEXT = "这些选项控制着声音硬件和游戏中各类声音效果的表现及音量。";
SOUND_VOLUME = "声音";
SPEAKERMODE = "通告模式";
SPEAKERMODE_HEADPHONES = "头戴式耳机";
SPEAKERMODE_STEREO = "立体声";
SPEAKERMODE_SURROUND = "环绕音效";
SPECIAL = "特殊";
SPECIAL_SKILLS = "%s技能：";
SPECIFIC_DUNGEONS = "指定地下城";
SPECIFIC_DUNGEON_IS_READY = "已经建好了一个队伍，准备前往：";
SPEED = "速度";
SPEED_ABBR = "SPD";
SPELLBOOK = "法术书";
SPELLBOOK_ABILITIES_BUTTON = "法术书和技能";
SPELLBOOK_BUTTON = "法术书";
SPELLDISMISSPETOTHER = "%s的%s被解散了。";
SPELLDISMISSPETSELF = "你的%s被解散了。";
SPELLHAPPINESSDRAINOTHER = "%s的%s失去了%d点快乐值。";
SPELLHAPPINESSDRAINSELF = "你的%s失去了%d点快乐值。";
SPELLS = "法术";
SPELLS_COMBATLOG_TOOLTIP = "显示有关法术和特殊技能的信息。";
SPELL_BONUS = "法术加成";
SPELL_CASTING = "法术施放";
SPELL_CASTING_COMBATLOG_TOOLTIP = "显示有关法术施放的信息。";
SPELL_CAST_CHANNELED = "需引导";
SPELL_CAST_FAILED_COMBATLOG_TOOLTIP = "当法术无法施放或施放失败时显示信息。";
SPELL_CAST_START_COMBATLOG_TOOLTIP = "当法术开始施放时显示信息。";
SPELL_CAST_SUCCESS_COMBATLOG_TOOLTIP = "当法术成功施放时显示信息。";
SPELL_CAST_TIME_INSTANT = "瞬发法术";
SPELL_CAST_TIME_INSTANT_NO_MANA = "瞬发";
SPELL_CAST_TIME_MIN = "%.3g分钟施法时间";
SPELL_CAST_TIME_RANGED = "攻击速度+%.3g秒";
SPELL_CAST_TIME_SEC = "%.3g秒施法时间";
SPELL_COLOR_BY_SCHOOL_COMBATLOG_TOOLTIP = "按类型用彩色标记法术名。";
SPELL_CREATE_COMBATLOG_TOOLTIP = "当法术或技能制造出物体时显示信息。";
SPELL_CRIT_CHANCE = "爆击率";
SPELL_DAMAGE_COMBATLOG_TOOLTIP = "当法术或技能造成伤害时显示信息。";
SPELL_DAMAGE_NUMBER_COMBATLOG_TOOLTIP = "以彩色标记非普通攻击的伤害数值。";
SPELL_DAMAGE_SCHOOL_COMBATLOG_TOOLTIP = "彩色标记伤害类型。（例如：暗影）";
SPELL_DETAIL = "法术细节";
SPELL_DRAIN_COMBATLOG_TOOLTIP = "当法术或技能降低法力、能量、怒气、集中或快乐值时显示信息。";
SPELL_DURATION = "持续%s";
SPELL_DURATION_DAYS = "%.2f天";
SPELL_DURATION_HOURS = "%.2f小时";
SPELL_DURATION_MIN = "%.2f分钟";
SPELL_DURATION_SEC = "%.2f秒";
SPELL_DURATION_UNTIL_CANCELLED = "直到主动取消";
SPELL_EQUIPPED_ITEM = "需要%s";
SPELL_EQUIPPED_ITEM_NOSPACE = "需要%s";
SPELL_EXTRA_ATTACKS_COMBATLOG_TOOLTIP = "当法术或技能提供额外攻击时（如风怒武器或剑专精）显示信息。";
SPELL_FAILED_AFFECTING_COMBAT = "你正处于交战状态";
SPELL_FAILED_ALREADY_BEING_TAMED = "该生物已经被驯化。";
SPELL_FAILED_ALREADY_HAVE_CHARM = "你已经控制了一个被魅惑的生物";
SPELL_FAILED_ALREADY_HAVE_SUMMON = "你已经控制了一个召唤生物";
SPELL_FAILED_ALREADY_OPEN = "已经打开";
SPELL_FAILED_ARTISAN_RIDING_REQUIREMENT = "需要专家级骑术";
SPELL_FAILED_AURA_BOUNCED = "已经有一个更强大的法术在发挥作用";
SPELL_FAILED_BAD_IMPLICIT_TARGETS = "没有目标";
SPELL_FAILED_BAD_TARGETS = "无效的目标";
SPELL_FAILED_BM_OR_INVISGOD = "该法术无法对beastmaster或invis god目标施放。";
SPELL_FAILED_CANT_BE_CHARMED = "目标无法被魅惑";
SPELL_FAILED_CANT_BE_DISENCHANTED = "物品无法被分解";
SPELL_FAILED_CANT_BE_DISENCHANTED_SKILL = "你的附魔技能点数不够，无法分解该物品";
SPELL_FAILED_CANT_BE_MILLED = "你不能研磨这件物品";
SPELL_FAILED_CANT_BE_PROSPECTED = "里面没有宝石";
SPELL_FAILED_CANT_CAST_ON_TAPPED = "目标已被其他玩家接触";
SPELL_FAILED_CANT_DO_THAT_RIGHT_NOW = "你还不能那么做。";
SPELL_FAILED_CANT_DUEL_WHILE_INVISIBLE = "你无法在隐形状态下开始决斗";
SPELL_FAILED_CANT_DUEL_WHILE_STEALTHED = "你无法在潜行状态下开始对决。";
SPELL_FAILED_CANT_STEALTH = "你与敌人的距离太近";
SPELL_FAILED_CASTER_AURASTATE = "你还不能那么做";
SPELL_FAILED_CASTER_DEAD = "你已经死亡";
SPELL_FAILED_CASTER_DEAD_FEMALE = "你死了";
SPELL_FAILED_CAST_NOT_HERE = "你不能在这里施放该法术";
SPELL_FAILED_CHARMED = "在被魅惑状态下无法那么做";
SPELL_FAILED_CHEST_IN_USE = "目标正在被使用";
SPELL_FAILED_CONFUSED = "无法在混乱状态下那样做";
SPELL_FAILED_CUSTOM_ERROR_1 = "Something bad happened, and we want to display a custom message!";
SPELL_FAILED_CUSTOM_ERROR_10 = "你还不能召唤另一只石像鬼。";
SPELL_FAILED_CUSTOM_ERROR_11 = "如果目标不是已死亡的人型生物，则需要尸尘。";
SPELL_FAILED_CUSTOM_ERROR_12 = "只能放在裂角附近";
SPELL_FAILED_CUSTOM_ERROR_13 = "你必须选定一枚始祖龙卵。";
SPELL_FAILED_CUSTOM_ERROR_14_NONE = "你必须靠近一棵被标记的树。";
SPELL_FAILED_CUSTOM_ERROR_15 = "你必须选定一只峡湾火鸡作为目标。";
SPELL_FAILED_CUSTOM_ERROR_16 = "你必须选定一只峡湾鹰。";
SPELL_FAILED_CUSTOM_ERROR_17 = "你离浮标太远了。";
SPELL_FAILED_CUSTOM_ERROR_18 = "必须靠近浮油才能使用。";
SPELL_FAILED_CUSTOM_ERROR_19 = "你必须更靠近浮标一点！";
SPELL_FAILED_CUSTOM_ERROR_2 = "Alex broke your quest! Thank him later!";
SPELL_FAILED_CUSTOM_ERROR_20 = "你只能在龙眠神殿、巨龙废土、迦拉克隆之墓和邪恶之旋召唤龙眠征服者。";
SPELL_FAILED_CUSTOM_ERROR_21 = "只能对冷心冰虫幼体使用。";
SPELL_FAILED_CUSTOM_ERROR_22 = "你必须靠近虫孔才能使用地图。";
SPELL_FAILED_CUSTOM_ERROR_23 = "你只能将哈罗德·兰恩作为召唤犀牛的目标。";
SPELL_FAILED_CUSTOM_ERROR_24 = "你只能对迦莫斯拉或血孢平原和迦莫斯地区的其它猛犸人使用血孢花的粉末。";
SPELL_FAILED_CUSTOM_ERROR_25 = "需要奈萨里奥之喉深处的熔岩之龙复活大厅。";
SPELL_FAILED_CUSTOM_ERROR_26 = "你只能在暮冬要塞或腐臭平原召唤暮冬城狮鹫。";
SPELL_FAILED_CUSTOM_ERROR_27 = "你在干什么？只能用它瞄准威尔海姆！";
SPELL_FAILED_CUSTOM_ERROR_28 = "生命值不足！";
SPELL_FAILED_CUSTOM_ERROR_29 = "附近没有可用的尸体";
SPELL_FAILED_CUSTOM_ERROR_3 = "这个法术只能对未被营救的无助的暮冬城平民使用。";
SPELL_FAILED_CUSTOM_ERROR_30 = "你已经控制了足够多的血色矿工。返回死亡裂口的收割者戈提克那里去吧。";
SPELL_FAILED_CUSTOM_ERROR_31 = "你的伙伴不愿意来这里。离崩裂碎片再远一点。";
SPELL_FAILED_CUSTOM_ERROR_32 = "必须在猎豹形态下";
SPELL_FAILED_CUSTOM_ERROR_33 = "只有死亡骑士可以进入黑锋要塞。";
SPELL_FAILED_CUSTOM_ERROR_34 = "必须在猎豹形态、熊形态或巨熊形态下";
SPELL_FAILED_CUSTOM_ERROR_35 = "你必须靠近无助的暮冬城平民。";
SPELL_FAILED_CUSTOM_ERROR_36 = "你不能以元素生物或机械的尸体作为目标。";
SPELL_FAILED_CUSTOM_ERROR_37 = "在至少使用一次达拉然传送水晶之前，你不能使用这枚传送水晶。";
SPELL_FAILED_CUSTOM_ERROR_38 = "你手里已经握着一个怪物了。你必须把它扔出去，然后才能抓起另一个。";
SPELL_FAILED_CUSTOM_ERROR_39 = "你手里没有东西可以扔！找到一个瓦古，使用盖米尔的抓握技能抓住它！";
SPELL_FAILED_CUSTOM_ERROR_4 = "你必须穿着战歌兽人伪装包。";
SPELL_FAILED_CUSTOM_ERROR_40 = "只能在风暴之子瓦杜兰身边10码范围内吹响布德克拉格的作战号角。";
SPELL_FAILED_CUSTOM_ERROR_41 = "你并没有携带任何乘客，所以没有人可以下车。";
SPELL_FAILED_CUSTOM_ERROR_42 = "你不能制造更多的攻城载具了。";
SPELL_FAILED_CUSTOM_ERROR_43 = "你已经携带一名银色北伐军战士了。你必须先回到银色前线基地医护所并放下你的乘客，然后才能营救其他人。";
SPELL_FAILED_CUSTOM_ERROR_44 = "你不能在被定身时那么做。";
SPELL_FAILED_CUSTOM_ERROR_45 = "需要附近的目标。";
SPELL_FAILED_CUSTOM_ERROR_46 = "没有可以发现的东西。";
SPELL_FAILED_CUSTOM_ERROR_47 = "附近没有目标可以哄骗。";
SPELL_FAILED_CUSTOM_ERROR_48 = "你的铁符文构造体距离太远。";
SPELL_FAILED_CUSTOM_ERROR_49 = "需要宗师级工程学";
SPELL_FAILED_CUSTOM_ERROR_5 = "你必须靠近瘟疫车才能放下第七军团攻城技师。";
SPELL_FAILED_CUSTOM_ERROR_50 = "你不能使用那种坐骑。";
SPELL_FAILED_CUSTOM_ERROR_51 = "无人可以弹射！";
SPELL_FAILED_CUSTOM_ERROR_52 = "目标必须已经与你绑定。";
SPELL_FAILED_CUSTOM_ERROR_53 = "目标必须是亡灵。";
SPELL_FAILED_CUSTOM_ERROR_54 = "你没有目标，或者目标太远。";
SPELL_FAILED_CUSTOM_ERROR_55 = "缺少材料：黑暗物质";
SPELL_FAILED_CUSTOM_ERROR_56 = "你不能使用那件物品";
SPELL_FAILED_CUSTOM_ERROR_57 = "你在受到旋风影响时无法那么做";
SPELL_FAILED_CUSTOM_ERROR_58 = "目标身上已经有一个卷轴效果了";
SPELL_FAILED_CUSTOM_ERROR_59 = "解毒药的效果不足以驱散毒性";
SPELL_FAILED_CUSTOM_ERROR_6 = "你不能将小队以外的友方单位作为目标。";
SPELL_FAILED_CUSTOM_ERROR_60 = "你必须装备一支长枪。";
SPELL_FAILED_CUSTOM_ERROR_61 = "你必须靠近冬息湖中的仙子。";
SPELL_FAILED_CUSTOM_ERROR_62 = "你已经学到了这本书上的所有知识";
SPELL_FAILED_CUSTOM_ERROR_63_NONE = "你的宠物已经死亡";
SPELL_FAILED_CUSTOM_ERROR_64_NONE = "射程范围内无有效目标。";
SPELL_FAILED_CUSTOM_ERROR_65 = "只有GM可以使用。你的账号已被汇报并将接受调查。";
SPELL_FAILED_CUSTOM_ERROR_66 = "你必须达到等级58才能使用这个传送门。";
SPELL_FAILED_CUSTOM_ERROR_67 = "你的荣誉点数已满。";
SPELL_FAILED_CUSTOM_ERROR_7 = "你必须选定一个虚弱的冰冷树妖作为目标。";
SPELL_FAILED_CUSTOM_ERROR_75 = "必须拥有一个激活的恶魔法阵。";
SPELL_FAILED_CUSTOM_ERROR_76 = "你的怒气值已满";
SPELL_FAILED_CUSTOM_ERROR_77 = "需要工程学（350）";
SPELL_FAILED_CUSTOM_ERROR_78 = "你的灵魂属于巫妖王";
SPELL_FAILED_CUSTOM_ERROR_79 = "你的侍从已经有了一匹银色马驹";
SPELL_FAILED_CUSTOM_ERROR_8 = "魔法天灾外衣只有在圣城恩其拉中装备时才会生效。";
SPELL_FAILED_CUSTOM_ERROR_83 = "必须有一个火焰图腾处于激活状态。";
SPELL_FAILED_CUSTOM_ERROR_84 = "你不能再咬别的吸血鬼了。";
SPELL_FAILED_CUSTOM_ERROR_85 = "你的宠物已经与你同级。";
SPELL_FAILED_CUSTOM_ERROR_86 = "你的级别没有达到该物品的要求。";
SPELL_FAILED_CUSTOM_ERROR_87 = "变异的憎恶数量过多。";
SPELL_FAILED_CUSTOM_ERROR_88 = "药水都被普崔塞德教授用完了。";
SPELL_FAILED_CUSTOM_ERROR_9 = "需要尸尘";
SPELL_FAILED_CUSTOM_ERROR_90 = "需要65级";
SPELL_FAILED_CUSTOM_ERROR_96 = "招募人数已达上限。";
SPELL_FAILED_CUSTOM_ERROR_97 = "志愿者人数已达上限。";
SPELL_FAILED_CUSTOM_ERROR_98 = "霜之哀伤使你无法复活。";
SPELL_FAILED_CUSTOM_ERROR_99 = "你无法在该变形效果下骑乘。";
SPELL_FAILED_DAMAGE_IMMUNE = "无法在免疫状态下那样做";
SPELL_FAILED_EQUIPPED_ITEM = "必须装备正确的物品。";
SPELL_FAILED_EQUIPPED_ITEM_CLASS = "必须装备一个%s。";
SPELL_FAILED_EQUIPPED_ITEM_CLASS_MAINHAND = "主手上必须装备%s";
SPELL_FAILED_EQUIPPED_ITEM_CLASS_OFFHAND = "必须在副手上装备一件%s";
SPELL_FAILED_ERROR = "内部错误";
SPELL_FAILED_EXPERT_RIDING_REQUIREMENT = "需要高级骑术";
SPELL_FAILED_FISHING_TOO_LOW = "需要钓鱼技能 %d";
SPELL_FAILED_FIZZLE = "失败";
SPELL_FAILED_FLEEING = "无法在逃跑状态下那样做";
SPELL_FAILED_FOOD_LOWLEVEL = "食物等级不够，不足以满足你的宠物。";
SPELL_FAILED_GLYPH_SOCKET_LOCKED = "你还不能将雕文嵌入那个栏位。";
SPELL_FAILED_HIGHLEVEL = "目标等级太高";
SPELL_FAILED_IMMUNE = "免疫";
SPELL_FAILED_INCORRECT_AREA = "你在错误的区域中。";
SPELL_FAILED_INTERRUPTED = "被打断";
SPELL_FAILED_INTERRUPTED_COMBAT = "被打断";
SPELL_FAILED_INVALID_GLYPH = "该雕文不能嵌入那个栏位中。";
SPELL_FAILED_ITEM_ALREADY_ENCHANTED = "物品已经被附魔";
SPELL_FAILED_ITEM_AT_MAX_CHARGES = "该物品的可使用次数已经达到上限。";
SPELL_FAILED_ITEM_ENCHANT_TRADE_WINDOW = "无法在交易时给物品附魔";
SPELL_FAILED_ITEM_GONE = "物品不存在";
SPELL_FAILED_ITEM_NOT_FOUND = "试图给一个不存在的物品附魔";
SPELL_FAILED_ITEM_NOT_READY = "物品还没有准备好";
SPELL_FAILED_LEVEL_REQUIREMENT = "你的角色等级不够";
SPELL_FAILED_LEVEL_REQUIREMENT_PET = "你的宠物等级不够";
SPELL_FAILED_LIMIT_CATEGORY_EXCEEDED = "你已经拥有太多此物品了";
SPELL_FAILED_LINE_OF_SIGHT = "目标不在视野中";
SPELL_FAILED_LOWLEVEL = "目标等级太低";
SPELL_FAILED_LOW_CASTLEVEL = "技能等级不够";
SPELL_FAILED_MAINHAND_EMPTY = "你拿武器的手是空着的";
SPELL_FAILED_MIN_SKILL = "你的技能不够高。需要%s（%d）。";
SPELL_FAILED_MOVING = "不能在移动中实施该动作";
SPELL_FAILED_NEED_AMMO = "使用弹药之前必须将弹药放在角色状态界面下方的弹药栏中。";
SPELL_FAILED_NEED_AMMO_POUCH = "需要：%s";
SPELL_FAILED_NEED_EXOTIC_AMMO = "需要异种弹药：%s";
SPELL_FAILED_NEED_MORE_ITEMS = "需要%d %s。";
SPELL_FAILED_NOPATH = "没有可以行进的路径";
SPELL_FAILED_NOTHING_TO_DISPEL = "没有可供驱散的法术";
SPELL_FAILED_NOTHING_TO_STEAL = "没有可以偷取的法术";
SPELL_FAILED_NOT_BEHIND = "你必须位于目标背后。";
SPELL_FAILED_NOT_FISHABLE = "你不能在非渔区钓鱼";
SPELL_FAILED_NOT_FLYING = "你正在飞行。";
SPELL_FAILED_NOT_HERE = "不能在这里使用。";
SPELL_FAILED_NOT_IDLE = "无法在空闲时使用";
SPELL_FAILED_NOT_INACTIVE = "无法在未激活时使用";
SPELL_FAILED_NOT_INFRONT = "你必须站在目标前方。";
SPELL_FAILED_NOT_IN_ARENA = "在竞技场内不能那么做。";
SPELL_FAILED_NOT_IN_BARBERSHOP = "你不能在理发店中那么做";
SPELL_FAILED_NOT_IN_BATTLEGROUND = "你不能在战场中那么做。";
SPELL_FAILED_NOT_IN_CONTROL = "你无法控制你自己的动作";
SPELL_FAILED_NOT_IN_RAID_INSTANCE = "你不能在团队副本中那么做。";
SPELL_FAILED_NOT_KNOWN = "法术尚未学会";
SPELL_FAILED_NOT_MOUNTED = "你正在骑乘状态。";
SPELL_FAILED_NOT_ON_DAMAGE_IMMUNE = "无法对一个免疫伤害的目标施放法术";
SPELL_FAILED_NOT_ON_GROUND = "不能在地面上使用";
SPELL_FAILED_NOT_ON_MOUNTED = "无法对骑乘状态下的单位施放该法术。";
SPELL_FAILED_NOT_ON_SHAPESHIFT = "无法对变形的目标使用。";
SPELL_FAILED_NOT_ON_STEALTHED = "无法对潜行目标施放该法术。";
SPELL_FAILED_NOT_ON_TAXI = "你正在飞行";
SPELL_FAILED_NOT_ON_TRANSPORT = "你正在运输工具上";
SPELL_FAILED_NOT_READY = "尚未恢复";
SPELL_FAILED_NOT_SHAPESHIFT = "你正在变形状态下";
SPELL_FAILED_NOT_STANDING = "你必须处于站立状态下才能那么做";
SPELL_FAILED_NOT_TRADEABLE = "你只能对自己拥有的物品使用这个";
SPELL_FAILED_NOT_TRADING = "你已试图给一件交易物品附魔，但不会进行交易";
SPELL_FAILED_NOT_UNSHEATHED = "你必须拔出武器才能那么做";
SPELL_FAILED_NOT_WHILE_FATIGUED = "疲倦时不能施法";
SPELL_FAILED_NOT_WHILE_GHOST = "无法在鬼魂状态下施放";
SPELL_FAILED_NOT_WHILE_LOOTING = "你正在拾取物品";
SPELL_FAILED_NOT_WHILE_TRADING = "当交易的时候，无法施法";
SPELL_FAILED_NO_AMMO = "弹药不足";
SPELL_FAILED_NO_CHAMPION = "你还没有选择一位勇士";
SPELL_FAILED_NO_CHARGES_REMAIN = "无使用次数";
SPELL_FAILED_NO_COMBO_POINTS = "这个技能需要连击点数";
SPELL_FAILED_NO_DUELING = "此地不允许进行决斗。";
SPELL_FAILED_NO_EDIBLE_CORPSES = "附近没有可用的尸体";
SPELL_FAILED_NO_ENDURANCE = "耐力不足";
SPELL_FAILED_NO_EVASIVE_CHARGES = "你需要闪避能量";
SPELL_FAILED_NO_FISH = "这里没有鱼。";
SPELL_FAILED_NO_ITEMS_WHILE_SHAPESHIFTED = "不能在变形状态下使用物品";
SPELL_FAILED_NO_MAGIC_TO_CONSUME = "法力值不足";
SPELL_FAILED_NO_MOUNTS_ALLOWED = "你不能在这里召唤坐骑。";
SPELL_FAILED_NO_PET = "你没有宠物";
SPELL_FAILED_NO_PLAYTIME = "你不能这么做。你的在线时间已经超过5小时。";
SPELL_FAILED_ONLY_ABOVEWATER = "不能在游泳的时候使用";
SPELL_FAILED_ONLY_BATTLEGROUNDS = "只能在战场中使用";
SPELL_FAILED_ONLY_DAYTIME = "只能在白天使用";
SPELL_FAILED_ONLY_INDOORS = "只能在室内使用";
SPELL_FAILED_ONLY_IN_ARENA = "你只能在竞技场中那么做。";
SPELL_FAILED_ONLY_MOUNTED = "在坐骑上才能使用";
SPELL_FAILED_ONLY_NIGHTTIME = "只能在夜晚使用";
SPELL_FAILED_ONLY_OUTDOORS = "只能在室外使用";
SPELL_FAILED_ONLY_SHAPESHIFT = "必须在%s中";
SPELL_FAILED_ONLY_STEALTHED = "你必须在潜行状态下。";
SPELL_FAILED_ONLY_UNDERWATER = "只有在游泳的时候才能够使用";
SPELL_FAILED_OUT_OF_RANGE = "超出范围";
SPELL_FAILED_PACIFIED = "不能在平静状态使用这个能力";
SPELL_FAILED_PARTIAL_PLAYTIME = "你不能这么做。你的在线时间已经超过3小时。";
SPELL_FAILED_PET_CAN_RENAME = "你可以给宠物了重新命名了";
SPELL_FAILED_POSSESSED = "你被占据了";
SPELL_FAILED_PREVENTED_BY_MECHANIC = "无法在%s时那样做";
SPELL_FAILED_REAGENTS = "缺少材料：%s";
SPELL_FAILED_REPUTATION = "你的声望不够高";
SPELL_FAILED_REQUIRES_AREA = "你必须处于%s状态下。";
SPELL_FAILED_REQUIRES_SPELL_FOCUS = "需要%s";
SPELL_FAILED_ROCKET_PACK = "你离扎弗德的传送器太远了！火箭背包无法发射。";
SPELL_FAILED_ROOTED = "你无法移动";
SPELL_FAILED_SILENCED = "不能在沉默状态中实施该动作";
SPELL_FAILED_SPELL_IN_PROGRESS = "另外一个动作正在进行中";
SPELL_FAILED_SPELL_LEARNED = "你已经学会了这个法术";
SPELL_FAILED_SPELL_UNAVAILABLE = "你无法使用这个法术";
SPELL_FAILED_SPELL_UNAVAILABLE_PET = "你的宠物无法学习这个技能";
SPELL_FAILED_STUNNED = "不能在昏迷状态实施该动作";
SPELL_FAILED_SUMMON_PENDING = "已经进行了召唤";
SPELL_FAILED_TARGETS_DEAD = "目标已经死亡";
SPELL_FAILED_TARGET_AFFECTING_COMBAT = "目标正处于战斗状态中";
SPELL_FAILED_TARGET_AURASTATE = "你还不能那么做";
SPELL_FAILED_TARGET_CANNOT_BE_RESURRECTED = "目标无法被复活";
SPELL_FAILED_TARGET_DUELING = "目标正在决斗中";
SPELL_FAILED_TARGET_ENEMY = "目标处于敌对状态";
SPELL_FAILED_TARGET_ENRAGED = "目标过于愤怒而无法被魅惑";
SPELL_FAILED_TARGET_FREEFORALL = "目标正处于自由PvP状态";
SPELL_FAILED_TARGET_FRIENDLY = "目标处于友好状态";
SPELL_FAILED_TARGET_IN_COMBAT = "目标无法进入战斗状态";
SPELL_FAILED_TARGET_IS_PLAYER = "不能将玩家作为目标";
SPELL_FAILED_TARGET_IS_PLAYER_CONTROLLED = "无法将玩家控制的单位作为目标";
SPELL_FAILED_TARGET_IS_TRIVIAL = "无效目标";
SPELL_FAILED_TARGET_LOCKED_TO_RAID_INSTANCE = "目标已锁定在另一个团队副本中";
SPELL_FAILED_TARGET_NOT_DEAD = "目标是活着的";
SPELL_FAILED_TARGET_NOT_GHOST = "你的军阶还不足以装备该物品";
SPELL_FAILED_TARGET_NOT_IN_INSTANCE = "目标必须在这个副本中";
SPELL_FAILED_TARGET_NOT_IN_PARTY = "目标和你不在同一个队伍中";
SPELL_FAILED_TARGET_NOT_IN_RAID = "目标不在你的小队或团队中";
SPELL_FAILED_TARGET_NOT_IN_SANCTUARY = "目标不在安全区域内";
SPELL_FAILED_TARGET_NOT_LOOTED = "必须先拿走该生物身上的物品";
SPELL_FAILED_TARGET_NOT_PLAYER = "目标不是玩家角色";
SPELL_FAILED_TARGET_NO_POCKETS = "没有物品可以拿取";
SPELL_FAILED_TARGET_NO_RANGED_WEAPONS = "目标没有装备远程武器";
SPELL_FAILED_TARGET_NO_WEAPONS = "目标没有装备任何武器";
SPELL_FAILED_TARGET_ON_TAXI = "你的目标正在飞行";
SPELL_FAILED_TARGET_UNSKINNABLE = "这个生物无法被剥皮";
SPELL_FAILED_TOO_CLOSE = "目标过于接近";
SPELL_FAILED_TOO_MANY_OF_ITEM = "你已经拥有过多的同类物品";
SPELL_FAILED_TOO_SHALLOW = "水太浅了";
SPELL_FAILED_TOTEMS = "需要%s";
SPELL_FAILED_TOTEM_CATEGORY = "需要 %s";
SPELL_FAILED_TRANSFORM_UNUSABLE = "你无法使用新物品";
SPELL_FAILED_TRY_AGAIN = "尝试失败";
SPELL_FAILED_UNIQUE_GLYPH = "该雕文已经嵌入你的法术书了。";
SPELL_FAILED_UNIT_NOT_BEHIND = "目标必须在你身后。";
SPELL_FAILED_UNIT_NOT_INFRONT = "你必须面对目标。";
SPELL_FAILED_UNKNOWN = "未知原因";
SPELL_FAILED_WRONG_PET_FOOD = "你的宠物不喜欢这种食物。";
SPELL_FAILED_WRONG_WEATHER = "目前的气候不适合那样做";
SPELL_HASTE = "急速等级";
SPELL_HASTE_ABBR = "Haste";
SPELL_HASTE_TOOLTIP = "使你的施法速度提高%.2f%%。";
SPELL_HEAL_COMBATLOG_TOOLTIP = "当法术或技能恢复生命值时显示信息。";
SPELL_INSTAKILL_COMBATLOG_TOOLTIP = "显示有关特殊法术类型的信息，比如造成耐久度损失或者与宠物分担伤害的法术。";
SPELL_INSTANT_EFFECT = "即时效果";
SPELL_INTERRUPT_COMBATLOG_TOOLTIP = "当法术或技能打断另一个法术时显示信息。";
SPELL_LASTING_EFFECT = "%s效果";
SPELL_MESSAGES = "法术信息";
SPELL_MISSED_COMBATLOG_TOOLTIP = "当法术或技能未能造成伤害时显示信息。这包括躲闪、招架、格挡、偏转、免疫、吸收和闪避。";
SPELL_NAMES = "法术名";
SPELL_NAMES_COMBATLOG_TOOLTIP = "彩色标记法术名。";
SPELL_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP = "在法术名称外显示括号。";
SPELL_NOT_SHAPESHIFTED = "不能在变形状态下实施该动作";
SPELL_NOT_SHAPESHIFTED_NOSPACE = "不能在变形状态下实施该动作";
SPELL_ON_NEXT_RANGED = "攻击速度";
SPELL_ON_NEXT_SWING = "下一次近战武器攻击";
SPELL_OTHER_MESSAGES = "法术信息（倒计数）";
SPELL_PASSIVE = "被动";
SPELL_PASSIVE_EFFECT = "被动";
SPELL_PENETRATION = "穿透";
SPELL_PENETRATION_TOOLTIP = "法术穿透%d（降低目标抗性%d点）";
SPELL_PERIODIC_COMBATLOG_TOOLTIP = "显示法术效果增量的信息。";
SPELL_PERIODIC_DAMAGE_COMBATLOG_TOOLTIP = "当法术或技能周期性地造成伤害时（如暗言术：痛或腐蚀术）显示信息。";
SPELL_PERIODIC_HEAL_COMBATLOG_TOOLTIP = "当法术或技能周期性地恢复生命值时（如恢复或回春术）显示信息。";
SPELL_PERIODIC_MISSED_COMBATLOG_TOOLTIP = "当法术或技能未能造成周期性的伤害时显示信息。这包括免疫、吸收和闪避。";
SPELL_PERIODIC_OTHER_COMBATLOG_TOOLTIP = "当法术或技能周期性地产生其它效果时（如恢复或吸取法力值）显示信息。";
SPELL_POINTS_SPREAD_TEMPLATE = "%.1f到%.1f";
SPELL_RANGE = "%s码射程";
SPELL_RANGE_AREA = "施法者周围区域";
SPELL_RANGE_DUAL = "%1$s：%2$s码范围";
SPELL_RANGE_UNLIMITED = "无限距离";
SPELL_REAGENTS = "材料：|n";
SPELL_RECAST_TIME_INSTANT = "即时冷却";
SPELL_RECAST_TIME_MIN = "%.3g分钟冷却时间";
SPELL_RECAST_TIME_SEC = "%.3g秒冷却时间";
SPELL_REQUIRED_FORM = "需要%s";
SPELL_REQUIRED_FORM_NOSPACE = "需要%s";
SPELL_RESURRECT_COMBATLOG_TOOLTIP = "当法术或技能复活另一个玩家或怪物时显示信息。";
SPELL_SCHOOL0_CAP = "物理";
SPELL_SCHOOL0_NAME = "物理";
SPELL_SCHOOL1_CAP = "神圣";
SPELL_SCHOOL1_NAME = "神圣";
SPELL_SCHOOL2_CAP = "火焰";
SPELL_SCHOOL2_NAME = "火焰";
SPELL_SCHOOL3_CAP = "自然";
SPELL_SCHOOL3_NAME = "自然";
SPELL_SCHOOL4_CAP = "冰霜";
SPELL_SCHOOL4_NAME = "冰霜";
SPELL_SCHOOL5_CAP = "暗影";
SPELL_SCHOOL5_NAME = "暗影";
SPELL_SCHOOL6_CAP = "奥术";
SPELL_SCHOOL6_NAME = "奥术";
SPELL_SCHOOLALL = "全部";
SPELL_SCHOOLMAGICAL = "魔法的";
SPELL_SKILL_LINE = "%s";
SPELL_STAT1_NAME = "力量";
SPELL_STAT2_NAME = "敏捷";
SPELL_STAT3_NAME = "耐力";
SPELL_STAT4_NAME = "智力";
SPELL_STAT5_NAME = "精神";
SPELL_STATALL = "所有属性";
SPELL_SUMMON_COMBATLOG_TOOLTIP = "当法术召唤出怪物或创造一个物体时显示信息。";
SPELL_TARGET_CENTER_CASTER = "施法者";
SPELL_TARGET_CENTER_LOC = "目标位置";
SPELL_TARGET_CHAIN_TEMPLATE = "目标%s，对%d个目标产生连锁效果";
SPELL_TARGET_CONE_TEMPLATE = "目标%1$s，位于%3$s面前锥形范围%2$d码内";
SPELL_TARGET_CREATURE_TYPE12_DESC = "所有%s";
SPELL_TARGET_CREATURE_TYPE13_DESC = "敌方%s";
SPELL_TARGET_CREATURE_TYPE1_DESC = "%s";
SPELL_TARGET_CREATURE_TYPE2_DESC = "友方%s";
SPELL_TARGET_CREATURE_TYPE3_DESC = "敌方%s";
SPELL_TARGET_CREATURE_TYPE8_DESC = "%s宠物";
SPELL_TARGET_CREATURE_TYPE_DEAD12_DESC = "所有已死亡%s";
SPELL_TARGET_CREATURE_TYPE_DEAD13_DESC = "死亡的敌方%s";
SPELL_TARGET_CREATURE_TYPE_DEAD1_DESC = "死亡的%s";
SPELL_TARGET_CREATURE_TYPE_DEAD2_DESC = "死亡的友方%s";
SPELL_TARGET_CREATURE_TYPE_DEAD3_DESC = "死亡的敌方%s";
SPELL_TARGET_CREATURE_TYPE_DEAD8_DESC = "死亡的%s宠物";
SPELL_TARGET_MULTIPLE_TEMPLATE = "目标%1$s，位于%3$s周围%2$d码的范围内";
SPELL_TARGET_TEMPLATE = "目标%s";
SPELL_TARGET_TYPE0_DESC = "施法者";
SPELL_TARGET_TYPE10_DESC = "副手物品";
SPELL_TARGET_TYPE11_DESC = "队伍";
SPELL_TARGET_TYPE12_DESC = "全部";
SPELL_TARGET_TYPE13_DESC = "敌人";
SPELL_TARGET_TYPE14_DESC = "队伍成员";
SPELL_TARGET_TYPE15_DESC = "主人";
SPELL_TARGET_TYPE16_DESC = "团队成员";
SPELL_TARGET_TYPE17_DESC = "团队成员";
SPELL_TARGET_TYPE1_DESC = "任何";
SPELL_TARGET_TYPE2_DESC = "友方";
SPELL_TARGET_TYPE3_DESC = "敌人";
SPELL_TARGET_TYPE4_DESC = "队伍成员";
SPELL_TARGET_TYPE5_DESC = "物品";
SPELL_TARGET_TYPE6_DESC = "所在地区";
SPELL_TARGET_TYPE7_DESC = "物体";
SPELL_TARGET_TYPE8_DESC = "宠物";
SPELL_TARGET_TYPE9_DESC = "主手物品";
SPELL_TARGET_TYPE_DEAD11_DESC = "死亡的队伍成员";
SPELL_TARGET_TYPE_DEAD12_DESC = "所有已死亡人物";
SPELL_TARGET_TYPE_DEAD13_DESC = "死亡的敌人";
SPELL_TARGET_TYPE_DEAD14_DESC = "死亡的队伍成员";
SPELL_TARGET_TYPE_DEAD16_DESC = "死亡的团队成员";
SPELL_TARGET_TYPE_DEAD17_DESC = "死亡的团队成员";
SPELL_TARGET_TYPE_DEAD1_DESC = "死亡的";
SPELL_TARGET_TYPE_DEAD2_DESC = "死亡的友方";
SPELL_TARGET_TYPE_DEAD3_DESC = "死亡的敌人";
SPELL_TARGET_TYPE_DEAD4_DESC = "死亡的队伍成员";
SPELL_TARGET_TYPE_DEAD8_DESC = "死亡的宠物";
SPELL_TIMER = "%s失败：你还没有完全恢复。";
SPELL_TIME_REMAINING_DAYS = "剩余%d天";
SPELL_TIME_REMAINING_HOURS = "剩余%d小时";
SPELL_TIME_REMAINING_MIN = "剩余%d分钟";
SPELL_TIME_REMAINING_SEC = "剩余%d秒";
SPELL_TOTEMS = "工具：";
SPELL_USE_ALL_ENERGY = "使用100%的能量值";
SPELL_USE_ALL_FOCUS = "使用全部集中值";
SPELL_USE_ALL_HEALTH = "使用全部生命值";
SPELL_USE_ALL_MANA = "使用全部法力值";
SPELL_USE_ALL_POWER_DISPLAY = "使用100%的%s";
SPELL_USE_ALL_RAGE = "使用全部怒气值";
SPI = "Spi";
SPIRIT_COLON = "精神：";
SPIRIT_HEALER_RELEASE_RED = "|cffff2020灵魂医者|r";
SPIRIT_TOOLTIP = "提高生命值和法力值的回复速度。精神\n影响所有角色在战斗状态下和非战斗状态下的\n法力值和生命值回复速度。";
STA = "Sta";
STABLED_PETS = "已存放的宠物：";
STABLES = "宠物存放处";
STABLE_PET_INFO_TEXT = "%1$s 等级 %2$d %3$s|n%4$s";
STABLE_PET_INFO_TOOLTIP_TEXT = "等级%1$d %2$s|n%3$s";
STABLE_SLOT_TEXT = "你想要再购买一个宠物存放栏位吗？";
STACKS = "%d 堆";
STAMINA_COLON = "耐力：";
STAMINA_TOOLTIP = "提高生命值";
STANDING = "关系";
START = "开始";
STARTING_PRICE = "起始价格";
STARTUP_TEXT_LINE1 = "输入 '/help 以获取简略的命令列表。";
STARTUP_TEXT_LINE2 = "";
STARTUP_TEXT_LINE3 = "";
STARTUP_TEXT_LINE4 = "";
STATISTICS = "统计数字";
STATS_LABEL = "状态：";
STATUS = "状态";
STATUSTEXT_LABEL = "状态文字";
STATUSTEXT_SUBTEXT = "这些选项可以让你在状态栏上显示详细信息。";
STATUS_BAR_TEXT = "状态栏数值";
STATUS_TEXT = "状态文字";
STATUS_TEXT_PARTY = "小队";
STATUS_TEXT_PERCENT = "显示百分比";
STATUS_TEXT_PET = "宠物";
STATUS_TEXT_PLAYER = "玩家";
STATUS_TEXT_TARGET = "目标";
STAT_ATTACK_POWER = "攻击强度提高%d点";
STAT_BLOCK = "格挡";
STAT_BLOCK_TOOLTIP = "格挡值提高%d";
STAT_DODGE = "躲闪";
STAT_EXPERTISE = "精准";
STAT_FORMAT = "%s：";
STAT_PARRY = "招架";
STAT_RESILIENCE = "韧性";
STAT_TEMPLATE = "%s状态";
STEREO_HARDWARE_CURSOR = "硬件指针";
STEREO_VIDEO_LABEL = "立体画面";
STEREO_VIDEO_SUBTEXT = "这些选项使你可以调整立体画面的各种细节。";
STOPWATCH_TIME_UNIT = "%02d";
STOPWATCH_TITLE = "秒表";
STOP_AUTO_ATTACK = "停止自动攻击";
STOP_IGNORE = "删除玩家";
STR = "Str";
STRENGTH_COLON = "力量：";
STRENGTH_TOOLTIP = "增加你的攻击强度和每秒伤害。\n力量不会影响爆击的几率。力量\n不会提高你的格挡几率，但是可以增加\n你每次格挡所减少的伤害。这一部分\n减少的伤害一部分由你的力量决定，\n另一部分由盾牌本身的属性决定。";
STRING_ENVIRONMENTAL_DAMAGE_DROWNING = "溺水";
STRING_ENVIRONMENTAL_DAMAGE_FALLING = "掉落";
STRING_ENVIRONMENTAL_DAMAGE_FATIGUE = "疲倦";
STRING_ENVIRONMENTAL_DAMAGE_FIRE = "火焰";
STRING_ENVIRONMENTAL_DAMAGE_LAVA = "熔岩";
STRING_ENVIRONMENTAL_DAMAGE_SLIME = "淤泥";
STRING_SCHOOL_ARCANE = "奥术";
STRING_SCHOOL_CHAOS = "混乱";
STRING_SCHOOL_CHROMATIC = "多彩";
STRING_SCHOOL_DIVINE = "神圣";
STRING_SCHOOL_ELEMENTAL = "元素";
STRING_SCHOOL_FIRE = "火焰";
STRING_SCHOOL_FIRESTORM = "火焰风暴";
STRING_SCHOOL_FLAMESTRIKE = "烈焰打击";
STRING_SCHOOL_FROST = "冰霜";
STRING_SCHOOL_FROSTFIRE = "霜火";
STRING_SCHOOL_FROSTSTORM = "冰霜风暴";
STRING_SCHOOL_FROSTSTRIKE = "冰霜打击";
STRING_SCHOOL_HOLY = "神圣";
STRING_SCHOOL_HOLYFIRE = "神圣火焰";
STRING_SCHOOL_HOLYFROST = "神圣冰霜";
STRING_SCHOOL_HOLYSTORM = "神圣风暴";
STRING_SCHOOL_HOLYSTRIKE = "神圣打击";
STRING_SCHOOL_MAGIC = "魔法";
STRING_SCHOOL_NATURE = "自然";
STRING_SCHOOL_PHYSICAL = "物理";
STRING_SCHOOL_SHADOW = "暗影";
STRING_SCHOOL_SHADOWFLAME = "暗影烈焰";
STRING_SCHOOL_SHADOWFROST = "暗影冰霜";
STRING_SCHOOL_SHADOWHOLY = "暮光";
STRING_SCHOOL_SHADOWLIGHT = "暮光";
STRING_SCHOOL_SHADOWSTORM = "瘟疫";
STRING_SCHOOL_SHADOWSTRIKE = "暗影打击";
STRING_SCHOOL_SPELLFIRE = "法术火焰";
STRING_SCHOOL_SPELLFROST = "法术冰霜";
STRING_SCHOOL_SPELLSHADOW = "法术暗影";
STRING_SCHOOL_SPELLSTORM = "法术风暴";
STRING_SCHOOL_SPELLSTRIKE = "法术打击";
STRING_SCHOOL_STORMSTRIKE = "风暴打击";
STRING_SCHOOL_UNKNOWN = "未知";
STUCK_BUTTON2_TEXT = "如果还不行的话，请联系GM";
STUCK_BUTTON_TEXT = "自动脱离卡死";
STUN = "昏迷";
STUNNED = "昏迷";
STUN_CAPS = "昏迷";
SUBCATEGORY = "子类";
SUBMIT = "提交";
SUCCESS = "成功";
SUGGESTFRAME_TITLE = "建议和Bug";
SUGGEST_SUBMITTED = "建议已经提交。";
SUGGEST_SUBMIT_FAILED = "建议提交失败。";
SUGGEST_TOOLTIP_TEXT = "请输入你想要提交的Bug或者建议\n你的名字、种族、职业、等级和位置\n将被自动提交";
SUMMARY_ACHIEVEMENT_INCOMPLETE = "未获得的成就";
SUMMARY_ACHIEVEMENT_INCOMPLETE_TEXT = "达到每个成就所要求的条件，赢取成就点数、奖励和荣耀！";
SUMMON = "召唤";
SUMMONS = "召唤";
SWING_DAMAGE_COMBATLOG_TOOLTIP = "显示造成了全额或部分伤害的近战攻击。";
SWING_MISSED_COMBATLOG_TOOLTIP = "显示未能造成伤害的近战攻击。";
SYSTEM_DEFAULT = "系统默认";
SYSTEM_MESSAGES = "系统信息";
TABARDSLOT = "战袍";
TABARDVENDORALREADYSETGREETING = "你已经拥有了一个徽章，但是仍可随意试穿";
TABARDVENDORCOST = "花费：";
TABARDVENDORGREETING = "欢迎！请选择你的公会标志和颜色。";
TABARDVENDORNOGUILDGREETING = "只有公会首领才能购买公会战袍，但是你可以随意预览。";
TAKE_ATTACHMENTS = "拿取附件：";
TAKE_GM_SURVEY = "你愿意花时间填写一份GM调查问卷吗？";
TALENTS = "天赋";
TALENTS_BUTTON = "天赋";
TALENTS_INVOLUNTARILY_RESET = "你的天赋已被重置。";
TALENTS_INVOLUNTARILY_RESET_PET = "你的宠物天赋已被重置。";
TALENT_ACTIVE_SPEC_STATUS = "这是你当前启用的天赋";
TALENT_POINTS = "天赋点数";
TALENT_POINTS_TOOLTIP = "天赋点数通过天赋界面分配，其作用是\n提升你的作战能力。";
TALENT_SPECTAB_TOOLTIP_ACTIVE = "这是你当前启用的天赋配置";
TALENT_SPECTAB_TOOLTIP_POINTS_SPENT = "%1$s%2$s：%3$s%4$d点|r";
TALENT_SPEC_ACTIVATE = "启用这些天赋";
TALENT_SPEC_PET_PRIMARY = "宠物天赋";
TALENT_SPEC_PRIMARY = "主天赋";
TALENT_SPEC_PRIMARY_GLYPH = "主雕文";
TALENT_SPEC_SECONDARY = "副天赋";
TALENT_SPEC_SECONDARY_GLYPH = "副雕文";
TALENT_TOOLTIP_ADDPREVIEWPOINT = "左键点击以添加点数";
TALENT_TOOLTIP_LEARNTALENTGROUP = "点击这里确认你已预览过的天赋点数。";
TALENT_TOOLTIP_REMOVEPREVIEWPOINT = "右键点击以移除点数";
TALENT_TOOLTIP_RESETTALENTGROUP = "点击这里重置你的预览天赋点数。";
TALENT_TRAINER = "天赋训练师";
TAMEABLE = "可驯服";
TAMEABLE_EXOTIC = "可驯服（特殊）";
TANK = "坦克";
TARGET = "目标";
TARGETFOCUS = "选中焦点目标";
TARGETICONS = "目标图标";
TARGET_ICON_SET = "|Hplayer:%s|h[%s]|h将|TInterface\\TargetingFrame\\UI-RaidTargetingIcon_%d:0|t标记在%s的头上。";
TARGET_TOKEN_NOT_FOUND = "<没有目标>";
TASKS_COLON = "任务：";
TAXINODEYOUAREHERE = "你在这里";
TAXISAMENODE = "你已经在那里了！";
TEAM = "战队";
TEAM_KICK = "开除";
TEAM_LEAVE = "退出战队";
TEAM_PROMOTE = "提升为队长";
TEAM_SKILL_TOOLTIP = "匹配值是用来为本场比赛搜索对手的数字。";
TELEPORT_OUT_OF_DUNGEON = "传送出地下城";
TELEPORT_TO_DUNGEON = "传送到地下城";
TERRAIN_HIGHLIGHTS = "镜面照明";
TERRAIN_MIP = "地貌混合";
TEST_TAG_TEST = "do not translate";
TEXTURE_DETAIL = "材质分辨率";
TEXT_MODE_A = "A";
TEXT_MODE_A_STRING_1 = "%s %s%s%s%s。%s";
TEXT_MODE_A_STRING_2 = "%1$s %3$s%2$s%4$s%5$s。%6$s";
TEXT_MODE_A_STRING_3 = "%1$s%5$s%3$s%4$s%2$s。%6$s";
TEXT_MODE_A_STRING_4 = "$timestamp $source$spell$action$dest$value。$result";
TEXT_MODE_A_STRING_5 = "$timestamp $source$spell$action$dest$value。$result";
TEXT_MODE_A_STRING_ACTION = "|Haction:%s|h%s|h";
TEXT_MODE_A_STRING_BRACE_ITEM = "|c%s[|r%s|c%s]|r";
TEXT_MODE_A_STRING_BRACE_SPELL = "|c%s[|r%s|c%s]|r";
TEXT_MODE_A_STRING_BRACE_UNIT = "|c%s[|r%s|c%s]|r";
TEXT_MODE_A_STRING_DEST = "$destIcon$destString";
TEXT_MODE_A_STRING_DEST_ICON = "|Hicon:%d:dest|h%s|h";
TEXT_MODE_A_STRING_DEST_UNIT = "%s|Hunit:%s:%s|h%s|h";
TEXT_MODE_A_STRING_ITEM = "|Hitem:%s|h%s|h";
TEXT_MODE_A_STRING_POSSESSIVE = "%s的";
TEXT_MODE_A_STRING_POSSESSIVE_STRING = "的";
TEXT_MODE_A_STRING_RESULT = " （$resultString）";
TEXT_MODE_A_STRING_RESULT_ABSORB = "（%d 吸收）";
TEXT_MODE_A_STRING_RESULT_BLOCK = "（%d 格挡）";
TEXT_MODE_A_STRING_RESULT_CRITICAL = "（爆击）";
TEXT_MODE_A_STRING_RESULT_CRITICAL_SPELL = "（爆击）";
TEXT_MODE_A_STRING_RESULT_CRUSHING = "（碾压）";
TEXT_MODE_A_STRING_RESULT_FORMAT = "$resultAmount点$resultType";
TEXT_MODE_A_STRING_RESULT_GLANCING = "（偏斜）";
TEXT_MODE_A_STRING_RESULT_OVERHEALING = "（%d 过量治疗）";
TEXT_MODE_A_STRING_RESULT_OVERKILLING = "（%d 过量伤害）";
TEXT_MODE_A_STRING_RESULT_REFLECT = "被反射";
TEXT_MODE_A_STRING_RESULT_RESIST = "（%d 抵抗）";
TEXT_MODE_A_STRING_RESULT_VULNERABILITY = "（%d 易伤伤害）";
TEXT_MODE_A_STRING_SOURCE = "$sourceIcon$sourceString";
TEXT_MODE_A_STRING_SOURCE_ICON = "|Hicon:%s:source|h%s|h";
TEXT_MODE_A_STRING_SOURCE_UNIT = "%s|Hunit:%s:%s|h%s|h";
TEXT_MODE_A_STRING_SPELL = "|Hspell:%s:%s|h%s|h";
TEXT_MODE_A_STRING_SPELL_EXTRA = "|Hspell:%s:%s|h%s|h";
TEXT_MODE_A_STRING_SPELL_EXTRA_LINK = "|Hspell:%s:%s|h%s|h (SpellID:%d)";
TEXT_MODE_A_STRING_SPELL_LINK = "|Hspell:%s:%s|h%s|h (SpellID:%d)";
TEXT_MODE_A_STRING_TIMESTAMP = "%s> %s";
TEXT_MODE_A_STRING_TOKEN_ICON = "$icon";
TEXT_MODE_A_STRING_VALUE = "$amount$amountType";
TEXT_MODE_A_STRING_VALUE_SCHOOL = "%s %s";
TEXT_MODE_A_STRING_VALUE_TYPE = "%s（%s）";
TEXT_MODE_A_TIMESTAMP = "%H:%M:%S";
THIS_DUNGEON_IN_PROGRESS = "该地下城正在进行中。";
THREAT_TOOLTIP = "%d%% 威胁";
TICKET_STATUS = "你发出了求助信息。";
TICKET_TYPE1 = "游戏";
TICKET_TYPE2 = "骚扰";
TICKET_TYPE3 = "卡死";
TICKET_TYPE4 = "Bug";
TIMEMANAGER_12HOUR = "%d";
TIMEMANAGER_24HOUR = "%02d";
TIMEMANAGER_24HOURMODE = "24小时模式";
TIMEMANAGER_ALARM_DISABLED = "提醒已关闭";
TIMEMANAGER_ALARM_ENABLED = "提醒已开启";
TIMEMANAGER_ALARM_MESSAGE = "提醒信息";
TIMEMANAGER_ALARM_TIME = "提醒时间";
TIMEMANAGER_ALARM_TOOLTIP_TURN_OFF = "点击这里关闭提醒。";
TIMEMANAGER_AM = "AM";
TIMEMANAGER_LOCALTIME = "使用本地时间";
TIMEMANAGER_MINUTE = "%02d";
TIMEMANAGER_PM = "PM";
TIMEMANAGER_SHOW_STOPWATCH = "显示秒表";
TIMEMANAGER_TICKER_12HOUR = "%d:%02d";
TIMEMANAGER_TICKER_24HOUR = "%02d:%02d";
TIMEMANAGER_TITLE = "时钟";
TIMEMANAGER_TOOLTIP_LOCALTIME = "本地时间：";
TIMEMANAGER_TOOLTIP_REALMTIME = "服务器时间：";
TIMEMANAGER_TOOLTIP_TITLE = "时间信息";
TIMESTAMPS_LABEL = "聊天时间戳|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
TIMESTAMP_COMBATLOG_TOOLTIP = "显示战斗记录信息的时间戳。";
TIMESTAMP_FORMAT_HHMM = "%I:%M";
TIMESTAMP_FORMAT_HHMMSS = "%I:%M:%S";
TIMESTAMP_FORMAT_HHMMSS_24HR = "%H:%M:%S";
TIMESTAMP_FORMAT_HHMMSS_AMPM = "%I:%M:%S %p";
TIMESTAMP_FORMAT_HHMM_24HR = "%H:%M";
TIMESTAMP_FORMAT_HHMM_AMPM = "%I:%M %p";
TIMESTAMP_FORMAT_NONE = "不显示";
TIME_DAYHOURMINUTESECOND = "%d天，%d小时，%d分钟，%d秒";
TIME_ELAPSED = "已用时间：";
TIME_IN_QUEUE = "队列时间：%s";
TIME_PLAYED_LEVEL = "你在这个等级的游戏时间：%s";
TIME_PLAYED_MSG = "已玩本游戏的时间";
TIME_PLAYED_TOTAL = "总游戏时间：%s";
TIME_REMAINING = "剩余时间：";
TIME_TEMPLATE_LONG = "%d天，%d小时，%d分钟，%d秒";
TIME_TO_PORT = "即将传送：";
TIME_TO_PORT_ARENA = "竞技场关闭倒计时：";
TIME_TWELVEHOURAM = "%d:%02d AM";
TIME_TWELVEHOURPM = "%d:%02d PM";
TIME_TWENTYFOURHOURS = "%d：%02d";
TIME_UNIT_DELIMITER = "";
TIME_UNKNOWN = "未知";
TIME_UNTIL_DELETED = "信息保留时间";
TIME_UNTIL_RETURNED = "信息退回时间";
TITLE_DOESNT_EXIST = "你没有该头衔。";
TITLE_REWARD = "奖励：%s";
TITLE_TEMPLATE = "%2$s - %1$s";
TOAST_DURATION_TEXT = "浮窗持续时间";
TOGGLESTICKYCAMERA = "切换视角";
TOGGLE_BATTLEFIELDMINIMAP_TOOLTIP = "打开/关闭区域地图。快捷键：<Shift>-M";
TOKENS = "货币";
TOKEN_MOVE_TO_UNUSED = "将这种货币移动到你的列表底部的“未使用”类别中。可以有效地归类那些你不再关心的货币类型。";
TOKEN_OPTIONS = "货币设置";
TOKEN_SHOW_ON_BACKPACK = "钩选此项可以在你的行囊上显示此类货币的数量。\n\n你也可以按住Shift点击某种货币，从行囊中添加或移除它。";
TOOLTIP_ARENA_POINTS = "竞技场点数是通过在竞技场战斗中获胜而赢得的。你可以消费这些点数来购买强大的奖励品！";
TOOLTIP_HONOR_POINTS = "荣誉点数是通过在PvP战斗中杀死敌对阵营的成员获得的。你可以使用荣誉点数购买特殊的物品。";
TOOLTIP_RAID_CLASS_BUTTON = "点击并拖拽这个按钮可以为该职业生成单独的团队窗口。";
TOOLTIP_RAID_CONTROL_TIP = "CTRL拖拽 - 职业";
TOOLTIP_RAID_DRAG_TIP = "点击并拖拽可以为该玩家生成单独的团队窗口。";
TOOLTIP_RAID_SHIFT_TIP = "按住SHIFT键并拖拽，可以为该玩家创建一个单独的团队窗口。";
TOOLTIP_TALENT_LEARN = "点击这里学习";
TOOLTIP_TALENT_NEXT_RANK = "下一级：";
TOOLTIP_TALENT_PREREQ = "需要%1$d点%2$s天赋";
TOOLTIP_TALENT_RANK = "等级 %d/%d";
TOOLTIP_TALENT_TIER_POINTS = "需要%1$d点%2$s天赋";
TOOLTIP_TRACKER_FILTER_ACHIEVEMENTS = "取消此项来隐藏所追踪的成就。";
TOOLTIP_TRACKER_FILTER_COMPLETED_QUESTS = "取消此项来隐藏已完成的任务。";
TOOLTIP_TRACKER_FILTER_REMOTE_ZONES = "取消此项来隐藏不处于你当前所在地图上的任务。";
TOOLTIP_TRACKER_SORT_DIFFICULTY_HIGH = "勾选此项，可使任务按照从难到易的顺序排序。";
TOOLTIP_TRACKER_SORT_DIFFICULTY_LOW = "勾选此项，可使任务按照从易到难的顺序排序。";
TOOLTIP_TRACKER_SORT_MANUAL = "勾选此项，可使任务以任何你所选择的顺序排列。";
TOOLTIP_TRACKER_SORT_PROXIMITY = "勾选此项，可使任务按照任务地点与你相隔距离的远近排序。";
TOOLTIP_UNIT_LEVEL = "等级 %s";
TOOLTIP_UNIT_LEVEL_CLASS = "等级 %s %s";
TOOLTIP_UNIT_LEVEL_CLASS_TYPE = "等级 %s %s（%s）";
TOOLTIP_UNIT_LEVEL_RACE_CLASS = "等级 %s %s %s";
TOOLTIP_UNIT_LEVEL_RACE_CLASS_TYPE = "等级 %s %s %s（%s）";
TOOLTIP_UNIT_LEVEL_TYPE = "等级 %s（%s）";
TOO_FAR_TO_LOOT = "你离尸体太远，无法拾取！";
TOO_MANY_LUA_ERRORS = "你的插件有大量错误，可能会导致游戏速度降低。你可以在界面选项中打开Lua错误显示。";
TOO_MANY_WATCHED_TOKENS = "你在同一时间内只能追踪%d种货币";
TOTAL_MEM_KB_ABBR = "插件内存：%.0f KB";
TOTAL_MEM_MB_ABBR = "插件内存：%.0f MB";
TRACKER_FILTER_ACHIEVEMENTS = "成就";
TRACKER_FILTER_COMPLETED_QUESTS = "已完成任务";
TRACKER_FILTER_LABEL = "显示";
TRACKER_FILTER_REMOTE_ZONES = "远距离区域";
TRACKER_SORT_DIFFICULTY_HIGH = "从难到易";
TRACKER_SORT_DIFFICULTY_LOW = "从易到难";
TRACKER_SORT_LABEL = "对任务进行排序";
TRACKER_SORT_MANUAL = "手动";
TRACKER_SORT_MANUAL_BOTTOM = "移至底部";
TRACKER_SORT_MANUAL_DOWN = "下移";
TRACKER_SORT_MANUAL_TOP = "移至顶部";
TRACKER_SORT_MANUAL_UP = "上移";
TRACKER_SORT_MANUAL_WARNING = "任务排序已改为手动";
TRACKER_SORT_PROXIMITY = "距离远近";
TRACK_ACHIEVEMENT = "追踪";
TRACK_ACHIEVEMENT_TOOLTIP = "钩选此项以追踪这个成就。";
TRACK_QUEST = "追踪任务";
TRACK_QUEST_ABBREV = "追踪";
TRADE = "交易";
TRADEFRAME_ENCHANT_SLOT_LABEL = "不会被交易";
TRADEFRAME_NOT_MODIFIED_TEXT = "物品未被改变";
TRADESKILLS = "商业技能";
TRADESKILL_LOG_FIRSTPERSON = "你制造了%s。";
TRADESKILL_LOG_THIRDPERSON = "%s制造了%s。";
TRADESKILL_SERVICE_LEARN = "配方";
TRADESKILL_SERVICE_PASSIVE = "被动";
TRADESKILL_SERVICE_STEP = "发展技能";
TRADE_POTENTIAL_BIND_ENCHANT = "将此物品附魔会使其与你绑定。";
TRADE_SKILLS = "专业";
TRADE_SKILL_TITLE = "%s";
TRADE_WITH_QUESTION = "和%s交易吗？";
TRAIN = "训练";
TRAINER_CAST_TIME_INSTANT = "施放时间：|cffffffff即时|r";
TRAINER_CAST_TIME_MIN = "施放时间：|cffffffff%d分|r";
TRAINER_CAST_TIME_SEC = "施放时间：|cffffffff%d秒|r";
TRAINER_COOLDOWN_TIME_INSTANT = "冷却时间：|cffffffff即时|r";
TRAINER_COOLDOWN_TIME_MIN = "冷却时间：|cffffffff%d分|r";
TRAINER_COOLDOWN_TIME_SEC = "冷却时间：|cffffffff%d秒|r";
TRAINER_COST_SP = "技能点数 |cffffffff%d|r";
TRAINER_COST_SP_RED = "技能点数 |cffff2020%d|r";
TRAINER_COST_TP = "天赋点数 |cffffffff%d|r";
TRAINER_COST_TP_RED = "天赋点数 |cffff2020%d|r";
TRAINER_LIST_SP = "%d SP";
TRAINER_MANA_COST = "法力消耗：|cffffffff%d|r";
TRAINER_MANA_COST_PER_TIME = "法力消耗：|cffffffff%d，外加%d每秒|r";
TRAINER_RANGE = "射程：|cffffffff%s (%d)|r";
TRAINER_REQ_ABILITY = "|cffffffff%s|r";
TRAINER_REQ_ABILITY_RED = "|cffff2020%s|r";
TRAINER_REQ_LEVEL = "等级 |cffffffff%d|r";
TRAINER_REQ_LEVEL_RED = "等级 |cffff2020%d|r";
TRAINER_REQ_SKILL_RANK = "%s (|cffffffff%d|r)";
TRAINER_REQ_SKILL_RANK_RED = "%s (|cffff2020%d|r)";
TRANSFER_ABORT_DIFFICULTY1 = "%s没有普通难度模式的选项。";
TRANSFER_ABORT_DIFFICULTY2 = "英雄难度模式对%s不可用。";
TRANSFER_ABORT_DIFFICULTY3 = "%s没有史诗难度模式的选项。";
TRANSFER_ABORT_INSUF_EXPAN_LVL1 = "你必须安装《魔兽世界：燃烧的远征》才能进入该区域。";
TRANSFER_ABORT_INSUF_EXPAN_LVL2 = "您目前尚未满15周岁，无法开启相关游戏内容。";
TRANSFER_ABORT_MAP_NOT_ALLOWED = "现在无法进入地图。";
TRANSFER_ABORT_MAX_PLAYERS = "传输中断：副本已满";
TRANSFER_ABORT_NEED_GROUP = "传送中断：你必须在一个团队中才能进入此副本。";
TRANSFER_ABORT_NOT_FOUND = "传输失败：副本未找到";
TRANSFER_ABORT_REALM_ONLY = "传输中断：队伍中的所有玩家必须来自同一个服务器。";
TRANSFER_ABORT_TOO_MANY_INSTANCES = "你在短时间内进入副本的次数过多。";
TRANSFER_ABORT_TOO_MANY_REALM_INSTANCES = "无法启动更多副本，请稍后重试。";
TRANSFER_ABORT_UNIQUE_MESSAGE1 = "在你逃离巫妖王的魔掌之前，不能离开这里半步！";
TRANSFER_ABORT_ZONE_IN_COMBAT = "无法在战斗进行时切换所在地区。";
TRILINEAR_FILTERING = "三线过滤";
TRINKET0SLOT = "饰品";
TRINKET0SLOT_UNIQUE = "饰品1";
TRINKET1SLOT = "饰品";
TRINKET1SLOT_UNIQUE = "饰品2";
TRIPLE_BUFFER = "三倍缓冲";
TRIVIAL_QUEST_DISPLAY = "|cff000000%s （低等级）|r";
TURN_IN_ITEMS = "需要物品：";
TURN_IN_QUEST = "交还";
TUTORIAL1 = "通过与任务NPC交谈可以获得任务，任务NPC头顶上都会有一个感叹号。|n|n走近任务NPC后|cffffd200右键点击|r它便可与其交谈。";
TUTORIAL2 = "你可以使用A、S、D、W键来控制角色的移动。";
TUTORIAL3 = "|cffffd200按住右键|r再移动鼠标可以让你的角色转身。|n|n而按住左键并移动鼠标则可以调整视角。";
TUTORIAL4 = "|cffffd200右键点击|r友方角色可以与其交谈。|n|n有时候你需要靠近他们才能与其交谈。";
TUTORIAL5 = "要攻击一个敌人，首先|cffffd200左键点击|r将目标选中。然后点击屏幕下方动作条中的某个技能。|n|n目标较远时应使用远程攻击。其它技能则需要你靠近目标才能使用，特别是对于擅长近战武器的职业来说。";
TUTORIAL6 = "许多技能都属于武器攻击或战斗法术。|n|n不同职业的动作条上可能还会出现防御技能甚至是治疗法术。";
TUTORIAL7 = "当你击败了某个敌人后，发现它的尸体上闪着光芒，这就意味着它的身上有东西拾取。|n|n|cffffd200右键点击|r生物的尸体来对它进行搜索，然后在弹出的拾取窗口中|cffffd200右键点击|r 物品将其放入背包中。";
TUTORIAL8 = "一件物品已经装入你的背包。|n|n你可以点击屏幕右下角的|cffffd200背包按钮|r打开背包。将鼠标移动到物品上来查看它的信息。";
TUTORIAL9 = "某些物品可以通过|cffffd200右键点击|r来使用它。|n|n如果你想要不打开背包就使用物品的话，可以事先把要使用的物品拖入动作条中。";
TUTORIAL10 = "你获得了一个背包，现在你可以携带更多的东西了。|n|n把背包放入屏幕右下角的空余的背包空格处，然后就可以点击打开这个背包了。";
TUTORIAL11 = "你可以通过吃食物来加快生命值的回复速度。|cffffd200右键点击|r食物图标即可食用。";
TUTORIAL12 = "你可以通过喝饮料来加快法力值的回复速度。|cffffd200右键点击|r饮料图标即可饮用。|n|n不使用法力值的职业不需要喝饮料。";
TUTORIAL13 = "你可以在天赋界面学习一个新的天赋。|n|n天赋界面可以通过点击快捷栏上闪烁的|cffffd200天赋按钮|进入。";
TUTORIAL14 = "你可以拜访你的职业训练师来学习新的技能。|cffffd200右键点击|r训练师同其交谈。|n|n在新手区以及许多城镇都可以找到训练师。不过你可能要四处走动一下才能找到他们。";
TUTORIAL15 = "你可以点击屏幕中央下部的|cffffd200法术书和技能按钮|r，将法术或者技能的图标拖到动作条中。|n|n你也可以在技能页面中点击图标来使用相应的法术或者技能。";
TUTORIAL16 = "你可以在声望界面中查看你在世界上各个组织中的声望。|n|n点击|cffffd200角色面板按钮|r，从中找到声望界面。";
TUTORIAL17 = "在收到其他玩家发来的悄悄话之后，你可以按R键来进行回复，或者直接输入/tell <对方的名字>然后输入你想要发出的信息。";
TUTORIAL18 = "你可以|cffffd200右键点击|r对方的头像然后选择|cffffd200邀请|r来邀请另外一个玩家加入你的队伍。|n|n你也可以右键点击你的角色头像并选择离开小队。";
TUTORIAL19 = "那是另一个其他玩家控制的角色，他头上的蓝色名字说明了这一点。";
TUTORIAL20 = "|cffffd200右键点击|r商人处的物品便可购买此物品，前提是你有足够的金钱。|n|n在打开商人货物界面的情况下右键点击自己背包中的某个物品可以将其出售给商人。";
TUTORIAL21 = "你可以点击屏幕中央下部的|cffffd200任务日志按钮|r来查看当前的任务。|n|n你还可以点击屏幕右上角的|cffffd200世界地图按钮|r 来查看任务地点。";
TUTORIAL22 = "如果你和另外一个玩家相处得很愉快，就把这个玩家加入好友名单吧！点击|cffffd200社交按钮|r然后将其加入你的好友名单。|n|n你只能将自己所在服务器的玩家加为好友。";
TUTORIAL23 = "你可以按回车键然后输入一条信息，再次按下回车键将该信息发出。你旁边的玩家都可以收到这条信息。";
TUTORIAL24 = "要装备一件物品，首先点击屏幕下方的|cffffd200角色面板按钮|r和屏幕右下方的|cffffd200背包按钮|r。|n|n将可装备的物品从背包中拖拽到你的角色身上。";
TUTORIAL25 = "你现在处于鬼魂状态，可以通过回去找到自己的尸体或者和旁边的灵魂医者对话来复活。|n|n你的尸体以图标的形式显示在屏幕右上角的小地图上。";
TUTORIAL26 = "你现在处于精力充沛状态，将在杀死怪物后获得一定的额外经验值奖励。";
TUTORIAL27 = "如果你游到远离陆地的深水区，就会看到一个疲倦槽。当你的疲倦槽完全耗尽时，你就会溺水死亡。";
TUTORIAL28 = "游泳与走路基本相同，但游泳时按住|cffffd200鼠标右键|r并移动鼠标便可以向上或向下游动。";
TUTORIAL29 = "当你的角色潜入水中时，你会看到一个呼吸槽。呼吸槽一旦用光，你的角色就会开始溺水。";
TUTORIAL30 = "你的头像周围的黄色闪光表示你正在休息中。在休息状态下度过一段时间或者退出游戏之后，你下次登录游戏时可以获得暂时性的奖励，通过杀死怪物所获得的经验值提高。|n|n你可以使用炉石快速返回旅店进行休息。";
TUTORIAL31 = "炉石可以将你从现在的位置传送回你最后登记的那个旅店里。";
TUTORIAL32 = "你进入了PvP模式，在这种状态下你的头像旁会显示你所属阵营的标志，敌对阵营的玩家可以自由攻击你。";
TUTORIAL33 = "你可以按下|cffffd200空格键|r来进行跳跃。跳跃可以帮助你越过障碍物，跳跃本身也很有趣。";
TUTORIAL34 = "你已经完成了第一个任务！要获取奖励，你需要到特定的任务NPC那里完成任务，通常便是交给你该任务的NPC。|n|n任务NPC通常在屏幕右上方的小地图上显示为|cffffd200？|r。|cffffd200右键点击|r一个任务NPC进行交谈。";
TUTORIAL35 = "这是一个专门训练飞行兽并提供飞行运输服务的飞行管理员。只需一小笔花费，你就可以快速地飞往任何你曾经接触过的飞行管理员那里。|n|n当你发现一座新的城镇之后，最好先去找到该城镇的飞行管理员，以便日后能够快速抵达这里。";
TUTORIAL36 = "你的一件装备的耐久值已经很低。耐久值低的装备显示为小地图下方角色图标的黄色部分。|n|n找到城镇中的商人，在装备完全损坏前进行修理。";
TUTORIAL37 = "你的一件装备已经损坏！损坏的装备显示为小地图下方角色图标的红色部分。|n|n你可以找城镇中的商人为你修理装备。装备得到修理前将无法为你提供属性加成。";
TUTORIAL38 = "你的角色可以学习专业，如烹饪或锻造，专业使你能够采集或制造珍贵的物品。|n|n你可以向各大主城中的守卫询问各种专业的训练师所在的位置。";
TUTORIAL39 = "你可以邀请其他玩家与你组队，使战斗过程更为轻松。|n|n组队使你可以轻松完成许多困难的任务，队伍中的成员将共享杀死的怪物。而且组队时还可以获得一定的经验值奖励。";
TUTORIAL40 = "你学会了新的法术或技能！使用屏幕下方的|cffffd200法术书和技能按钮|r打开你的法术书。|cffffd200左键点击并拖拽|r一个法术或技能，将其移至你的动作条。|n|n法术书中的内容按种类分别保存在不同的页面中，点击法术书右侧的标签可以访问每个种类的页面。";
TUTORIAL41 = "你接受了一个精英任务。这类任务会要求你前往精英怪物出现的地区，因此建议组队完成。|n|n精英怪物比普通怪物坚韧得多；但杀死精英怪物也将获得更多的经验值。怪物头像周围的金龙边框标志着它是一只精英怪物。";
TUTORIAL42 = "你可以从一个|cffffd200任务|r开始你新的历程。站在你面前的便是一个任务NPC。教程内容会时刻帮助你。\n\n感谢你参加游戏，祝你在你的冒险旅程中好运！";
TUTORIAL43 = "如果NPC头顶上标有灰色感叹号，则表示你的等级还无法接受他所给予的任务。当你升级之后再回来看看吧。";
TUTORIAL44 = "你获得了一件远程武器。要使用它，请首先打开|cffffd200角色面板|r和|cffffd200背包|r，将要装备的远程武器拖拽到你的角色身上。|n|n然后打开你的|cffffd200法术书|r，将|cffffd200射击|r或|cffffd200投掷|r技能的图标拖拽到你的快捷栏中。|n|n只有猎人、战士和盗贼可以使用远程武器。法师、牧师和术士可以使用魔杖。";
TUTORIAL45 = "你无法在不装备弹药的情况下使用弓和枪。你可以到城镇中的枪械或弓箭商人处购买弹药，然后|cffffd200右键点击|r弹药以装备它。";
TUTORIAL46 = "你加入了一个团队（可以容纳最多40人的队伍，但通常由10人或25人组成）。挑战最危险的地下城或最强大的敌人，特别是高等级的敌人，都需要由团队来完成。\n\n|cffff2020当你在一个团队中时，就无法完成大多数非团队任务。要离开一个团队，右键点击你的角色头像并选择离开队伍。|r";
TUTORIAL47 = "你可以使用图腾栏了！以后每当你获得一个新的图腾元素，图腾栏上就会出现对应的按钮。|n|n每种图腾元素只能在该栏放置一个图腾法术，达到较高等级时，你会学到同时放置多个图腾的技能。";
TUTORIAL48 = "你正在等待进入战场的队列中。你可以将鼠标移至小地图旁的战场图标上以查看你的状态。";
TUTORIAL49 = "你现在可以加入战场了。点击对话框中的“加入战斗”按钮或右键点击小地图上的战场图标以加入战场。";
TUTORIAL50 = "你获得了存放钥匙的钥匙链。|cffffd200钥匙链按钮|r显示在动作条上背包的左边。";
TUTORIAL51 = "你可以使用|cffffd200地下城查找器|r寻找地下城队伍。通过画面底部快捷栏的眼球按钮来打开这个界面。";
TUTORIAL52 = "恭喜！你获得了你的第一个小宠物。这个小家伙会跟随你在艾泽拉斯以及更遥远的地方冒险。|n|n你可以通过|cffffd200角色面板|r中的宠物标签看到自己所有的小宠物。";
TUTORIAL53 = "恭喜！你已经学会了如何召唤你的第一头坐骑。这头强大的坐骑会载着你走遍艾泽拉斯以及更遥远的地方。|n|n你可以通过|cffffd200角色面板|r中的宠物标签看到自己所有的坐骑。你可以将最喜欢的坐骑的图标拖放到你的技能栏中，便于快速使用。";
TUTORIAL54 = "你已经引起了一个具有敌意的怪物的注意。|n|n红色泛光表示怪物正在攻击你。黄色泛光表示你有受到攻击的危险。橙色泛光表示怪物正在攻击你，但有可能即将更换目标。";
TUTORIAL55 = "恭喜！你刚刚升了一级，变得更加强大了。|n|n你可以去拜访你的职业训练师，看看他/她们是否有什么新技能可以教你。";
TUTORIAL56 = "屏幕左上角显示的是你的绿色生命条。当你的生命值减为零时，你将死亡，所以请一定小心。|n|n生命条下面的第二个条表明了你在使用职业特殊技能时所需的相关能量，比如怒气值，能量值或者法力值。一般情况下，它们都会缓慢地自行回满（怒气值除外）。";
TUTORIAL57 = "你有绿色的生命条，你的敌人同样也有。你可以在屏幕顶端看到他们的生命条，由此来判断你需要多长时间来击败他们。";
TUTORIAL58 = "你的背包已经满了，所以你无法再拾取任何物品。如果要清空背包，请访问游戏中的商人，向他们出售你不要的物品。|n|n每座主城、城镇和哨站都有商人。";
TUTORIAL59 = "如果你想清空一下自己的背包或换点钱花，可以将你所收集到的部分物品售出。|n|n先找到一名商人，|cffffd200右键点击|r他。然后打开背包，|cffffd200右键点击|r你想要出售的任何物品。";
TUTORIAL60 = "请记住，你的角色在屏幕下方的动作条中有特殊技能可以使用。|n|n利用这些技能，你能更加轻松迅速地击败敌人。";
TUTORIAL61 = "该版本为《魔兽世界》试玩版。部分功能将无法使用。要体验游戏的全部特色，敬请访问官方网站www.warcraftchina.com。";
TUTORIAL_TITLE1 = "任务NPC";
TUTORIAL_TITLE2 = "移动";
TUTORIAL_TITLE3 = "视角";
TUTORIAL_TITLE4 = "锁定目标";
TUTORIAL_TITLE5 = "战斗模式";
TUTORIAL_TITLE6 = "法术和技能";
TUTORIAL_TITLE7 = "正在拾取";
TUTORIAL_TITLE8 = "行囊";
TUTORIAL_TITLE9 = "使用物品";
TUTORIAL_TITLE10 = "背包";
TUTORIAL_TITLE11 = "进食";
TUTORIAL_TITLE12 = "喝水";
TUTORIAL_TITLE13 = "学习天赋中";
TUTORIAL_TITLE14 = "训练师";
TUTORIAL_TITLE15 = "法术书和技能";
TUTORIAL_TITLE16 = "声望";
TUTORIAL_TITLE17 = "回复对话";
TUTORIAL_TITLE18 = "组队";
TUTORIAL_TITLE19 = "玩家";
TUTORIAL_TITLE20 = "正在购买物品";
TUTORIAL_TITLE21 = "任务日志";
TUTORIAL_TITLE22 = "好友";
TUTORIAL_TITLE23 = "聊天";
TUTORIAL_TITLE24 = "可装备的物品";
TUTORIAL_TITLE25 = "死亡";
TUTORIAL_TITLE26 = "精力充沛";
TUTORIAL_TITLE27 = "疲倦";
TUTORIAL_TITLE28 = "游泳";
TUTORIAL_TITLE29 = "呼吸";
TUTORIAL_TITLE30 = "旅店";
TUTORIAL_TITLE31 = "炉石";
TUTORIAL_TITLE32 = "玩家对玩家战斗";
TUTORIAL_TITLE33 = "跳跃";
TUTORIAL_TITLE34 = "完成任务";
TUTORIAL_TITLE35 = "旅行";
TUTORIAL_TITLE36 = "损坏的物品";
TUTORIAL_TITLE37 = "破损的物品";
TUTORIAL_TITLE38 = "专业";
TUTORIAL_TITLE39 = "组队";
TUTORIAL_TITLE40 = "法术书";
TUTORIAL_TITLE41 = "精英任务";
TUTORIAL_TITLE42 = "欢迎来到魔兽世界！";
TUTORIAL_TITLE43 = "不可用的任务NPC";
TUTORIAL_TITLE44 = "远程武器";
TUTORIAL_TITLE45 = "弹药";
TUTORIAL_TITLE46 = "团队";
TUTORIAL_TITLE47 = "图腾栏";
TUTORIAL_TITLE48 = "战场队列";
TUTORIAL_TITLE49 = "传送到战场";
TUTORIAL_TITLE50 = "钥匙链";
TUTORIAL_TITLE51 = "地下城查找器";
TUTORIAL_TITLE52 = "小宠物";
TUTORIAL_TITLE53 = "坐骑";
TUTORIAL_TITLE54 = "威胁警报";
TUTORIAL_TITLE55 = "升级！";
TUTORIAL_TITLE56 = "玩家状态条";
TUTORIAL_TITLE57 = "敌人状态条";
TUTORIAL_TITLE58 = "背包已满";
TUTORIAL_TITLE59 = "清理背包空间";
TUTORIAL_TITLE60 = "特殊技能";
TUTORIAL_TITLE61 = "试玩版";
TWOHANDEDWEAPONBEINGWIELDED = "不能装备在副手上，因为你现在持有的是双手武器！";
TWO_HANDED = "双手武器";
TYPE = "类型";
TYPE_LFR_COMMENT_HERE = "在此处输入你的注释。其他玩家将在团队浏览器中看到这些注释。";
UIOPTIONS_MENU = "界面";
UI_DEPTH = "UI深度";
UI_HIDDEN = "用户界面已隐藏。按下%s可以重新显示。";
UI_SCALE = "UI缩放";
UKNOWNBEING = "未知生物";
UNABLE_TO_REFUND_ITEM = "很抱歉，无法退还该物品。";
UNAVAILABLE = "不可用";
UNBIND = "解除键位";
UNEXTEND_RAID_LOCK = "移除副本锁定延长";
UNITFRAME_LABEL = "单位框体";
UNITFRAME_SUBTEXT = "这些选项可以用来改变用户界面中的单位框体的显示设定。";
UNITNAME_SUMMON_TITLE1 = "%s的宠物";
UNITNAME_SUMMON_TITLE10 = "%s的坐骑";
UNITNAME_SUMMON_TITLE11 = "%s的光明之泉";
UNITNAME_SUMMON_TITLE12 = "%s的管家";
UNITNAME_SUMMON_TITLE2 = "%s的守护者";
UNITNAME_SUMMON_TITLE3 = "%s的爪牙";
UNITNAME_SUMMON_TITLE4 = "%s的图腾";
UNITNAME_SUMMON_TITLE5 = "%s的小伙伴";
UNITNAME_SUMMON_TITLE6 = "%s的符文之刃";
UNITNAME_SUMMON_TITLE7 = "%s的构造体";
UNITNAME_SUMMON_TITLE8 = "%s的对手";
UNITNAME_SUMMON_TITLE9 = "%s的载具";
UNITNAME_TITLE = "%s";
UNITNAME_TITLE_CHARM = "%s的仆从";
UNITNAME_TITLE_COMPANION = "%s的小伙伴";
UNITNAME_TITLE_CREATION = "%s的创造物";
UNITNAME_TITLE_GUARDIAN = "%s的守护者";
UNITNAME_TITLE_MINION = "%s的仆从";
UNITNAME_TITLE_OPPONENT = "%s的对手";
UNITNAME_TITLE_PET = "%s的宠物";
UNITNAME_TITLE_SQUIRE = "%s的侍从";
UNIT_COLORS = "单位颜色：";
UNIT_LETHAL_LEVEL_DEAD_TEMPLATE = "等级未知 尸体";
UNIT_LETHAL_LEVEL_TEMPLATE = "等级未知";
UNIT_LEVEL_DEAD_TEMPLATE = "等级%d 尸体";
UNIT_LEVEL_TEMPLATE = "等级 %d";
UNIT_NAMEPLATES = "单位姓名板";
UNIT_NAMEPLATES_ALLOW_OVERLAP = "允许单位姓名板重叠";
UNIT_NAMEPLATES_SHOW_ENEMIES = "敌对单位";
UNIT_NAMEPLATES_SHOW_ENEMY_GUARDIANS = "守护者";
UNIT_NAMEPLATES_SHOW_ENEMY_PETS = "宠物";
UNIT_NAMEPLATES_SHOW_ENEMY_TOTEMS = "图腾";
UNIT_NAMEPLATES_SHOW_FRIENDLY_GUARDIANS = "守护者";
UNIT_NAMEPLATES_SHOW_FRIENDLY_PETS = "宠物";
UNIT_NAMEPLATES_SHOW_FRIENDLY_TOTEMS = "图腾";
UNIT_NAMEPLATES_SHOW_FRIENDS = "友方单位";
UNIT_NAMES = "单位名称";
UNIT_NAMES_COMBATLOG_TOOLTIP = "彩色标记单位名。";
UNIT_NAMES_SHOW_BRACES_COMBATLOG_TOOLTIP = "在单位名称外显示括号。";
UNIT_NAME_ENEMY = "敌方玩家";
UNIT_NAME_ENEMY_GUARDIANS = "守护者";
UNIT_NAME_ENEMY_PETS = "宠物";
UNIT_NAME_ENEMY_TOTEMS = "图腾";
UNIT_NAME_FRIENDLY = "友方玩家";
UNIT_NAME_FRIENDLY_GUARDIANS = "守护者";
UNIT_NAME_FRIENDLY_PETS = "宠物";
UNIT_NAME_FRIENDLY_TOTEMS = "图腾";
UNIT_NAME_GUILD = "公会名";
UNIT_NAME_NONCOMBAT_CREATURE = "小伙伴";
UNIT_NAME_NPC = "NPC名字";
UNIT_NAME_OWN = "我的名字";
UNIT_NAME_PLAYER_TITLE = "头衔";
UNIT_PLUS_LEVEL_TEMPLATE = "等级%d 精英";
UNIT_PVP_NAME = "%s %s%s";
UNIT_SKINNABLE_BOLTS = "需要工程学";
UNIT_SKINNABLE_HERB = "需要草药学技能";
UNIT_SKINNABLE_LEATHER = "可剥皮";
UNIT_SKINNABLE_ROCK = "需要采矿技能";
UNIT_TYPE_LETHAL_LEVEL_TEMPLATE = "等级未知 %s";
UNIT_TYPE_LEVEL_TEMPLATE = "等级%d %s";
UNIT_TYPE_PLUS_LEVEL_TEMPLATE = "等级%d 精英%s";
UNIT_YOU = "你";
UNIT_YOU_DEST = "你";
UNIT_YOU_DEST_POSSESSIVE = "你的";
UNIT_YOU_SOURCE = "你";
UNIT_YOU_SOURCE_POSSESSIVE = "你的";
UNKNOWN = "未知";
UNKNOWNOBJECT = "未知目标";
UNLEARN = "忘却这个技能";
UNLEARN_SKILL = "你要忘却%s？";
UNLEARN_SKILL_TOOLTIP = "忘却这个技能";
UNLIMITED = "无限";
UNLIST_ME = "取消列出我的名字";
UNLIST_MY_GROUP = "取消列出我的队伍";
UNLOCK_FOCUS_FRAME = "解锁框体";
UNLOCK_WINDOW = "解锁窗口";
UNMUTE = "解除禁声";
UNSPENT_TALENT_POINTS = "剩余天赋点数：%s";
UNTRACK_ACHIEVEMENT_TOOLTIP = "反选此项以停止追踪这个成就。";
UNUSED = "未使用";
UPDATE = "更新";
USABLE_ITEMS = "可用物品";
USE = "使用";
USED = "已经学会";
USE_COLON = "使用：";
USE_COLORBLIND_MODE = "色盲模式";
USE_ENGLISH_AUDIO = "使用英文语音";
USE_EQUIPMENT_MANAGER = "启用装备管理";
USE_FULL_TEXT_MODE = "使用详细模式";
USE_GUILDBANK_REPAIR = "你想要使用公会资金修理吗？";
USE_ITEM = "使用物品";
USE_NO_DROP = "使用该物品后会将它和你绑定";
USE_PERSONAL_FUNDS = "使用个人资金";
USE_SOULSTONE = "复活";
USE_UBERTOOLTIPS = "详细提示信息";
USE_UISCALE = "使用UI缩放";
USE_WEATHER_SHADER = "天气遮罩";
VEHICLE_LEAVE = "离开";
VEHICLE_STEAM = "蒸汽";
VERBAL_HARASSMENT = "语言骚扰";
VERBAL_HARASSMENT_DESCRIPTION = "一个玩家对于另外一个玩家的语言攻击。";
VERBAL_HARASSMENT_TEXT1 = "以下是一些对付语言骚扰的方法：";
VERBAL_HARASSMENT_TEXT2 = "要求他/她停止骚扰，并将这个玩家加入你的屏蔽名单中。";
VERBAL_HARASSMENT_TEXT3 = "保证你不再遭受更多的骚扰。";
VERBAL_HARASSMENT_TEXT4 = "报告言论中包含种族歧视、性和/或暴力内容的行为。此类行为均被视为骚扰。";
VERTEX_ANIMATION_SHADERS = "顶点动态遮罩";
VERTICAL_SYNC = "垂直同步";
VICTORY_TEXT0 = "部落获胜";
VICTORY_TEXT1 = "联盟获胜";
VICTORY_TEXT_ARENA0 = "绿队获胜";
VICTORY_TEXT_ARENA1 = "金队获胜";
VICTORY_TEXT_ARENA_DRAW = "平局";
VICTORY_TEXT_ARENA_WINS = "%s 胜利";
VIDEOOPTIONS_MENU = "视频";
VIDEO_QUALITY_LABEL1 = "低";
VIDEO_QUALITY_LABEL2 = "普通";
VIDEO_QUALITY_LABEL3 = "优良";
VIDEO_QUALITY_LABEL4 = "高";
VIDEO_QUALITY_LABEL5 = "极佳";
VIDEO_QUALITY_LABEL6 = "自定义";
VIDEO_QUALITY_S = "画面质量：%s";
VIDEO_QUALITY_SUBTEXT1 = "这些选项是最低的游戏画面设置推荐方案，可以使你获得最佳的运行速度。";
VIDEO_QUALITY_SUBTEXT2 = "中等材质细节，短距离描绘范围，低法术细节。这些选项可以使你获得优秀的运行速度。";
VIDEO_QUALITY_SUBTEXT3 = "高材质细节，中等距离描绘范围，中等法术细节。用于平衡画面效果和运行速度。";
VIDEO_QUALITY_SUBTEXT4 = "高材质细节，最远的距离描绘范围，高法术细节。这些选项可以使你获得很高的画面质量。";
VIDEO_QUALITY_SUBTEXT5 = "这些设置可以提供最佳的画面质量，建议仅在高端系统上使用。";
VIDEO_QUALITY_SUBTEXT6 = "自定义各种描绘距离、细节层次和效果。设置决定效果和运行速度。";
VIEW_FRIENDS_OF_FRIENDS = "查看好友";
VOICE = "语音";
VOICECHAT_DISABLED = "已禁用语音聊天";
VOICECHAT_DISABLED_TEXT = "另一个本游戏的程序正在运行，或者你的电脑不支持语音聊天，因此语音聊天已被禁止。";
VOICEMACRO_0_Dw_0 = "帮助我！";
VOICEMACRO_0_Dw_0_FEMALE = "帮助我！";
VOICEMACRO_0_Dw_1 = "我需要帮助！";
VOICEMACRO_0_Dw_1_FEMALE = "我需要帮助！";
VOICEMACRO_0_Gn_0 = "你能帮助我吗？";
VOICEMACRO_0_Gn_0_FEMALE = "你能帮助我吗？";
VOICEMACRO_0_Gn_1 = "请帮助我！";
VOICEMACRO_0_Gn_1_FEMALE = "请帮助我！";
VOICEMACRO_0_Gn_2_FEMALE = "我需要帮助！";
VOICEMACRO_0_Hu_0 = "我需要协助！";
VOICEMACRO_0_Hu_0_FEMALE = "我需要帮助！";
VOICEMACRO_0_Hu_1 = "帮帮我！";
VOICEMACRO_0_Hu_1_FEMALE = "帮助我！";
VOICEMACRO_0_Ni_0 = "帮帮我！";
VOICEMACRO_0_Ni_0_FEMALE = "帮帮我！";
VOICEMACRO_0_Ni_1 = "来我这边！";
VOICEMACRO_0_Ni_1_FEMALE = "协助我！";
VOICEMACRO_0_Ni_2 = "协助我！";
VOICEMACRO_0_Or_0 = "我需要帮助！";
VOICEMACRO_0_Or_0_FEMALE = "我需要帮助！";
VOICEMACRO_0_Or_1 = "帮帮我！";
VOICEMACRO_0_Or_1_FEMALE = "帮帮我！";
VOICEMACRO_0_Sc_0 = "我需要帮助！";
VOICEMACRO_0_Sc_0_FEMALE = "需要你的帮助！";
VOICEMACRO_0_Sc_1 = "帮助我！";
VOICEMACRO_0_Sc_1_FEMALE = "帮助我！";
VOICEMACRO_0_Ta_0 = "支援我！";
VOICEMACRO_0_Ta_0_FEMALE = "帮帮我！";
VOICEMACRO_0_Ta_1 = "帮助我！";
VOICEMACRO_0_Ta_1_FEMALE = "帮助我！";
VOICEMACRO_0_Ta_2 = "帮帮我！";
VOICEMACRO_0_Tr_0 = "帮助我！";
VOICEMACRO_0_Tr_0_FEMALE = "帮助我！";
VOICEMACRO_0_Tr_1 = "协助我！";
VOICEMACRO_0_Tr_1_FEMALE = "协助我！";
VOICEMACRO_10_Dw_0 = "开火！";
VOICEMACRO_10_Dw_0_FEMALE = "开枪！";
VOICEMACRO_10_Dw_1 = "开火！";
VOICEMACRO_10_Dw_1_FEMALE = "开火！";
VOICEMACRO_10_Gn_0 = "快，开火！";
VOICEMACRO_10_Gn_0_FEMALE = "射击！";
VOICEMACRO_10_Gn_1 = "射击！";
VOICEMACRO_10_Gn_1_FEMALE = "开火！";
VOICEMACRO_10_Hu_0 = "自由射击！";
VOICEMACRO_10_Hu_0_FEMALE = "攻击！";
VOICEMACRO_10_Hu_1 = "开火！";
VOICEMACRO_10_Hu_1_FEMALE = "开火！";
VOICEMACRO_10_Ni_0 = "自由射击！";
VOICEMACRO_10_Ni_0_FEMALE = "快点攻击！";
VOICEMACRO_10_Ni_1 = "开火！";
VOICEMACRO_10_Ni_1_FEMALE = "开火！";
VOICEMACRO_10_Or_0 = "射击！";
VOICEMACRO_10_Or_0_FEMALE = "开火！";
VOICEMACRO_10_Or_1 = "让我们解决它！";
VOICEMACRO_10_Or_1_FEMALE = "开火！";
VOICEMACRO_10_Or_2_FEMALE = "射击！";
VOICEMACRO_10_Sc_0 = "开枪！";
VOICEMACRO_10_Sc_0_FEMALE = "开火！";
VOICEMACRO_10_Sc_1 = "开火！";
VOICEMACRO_10_Sc_1_FEMALE = "开火！";
VOICEMACRO_10_Ta_0 = "释放你的怒气吧！";
VOICEMACRO_10_Ta_0_FEMALE = "射击！";
VOICEMACRO_10_Ta_1 = "现在攻击！";
VOICEMACRO_10_Ta_1_FEMALE = "现在攻击！";
VOICEMACRO_10_Tr_0 = "射击！";
VOICEMACRO_10_Tr_0_FEMALE = "向他们射击！";
VOICEMACRO_10_Tr_1 = "干掉他们!";
VOICEMACRO_10_Tr_1_FEMALE = "干掉他们!";
VOICEMACRO_12_Dw_0 = "发生了什么？";
VOICEMACRO_12_Dw_0_FEMALE = "你好。";
VOICEMACRO_12_Dw_1 = "你好。";
VOICEMACRO_12_Dw_1_FEMALE = "你好啊。";
VOICEMACRO_12_Dw_2 = "你好。";
VOICEMACRO_12_Dw_2_FEMALE = "你好啊。";
VOICEMACRO_12_Dw_3 = "你好啊。";
VOICEMACRO_12_Gn_0 = "你好。";
VOICEMACRO_12_Gn_0_FEMALE = "你好。";
VOICEMACRO_12_Gn_1 = "你好。";
VOICEMACRO_12_Gn_1_FEMALE = "你好。";
VOICEMACRO_12_Gn_2 = "打招呼.";
VOICEMACRO_12_Gn_2_FEMALE = "你好啊。";
VOICEMACRO_12_Gn_3 = "你好啊。";
VOICEMACRO_12_Hu_0 = "你好。";
VOICEMACRO_12_Hu_0_FEMALE = "你好。";
VOICEMACRO_12_Hu_1 = "好消息";
VOICEMACRO_12_Hu_1_FEMALE = "你好。";
VOICEMACRO_12_Hu_2 = "欢呼。";
VOICEMACRO_12_Hu_2_FEMALE = "你好。";
VOICEMACRO_12_Hu_3 = "幸会。";
VOICEMACRO_12_Ni_0 = "你好。";
VOICEMACRO_12_Ni_0_FEMALE = "你好。";
VOICEMACRO_12_Ni_1 = "幸会。";
VOICEMACRO_12_Ni_1_FEMALE = "你好。";
VOICEMACRO_12_Ni_2 = "你好。";
VOICEMACRO_12_Ni_2_FEMALE = "幸会。";
VOICEMACRO_12_Ni_3_FEMALE = "你好。";
VOICEMACRO_12_Or_0 = "你好。";
VOICEMACRO_12_Or_0_FEMALE = "你好。";
VOICEMACRO_12_Or_1 = "嘿。";
VOICEMACRO_12_Or_1_FEMALE = "嘿。";
VOICEMACRO_12_Or_2 = "你好。";
VOICEMACRO_12_Or_2_FEMALE = "你好。";
VOICEMACRO_12_Sc_0 = "你好。";
VOICEMACRO_12_Sc_0_FEMALE = "你好。";
VOICEMACRO_12_Sc_1 = "你好啊。";
VOICEMACRO_12_Sc_1_FEMALE = "你好啊。";
VOICEMACRO_12_Sc_2 = "你好。";
VOICEMACRO_12_Sc_2_FEMALE = "嘿。";
VOICEMACRO_12_Ta_0 = "你好。";
VOICEMACRO_12_Ta_0_FEMALE = "你好。";
VOICEMACRO_12_Ta_1 = "你好。";
VOICEMACRO_12_Ta_1_FEMALE = "你好。";
VOICEMACRO_12_Ta_2 = "你好。";
VOICEMACRO_12_Ta_2_FEMALE = "你好啊。";
VOICEMACRO_12_Tr_0 = "嘿，叫你呢。";
VOICEMACRO_12_Tr_0_FEMALE = "你好。";
VOICEMACRO_12_Tr_1 = "嘿，伙计。";
VOICEMACRO_12_Tr_1_FEMALE = "嘿，叫你呢。";
VOICEMACRO_12_Tr_2 = "发生了什么？";
VOICEMACRO_12_Tr_2_FEMALE = "你好。";
VOICEMACRO_13_Dw_0 = "保重。";
VOICEMACRO_13_Dw_0_FEMALE = "好长啊。";
VOICEMACRO_13_Dw_1 = "愿我们能再次相见。";
VOICEMACRO_13_Dw_1_FEMALE = "再见。";
VOICEMACRO_13_Dw_2 = "再见。";
VOICEMACRO_13_Dw_2_FEMALE = "再见。";
VOICEMACRO_13_Gn_0 = "保重。";
VOICEMACRO_13_Gn_0_FEMALE = "保重。";
VOICEMACRO_13_Gn_1 = "再见。";
VOICEMACRO_13_Gn_1_FEMALE = "再见。";
VOICEMACRO_13_Gn_2 = "那很有趣。";
VOICEMACRO_13_Gn_2_FEMALE = "这可是个教训。";
VOICEMACRO_13_Gn_3 = "我会想念你的。";
VOICEMACRO_13_Gn_3_FEMALE = "我会一直珍稀我们在一起的时光的。";
VOICEMACRO_13_Hu_0 = "保重。";
VOICEMACRO_13_Hu_0_FEMALE = "再见。";
VOICEMACRO_13_Hu_1 = "愿我们能再次相见。";
VOICEMACRO_13_Hu_1_FEMALE = "愿我们能再次相见。";
VOICEMACRO_13_Hu_2 = "那很有趣。";
VOICEMACRO_13_Hu_2_FEMALE = "那很有趣。";
VOICEMACRO_13_Ni_0 = "祝你一路顺风。";
VOICEMACRO_13_Ni_0_FEMALE = "再见。";
VOICEMACRO_13_Ni_1 = "再见。";
VOICEMACRO_13_Ni_1_FEMALE = "我祝你一切安好。";
VOICEMACRO_13_Ni_2 = "我祝你一切安好。";
VOICEMACRO_13_Ni_2_FEMALE = "愿我们能再次相见。";
VOICEMACRO_13_Or_0 = "愿我们能够再次见面。";
VOICEMACRO_13_Or_0_FEMALE = "保持强大。";
VOICEMACRO_13_Or_1 = "保持强大。";
VOICEMACRO_13_Or_1_FEMALE = "愿我们百战常胜。";
VOICEMACRO_13_Or_2 = "愿你的剑真的如此锋利。";
VOICEMACRO_13_Or_2_FEMALE = "NPC商人没有物品出售或者没有反映";
VOICEMACRO_13_Sc_0 = "再见。";
VOICEMACRO_13_Sc_0_FEMALE = "再见。";
VOICEMACRO_13_Sc_1 = "祝你一天过得不愉快。";
VOICEMACRO_13_Sc_1_FEMALE = "这是真的。";
VOICEMACRO_13_Sc_2 = "保持乐观。";
VOICEMACRO_13_Sc_2_FEMALE = "祝你好运。";
VOICEMACRO_13_Ta_0 = "祝你一路顺风。";
VOICEMACRO_13_Ta_0_FEMALE = "保重。";
VOICEMACRO_13_Ta_1 = "愿我们能再次相见。";
VOICEMACRO_13_Ta_1_FEMALE = "再见。";
VOICEMACRO_13_Ta_2 = "愿你一路顺风。";
VOICEMACRO_13_Ta_2_FEMALE = "愿我们能再次相见。";
VOICEMACRO_13_Tr_0 = "再见。";
VOICEMACRO_13_Tr_0_FEMALE = "再见。";
VOICEMACRO_13_Tr_1 = "这是真的。";
VOICEMACRO_13_Tr_1_FEMALE = "好长啊。";
VOICEMACRO_13_Tr_2 = "回头见。";
VOICEMACRO_13_Tr_2_FEMALE = "再见。";
VOICEMACRO_14_Dw_0 = "是的。";
VOICEMACRO_14_Dw_0_FEMALE = "当然。";
VOICEMACRO_14_Dw_1 = "当然。";
VOICEMACRO_14_Dw_1_FEMALE = "真直接。";
VOICEMACRO_14_Dw_2 = "是的。";
VOICEMACRO_14_Dw_2_FEMALE = "当然。";
VOICEMACRO_14_Dw_3 = "真直接。";
VOICEMACRO_14_Gn_0 = "当然。";
VOICEMACRO_14_Gn_0_FEMALE = "当然。";
VOICEMACRO_14_Gn_1 = "是的。";
VOICEMACRO_14_Gn_1_FEMALE = "当然。";
VOICEMACRO_14_Gn_2 = "确实如此。";
VOICEMACRO_14_Gn_2_FEMALE = "确实如此。";
VOICEMACRO_14_Hu_0 = "当然。";
VOICEMACRO_14_Hu_0_FEMALE = "是的。";
VOICEMACRO_14_Hu_1 = "听上去不错。";
VOICEMACRO_14_Hu_1_FEMALE = "当然。";
VOICEMACRO_14_Hu_2 = "噢噢。";
VOICEMACRO_14_Hu_2_FEMALE = "为什么不。";
VOICEMACRO_14_Ni_0 = "是的。";
VOICEMACRO_14_Ni_0_FEMALE = "是的。";
VOICEMACRO_14_Ni_1 = "当然。";
VOICEMACRO_14_Ni_1_FEMALE = "当然。";
VOICEMACRO_14_Ni_2 = "自然。";
VOICEMACRO_14_Ni_2_FEMALE = "自然。";
VOICEMACRO_14_Or_0 = "你好。";
VOICEMACRO_14_Or_0_FEMALE = "你好。";
VOICEMACRO_14_Or_1 = "Zug zug.";
VOICEMACRO_14_Or_1_FEMALE = "Zug zug.";
VOICEMACRO_14_Or_2 = "（兽人步兵）";
VOICEMACRO_14_Or_2_FEMALE = "（兽人步兵）";
VOICEMACRO_14_Or_3 = "是的。";
VOICEMACRO_14_Or_3_FEMALE = "噢噢。";
VOICEMACRO_14_Sc_0 = "是的。";
VOICEMACRO_14_Sc_0_FEMALE = "是的。";
VOICEMACRO_14_Sc_1 = "当然。";
VOICEMACRO_14_Sc_1_FEMALE = "为什么不。";
VOICEMACRO_14_Sc_2 = "我认为是这样的。";
VOICEMACRO_14_Sc_2_FEMALE = "我认为是这样的。";
VOICEMACRO_14_Ta_0 = "是的。";
VOICEMACRO_14_Ta_0_FEMALE = "是的。";
VOICEMACRO_14_Ta_1 = "我是那么认为的。";
VOICEMACRO_14_Ta_1_FEMALE = "自然。";
VOICEMACRO_14_Ta_2 = "事情应该就是这样。";
VOICEMACRO_14_Ta_2_FEMALE = "当然。";
VOICEMACRO_14_Tr_0 = "当然可以，伙计。";
VOICEMACRO_14_Tr_0_FEMALE = "当然。";
VOICEMACRO_14_Tr_1 = "当然。";
VOICEMACRO_14_Tr_1_FEMALE = "挖它。";
VOICEMACRO_14_Tr_2 = "我一直感觉得到你。";
VOICEMACRO_14_Tr_2_FEMALE = "是的，伙计。";
VOICEMACRO_14_Tr_3 = "搞笑语音";
VOICEMACRO_15_Dw_0 = "不。";
VOICEMACRO_15_Dw_0_FEMALE = "不。";
VOICEMACRO_15_Dw_1 = "噢噢。";
VOICEMACRO_15_Dw_1_FEMALE = "随你喜欢去拿吧。";
VOICEMACRO_15_Dw_2 = "不可能。";
VOICEMACRO_15_Dw_2_FEMALE = "门都没有";
VOICEMACRO_15_Dw_3 = "决不。";
VOICEMACRO_15_Gn_0 = "不。";
VOICEMACRO_15_Gn_0_FEMALE = "不可能。";
VOICEMACRO_15_Gn_1 = "不可能。";
VOICEMACRO_15_Gn_1_FEMALE = "决不。";
VOICEMACRO_15_Gn_2 = "我不这么认为。";
VOICEMACRO_15_Gn_2_FEMALE = "我不这么认为。";
VOICEMACRO_15_Hu_0 = "不。";
VOICEMACRO_15_Hu_0_FEMALE = "不。";
VOICEMACRO_15_Hu_1 = "不。";
VOICEMACRO_15_Hu_1_FEMALE = "不可能。";
VOICEMACRO_15_Hu_2 = "我不这么认为。";
VOICEMACRO_15_Hu_2_FEMALE = "我不这么认为。";
VOICEMACRO_15_Hu_3 = "不。";
VOICEMACRO_15_Ni_0 = "不。";
VOICEMACRO_15_Ni_0_FEMALE = "不。";
VOICEMACRO_15_Ni_1 = "我不这么认为。";
VOICEMACRO_15_Ni_1_FEMALE = "我不这么认为。";
VOICEMACRO_15_Ni_2 = "完全不是。";
VOICEMACRO_15_Ni_2_FEMALE = "完全不是。";
VOICEMACRO_15_Or_0 = "不。";
VOICEMACRO_15_Or_0_FEMALE = "不。";
VOICEMACRO_15_Or_1 = "你一定是在开玩笑。";
VOICEMACRO_15_Or_1_FEMALE = "决不。";
VOICEMACRO_15_Or_2 = "（兽人步兵）";
VOICEMACRO_15_Or_2_FEMALE = "（兽人步兵）";
VOICEMACRO_15_Sc_0 = "不。";
VOICEMACRO_15_Sc_0_FEMALE = "不。";
VOICEMACRO_15_Sc_1 = "决不。";
VOICEMACRO_15_Sc_1_FEMALE = "决不。";
VOICEMACRO_15_Sc_2 = "我不这么认为。";
VOICEMACRO_15_Sc_2_FEMALE = "我不这么认为。";
VOICEMACRO_15_Ta_0 = "不。";
VOICEMACRO_15_Ta_0_FEMALE = "不。";
VOICEMACRO_15_Ta_1 = "我不这么认为。";
VOICEMACRO_15_Ta_1_FEMALE = "我不这么认为。";
VOICEMACRO_15_Ta_2 = "决不。";
VOICEMACRO_15_Ta_2_FEMALE = "事情不应该是这样的。";
VOICEMACRO_15_Tr_0 = "不。";
VOICEMACRO_15_Tr_0_FEMALE = "不可能。";
VOICEMACRO_15_Tr_1 = "不可能。";
VOICEMACRO_15_Tr_1_FEMALE = "不可能。";
VOICEMACRO_15_Tr_2 = "决不。";
VOICEMACRO_15_Tr_2_FEMALE = "决不。";
VOICEMACRO_15_Tr_3 = "噢噢。";
VOICEMACRO_16_Dw_0 = "谢谢你。";
VOICEMACRO_16_Dw_0_FEMALE = "谢谢你。";
VOICEMACRO_16_Dw_1 = "非常感谢。";
VOICEMACRO_16_Dw_1_FEMALE = "啊，你太好了。";
VOICEMACRO_16_Dw_2 = "啊，你太好了。";
VOICEMACRO_16_Dw_2_FEMALE = "非常感谢。";
VOICEMACRO_16_Dw_3 = "愿你再次表现的如此慷慨而且其程度可以是现在的一百倍。";
VOICEMACRO_16_Dw_3_FEMALE = "愿你再次表现的如此慷慨而且其程度可以是现在的一百倍。";
VOICEMACRO_16_Gn_0 = "谢谢你。";
VOICEMACRO_16_Gn_0_FEMALE = "你真慷慨。";
VOICEMACRO_16_Gn_1 = "我欠你的人情。";
VOICEMACRO_16_Gn_1_FEMALE = "你真好。";
VOICEMACRO_16_Gn_2 = "请允许我表示我深深的感谢。";
VOICEMACRO_16_Gn_2_FEMALE = "非常感谢。";
VOICEMACRO_16_Hu_0 = "谢谢你。";
VOICEMACRO_16_Hu_0_FEMALE = "谢谢你。";
VOICEMACRO_16_Hu_1 = "你真慷慨。";
VOICEMACRO_16_Hu_1_FEMALE = "你真慷慨。";
VOICEMACRO_16_Hu_2 = "你真好。";
VOICEMACRO_16_Hu_2_FEMALE = "非常感谢。";
VOICEMACRO_16_Ni_0 = "谢谢你。";
VOICEMACRO_16_Ni_0_FEMALE = "谢谢你。";
VOICEMACRO_16_Ni_1 = "太棒了。";
VOICEMACRO_16_Ni_1_FEMALE = "你真慷慨。";
VOICEMACRO_16_Ni_2 = "非常感谢。";
VOICEMACRO_16_Ni_2_FEMALE = "你太好了。";
VOICEMACRO_16_Or_0 = "我会还你这个人情的。";
VOICEMACRO_16_Or_0_FEMALE = "我不会忘记这个的。";
VOICEMACRO_16_Or_1 = "我不会忘记这个的。";
VOICEMACRO_16_Or_1_FEMALE = "谢谢你。";
VOICEMACRO_16_Or_2 = "谢谢你。";
VOICEMACRO_16_Or_2_FEMALE = "谢谢。";
VOICEMACRO_16_Sc_0 = "我欠你的人情。";
VOICEMACRO_16_Sc_0_FEMALE = "谢谢你。";
VOICEMACRO_16_Sc_1 = "啊，谢谢你。";
VOICEMACRO_16_Sc_1_FEMALE = "啊，谢谢你。";
VOICEMACRO_16_Sc_2 = "感谢你。";
VOICEMACRO_16_Sc_2_FEMALE = "你真慷慨。";
VOICEMACRO_16_Ta_0 = "你真慷慨。";
VOICEMACRO_16_Ta_0_FEMALE = "谢谢你。";
VOICEMACRO_16_Ta_1 = "你真好。";
VOICEMACRO_16_Ta_1_FEMALE = "你真好。";
VOICEMACRO_16_Ta_2 = "我感谢你。";
VOICEMACRO_16_Ta_2_FEMALE = "你太好了。";
VOICEMACRO_16_Ta_3 = "愿你的祖先会永远指引你前进。";
VOICEMACRO_16_Tr_0 = "谢谢。";
VOICEMACRO_16_Tr_0_FEMALE = "谢谢。";
VOICEMACRO_16_Tr_1 = "非常感谢。";
VOICEMACRO_16_Tr_1_FEMALE = "非常感谢。";
VOICEMACRO_16_Tr_2 = "我欠你一个人情。";
VOICEMACRO_16_Tr_2_FEMALE = "我欠你一个人情。";
VOICEMACRO_17_Dw_0 = "没问题。";
VOICEMACRO_17_Dw_0_FEMALE = "没问题。";
VOICEMACRO_17_Dw_1 = "愿意为您效劳。";
VOICEMACRO_17_Dw_1_FEMALE = "愿意为您效劳。";
VOICEMACRO_17_Dw_2 = "别提了。";
VOICEMACRO_17_Dw_2_FEMALE = "不客气。";
VOICEMACRO_17_Gn_0 = "没问题。";
VOICEMACRO_17_Gn_0_FEMALE = "随时效劳。";
VOICEMACRO_17_Gn_1 = "随时效劳。";
VOICEMACRO_17_Gn_1_FEMALE = "这是我为朋友该做的。";
VOICEMACRO_17_Gn_2 = "不客气。";
VOICEMACRO_17_Gn_2_FEMALE = "不客气。";
VOICEMACRO_17_Hu_0 = "我一直都会乐意帮助你的。";
VOICEMACRO_17_Hu_0_FEMALE = "没问题。";
VOICEMACRO_17_Hu_1 = "没有你办不成的事情。";
VOICEMACRO_17_Hu_1_FEMALE = "随时效劳。";
VOICEMACRO_17_Hu_2 = "随时愿意为你服务。";
VOICEMACRO_17_Hu_2_FEMALE = "不客气。";
VOICEMACRO_17_Ni_0 = "不客气。";
VOICEMACRO_17_Ni_0_FEMALE = "愿意为您效劳。";
VOICEMACRO_17_Ni_1 = "愿意为您效劳。";
VOICEMACRO_17_Ni_1_FEMALE = "没什么。";
VOICEMACRO_17_Ni_2 = "我很荣幸。";
VOICEMACRO_17_Ni_2_FEMALE = "我很荣幸。";
VOICEMACRO_17_Or_0 = "我预期的一样。";
VOICEMACRO_17_Or_0_FEMALE = "随时效劳。";
VOICEMACRO_17_Or_1 = "为了荣耀。";
VOICEMACRO_17_Or_1_FEMALE = "这是我为朋友该做的。";
VOICEMACRO_17_Or_2 = "没什么。";
VOICEMACRO_17_Or_2_FEMALE = "不客气。";
VOICEMACRO_17_Sc_0 = "随时效劳。";
VOICEMACRO_17_Sc_0_FEMALE = "随时效劳。";
VOICEMACRO_17_Sc_1 = "呃。";
VOICEMACRO_17_Sc_1_FEMALE = "呃。";
VOICEMACRO_17_Sc_2_FEMALE = "这不意味着我不喜欢你。";
VOICEMACRO_17_Ta_0 = "没什么。";
VOICEMACRO_17_Ta_0_FEMALE = "愿意为您效劳。";
VOICEMACRO_17_Ta_1 = "我一直都会乐意帮助你的。";
VOICEMACRO_17_Ta_1_FEMALE = "我一直都会乐意帮助你的。";
VOICEMACRO_17_Ta_2 = "这是我为联盟该做的。";
VOICEMACRO_17_Ta_2_FEMALE = "不用谢。";
VOICEMACRO_17_Tr_0 = "没问题。";
VOICEMACRO_17_Tr_0_FEMALE = "没问题。";
VOICEMACRO_17_Tr_1 = "随时效劳，伙计。";
VOICEMACRO_17_Tr_1_FEMALE = "愿意为您效劳。";
VOICEMACRO_17_Tr_2 = "你帮我抓背，我帮你抓背。";
VOICEMACRO_17_Tr_2_FEMALE = "随时效劳，伙计。";
VOICEMACRO_18_Dw_0 = "祝贺你。";
VOICEMACRO_18_Dw_0_FEMALE = "祝贺你。";
VOICEMACRO_18_Dw_1 = "太好了。";
VOICEMACRO_18_Dw_1_FEMALE = "出发了。";
VOICEMACRO_18_Dw_2 = "太棒了。";
VOICEMACRO_18_Dw_2_FEMALE = "哦，那真不错。";
VOICEMACRO_18_Dw_3 = "干得好。";
VOICEMACRO_18_Dw_3_FEMALE = "太棒了。";
VOICEMACRO_18_Dw_4 = "出发了。";
VOICEMACRO_18_Gn_0 = "祝贺你。";
VOICEMACRO_18_Gn_0_FEMALE = "祝贺你。";
VOICEMACRO_18_Gn_1 = "干得好。";
VOICEMACRO_18_Gn_1_FEMALE = "出发了。";
VOICEMACRO_18_Gn_2 = "干得好。";
VOICEMACRO_18_Gn_2_FEMALE = "太棒了。";
VOICEMACRO_18_Gn_3_FEMALE = "太好了。";
VOICEMACRO_18_Hu_0 = "祝贺你。";
VOICEMACRO_18_Hu_0_FEMALE = "祝贺你。";
VOICEMACRO_18_Hu_1 = "太棒了。";
VOICEMACRO_18_Hu_1_FEMALE = "太棒了。";
VOICEMACRO_18_Hu_2 = "太好了。";
VOICEMACRO_18_Hu_2_FEMALE = "哈哈。";
VOICEMACRO_18_Ni_0 = "祝贺你。";
VOICEMACRO_18_Ni_0_FEMALE = "祝贺你。";
VOICEMACRO_18_Ni_1 = "你是有价值的人。";
VOICEMACRO_18_Ni_1_FEMALE = "干得好。";
VOICEMACRO_18_Ni_2 = "太棒了。";
VOICEMACRO_18_Ni_2_FEMALE = "太棒了。";
VOICEMACRO_18_Or_0 = "你应该感到光荣。";
VOICEMACRO_18_Or_0_FEMALE = "很有技巧性地完成了。";
VOICEMACRO_18_Or_1 = "干得好。";
VOICEMACRO_18_Or_1_FEMALE = "还不错。";
VOICEMACRO_18_Or_2 = "你是有价值的人。";
VOICEMACRO_18_Or_2_FEMALE = "祝贺你。";
VOICEMACRO_18_Sc_0 = "祝贺你。";
VOICEMACRO_18_Sc_0_FEMALE = "祝贺你。";
VOICEMACRO_18_Sc_1 = "干得好。";
VOICEMACRO_18_Sc_1_FEMALE = "总是别的什么人。";
VOICEMACRO_18_Sc_2 = "干得好。";
VOICEMACRO_18_Sc_2_FEMALE = "好吧，你难道不感到幸运吗。";
VOICEMACRO_18_Sc_3_FEMALE = "今天显然是你的日子。";
VOICEMACRO_18_Ta_0 = "干得好。";
VOICEMACRO_18_Ta_0_FEMALE = "干得好。";
VOICEMACRO_18_Ta_1 = "你是有价值的人。";
VOICEMACRO_18_Ta_1_FEMALE = "太棒了。";
VOICEMACRO_18_Ta_2 = "命运对着你微笑。";
VOICEMACRO_18_Ta_2_FEMALE = "命运对着你微笑。";
VOICEMACRO_18_Tr_0 = "干得好。";
VOICEMACRO_18_Tr_0_FEMALE = "干得好。";
VOICEMACRO_18_Tr_1 = "太好了。";
VOICEMACRO_18_Tr_1_FEMALE = "出发了。";
VOICEMACRO_18_Tr_2 = "他们会世代颂扬你的发现的。";
VOICEMACRO_18_Tr_2_FEMALE = "太好了。";
VOICEMACRO_19_Dw_0 = "废话够了。让我们开始吧。";
VOICEMACRO_19_Dw_0_FEMALE = "搞笑语音";
VOICEMACRO_19_Dw_1 = "你是不是想要摸摸我的胡子，是不是？";
VOICEMACRO_19_Dw_1_FEMALE = "我会让你知道我能用我的大腿打磨钢。";
VOICEMACRO_19_Dw_2 = "搞笑语音";
VOICEMACRO_19_Dw_2_FEMALE = "搞笑语音";
VOICEMACRO_19_Dw_3 = "你很漂亮。我喜欢你的头发。这是你的饮料。你现在准备好了没有？";
VOICEMACRO_19_Dw_3_FEMALE = "搞笑语音。";
VOICEMACRO_19_Dw_4 = "你从哪里来的？那并不重要。";
VOICEMACRO_19_Dw_4_FEMALE = "不要再罗嗦了，我知道你认为所有的矮人女性都一个样子。";
VOICEMACRO_19_Dw_5 = "搞笑语音";
VOICEMACRO_19_Gn_0 = "搞笑语音";
VOICEMACRO_19_Gn_0_FEMALE = "你很可爱。";
VOICEMACRO_19_Gn_1 = "搞笑语音";
VOICEMACRO_19_Gn_1_FEMALE = "搞笑语音";
VOICEMACRO_19_Gn_2 = "搞笑语音";
VOICEMACRO_19_Gn_2_FEMALE = "你能够完全组成一句句子是个附加功能。";
VOICEMACRO_19_Gn_3 = "嘿，样子不错。";
VOICEMACRO_19_Gn_3_FEMALE = "搞笑语音";
VOICEMACRO_19_Gn_4_FEMALE = "搞笑语音";
VOICEMACRO_19_Hu_0 = "你在干什么？";
VOICEMACRO_19_Hu_0_FEMALE = "你让我心烦意乱。";
VOICEMACRO_19_Hu_1 = "搞笑语音";
VOICEMACRO_19_Hu_1_FEMALE = "搞笑语音";
VOICEMACRO_19_Hu_2 = "嘿，甜心。";
VOICEMACRO_19_Hu_2_FEMALE = "我需要一个英雄。";
VOICEMACRO_19_Hu_3 = "你是哪个团队的？";
VOICEMACRO_19_Hu_4 = "搞笑语音";
VOICEMACRO_19_Hu_5 = "搞笑语音";
VOICEMACRO_19_Ni_0 = "你简直好像是翡翠梦境变成了真实一样。";
VOICEMACRO_19_Ni_0_FEMALE = "如果我不是紫色的话，你会看到我冒着红光。";
VOICEMACRO_19_Ni_1 = "搞笑语音";
VOICEMACRO_19_Ni_1_FEMALE = "当然，我的东西都相当具有穿透力。";
VOICEMACRO_19_Ni_2 = "搞笑语音";
VOICEMACRO_19_Ni_2_FEMALE = "我是我妈妈警告过的那种女孩。";
VOICEMACRO_19_Ni_3 = "我是自然之力。";
VOICEMACRO_19_Ni_3_FEMALE = "没有什么比得上在月光沐浴下在森林里睡觉的事情了。";
VOICEMACRO_19_Ni_4 = "想要我这里的动物吗？";
VOICEMACRO_19_Or_0 = "你穿的护甲不错。如果它能躺在我的地板上的话会更好。";
VOICEMACRO_19_Or_0_FEMALE = "你让我忙得不可开交。";
VOICEMACRO_19_Or_1 = "这就是真爱。你认为这样的事情天天发生吗？";
VOICEMACRO_19_Or_1_FEMALE = "你会给与我疯狂的爱。";
VOICEMACRO_19_Or_2 = "搞笑语音";
VOICEMACRO_19_Or_2_FEMALE = "搞笑语音";
VOICEMACRO_19_Or_3 = "搞笑语音";
VOICEMACRO_19_Or_3_FEMALE = "你可以的。让我们出发吧。";
VOICEMACRO_19_Or_4 = "女士，当我看到你的时候我……我没有想到我会走得那么远。";
VOICEMACRO_19_Or_4_FEMALE = "不要说话，只要跟着我。";
VOICEMACRO_19_Or_5 = "呃，你看上去像个女人。";
VOICEMACRO_19_Or_5_FEMALE = "搞笑语音";
VOICEMACRO_19_Sc_0 = "搞笑语音";
VOICEMACRO_19_Sc_0_FEMALE = "搞笑语音";
VOICEMACRO_19_Sc_1 = "你的皮肤很漂亮；一个虫眼也没有。";
VOICEMACRO_19_Sc_1_FEMALE = "搞笑语音";
VOICEMACRO_19_Sc_2 = "搞笑语音";
VOICEMACRO_19_Sc_2_FEMALE = "搞笑账号";
VOICEMACRO_19_Sc_3 = "搞笑语音";
VOICEMACRO_19_Sc_3_FEMALE = "搞笑语音";
VOICEMACRO_19_Sc_4 = "搞笑语音";
VOICEMACRO_19_Sc_4_FEMALE = "搞笑语音";
VOICEMACRO_19_Sc_5 = "搞笑语音。";
VOICEMACRO_19_Sc_5_FEMALE = "搞笑语音";
VOICEMACRO_19_Ta_0 = "你让我动心了。";
VOICEMACRO_19_Ta_0_FEMALE = "搞笑语音";
VOICEMACRO_19_Ta_1 = "嘿，你是制皮的？";
VOICEMACRO_19_Ta_1_FEMALE = "搞笑语音";
VOICEMACRO_19_Ta_2 = "搞笑语音";
VOICEMACRO_19_Ta_2_FEMALE = "搞笑语音";
VOICEMACRO_19_Ta_3 = "嘿，你办到了？";
VOICEMACRO_19_Ta_3_FEMALE = "过来，水手";
VOICEMACRO_19_Ta_4 = "你要知道，年长的公牛只有一个功能。";
VOICEMACRO_19_Ta_4_FEMALE = "想要看看正宗的木屐舞吗？";
VOICEMACRO_19_Ta_5 = "你对复杂的机器感觉还头痛不？";
VOICEMACRO_19_Tr_0 = "想要一些我做的丛林之爱吗？";
VOICEMACRO_19_Tr_0_FEMALE = "搞笑语音";
VOICEMACRO_19_Tr_1 = "搞笑语音。";
VOICEMACRO_19_Tr_1_FEMALE = "搞笑语音";
VOICEMACRO_19_Tr_2 = "你看上去相当……相当美味。";
VOICEMACRO_19_Tr_2_FEMALE = "你准备把我挖出来吗？";
VOICEMACRO_19_Tr_3 = "搞笑语音";
VOICEMACRO_19_Tr_3_FEMALE = "我不会咬你露在外面的地方的。";
VOICEMACRO_19_Tr_4_FEMALE = "你是那种我恨之入骨的人。";
VOICEMACRO_1_Dw_0 = "危险在靠近！";
VOICEMACRO_1_Dw_0_FEMALE = "危险在靠近！";
VOICEMACRO_1_Dw_1 = "小心！";
VOICEMACRO_1_Dw_1_FEMALE = "小心！";
VOICEMACRO_1_Gn_0 = "危险在靠近！";
VOICEMACRO_1_Gn_0_FEMALE = "危险在靠近！";
VOICEMACRO_1_Hu_0 = "保护好你自己！";
VOICEMACRO_1_Hu_0_FEMALE = "保护好你自己！";
VOICEMACRO_1_Hu_1 = "保持警惕！";
VOICEMACRO_1_Hu_1_FEMALE = "保持警惕！";
VOICEMACRO_1_Ni_0 = "小心！";
VOICEMACRO_1_Ni_0_FEMALE = "危险！";
VOICEMACRO_1_Ni_1 = "保持警惕！";
VOICEMACRO_1_Ni_1_FEMALE = "保持警惕！";
VOICEMACRO_1_Or_0 = "看这个！";
VOICEMACRO_1_Or_0_FEMALE = "保持警惕！";
VOICEMACRO_1_Or_1 = "来了！";
VOICEMACRO_1_Or_1_FEMALE = "来了！";
VOICEMACRO_1_Or_2 = "保持警惕！";
VOICEMACRO_1_Sc_0 = "危险！";
VOICEMACRO_1_Sc_0_FEMALE = "危险！";
VOICEMACRO_1_Sc_1 = "来了！";
VOICEMACRO_1_Sc_1_FEMALE = "来了！";
VOICEMACRO_1_Ta_0 = "危险在靠近！";
VOICEMACRO_1_Ta_0_FEMALE = "危险在靠近！";
VOICEMACRO_1_Ta_1 = "保持警惕！";
VOICEMACRO_1_Ta_1_FEMALE = "保持警惕！";
VOICEMACRO_1_Ta_2_FEMALE = "小心！";
VOICEMACRO_1_Tr_0 = "危险！";
VOICEMACRO_1_Tr_0_FEMALE = "危险！";
VOICEMACRO_1_Tr_1 = "有危险的东西在靠近！";
VOICEMACRO_1_Tr_1_FEMALE = "有危险的东西在靠近！";
VOICEMACRO_20_Dw_0 = "搞笑语音";
VOICEMACRO_20_Dw_0_FEMALE = "不，它们不是真的，但是感谢你注意到了它们。";
VOICEMACRO_20_Dw_1 = "啊，冬天。是的，冬天。";
VOICEMACRO_20_Dw_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Dw_2 = "哦，我的衣橱有点问题。哦，那是我的锤子。";
VOICEMACRO_20_Dw_2_FEMALE = "搞笑语音";
VOICEMACRO_20_Dw_3 = "搞笑语音";
VOICEMACRO_20_Dw_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Dw_4 = "搞笑语音";
VOICEMACRO_20_Dw_4_FEMALE = "我的叔叔有黄铜作的球。不，这是真的！";
VOICEMACRO_20_Dw_5 = "搞笑语音";
VOICEMACRO_20_Dw_5_FEMALE = "搞笑语音";
VOICEMACRO_20_Dw_6 = "搞笑语音";
VOICEMACRO_20_Gn_0 = "你要知道，我希望自己有个花园可以放一些人形雕塑。";
VOICEMACRO_20_Gn_0_FEMALE = "搞笑语音";
VOICEMACRO_20_Gn_1 = "搞笑语音";
VOICEMACRO_20_Gn_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Gn_2 = "搞笑语音";
VOICEMACRO_20_Gn_2_FEMALE = "你要知道，如果被逼到绝路，松鼠也会是致命的。";
VOICEMACRO_20_Gn_3 = "搞笑语音";
VOICEMACRO_20_Gn_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Gn_4 = "搞笑语音";
VOICEMACRO_20_Gn_5 = "搞笑语音";
VOICEMACRO_20_Hu_0 = "搞笑语音";
VOICEMACRO_20_Hu_0_FEMALE = "搞笑语音？";
VOICEMACRO_20_Hu_1 = "搞笑语音";
VOICEMACRO_20_Hu_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Hu_2 = "搞笑语音";
VOICEMACRO_20_Hu_2_FEMALE = "搞笑语音";
VOICEMACRO_20_Hu_3 = "搞笑语音";
VOICEMACRO_20_Hu_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Hu_4 = "搞笑语音";
VOICEMACRO_20_Hu_4_FEMALE = "搞笑语音。";
VOICEMACRO_20_Hu_5 = "搞笑语音";
VOICEMACRO_20_Hu_5_FEMALE = "搞笑语音";
VOICEMACRO_20_Hu_6_FEMALE = "搞笑语音";
VOICEMACRO_20_Ni_0 = "搞笑语音";
VOICEMACRO_20_Ni_0_FEMALE = "你知道我在晚上要不断移动，否则我会消失的。";
VOICEMACRO_20_Ni_1 = "你知道达纳苏斯的古代保护者吗? 它们并不是那么古老。";
VOICEMACRO_20_Ni_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Ni_2 = "搞笑语音";
VOICEMACRO_20_Ni_2_FEMALE = "你要知道，小精灵对于个人卫生很有用处。";
VOICEMACRO_20_Ni_3 = "搞笑语音";
VOICEMACRO_20_Ni_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Ni_4 = "搞笑语音";
VOICEMACRO_20_Ni_4_FEMALE = "哦，我又在跳舞了。我希望你所有的朋友都喜欢这个表演。";
VOICEMACRO_20_Ni_5 = "谁想要长生不老？";
VOICEMACRO_20_Ni_6 = "什么？我没有听清楚。";
VOICEMACRO_20_Ni_7 = "搞笑语音";
VOICEMACRO_20_Or_0 = "搞笑语音";
VOICEMACRO_20_Or_0_FEMALE = "如果你敢挡在我和我的食物中间的话，你会损失一只手。";
VOICEMACRO_20_Or_1 = "要变绿可不容易。";
VOICEMACRO_20_Or_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Or_2 = "搞笑语音";
VOICEMACRO_20_Or_2_FEMALE = "搞笑语音";
VOICEMACRO_20_Or_3 = "搞笑语音";
VOICEMACRO_20_Or_3_FEMALE = "什么是estrogen？你能吃它吗？";
VOICEMACRO_20_Or_4 = "兽人战无不胜！";
VOICEMACRO_20_Or_4_FEMALE = "搞笑语音";
VOICEMACRO_20_Or_5 = "搞笑语音。";
VOICEMACRO_20_Or_5_FEMALE = "搞笑语音";
VOICEMACRO_20_Sc_0 = "搞笑语音";
VOICEMACRO_20_Sc_0_FEMALE = "搞笑语音";
VOICEMACRO_20_Sc_1 = "我死了，我发怒了。";
VOICEMACRO_20_Sc_1_FEMALE = "是的，他们是真的。他们不是我的，但是他们是真的。";
VOICEMACRO_20_Sc_2 = "搞笑语音";
VOICEMACRO_20_Sc_2_FEMALE = "我现在心情糟透了。";
VOICEMACRO_20_Sc_3 = "搞笑语音";
VOICEMACRO_20_Sc_3_FEMALE = "这东西发臭了。";
VOICEMACRO_20_Sc_4 = "搞笑语音";
VOICEMACRO_20_Sc_4_FEMALE = "你要知道，一旦你死了，就没有什么不好闻的东西了。腐臭的鸡蛋？没有问题。死鱼？那好像一股清新的威风。";
VOICEMACRO_20_Sc_5_FEMALE = "如果你没有腋窝的话既不需要任何除臭剂。";
VOICEMACRO_20_Sc_6_FEMALE = "啊，门钉。";
VOICEMACRO_20_Sc_7_FEMALE = "搞笑语音";
VOICEMACRO_20_Ta_0 = "不入虎穴，焉得虎子。";
VOICEMACRO_20_Ta_0_FEMALE = "搞笑语音";
VOICEMACRO_20_Ta_1 = "给你牛肉。";
VOICEMACRO_20_Ta_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Ta_2 = "搞笑语音";
VOICEMACRO_20_Ta_2_FEMALE = "来自莫高雷的快乐牛头人";
VOICEMACRO_20_Ta_3 = "你要知道，牛头人天生就是猎人。你看见过牛头人从小溪中抓鲑鱼的场景没有？这非常令人兴奋。还有，你见过牛头人扼住大蟒的情景没有？当然你没有。那是因为牛头人和其周围的环境和谐地想出着。";
VOICEMACRO_20_Ta_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Ta_4 = "嗯。你现在高兴了吗？";
VOICEMACRO_20_Tr_0 = "搞笑语音";
VOICEMACRO_20_Tr_0_FEMALE = "搞笑语音。";
VOICEMACRO_20_Tr_1 = "搞笑语音";
VOICEMACRO_20_Tr_1_FEMALE = "搞笑语音";
VOICEMACRO_20_Tr_2 = "搞笑语音";
VOICEMACRO_20_Tr_2_FEMALE = "搞笑语音";
VOICEMACRO_20_Tr_3 = "新兵巨魔报道。";
VOICEMACRO_20_Tr_3_FEMALE = "搞笑语音";
VOICEMACRO_20_Tr_4 = "搞笑语音";
VOICEMACRO_20_Tr_4_FEMALE = "如果食尸是错误的话，那我宁愿一错再错。";
VOICEMACRO_20_Tr_5 = "搞笑语音";
VOICEMACRO_2_Dw_0 = "冲锋！";
VOICEMACRO_2_Dw_0_FEMALE = "冲锋！";
VOICEMACRO_2_Dw_1 = "为了卡兹莫丹！";
VOICEMACRO_2_Dw_1_FEMALE = "以穆拉丁胡须的名义！";
VOICEMACRO_2_Gn_0 = "搞定他们!";
VOICEMACRO_2_Gn_0_FEMALE = "为了诺莫瑞根！";
VOICEMACRO_2_Gn_1 = "攻击！";
VOICEMACRO_2_Gn_1_FEMALE = "向前冲锋！";
VOICEMACRO_2_Gn_2 = "为了诺莫瑞根！";
VOICEMACRO_2_Hu_0 = "冲锋！";
VOICEMACRO_2_Hu_0_FEMALE = "攻击！";
VOICEMACRO_2_Hu_1 = "战斗！";
VOICEMACRO_2_Hu_1_FEMALE = "开战！";
VOICEMACRO_2_Ni_0 = "为塞纳留斯而战!";
VOICEMACRO_2_Ni_0_FEMALE = "以艾露恩的名义！";
VOICEMACRO_2_Ni_1 = "攻击！";
VOICEMACRO_2_Ni_1_FEMALE = "为了女神，进攻！";
VOICEMACRO_2_Ni_2_FEMALE = "冲锋！";
VOICEMACRO_2_Or_0 = "消灭他们！";
VOICEMACRO_2_Or_0_FEMALE = "攻击！";
VOICEMACRO_2_Or_1 = "把他们都干掉！";
VOICEMACRO_2_Or_1_FEMALE = "把他们都干掉！";
VOICEMACRO_2_Or_2 = "狠狠的揍他们！";
VOICEMACRO_2_Or_2_FEMALE = "不要留活口！";
VOICEMACRO_2_Sc_0 = "为了遗忘者而战！";
VOICEMACRO_2_Sc_0_FEMALE = "和我一起奋战！";
VOICEMACRO_2_Sc_1 = "和我一起奋战！";
VOICEMACRO_2_Sc_1_FEMALE = "为了遗忘者而战！";
VOICEMACRO_2_Ta_0 = "为了卡利姆多而战！";
VOICEMACRO_2_Ta_0_FEMALE = "为了卡利姆多而战！";
VOICEMACRO_2_Ta_1 = "爆发吧！";
VOICEMACRO_2_Ta_1_FEMALE = "攻击！";
VOICEMACRO_2_Ta_2 = "冲锋！";
VOICEMACRO_2_Tr_0 = "为了祖尔金而战！";
VOICEMACRO_2_Tr_0_FEMALE = "为了祖尔金而战！";
VOICEMACRO_2_Tr_1 = "现在，让我们大开杀戒吧！";
VOICEMACRO_2_Tr_1_FEMALE = "让他们感受痛苦吧！";
VOICEMACRO_3_Dw_0 = "快跑！";
VOICEMACRO_3_Dw_0_FEMALE = "快跑！";
VOICEMACRO_3_Dw_1 = "快跑！";
VOICEMACRO_3_Dw_1_FEMALE = "快跑！";
VOICEMACRO_3_Dw_2 = "快跑！";
VOICEMACRO_3_Gn_0 = "快跑！";
VOICEMACRO_3_Gn_0_FEMALE = "快跑！";
VOICEMACRO_3_Gn_1 = "让我们离开这里！";
VOICEMACRO_3_Gn_1_FEMALE = "让我们离开这里！";
VOICEMACRO_3_Gn_2 = "撤退！";
VOICEMACRO_3_Gn_2_FEMALE = "撤退！";
VOICEMACRO_3_Hu_0 = "快跑！";
VOICEMACRO_3_Hu_0_FEMALE = "快跑！";
VOICEMACRO_3_Hu_1 = "撤退！";
VOICEMACRO_3_Hu_1_FEMALE = "撤退！";
VOICEMACRO_3_Ni_0 = "撤退！";
VOICEMACRO_3_Ni_0_FEMALE = "快跑！";
VOICEMACRO_3_Ni_1 = "散开！";
VOICEMACRO_3_Ni_1_FEMALE = "我们的敌人太强了！";
VOICEMACRO_3_Or_0 = "撤退！";
VOICEMACRO_3_Or_0_FEMALE = "撤退！";
VOICEMACRO_3_Or_1 = "快跑！";
VOICEMACRO_3_Or_1_FEMALE = "快跑！";
VOICEMACRO_3_Sc_0 = "靠边站！";
VOICEMACRO_3_Sc_0_FEMALE = "快跑！";
VOICEMACRO_3_Sc_1 = "赶快回去！";
VOICEMACRO_3_Sc_1_FEMALE = "靠边站！";
VOICEMACRO_3_Ta_0 = "快跑！";
VOICEMACRO_3_Ta_0_FEMALE = "撤退！";
VOICEMACRO_3_Ta_1 = "撤退！";
VOICEMACRO_3_Ta_1_FEMALE = "躲起来！";
VOICEMACRO_3_Tr_0 = "快跑！";
VOICEMACRO_3_Tr_0_FEMALE = "快跑啊！";
VOICEMACRO_3_Tr_1 = "离开这里！";
VOICEMACRO_3_Tr_1_FEMALE = "离开这里！";
VOICEMACRO_4_Dw_0 = "一起上！";
VOICEMACRO_4_Dw_0_FEMALE = "一起上！";
VOICEMACRO_4_Dw_1 = "攻击这个！";
VOICEMACRO_4_Dw_1_FEMALE = "攻击这个！";
VOICEMACRO_4_Dw_2 = "帮我干掉这个家伙！";
VOICEMACRO_4_Gn_0 = "请和我一起做战！";
VOICEMACRO_4_Gn_0_FEMALE = "你能帮助我吗？";
VOICEMACRO_4_Gn_1 = "请帮我在这里做战！";
VOICEMACRO_4_Gn_1_FEMALE = "嗨，帮帮我！";
VOICEMACRO_4_Hu_0 = "协助我的攻击！";
VOICEMACRO_4_Hu_0_FEMALE = "协助我的攻击！";
VOICEMACRO_4_Hu_1 = "从这里发起攻击！";
VOICEMACRO_4_Hu_1_FEMALE = "从这里发起攻击！";
VOICEMACRO_4_Ni_0 = "攻击我的敌人！";
VOICEMACRO_4_Ni_0_FEMALE = "攻击这个敌人！";
VOICEMACRO_4_Ni_1 = "干掉我的敌人！";
VOICEMACRO_4_Ni_1_FEMALE = "在这里！";
VOICEMACRO_4_Ni_2_FEMALE = "攻击我的敌人！";
VOICEMACRO_4_Or_0 = "从这里发起攻击！";
VOICEMACRO_4_Or_0_FEMALE = "从这里发起攻击！";
VOICEMACRO_4_Or_1 = "和我一起攻上去！";
VOICEMACRO_4_Or_1_FEMALE = "让我们一起奋战！";
VOICEMACRO_4_Or_2 = "让我们一起奋战！";
VOICEMACRO_4_Or_2_FEMALE = "一起上！";
VOICEMACRO_4_Sc_0 = "和我一起作战！";
VOICEMACRO_4_Sc_0_FEMALE = "屠杀吧!";
VOICEMACRO_4_Sc_1 = "帮助我一起战斗！";
VOICEMACRO_4_Sc_1_FEMALE = "帮助我一起战斗！";
VOICEMACRO_4_Ta_0 = "和我并肩作战！";
VOICEMACRO_4_Ta_0_FEMALE = "和我并肩作战！";
VOICEMACRO_4_Ta_1 = "和我一起作战！";
VOICEMACRO_4_Ta_1_FEMALE = "和我一起作战！";
VOICEMACRO_4_Tr_0 = "攻击这个敌人！";
VOICEMACRO_4_Tr_0_FEMALE = "攻击这个敌人！";
VOICEMACRO_4_Tr_1 = "帮助我！";
VOICEMACRO_4_Tr_1_FEMALE = "这是我们攻击的目标！";
VOICEMACRO_5_Dw_0 = "我没有法力值了！";
VOICEMACRO_5_Dw_0_FEMALE = "我没有法力值了！";
VOICEMACRO_5_Dw_1 = "我需要更多的法力值！";
VOICEMACRO_5_Dw_1_FEMALE = "我需要更多的法力值！";
VOICEMACRO_5_Gn_0 = "我需要法力值！";
VOICEMACRO_5_Gn_0_FEMALE = "我的法力值快用完了！";
VOICEMACRO_5_Gn_1 = "我的法力值不足！";
VOICEMACRO_5_Gn_1_FEMALE = "我的法力值不足！";
VOICEMACRO_5_Hu_0 = "我没有法力值了！";
VOICEMACRO_5_Hu_0_FEMALE = "我没有法力值了！";
VOICEMACRO_5_Hu_1 = "我的法力值很少！";
VOICEMACRO_5_Hu_1_FEMALE = "我需要更多的法力值！";
VOICEMACRO_5_Ni_0 = "我的法力值很少！";
VOICEMACRO_5_Ni_0_FEMALE = "我的法力值很少！";
VOICEMACRO_5_Ni_1 = "我的法力值要用光了！";
VOICEMACRO_5_Ni_1_FEMALE = "我的法力值要用光了！";
VOICEMACRO_5_Or_0 = "我需要法力值！";
VOICEMACRO_5_Or_0_FEMALE = "我需要法力值！";
VOICEMACRO_5_Or_1 = "我的法力值很少！";
VOICEMACRO_5_Or_1_FEMALE = "我的法力值很少！";
VOICEMACRO_5_Sc_0 = "我的法力值要用光了！";
VOICEMACRO_5_Sc_0_FEMALE = "我需要法力值！";
VOICEMACRO_5_Sc_1 = "我需要法力值！";
VOICEMACRO_5_Sc_1_FEMALE = "我的法力值用完了！";
VOICEMACRO_5_Ta_0 = "我需要更多的法力值！";
VOICEMACRO_5_Ta_0_FEMALE = "我必须补充我的法力值！";
VOICEMACRO_5_Ta_1 = "我的法力值用完了！";
VOICEMACRO_5_Ta_1_FEMALE = "我需要更多的法力值！";
VOICEMACRO_5_Tr_0 = "我的法力值很少！";
VOICEMACRO_5_Tr_0_FEMALE = "我的法力值不足！";
VOICEMACRO_5_Tr_1 = "我需要更多的法力值！";
VOICEMACRO_5_Tr_1_FEMALE = "我需要更多的法力值！";
VOICEMACRO_6_Dw_0 = "我来带路。";
VOICEMACRO_6_Dw_0_FEMALE = "我来带路。";
VOICEMACRO_6_Dw_1 = "跟着我。";
VOICEMACRO_6_Dw_1_FEMALE = "跟着我！";
VOICEMACRO_6_Dw_2 = "跟着我，快。";
VOICEMACRO_6_Gn_0 = "我来带路。";
VOICEMACRO_6_Gn_0_FEMALE = "跟着我。";
VOICEMACRO_6_Gn_1 = "跟着我。";
VOICEMACRO_6_Hu_0 = "跟着我。";
VOICEMACRO_6_Hu_0_FEMALE = "跟着我。";
VOICEMACRO_6_Hu_1 = "我来带路。";
VOICEMACRO_6_Hu_1_FEMALE = "我来带路。";
VOICEMACRO_6_Ni_0 = "我来带路。";
VOICEMACRO_6_Ni_0_FEMALE = "我来带路。";
VOICEMACRO_6_Ni_1 = "跟着我。";
VOICEMACRO_6_Ni_1_FEMALE = "跟着我。";
VOICEMACRO_6_Or_0 = "跟着我。";
VOICEMACRO_6_Or_0_FEMALE = "跟着我。";
VOICEMACRO_6_Or_1 = "来。";
VOICEMACRO_6_Or_1_FEMALE = "来。";
VOICEMACRO_6_Sc_0 = "这条路。";
VOICEMACRO_6_Sc_0_FEMALE = "走这边。";
VOICEMACRO_6_Sc_1 = "我来带路。";
VOICEMACRO_6_Sc_1_FEMALE = "跟着我。";
VOICEMACRO_6_Ta_0 = "跟我来。";
VOICEMACRO_6_Ta_0_FEMALE = "跟我来。";
VOICEMACRO_6_Ta_1 = "我来带路。";
VOICEMACRO_6_Ta_1_FEMALE = "跟着我。";
VOICEMACRO_6_Tr_0 = "跟着我。";
VOICEMACRO_6_Tr_0_FEMALE = "我来带路。";
VOICEMACRO_6_Tr_1 = "你跟着我。";
VOICEMACRO_6_Tr_1_FEMALE = "你跟着我。";
VOICEMACRO_7_Dw_0 = "在这里等着。";
VOICEMACRO_7_Dw_0_FEMALE = "待在这里。";
VOICEMACRO_7_Dw_1 = "待着不要动。";
VOICEMACRO_7_Dw_1_FEMALE = "在这里等着。";
VOICEMACRO_7_Dw_2 = "待在这里。";
VOICEMACRO_7_Gn_0 = "在这里等一会。";
VOICEMACRO_7_Gn_0_FEMALE = "在这里等一会。";
VOICEMACRO_7_Gn_1 = "请等在这里。";
VOICEMACRO_7_Gn_1_FEMALE = "请等在这里。";
VOICEMACRO_7_Hu_0 = "待着不要动。";
VOICEMACRO_7_Hu_0_FEMALE = "待着不要动。";
VOICEMACRO_7_Hu_1 = "待在这里。";
VOICEMACRO_7_Hu_1_FEMALE = "待在这里。";
VOICEMACRO_7_Hu_2 = "在这里等着。";
VOICEMACRO_7_Hu_2_FEMALE = "在这里等着。";
VOICEMACRO_7_Ni_0 = "待在这里。";
VOICEMACRO_7_Ni_0_FEMALE = "待在这里。";
VOICEMACRO_7_Ni_1 = "在这里等着。";
VOICEMACRO_7_Ni_1_FEMALE = "在这里等着。";
VOICEMACRO_7_Or_0 = "待在这里。";
VOICEMACRO_7_Or_0_FEMALE = "待着。";
VOICEMACRO_7_Or_1 = "待在这里。";
VOICEMACRO_7_Or_1_FEMALE = "在这里等着。";
VOICEMACRO_7_Sc_0 = "等等。";
VOICEMACRO_7_Sc_0_FEMALE = "待在这里。";
VOICEMACRO_7_Sc_1 = "待在这里。";
VOICEMACRO_7_Sc_1_FEMALE = "不要移动。";
VOICEMACRO_7_Ta_0 = "休息一会。";
VOICEMACRO_7_Ta_0_FEMALE = "休息一下。";
VOICEMACRO_7_Ta_1 = "待在这里。";
VOICEMACRO_7_Ta_1_FEMALE = "待在这里。";
VOICEMACRO_7_Ta_2 = "等在这里。";
VOICEMACRO_7_Tr_0 = "你待在这里。";
VOICEMACRO_7_Tr_0_FEMALE = "你待在这里。";
VOICEMACRO_7_Tr_1 = "待着不要动。";
VOICEMACRO_7_Tr_1_FEMALE = "待着不要动。";
VOICEMACRO_7_Tr_2 = "不要四处乱走。";
VOICEMACRO_7_Tr_2_FEMALE = "不要四处乱走。";
VOICEMACRO_8_Dw_0 = "我需要治疗！";
VOICEMACRO_8_Dw_0_FEMALE = "我需要治疗！";
VOICEMACRO_8_Dw_1 = "治疗我的伤口！";
VOICEMACRO_8_Dw_1_FEMALE = "治疗我的伤口！";
VOICEMACRO_8_Dw_2 = "治疗我！";
VOICEMACRO_8_Dw_2_FEMALE = "治疗我！";
VOICEMACRO_8_Gn_0 = "请治疗我！";
VOICEMACRO_8_Gn_0_FEMALE = "请治疗我！";
VOICEMACRO_8_Gn_1 = "你能帮忙治疗我一下吗？";
VOICEMACRO_8_Gn_1_FEMALE = "你能帮忙治疗我一下吗？";
VOICEMACRO_8_Hu_0 = "我需要治疗！";
VOICEMACRO_8_Hu_0_FEMALE = "我需要治疗。";
VOICEMACRO_8_Hu_1 = "治疗我！";
VOICEMACRO_8_Hu_1_FEMALE = "治疗我！";
VOICEMACRO_8_Ni_0 = "治疗我！";
VOICEMACRO_8_Ni_0_FEMALE = "治疗我。";
VOICEMACRO_8_Ni_1 = "我需要治疗！";
VOICEMACRO_8_Ni_1_FEMALE = "我需要治疗！";
VOICEMACRO_8_Or_0 = "治疗我！";
VOICEMACRO_8_Or_0_FEMALE = "治疗我！";
VOICEMACRO_8_Or_1 = "我需要治疗！";
VOICEMACRO_8_Or_1_FEMALE = "我需要治疗！";
VOICEMACRO_8_Sc_0 = "治疗我！";
VOICEMACRO_8_Sc_0_FEMALE = "治疗我！";
VOICEMACRO_8_Sc_1 = "治疗我！";
VOICEMACRO_8_Sc_1_FEMALE = "治疗我！";
VOICEMACRO_8_Ta_0 = "治疗我！";
VOICEMACRO_8_Ta_0_FEMALE = "治疗我！";
VOICEMACRO_8_Ta_1 = "我需要治疗！";
VOICEMACRO_8_Ta_1_FEMALE = "我需要治疗！";
VOICEMACRO_8_Tr_0 = "治疗我！";
VOICEMACRO_8_Tr_0_FEMALE = "我情况不妙!";
VOICEMACRO_8_Tr_1 = "治疗我！";
VOICEMACRO_8_Tr_1_FEMALE = "治疗我！";
VOICEMACRO_8_Tr_2 = "我情况不太妙啊!";
VOICEMACRO_8_Tr_2_FEMALE = "治疗我！";
VOICEMACRO_LABEL = "谈话";
VOICEMACRO_LABEL_AID1 = "协助";
VOICEMACRO_LABEL_ATTACKMYTARGET1 = "assist";
VOICEMACRO_LABEL_ATTACKMYTARGET2 = "as";
VOICEMACRO_LABEL_CHARGE1 = "次";
VOICEMACRO_LABEL_CHEER1 = "cheer";
VOICEMACRO_LABEL_CONGRATULATIONS1 = "grats";
VOICEMACRO_LABEL_CONGRATULATIONS2 = "congrats";
VOICEMACRO_LABEL_CONGRATULATIONS3 = "congratulations";
VOICEMACRO_LABEL_FLEE1 = "flee";
VOICEMACRO_LABEL_FLEE2 = "run";
VOICEMACRO_LABEL_FLIRT1 = "flirt";
VOICEMACRO_LABEL_FOLLOW1 = "followme";
VOICEMACRO_LABEL_FOLLOWME1 = "followme";
VOICEMACRO_LABEL_FOLLOWME2 = "跟随";
VOICEMACRO_LABEL_FOLLOWME3 = "跟着我";
VOICEMACRO_LABEL_GOODBYE1 = "goodbye";
VOICEMACRO_LABEL_GOODBYE2 = "bye";
VOICEMACRO_LABEL_HEALME1 = "heal";
VOICEMACRO_LABEL_HEALME2 = "治疗我";
VOICEMACRO_LABEL_HELLO1 = "hello";
VOICEMACRO_LABEL_HELP1 = "helpme";
VOICEMACRO_LABEL_HELPME1 = "help";
VOICEMACRO_LABEL_HELPME2 = "帮我";
VOICEMACRO_LABEL_INCOMING1 = "incoming";
VOICEMACRO_LABEL_INCOMING2 = "inc";
VOICEMACRO_LABEL_JOKE1 = "silly";
VOICEMACRO_LABEL_NO1 = "no";
VOICEMACRO_LABEL_OPENFIRE1 = "火焰";
VOICEMACRO_LABEL_OPENFIRE2 = "开火";
VOICEMACRO_LABEL_OUTOFMANA1 = "oom";
VOICEMACRO_LABEL_OUTOFMANA2 = "法力值不足";
VOICEMACRO_LABEL_RASPBERRY1 = "rasp";
VOICEMACRO_LABEL_RASPBERRY2 = "raspberry";
VOICEMACRO_LABEL_SILLY1 = "silly";
VOICEMACRO_LABEL_THANKYOU1 = "thankyou";
VOICEMACRO_LABEL_THANKYOU2 = "thanks";
VOICEMACRO_LABEL_THANKYOU3 = "thank";
VOICEMACRO_LABEL_TRAIN1 = "train";
VOICEMACRO_LABEL_WAITHERE1 = "等待";
VOICEMACRO_LABEL_WAITHERE2 = "在这里等待";
VOICEMACRO_LABEL_YES1 = "yes";
VOICEMACRO_LABEL_YOUREWELCOME1 = "welcome";
VOICEMACRO_LABEL_YOUREWELCOME2 = "welc";
VOICE_ACTIVATED = "启用语音";
VOICE_ACTIVATION_SENSITIVITY = "活动敏感度";
VOICE_AMBIENCE = "环境音量";
VOICE_CHAT = "语音聊天";
VOICE_CHAT_AUDIO_DUCKING = "调整在接收语音聊天时的游戏音量。";
VOICE_CHAT_BATTLEGROUND = "战场";
VOICE_CHAT_MODE = "语音聊天模式";
VOICE_CHAT_NORMAL = "普通";
VOICE_CHAT_OPTIONS = "语音设置";
VOICE_CHAT_OUTPUT_DEVICE = "扬声器";
VOICE_CHAT_PARTY_RAID = "小队/团队";
VOICE_GAME_DUCKING = "游戏声音衰减";
VOICE_INPUT_VOLUME = "麦克风音量";
VOICE_LABEL = "语音";
VOICE_LISTENING = "接听";
VOICE_MICROPHONE_TEST = "麦克风测试";
VOICE_MIC_TEST_PLAY = "播放已录制的样片。";
VOICE_MIC_TEST_RECORD = "录制一份样片。";
VOICE_MUSIC = "音乐";
VOICE_OUTPUT_VOLUME = "扬声器音量";
VOICE_SOUND = "声音";
VOICE_SUBTEXT = "这些选项可以供你更改语音聊天系统的声音硬件和输入设置。";
VOICE_TALKING = "发言";
VOLUME = "音量";
VOTE_BOOT_PLAYER = "有人发起了一个将%1$s从队伍中移出的投票。\n\n理由为：\n|cffffd200%2$s|r\n\n你同意将%1$s移出队伍吗？";
VOTE_BOOT_REASON_REQUIRED = "请写明将%s投票移出的理由：";
VOTE_TO_KICK = "投票踢人";
VULNERABLE_TRAILER = "(+%d 易伤加成)";
WAISTSLOT = "腰部";
WARLOCK_INTELLECT_TOOLTIP = "提高你的法力值上限和法术的爆击几率。\n使你更快地提升武器技能熟练度。";
WARRIOR_STRENGTH_TOOLTIP = "提高你的近战武器的攻击强度。\n使你用盾牌格挡攻击时减免更多的伤害。";
WATCHFRAME_LOCK = "锁定目标框体";
WATCH_FRAME_WIDTH_TEXT = "更宽的任务目标追踪器|TInterface\\OptionsFrame\\UI-OptionsFrame-NewFeatureIcon:0:0:0:-1|t";
WATER_COLLISION = "水体碰撞";
WATER_DETAIL = "水面细节";
WEAPON_SKILL_RATING = "武器技能等级 %d";
WEAPON_SKILL_RATING_BONUS = "（+%d 技能）";
WEAPON_SPEED = "速度";
WEATHER_DETAIL = "天气强度";
WEEKDAY_FRIDAY = "周五";
WEEKDAY_MONDAY = "周一";
WEEKDAY_SATURDAY = "周六";
WEEKDAY_SUNDAY = "周日";
WEEKDAY_THURSDAY = "周四";
WEEKDAY_TUESDAY = "周二";
WEEKDAY_WEDNESDAY = "周三";
WHISPER = "悄悄话";
WHISPER_MESSAGE = "悄悄话";
WHO = "查询";
WHO_FRAME_SHOWN_TEMPLATE = "（显示%d）";
WHO_FRAME_TOTAL_TEMPLATE = "找到%d个人";
WHO_LIST = "名单列表";
WHO_LIST_FORMAT = "|Hplayer:%s|h[%s]|h: 等级 %d %s %s - %s";
WHO_LIST_GUILD_FORMAT = "|Hplayer:%s|h[%s]|h: 等级 %d %s %s <%s> - %s";
WHO_NUM_RESULTS = "共计%d个玩家";
WHO_TAG_CLASS = "c-";
WHO_TAG_GUILD = "g-";
WHO_TAG_NAME = "n-";
WHO_TAG_RACE = "r-";
WHO_TAG_ZONE = "z-";
WIDESCREEN_TAG = "（宽屏）";
WIN = "胜";
WINDOWED_MAXIMIZED = "最大化";
WINDOWED_MODE = "窗口模式";
WINDOW_LOCK = "禁用缩放";
WINTERGRASP_IN_PROGRESS = "进行中";
WIN_LOSS = "胜 - 负";
WITHDRAW = "提取";
WORK_IN_PROGRESS = "正在进行";
WORLDMAP_BUTTON = "世界地图";
WORLD_APPEARANCE = "世界外观";
WORLD_LOD = "世界细节层次";
WORLD_MAP = "世界地图";
WORLD_PORT_ROOT_TIMER = "你掉落穿过了世界。你将在这里逗留%d%s。";
WORLD_PVP_DISPLAY = "显示世界PvP目标";
WORLD_PVP_ENTER = "冬拥湖的战斗又一次在召唤你！|n现在进入？|n剩余时间：%d %s";
WORLD_PVP_EXITED_BATTLE = "你已经从冬拥湖的战斗中退出。";
WORLD_PVP_FAIL = "你现在无法进入冬拥湖战场的等待队列。";
WORLD_PVP_INVITED = "你想要加入冬拥湖的战斗吗？";
WORLD_PVP_INVITED_WARMUP = "冬拥湖的战斗即将打响！你要加入等待队列吗？";
WORLD_PVP_LOW_LEVEL = "你的级别太低，无法进入冬拥湖战场。";
WORLD_PVP_PENDING = "冬拥湖战场已满。|n你已在队列中但没有收到战斗的召唤。稍后你将被传出战场。";
WORLD_PVP_PENDING_REMOTE = "冬拥湖战场已满。|n你已在队列中但没有收到战斗的召唤。";
WORLD_PVP_QUEUED = "冬拥湖战场已满。|n你已在队列中。请等候。";
WORLD_PVP_QUEUED_WARMUP = "你正在下一场冬拥湖战斗的等待队列中。";
WOW_MOUSE = "检测到魔兽世界专用鼠标";
WOW_MOUSE_NOT_FOUND = "无法找到魔兽世界专用鼠标。请连接鼠标后在用户界面中再次启动该选项。";
WRISTSLOT = "手腕";
WRONG_SLOT_FOR_ITEM = "物品无法放入该空位。";
XP = "经验值";
XPBAR_LABEL = "经验槽";
XP_BAR_TEXT = "经验条文字";
XP_TEXT = "|cffffffff%d / %d  ( %d%% )|r";
YELL = "大喊";
YELLOW_GEM = "黄色";
YELL_MESSAGE = "呼喊";
YES = "是";
YOU = "你";
YOUR_BID = "你的出价：";
YOUR_CLASS_MAY_NOT_PERFORM_ROLE = "你的职业无法担任该职责。";
YOUR_ROLE = "你的职责";
YOU_ARE_IN_DUNGEON_GROUP = "你在一个地下城小队中。";
YOU_ARE_LISTED_IN_LFR = "你当前处于团队浏览器的列表中。";
YOU_LOOT_MONEY = "你拾取了%s";
YOU_MAY_NOT_QUEUE_FOR_DUNGEON = "你不能进入这个地下城的队列。";
YOU_MAY_NOT_QUEUE_FOR_THIS = "你不能进入此队列。";
YOU_RECEIVED = "你获得了：";
ZHCN = "简体中文";
ZHTW = "繁体中文";
ZONE = "地区";
ZONE_COLON = "地区：";
ZONE_UNDER_ATTACK = "|cffffff00%s遭到攻击！|r";
ZOOM_IN = "放大";
ZOOM_OUT = "缩小";
ZOOM_OUT_BUTTON_TEXT = "右键点击可以缩小地图";
_RECORDING_WARNING_CORRUPTED = "视频文件无效。";
