local SHINEVISUAL_QUALITY = {
	CONST_GRAY = 0,
	WHITE = 1,
	GREEN = 2,
	BLUE = 3,
	PURPLE = 4,
	ORANGE = 5,
	LIGHT_GOLD = 6,
	BLIZZARD_BLUE = 7,
}

local particlePositions = {
    { -118, 128, offset = { -32, 32 }, degrees = -60 },
    { 118, 128, offset = { 32, 32 }, degrees = 60, flipH = true },
    { -118, -128, offset = { -32, -92 }, degrees = -60, flipV = true },
    { 118, -128, offset = { 32, -32 }, degrees = 60, flipH = true, flipV = true },
    { 118, 0, offset = { 92, 0 }, degrees = 60 },
    { -118, 0, offset = { -62, 0 }, degrees = -60, flipH = true, flipV = true },
    { 0, 138, offset = { 0, 92 }, degrees = 60 },
    { 84, 128, offset = { 32, 32 }, degrees = 60 },
    { -84, 128, offset = { -32, 32 }, degrees = -60, flipH = true, flipV = true },
    { 0, -138, offset = { 0, -62 }, degrees = 60, flipH = true, flipV = true },
    { 84, -128, offset = { 32, -32 }, degrees = 60 },
    { -84, -128, offset = { -32, -62 }, degrees = -60 },
    { 84, 0, offset = { 62, 0 }, degrees = 60 },
    { -84, 0, offset = { -92, 0 }, degrees = -60, flipH = true },
}

local offsetAdjust  = 10
local CoverMinScale = 0.85
local CoverMaxScale = 0.91
local MaxScale 		= 1.00
local KPZBX  		= -2000
local KPZBY  		= -30

local function GetAtlasDataTabs(value)
	if value == 1 then
		return "draft-rarity-1-highlight"
	elseif value == 2 then
		return "draft-rarity-2-highlight"
	elseif value == 3 then
		return "draft-rarity-3-highlight"
	elseif value == 4 then
		return "draft-rarity-4-highlight"
	elseif value == 5 then
		return "draft-rarity-5-highlight"
	else
		return nil
	end
end

function UpdateAllVariables(self,...)
	local SpellId, Quality, ClassId, SpecId, BannerName = ...
	local BannerNames = BannerName or ""
	if SpecId ~= 0 then
		if not select(2, GetSpecializationInfoByID(SpecId)) then
			BannerNames = select(2, GetSpecializationInfoByID(SpecId))
		end
	end

	self.ZMBannerFont:SetText(BannerNames)
	self.AtlasPhat = GetAtlasDataTabs(self.Quality)
	self.ZMFrame.ZMShine:SetVertexColor(GetItemQualityColor(Quality)) 				-- 渐变外框的放光效果
	self.ZMTalentIcon:SetTexture(TalentIconId[math.random(#TalentIconId)])			-- 法术图标后的背景板
	self.ZMSpellIcon.SpellName:SetText(select(1,GetSpellInfo(SpellId)))				-- 法术名称
	self.ZMSpellIcon.HighlightTex:SetTexture(KP_SPELLICON_HIGHLIGHT_PHAT[Quality+1])-- 法术图标小框外颜色
	self.ZMClassInfo:SetTexture(KP_CLASS_ICON[ClassId])								-- 左上角职业图标
	self.ZMScrollFrame.ScrollText:SetText(GetSpellDescription(SpellId))				-- 法术描述
	SetPortraitToTexture(self.ZMSpellIcon.IconTex, select(3,GetSpellInfo(SpellId)))	-- 法术图标小框
	if(Quality >= 1 and Quality < 6) then
		SetTexCoordSize(self.ZMEffect,GetAtlasDataTabs(Quality))					-- 渐变色外框
	end
	for i = 1, #self.ZMPar do
		local color = ITEM_QUALITY_COLORS[Quality]
		self.ZMPar[i]:Show()
		self.ZMPar[i]:SetVertexColor(color:GetRGBA())
		self.ZMPar[i]:Hide()
	end
end

local function CreatePositionEff(target, textureName, i, positionData)
    local textures = target:CreateTexture(textureName, "OVERLAY")
    if i > 6 then
        textures:SetTexture("SPELLS\\t_vfx_smoke_cigarette_blackwhite")
    else
        textures:SetTexture("SPELLS\\t_vfx_freeze_c")
    end
    textures:SetSize(256, 256)
    textures:SetPoint("CENTER")
    textures:SetBlendMode("ADD")

    local data = positionData

    local left, right, top, bottom = 0, 1, 0, 1
	if data.flipH then
		left, right = right, left
	end

	if data.flipV then
		top, bottom = bottom, top
	end

    textures:SetTexCoord(left, right, top, bottom)
    textures:SetPoint("CENTER", target, "CENTER", unpack(positionData))
    local AnimGroups = textures:CreateAnimationGroup()

    AnimGroups:SetLooping("REPEAT")

    local Rotation = AnimGroups:CreateAnimation("Rotation")
    Rotation:SetOrder(1)
    Rotation:SetStartDelay(0.1)
    Rotation:SetDuration(2)
    Rotation:SetDegrees(data.degrees)
    Rotation:SetSmoothing("OUT")

    local Translation = AnimGroups:CreateAnimation("Translation")
    Translation:SetOrder(1)
    Translation:SetStartDelay(0.1)
    Translation:SetDuration(2)
    Translation:SetOffset(data.offset[1], data.offset[2])
    Translation:SetSmoothing("OUT")

    textures.Anim = AnimGroups

    return textures;
end

local function CreateKPScrollFrame(self)
	--滚动窗口
	local KPScrollFrame = CreateFrame("ScrollFrame","KPScrollFrame",self)
	KPScrollFrame:SetSize(230,170)
	KPScrollFrame:SetPoint("CENTER",-10,-50)
	KPScrollFrame:Show()
	KPScrollFrame:EnableMouse(true)
	KPScrollFrame:EnableMouseWheel(true)

	local KPScrollChildFrame = CreateFrame("Frame", "KPScrollChildFrame", KPScrollFrame)
	KPScrollChildFrame:SetSize(235, 500)
	-- KPScrollChildFrame:SetBackdrop({bgFile = "Interface/Tooltips/UI-Tooltip-Background",edgeFile = "Interface/DialogFrame/UI-DialogBox-Border", tile = false, tileSize = 0, edgeSize = 32, insets = { left = 12, right = 12, top = 12, bottom = 12 }});
	KPScrollChildFrame:SetPoint("CENTER",0,0)
	-- KPScrollChildFrame:SetBackdropColor(0,0,0,0.5)
	KPScrollChildFrame:Show()
	KPScrollFrame:SetScrollChild(KPScrollChildFrame)

	local KPScrollText = KPScrollChildFrame:CreateFontString("KPScrollText","OVERLAY","GameFontNormal")
	KPScrollText:SetText(GetSpellDescription(197491))
	KPScrollText:SetSize(200,250)
	KPScrollText:SetPoint("TOP")
	KPScrollText:SetJustifyV("TOP")
	KPScrollFrame.ScrollText = KPScrollText

	local KPSlider = CreateFrame("Slider","KPSlider",KPScrollFrame)
	KPSlider:SetOrientation("VERTICAL")
	KPSlider:SetMinMaxValues(0,100)
	KPSlider:SetValue(0)
	KPSlider:SetValueStep(1.0)
	KPSlider:SetPoint("LEFT",KPScrollFrame,"RIGHT",-7,0)
	KPSlider:SetSize(32,185)
	KPSlider:EnableMouse(true)
	KPSlider:EnableMouseWheel(true)
	local KPSliderTex = KPSlider:CreateTexture("KPSliderTex","HIGHLIGHT",nil)	--滑块图标
	KPSliderTex:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
	KPSliderTex:SetSize(32, 32)
	local Slidertexs = KPSlider:SetThumbTexture(KPSliderTex)

	--滑块注册滚动窗口移动
	KPSlider:SetScript("OnValueChanged",
	function(self,offset) 
	--子窗口的大小改变后 这里也需要改变 这里是设置滑动条偏移值
	-- print("OnValue is : "..math.ceil((KPScrollChildFrame:GetHeight()-KPScrollFrame:GetHeight())/100 * self:GetValue()))
	KPScrollFrame:SetVerticalScroll(1*KPSlider:GetValue())
	end)

	--滚动窗口注册鼠标中键关联滑块
	KPScrollFrame:SetScript("OnMouseWheel",
	function(self,val)
		--print("OnMouseWheel")
		--往上滑 val是正数	方向：上，左
		--往下滑 val是负数	方向：下，右
		local s1 = KPSlider	-- 滚动条
		--获取最小最大值
		local minv,maxv = s1:GetMinMaxValues()
		--获取滚动条当前值
		local cv = s1:GetValue()
		--计算新数值
		local nv = cv - ( val * 5 )
		--新数值等于 nv 和 minv中最大的那个
		nv = max(nv, minv)
		--新数值等于 nv 和 maxv中最小的那个
		nv = min(nv, maxv)
		--print("minv : "..minv.." maxv : "..maxv.." cv : "..cv.." nv : "..nv)
		--如果新值 不等于 旧值 
		--设置滚动条的值为新值  -->设置滚动到哪个点(值)
		if ( nv ~= cv ) then
			s1:SetValue(nv);
		end
	end)

	return KPScrollFrame
end

local function CreateKPFullSpellIcon(target)
	local SpellIcon = CreateFrame("Button", "$parenSpellIcon", target)
	SpellIcon:SetSize(50,50)
	SpellIcon:SetPoint("CENTER",0,125)

	local IconBackTex = SpellIcon:CreateTexture()
	IconBackTex:SetTexture("Interface\\Draft\\Shadow")
	IconBackTex:SetSize(142,142)
	IconBackTex:SetDrawLayer("BACKGROUND")
	ClearAndSetPoint(IconBackTex, "CENTER", SpellIcon, "CENTER",0,-2)

	local IconBorderTex = SpellIcon:CreateTexture()
	IconBorderTex:SetTexture("Interface\\Draft\\ArtifactPower-QuestBorder")
	IconBorderTex:SetSize(50,50)
	IconBorderTex:SetDrawLayer("BORDER")
	ClearAndSetPoint(IconBorderTex, "CENTER", SpellIcon, "CENTER")

	local HighlightTex = SpellIcon:CreateTexture()
	HighlightTex:SetTexture(KP_SPELLICON_HIGHLIGHT_PHAT[6])
	HighlightTex:SetSize(52,52)
	HighlightTex:SetPoint("CENTER")
	HighlightTex:SetBlendMode("ADD")
	SpellIcon.HighlightTex = HighlightTex

	local IconTex = SpellIcon:CreateTexture()
	SetPortraitToTexture(IconTex, select(3,GetSpellInfo(102280)))
	IconTex:SetPoint("CENTER")
	IconTex:SetSize(50,50)
	SpellIcon.IconTex = IconTex

	local SpellName = SpellIcon:CreateFontString("$parentSpellName", nil, "GameFontNormal")
	SpellName:SetJustifyH("LEFT")
	SpellName:SetJustifyV("TOP")
	SpellName:SetText("")
	SpellName:SetPoint("BOTTOM", 0, -18)
	SpellIcon.SpellName = SpellName

	return SpellIcon
end

function CreatureKPFrame(x, y, name)
	local frame = CreateFrame("Frame", name, UIParent)
	frame:SetSize(268, 386)
	frame:SetPoint("CENTER", x, y)

	local BJFrame = CreateFrame("Button", "$parentBJFrame", frame)
	BJFrame:SetSize(268, 386)
	BJFrame:SetPoint("CENTER", 0, 0)
	BJFrame:SetScale(CoverMinScale)
	frame.BJFrame = BJFrame

	local Background = BJFrame:CreateTexture()
	SetTexCoordSize(Background,"draft-card-back")
	Background:SetPoint("CENTER")
	frame.Background = Background

	local BackgroundGlow = BJFrame:CreateTexture()
	BackgroundGlow:SetTexture("Interface\\Draft\\CardBackGlow")
	BackgroundGlow:SetBlendMode("ADD")
	BackgroundGlow:SetPoint("CENTER")
	BackgroundGlow:SetSize(512, 1100)
	frame.BackgroundGlow = BackgroundGlow

	local ZMFrame = CreateFrame("Frame", "$parentZMFrame", frame)
	ZMFrame:SetSize(268, 386)
	ZMFrame:SetPoint("CENTER", 0, 0)
	-- ZMFrame:SetScale(CoverMinScale)
	frame.ZMFrame = ZMFrame

	local ZMADDButton = CreateFrame('Button', nil, ZMFrame, "UIPanelButtonTemplate")
	ZMADDButton:SetSize(100, 20)
	ZMADDButton:SetPoint("BOTTOM", 0, -5)
	ZMADDButton:SetText("应用")
	ZMADDButton:Disable()
	ZMADDButton:EnableMouse(false)
	frame.ZMADDButton = ZMADDButton
	
	local ZMAddBorder = ZMFrame:CreateTexture()
	ZMAddBorder:SetTexture("Interface\\Draft\\Enchant_RefundButton")
	ZMAddBorder:SetSize(175, 38)
	ZMAddBorder:SetPoint("BOTTOM",-1,-15)

	local ZMBackground = ZMFrame:CreateTexture()
    ZMBackground:SetTexture("Interface\\Draft\\cardbg")
	ZMBackground:SetPoint("CENTER")
	ZMBackground:SetDrawLayer("BACKGROUND",2)
	frame.ZMBackground = ZMBackground

	local ZMShine = ZMFrame:CreateTexture()
	ZMShine:SetTexture("Interface\\Draft\\CardBackGlow")
	ZMShine:SetPoint("CENTER")
	ZMShine:SetDrawLayer("BACKGROUND")
	ZMShine:SetBlendMode("ADD")
	ZMShine:SetSize(520, 1045)
	ZMShine:SetVertexColor(GetItemQualityColor(SHINEVISUAL_QUALITY.ORANGE))
	frame.ZMFrame.ZMShine = ZMShine	--渐变外框的放光效果

	local ZMTalentIcon = ZMFrame:CreateTexture()
	ZMTalentIcon:SetTexture(TalentIconId[math.random(#TalentIconId)])
	ZMTalentIcon:SetDrawLayer("BACKGROUND",1)
	ZMTalentIcon:SetPoint("TOPLEFT")
	ZMTalentIcon:SetBlendMode("ALPHAKEY")
	frame.ZMTalentIcon = ZMTalentIcon

	local ZMTalentBanner = ZMFrame:CreateTexture()
	SetTexCoordSize(ZMTalentBanner,"draft-name-highlight")
	ZMTalentBanner:SetPoint("TOP",0,-5)
	ZMTalentBanner:SetDrawLayer("BACKGROUND",3)
	local bannercolor = ITEM_QUALITY_COLORS[5]
	ZMTalentBanner:SetVertexColor(bannercolor:GetRGBA())
	ZMTalentBanner:SetAlpha(0.8)
	frame.ZMTalentBanner = ZMTalentBanner

	local ZMSpellIcon = CreateKPFullSpellIcon(ZMFrame)
	frame.ZMSpellIcon = ZMSpellIcon

	local ZMBannerFont = ZMFrame:CreateFontString(nil,"OVERLAY","GameFontNormalSmall")
	ZMBannerFont:SetPoint("TOP",0,-15)
	ZMBannerFont:SetText("")
	frame.ZMBannerFont = ZMBannerFont	-- 天赋名称

	local ZMTopRightIcon = ZMFrame:CreateTexture()
	SetTexCoordSize(ZMTopRightIcon,"draft-filigree")
	ZMTopRightIcon:SetDrawLayer("BACKGROUND",3)
	ZMTopRightIcon:SetPoint("TOPRIGHT",-10,-10)

	local ZMBottomLeftIcon = ZMFrame:CreateTexture()
	-- SetTexCoordSize(ZMBottomLeftIcon,"draft-cost-rune")
    ZMBottomLeftIcon:SetTexture("Interface\\Draft\\CornerL")
	ZMBottomLeftIcon:SetSize(64,128)
	ZMBottomLeftIcon:SetDrawLayer("BACKGROUND",3)
	-- ZMBottomLeftIcon:SetPoint("BOTTOMLEFT",7,10)
	ZMBottomLeftIcon:SetPoint("BOTTOMLEFT",5,-18)

	-- local ZMBottomRightIcon = ZMFrame:CreateTexture()
    -- ZMBottomRightIcon:SetTexture("Interface\\Draft\\CornerR")
	-- ZMBottomRightIcon:SetSize(64,128)
	-- ZMBottomRightIcon:SetDrawLayer("BACKGROUND",3)
	-- ZMBottomRightIcon:SetPoint("BOTTOMRIGHT",-5,-18)

	local ZMScrollFrame = CreateKPScrollFrame(ZMFrame)
	frame.ZMScrollFrame = ZMScrollFrame

	local ZMRing = ZMFrame:CreateTexture()
	SetTexCoordSize(ZMRing,"draft-ring")
	ZMRing:SetDrawLayer("BORDER",2)
	ZMRing:SetPoint("TOPLEFT",-30,30)

	local ZMClassIcon = ZMFrame:CreateTexture()
    ZMClassIcon:SetTexture("Interface\\Draft\\hunter")
	ZMClassIcon:SetPoint("TOPLEFT",-50,115)
	ZMClassIcon:SetSize(128,256)
	ZMClassIcon:SetDrawLayer("BORDER",3)
	frame.ZMClassInfo = ZMClassIcon	--职业图标

	local ZMEffect = ZMFrame:CreateTexture()
	SetTexCoordSize(ZMEffect,"draft-rarity-2-highlight")
	ZMEffect:SetBlendMode("ADD")
	ZMEffect:SetDrawLayer("BORDER",1)
	ZMEffect:SetPoint("CENTER")
	frame.ZMEffect = ZMEffect	--渐变色外框

	-- local ZMPar = CreateKPParticleEffect(ZMFrame)
	local ZMPar = {}
	for i = 1, #particlePositions do
		local positionData = particlePositions[i]   -- 粒子效果的坐标系
	
		ZMPar[i] = CreatePositionEff(ZMFrame,"texTab"..i, i, positionData)
		local color = ITEM_QUALITY_COLORS[5]
		ZMPar[i]:SetVertexColor(color:GetRGBA())
		ZMPar[i]:Hide()
	end
	frame.ZMPar = ZMPar	--烟雾粒子特效组

	BackgroundGlow:Hide()
	ZMFrame:Hide()

	return frame
end

local function PositionEffDone(target)
	for i = 1, #target.ZMPar do
        if not target.ZMPar[i].Anim:IsPlaying() then 
			return 
		else
			target.ZMPar[i].Anim:Stop()
			target.ZMPar[i]:Hide()
		end
    end
end

local function PlayPositionEff(self, elapsed)
	local target = self:GetParent()
    for i = 1, #target.ZMPar do
        if not target.ZMPar[i].Anim:IsPlaying() then return end
        if not target.time or target.time >= 1 then
            target.time = 0
            target.StartAlpha = target.TargetAlpha or 1
            target.TargetAlpha = math.random(0.2, 0.8)
        else
            target.time = target.time + (elapsed * 0.1)
        end
        -- print("self.time = "..target.time)
        -- print("self.TargetAlpha = "..target.TargetAlpha)
        -- print("self.StartAlpha = "..target.StartAlpha)
        -- print("lerp = "..lerp(target.StartAlpha, target.TargetAlpha, EaseInOut(target.time)))
	    target.ZMPar[i]:SetAlpha(lerp(target.StartAlpha, target.TargetAlpha, EaseInOut(target.time)))
		if i == 14 and target.ZMPar[i]:GetAlpha() == 0 then
			--print("IsDone")
			target.StartAlpha = 1
			target.time = 0
			target.ZMADDButton:Enable()
			target.ZMADDButton:EnableMouse(true)
			PositionEffDone(target)
			self:SetScript("OnUpdate", nil)
		end
    end
end

local function KPOnClickAnimEffec(self, elapsed)
	if self.FadeIn <= 1 then
		self.FadeIn = self.FadeIn + elapsed * 2
		-- print("FadeIn = " .. self.FadeIn)
		self.BackgroundGlow:SetAlpha(self.FadeIn)
	else
		self.FadeOut = self.FadeOut - elapsed
		self.BackgroundGlow:SetAlpha(self.FadeOut)
	end

	if self.FadeOut < 0.9 then
		self.Background:Hide()
		self.BackgroundGlow:Hide()
	end

	if not self.Background:IsShown() then
		self.BJFrame:Hide()	-- 这里不写的话，可以重复点击
		self.ZMFrame:Show()
		for i = 1, #self.ZMPar do
            if i <= 15 then
                local particle = self.ZMPar[i]
                particle:Show()
                particle.Anim:Play()
            end
        end
		self.ZMFrame:SetScript("OnUpdate", PlayPositionEff)
		self:SetScript("OnUpdate", nil)
	end
end

local function KPOnUpdates(self, elapsed)
	KPOnClickAnimEffec(self,elapsed)
end

local function KPOnEnter(target)
	target.CurrentScale = MaxScale
	target.TargetScale = CoverMaxScale
	target.BJFrame:SetScale(CoverMaxScale)
end

local function KPOnLeave(target)
	target.BJFrame:SetScale(CoverMinScale)
end
local function KPOnClick(target)
	local KP = target:GetParent()
	KP.BackgroundGlow:Show()
	KP.FadeIn = 0
	KP.FadeOut = 1
	KP:SetScript("OnUpdate", KPOnUpdates)
end

-- local KP1 = CreatureKPFrame(-350,-30, "KP1")
-- local KP2 = CreatureKPFrame(0,-30, "KP2")
-- local KP3 = CreatureKPFrame(350,-30, "KP3")
local KP1 = CreatureKPFrame(KPZBX,KPZBY, "KP1")
local KP2 = CreatureKPFrame(KPZBX,KPZBY, "KP2")
local KP3 = CreatureKPFrame(KPZBX,KPZBY, "KP3")

local function KPCreateFPAnimation(target, realOffset)
	local KPAG = target:CreateAnimationGroup()
	KPAG:SetLooping("REPEAT")

	local An = KPAG:CreateAnimation()
	An:SetDuration(2 / 1000)

	target.offset = KPZBX

	KPAG:SetScript("OnLoop", function(self)
		target.offset = target.offset + offsetAdjust
		if target.offset >= realOffset then
			target.offset = realOffset
			self:Stop()
		end
		target:SetPoint("CENTER", target.offset, KPZBY)
	end)
	
	target.KPAG = KPAG
end

local function ResetKP(target)
	target.ZMFrame:Hide()
	target.BJFrame:Show()
	target.Background:Show()
	target.offset = KPZBX
	target:SetPoint("CENTER", KPZBX, KPZBY)
end

function ResetALLKP()
	ResetKP(KP1)
	ResetKP(KP2)
	ResetKP(KP3)
end


KPCreateFPAnimation(KP1, -350)
KPCreateFPAnimation(KP2, 0)
KPCreateFPAnimation(KP3, 350)


KP1.BJFrame:SetScript("OnEnter", function (self) KPOnEnter(self:GetParent()) end)
KP2.BJFrame:SetScript("OnEnter", function (self) KPOnEnter(self:GetParent()) end)
KP3.BJFrame:SetScript("OnEnter", function (self) KPOnEnter(self:GetParent()) end)

KP1.BJFrame:SetScript("OnLeave", function(self) KPOnLeave(self:GetParent()) end)
KP2.BJFrame:SetScript("OnLeave", function(self) KPOnLeave(self:GetParent()) end)
KP3.BJFrame:SetScript("OnLeave", function(self) KPOnLeave(self:GetParent()) end)

KP1.BJFrame:SetScript("OnClick", function(self) KPOnClick(self) end)
KP2.BJFrame:SetScript("OnClick", function(self) KPOnClick(self) end)
KP3.BJFrame:SetScript("OnClick", function(self) KPOnClick(self) end)

KP1.ZMADDButton:SetScript("OnClick", function(self) Ghost_SendData("CLIENT_CARD",tostring(1)) ResetALLKP() end)
KP2.ZMADDButton:SetScript("OnClick", function(self) Ghost_SendData("CLIENT_CARD",tostring(2)) ResetALLKP() end)
KP3.ZMADDButton:SetScript("OnClick", function(self) Ghost_SendData("CLIENT_CARD",tostring(3)) ResetALLKP() end)

UpdateAllVariables(KP1,122783,5,10,270)
UpdateAllVariables(KP2,202770,4,11,102)
UpdateAllVariables(KP3,232893,3,12,577)


-- print("创建动画效果")
-- print(select(3, GetSpellInfo(197491)))
-- print(GetSpellLevelLearned(186257))
-- print(GetSpellDescription(186257))
-- print(GetSpellDescription(31589))
-- print("僚机")

KP_Card[1] = KP1
KP_Card[2] = KP2
KP_Card[3] = KP3

SLASH_HELLOWORLD1, SLASH_HELLOWORLD2 = '/hiw', '/hellow';
local function handler(msg, editBox)
	KP1.KPAG:Play()
	KP2.KPAG:Play()
	KP3.KPAG:Play()
end
SlashCmdList["HELLOWORLD"] = handler; -- Also a valid assignment strategy

SLASH_RESTCRAD1 = '/restkp';

local function handlers(msg, editBox)
	ResetKP(KP1)
	ResetKP(KP2)
	ResetKP(KP3)
end

SlashCmdList["RESTCRAD"] = handlers; -- Also a valid assignment strategy
