mysqldump -uroot -p ahworld > D:ssssssssssssssss.sql					//把ahworld整个库的数据 备份到D盘根目录 文件名是ssssssssssssssss.sql
mysql -u root -p _yws03 < F:s3.sql											//把 001.sql 还原到数据库 _xw库
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _yws03 < E:001.sql		//把E盘根目录下的001.sql 还原到_yws03数据库 格式默认为utf8
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _yws03 < F:s3.sql		//把E盘根目录下的001.sql 还原数据库 格式默认为utf8
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _yas03 < F:s1.sql		//把E盘根目录下的001.sql 还原数据库 格式默认为utf8
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _ycs03 < F:s2.sql		//把E盘根目录下的001.sql 还原数据库 格式默认为utf8

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 __aw < M:w2.sql		
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 __ac < M:c2.sql		
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 __aa < M:a2.sql		
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548dbc < M:spell.sql	
mysql -hlocalhost -uroot -p  --default-character-set=utf8 548dbc < E:spell.sql	
mysql -u root -p 548dbc < M:spell.sql	
mysqldump -uroot -p ahworld > D:ssssssssssssssss.sql

mysqldump -uroot -p sgworld > D:s1.sql
mysqldump -uroot -p sgauth > D:s2.sql
mysqldump -uroot -p sgchar > D:s3.sql
	
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowauth < E:mop_a.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowworld < E:mop_w.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowcharacters < E:mop_c.sql

mysqldump -uroot -p _yas03 > M:a1.sql
mysqldump -uroot -p _ycs03 > M:c1.sql
mysqldump -uroot -p _yws03 > M:w1.sql


mysqldump -uroot -p ahworld > D:w2.sql
mysqldump -uroot -p ahchar > D:c2.sql
mysqldump -uroot -p ahauth > D:a2.sql

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 sgworld < E:s1.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 sgauth < E:s2.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 sgchar < E:s3.sql


mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 fusion < D:fusion.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 world < D:world.sql

mysqldump -uroot -p shujvku > D:world.sql
mysqldump -uroot -p aa > D:aa.sql
mysqldump -uroot -p char > D:char.sql

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 dabo-aa < E:aa.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 dabo-cc < E:cc.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 dabo-ww < E:ww.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 mop_w < D:mop_w.sql

REPLACE INTO `[记录表]玩家签到记录`(玩家GUID,日期,是否签到,是否补签,签到时间) VALUES (1,"2020-10-5",1,1,"");



mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 __aa < M:a2.sql	

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548world < M:auth.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548world < M:world.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548characters < M:characters.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548archive < M:archive.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 548fusion	< M:fusion.sql



mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xworld < E:7x-world.sql

mysqldump -uroot -p _548wowworld > E:w15x.sql
mysqldump -uroot -p _548wowcharacters > E:c15x.sql
mysqldump -uroot -p _548wowauth > E:a15x.sql

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowworld < D:w5x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowcharacters < D:c5x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 _548wowauth < D:a5x.sql

mysqldump -uroot -p ___azauth > E:a3x.sql
mysqldump -uroot -p ___azchar > E:c3x.sql
mysqldump -uroot -p ___azworld > E:w3x.sql

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 ___azworld < D:w3x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 ___azchar < D:c3x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 ___azauth < D:a3x.sql

CREATE DATABASE ___azauth;
CREATE DATABASE ___azchar;
CREATE DATABASE ___azworld;

mysqldump -uroot -p 7xworlds > E:w7x-2024年11月2日.sql
mysqldump -uroot -p 7xcharacters > E:c7x.sql
mysqldump -uroot -p 7xauth > E:a7x.sql
mysqldump -uroot -p 7xhotfixes > E:h7x.sql

CREATE DATABASE 7xworld;
CREATE DATABASE 7xcharacters;
CREATE DATABASE 7xauth;
CREATE DATABASE 7xhotfixes;

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xworldss < E:legion_world.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xworlds < C:w7x-2024年10月8日.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xcharacters < D:c7x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xauth < D:a7x.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xhotfixes < D:h7x.sql

mysqldump -uroot -p 10x > D:10XHOST.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 10dbc < E:10XHOST.sql
9280b5273e58f8c3c24267b056c6b4c8   	-- 测试云服务器 加密关键词：徒弟001 | MD5加密
e7dea7c405682dbef5eeb0a8138b8f0a	-- 客服	MD5加密 huhu
1e0c2a7a2be95ce84f37220594c49e8b	-- 784458064 MD5 已经禁用
200bcb6f490695d46e0d978d03b7265a	-- QQ 596732375
3aff7ef96700f60698b0359d1043425f	-- 刀剑服务端
af3218ffdc0d334154c1ddc80bc5278d	-- 徒弟介绍的人买的 加密关键词：郑浩买的
df2d66372ae7ee9fea87f23cf94fda68	-- 徒弟客服园长  加密关键词：园长

mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xworlds < F:7xws.sql
mysql -hlocalhost -uroot -p  --default-character-set=utf8mb4 7xss-world < E:7xss-world.sql

impact.blp好看