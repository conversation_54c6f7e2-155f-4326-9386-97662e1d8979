# UI

#### 介绍
WOW UI 界面插件

#### 软件架构
软件架构说明


#### 安装教程

1. 将scr文件夹的cpp及h文件移植到服务端源码内编译
2. 将插件文件夹复制到客户端并勾选加载


#### 文件夹说明

1. 商城UI文件夹里的是2.0版本商城UI
2. TestUI文件夹里的是测试版本UI(非商城UI)
3. xxxxxx

#### 参与贡献

本人独有


#### 码云特技

1. 使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2. 码云官方博客 [blog.gitee.com](https://blog.gitee.com)
3. 你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解码云上的优秀开源项目
4. [GVP](https://gitee.com/gvp) 全称是码云最有价值开源项目，是码云综合评定出的优秀开源项目
5. 码云官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6. 码云封面人物是一档用来展示码云会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)游戏中查看UI信息的宏/framestack
