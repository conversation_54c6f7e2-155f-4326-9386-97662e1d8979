local frame = CreateFrame("Frame", nil, UIParent)
frame:SetSize(256, 256)
frame:SetPoint("CENTER")
frame:SetScale(0.85)
    

local texture = frame:CreateTexture(nil, "OVERLAY")
SetTexCoordSize(texture,"draft-rarity-3-highlight")

texture:SetPoint("CENTER")
texture:SetBlendMode("ADD")

local AnimGroup = texture:CreateAnimationGroup()

AnimGroup:SetLooping("REPEAT")

local particlePositions = {
    { -118, 128, offset = { -32, 32 }, degrees = -60 },
    { 118, 128, offset = { 32, 32 }, degrees = 60, flipH = true },
    { -118, -128, offset = { -32, -92 }, degrees = -60, flipV = true },
    { 118, -128, offset = { 32, -32 }, degrees = 60, flipH = true, flipV = true },
    { 118, 0, offset = { 92, 0 }, degrees = 60 },
    { -118, 0, offset = { -62, 0 }, degrees = -60, flipH = true, flipV = true },
    { 0, 138, offset = { 0, 92 }, degrees = 60 },
    { 84, 128, offset = { 32, 32 }, degrees = 60 },
    { -84, 128, offset = { -32, 32 }, degrees = -60, flipH = true, flipV = true },
    { 0, -138, offset = { 0, -62 }, degrees = 60, flipH = true, flipV = true },
    { 84, -128, offset = { 32, -32 }, degrees = 60 },
    { -84, -128, offset = { -32, -62 }, degrees = -60 },
    { 84, 0, offset = { 62, 0 }, degrees = 60 },
    { -84, 0, offset = { -92, 0 }, degrees = -60, flipH = true },
}

local function CreatePositionEff(target, textureName, i, positionData)
    local textures = target:CreateTexture(textureName, "OVERLAY")
    if i > 6 then
        textures:SetTexture("SPELLS\\t_vfx_smoke_cigarette_blackwhite")
    else
        textures:SetTexture("SPELLS\\t_vfx_freeze_c")
    end
    textures:SetSize(256, 256)
    textures:SetPoint("CENTER")
    textures:SetBlendMode("ADD")

    local data = positionData

    local left, right, top, bottom = 0, 1, 0, 1
	if data.flipH then
		left, right = right, left
	end

	if data.flipV then
		top, bottom = bottom, top
	end

    textures:SetTexCoord(left, right, top, bottom)
    textures:SetPoint("CENTER", frame, "CENTER", unpack(positionData))
    local AnimGroups = textures:CreateAnimationGroup()

    AnimGroups:SetLooping("REPEAT")

    local Rotation = AnimGroups:CreateAnimation("Rotation")
    Rotation:SetOrder(1)
    Rotation:SetStartDelay(0.1)
    Rotation:SetDuration(2)
    Rotation:SetDegrees(data.degrees)
    Rotation:SetSmoothing("OUT")

    local Translation = AnimGroups:CreateAnimation("Translation")
    Translation:SetOrder(1)
    Translation:SetStartDelay(0.1)
    Translation:SetDuration(2)
    Translation:SetOffset(data.offset[1], data.offset[2])
    Translation:SetSmoothing("OUT")

    textures.Anim = AnimGroups

    return textures;
end

local TexTab = {}

for i = 1, #particlePositions do
    local positionData = particlePositions[i]   -- 粒子效果的坐标系

    TexTab[i] = CreatePositionEff(frame,"texTab"..i, i, positionData)
    local color = ITEM_QUALITY_COLORS[3]
    TexTab[i]:SetVertexColor(color:GetRGBA())
    TexTab[i]:Hide()
end

local function PlayPositionEff(self, elapsed)
    for i = 1, #TexTab do
        if not TexTab[i].Anim:IsPlaying() then return end
        if not self.time or self.time >= 1 then
            self.time = 0
            self.StartAlpha = self.TargetAlpha or 1
            self.TargetAlpha = math.random(0.2, 0.8)
        else
            self.time = self.time + (elapsed * 0.05)
        end
        print("self.time = "..self.time)
        print("self.TargetAlpha = "..self.TargetAlpha)
        print("self.StartAlpha = "..self.StartAlpha)
        
        print("lerp = "..lerp(self.StartAlpha, self.TargetAlpha, EaseInOut(self.time)))
	    TexTab[i]:SetAlpha(lerp(self.StartAlpha, self.TargetAlpha, EaseInOut(self.time)))
    end
end

frame:SetScript("OnEvent", function(self, event)
    if event == "PLAYER_REGEN_DISABLED" then
        print("PLAYER_REGEN_DISABLED")
        frame:SetScript("OnUpdate", PlayPositionEff)
        for i = 1, #TexTab do
            if i <= 15 then
                local particle = TexTab[i]
                particle:Show()
                particle.Anim:Play()
            end
        end
    else
        print("PLAYER_REGEN_ENABLED")
        self.StartAlpha = 1
        self.time = 0
        frame:SetScript("OnUpdate", nil)
        for i = 1, #TexTab do
            if i <= 15 then
                local particle = TexTab[i]
                particle:SetAlpha(1)
                particle.Anim:Stop()
                particle:Hide()
            end
        end
    end
end)

frame:RegisterEvent("PLAYER_REGEN_DISABLED")    --进入战斗状态
frame:RegisterEvent("PLAYER_REGEN_ENABLED")     --推出战斗状态