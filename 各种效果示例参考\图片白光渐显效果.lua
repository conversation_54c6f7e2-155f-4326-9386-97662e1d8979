local CoverMinScale = 0.85
local CoverMaxScale = 0.91

-- Clamps a value between minValue and maxValue
local function clamp(value, minValue, maxValue)
    if not minValue then
        minValue = 0
        maxValue = 1
    end
    return min(max(minValue, value), max(minValue, maxValue))
end

-- Interpolate between startValue and endValue based on amount
local function lerp(startValue, endValue, a)
    if startValue == endValue then
        return startValue
    end

    a = clamp(a, 0, 1)

    return (startValue + (endValue - startValue) * a)
end





local frame = CreateFrame("Frame", nil, UIParent)
frame:SetSize(268, 386)
frame:SetPoint("CENTER", 0, 0)


local button = CreateFrame("Button", nil, frame)
button:SetSize(268, 386)
button:SetPoint("CENTER", 0, 0)


local Background = button:CreateTexture()
Background:SetTexture("Interface\\Draft\\CardBack2")
Background:SetSize(512, 1126)
Background:SetPoint("CENTER")


local BackgroundGlow = button:CreateTexture()
BackgroundGlow:SetTexture("Interface\\Draft\\CardHover")
BackgroundGlow:SetBlendMode("ADD")
BackgroundGlow:SetPoint("CENTER")
BackgroundGlow:SetSize(512, 1126)


-- 创建一个动画组，用于管理一系列动画效果
local animationGroup = BackgroundGlow:CreateAnimationGroup()

-- 设置动画组循环方式为重复循环，以达到持续动画效果
animationGroup:SetLooping("REPEAT")

-- 在动画组中创建一个动画效果
local animation1 = animationGroup:CreateAnimation("Alpha")
local animation2 = animationGroup:CreateAnimation("Alpha")
local animation3 = animationGroup:CreateAnimation("Alpha")
animation1:SetOrder(1)
animation1:SetDuration(0)
animation1:SetToAlpha(-1)
animation2:SetOrder(2)
animation2:SetDuration(1.2)
animation2:SetToAlpha(1)
animation2:SetSmoothing("OUT")
animation3:SetOrder(3)
animation3:SetDuration(1.5)
animation3:SetToAlpha(-1)
animation3:SetSmoothing("IN")

local function UpdateScale(self, elapsed)
	if button.AnimationTime >= 1 then
		button:SetScale(button.TargetScale)
		button:SetScript("OnUpdate", nil)
		return
	end
	button:SetScale(lerp(button.CurrentScale, button.TargetScale, button.AnimationTime))
	button.AnimationTime = button.AnimationTime + (elapsed * 5)
end

button:SetScript("OnLoad", function(self)
	self:SetScale(CoverMinScale)
end)

button:SetScript("OnEnter", function(self)
    animationGroup:Stop()
	BackgroundGlow:Show()
    self.AnimationTime = 0
	self.CurrentScale = self:GetScale()
	self.TargetScale = CoverMaxScale

    self:SetScript("OnUpdate", UpdateScale)
    animationGroup:Play()
end)

button:SetScript("OnLeave", function(self)
	animationGroup:Stop()
	BackgroundGlow:Hide()

	-- if self:GetParent():IsCoverFadingOrHidden() then return end
	-- self.AnimationTime = 0
	-- self.CurrentScale = self:GetScale()
	-- self.TargetScale = CoverMinScale
	-- self:SetScript("OnUpdate", UpdateScale)
end)



-- local offset, offsetAdjust = -200, 10
-- animationGroup:SetScript("OnLoop", function(self)
--     print("OnLoop OnLoop".. offset)
--     -- 增加偏移量以实现动画效果
--     offset = offset + offsetAdjust
--     -- 当偏移量达到或超过上限时，停止动画
--     if offset >= 200 then
--         offset = 200
--         self:Stop()
--     -- 当偏移量达到或低于下限时，停止动画并改变纹理颜色
--     elseif offset <= -200 then
--         offset = -200
--         self:Stop()
--         -- texture:SetVertexColor(1, 0, 1)
--     end
--     -- 根据偏移量调整框架位置，以实现动画效果
--     -- frame:SetPoint("LEFT", offset, 0)
-- end)

-- frame:SetScript("OnEnter", function(self)
--     if offset < 0 then
--         print("OnEnter OnEnter")
--             offsetAdjust = 2
--             animationGroup:Play()
--             -- texture:SetVertexColor(1, 1, 0)
--     end
--     if offset > 0 then
--         offsetAdjust = -2
--         animationGroup:Play()
--     end
-- end)

print("创建动画效果")

