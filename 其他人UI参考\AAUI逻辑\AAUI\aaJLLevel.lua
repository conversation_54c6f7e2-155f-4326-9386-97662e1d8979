 aaJLLevel = {};
 aaJLLevel.LevelViews = {};
 aaJLLevel.levels = {};
 local titletop = -100;
local titleleft = 50;
 function aaJLLevel:ClickDoneBtn(index,btn) 
 local level = aaJLLevel.levels[index];
 aaData:sendAddonMsg(10002, level, "GUILD");
 end;
 aaJLLevel.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaJLLevel.mainView:SetFrameStrata("TOOLTIP");
 aaJLLevel.mainView:SetBackdrop ({bgFile="Interface\\AddOns\\aaAddon\\Icons\\aa-sjjl"});
 aaJLLevel.mainView:SetWidth(512);
 aaJLLevel.mainView:SetHeight(512);
 aaJLLevel.mainView:SetPoint("CENTER",0,0);
 aaJLLevel.mainView:SetMovable(1);
 aaJLLevel.mainView:EnableMouse();
 aaJLLevel.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaJLLevel.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaJLLevel.mainView:Hide();
 end;
 local titlewidth1 = 80;
local titlewidth2 = 262;
local titlewidth3 = 80;
local titletop = -120;
 aaJLLevel.leveltitle = aaJLLevel.mainView:CreateFontString("aaJLLevel.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLevel.leveltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLevel.leveltitle:SetPoint("TOPLEFT", aaJLLevel.mainView, "TOPLEFT", 45,titletop);
 aaJLLevel.leveltitle:SetWidth(titlewidth1);
 aaJLLevel.leveltitle:SetHeight(20);
 aaJLLevel.leveltitle:SetSpacing(5);
 aaJLLevel.leveltitle:SetText(aaData.color_green.."等级");
 end;
 aaJLLevel.jltitle = aaJLLevel.mainView:CreateFontString("aaJLLevel.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLevel.jltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLevel.jltitle:SetPoint("TOPLEFT", aaJLLevel.mainView, "TOPLEFT", 45+titlewidth1,titletop);
 aaJLLevel.jltitle:SetWidth(titlewidth2);
 aaJLLevel.jltitle:SetHeight(20);
 aaJLLevel.jltitle:SetSpacing(5);
 aaJLLevel.jltitle:SetText(aaData.color_green.."奖励");
 end;
 aaJLLevel.zttitle = aaJLLevel.mainView:CreateFontString("aaJLLevel.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaJLLevel.zttitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaJLLevel.zttitle:SetPoint("TOPLEFT", aaJLLevel.mainView, "TOPLEFT", 45+titlewidth1+titlewidth2,titletop);
 aaJLLevel.zttitle:SetWidth(titlewidth3);
 aaJLLevel.zttitle:SetHeight(20);
 aaJLLevel.zttitle:SetSpacing(5);
 aaJLLevel.zttitle:SetText(aaData.color_green.."状态");
 end;
 function aaJLLevel:CreateCell(index,x,y) 
 local menuView = CreateFrame("Frame",nil,aaJLLevel.mainView) do 
 menuView:SetFrameStrata("TOOLTIP");
 menuView:SetWidth(titlewidth1+titlewidth2+titlewidth3);
 menuView:SetHeight(30);
 menuView:SetPoint("TOPLEFT",aaJLLevel.mainView,'TOPLEFT',x,y);
 menuView:Hide();
 end;
 menuView.levelText = menuView:CreateFontString("menuView.levelText", "OVERLAY", "GameFontNormal") do 
 menuView.levelText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.levelText:SetPoint("TOPLEFT", menuView, "TOPLEFT", 0,0);
 menuView.levelText:SetWidth(titlewidth1);
 menuView.levelText:SetHeight(30);
 menuView.levelText:SetSpacing(5);
 menuView.levelText:SetText(aaData.color_blue.."Lv888");
 end;
 menuView.itemText = menuView:CreateFontString("menuView.itemText", "OVERLAY", "GameFontNormal") do 
 menuView.itemText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 menuView.itemText:SetPoint("TOPLEFT", menuView, "TOPLEFT", titlewidth1,0);
 menuView.itemText:SetWidth(titlewidth2);
 menuView.itemText:SetHeight(30);
 menuView.itemText:SetSpacing(5);
 menuView.itemText:SetText(aaData.color_blue.."Lv888");
 end;
 local btnwidth = 70;
 local btnheight = 30;
 menuView.levelBtn = CreateFrame('Button', nil, menuView, 'UIPanelButtonTemplate') do 
 menuView.levelBtn:SetPoint('TOPLEFT', menuView, 'TOPLEFT', titlewidth1+titlewidth2+(titlewidth3-btnwidth)/2, 0);
 menuView.levelBtn:SetSize(btnwidth, btnheight);
 menuView.levelBtn:Disable();
 menuView.levelBtn:SetText("已领取");
 menuView.levelBtn:SetScript('OnClick', function() aaJLLevel:ClickDoneBtn(index,menuView.levelBtn);
 end);
 end;
 return menuView;
 end;
 local top = -145;
 local topspace = -35;
 aaJLLevel.LevelViews[1] = aaJLLevel:CreateCell(1,45,top+topspace*0);
 aaJLLevel.LevelViews[2] = aaJLLevel:CreateCell(2,45,top+topspace*1);
 aaJLLevel.LevelViews[3] = aaJLLevel:CreateCell(3,45,top+topspace*2);
 aaJLLevel.LevelViews[4] = aaJLLevel:CreateCell(4,45,top+topspace*3);
 aaJLLevel.LevelViews[5] = aaJLLevel:CreateCell(5,45,top+topspace*4);
 aaJLLevel.LevelViews[6] = aaJLLevel:CreateCell(6,45,top+topspace*5);
 aaJLLevel.LevelViews[7] = aaJLLevel:CreateCell(7,45,top+topspace*6);
 aaJLLevel.LevelViews[8] = aaJLLevel:CreateCell(8,45,top+topspace*7);
 aaJLLevel.LevelViews[9] = aaJLLevel:CreateCell(9,45,top+topspace*8);
 aaJLLevel.LevelViews[10] = aaJLLevel:CreateCell(10,45,top+topspace*9);
 function aaJLLevel:reload() 
 local index = 1;
 aaJLLevel.levels = {};
 for k,v in pairs(aa_jllevel) do 
 aaJLLevel.levels[index] = k;
 index = index + 1;
 end;
 table.sort(aaJLLevel.levels);
 index = 1;
 for i = 1,#aaJLLevel.levels do 
 local k = aaJLLevel.levels[i];
 local v = aa_jllevel[k];
 if k>0 and v~=nil and v~="" and v~={} then aaJLLevel.LevelViews[index]:Show();
 local isOk = v[1]+0;
 local type = v[2]+0;
 local level = k+0;
 local reward = v[3];
 aaJLLevel.LevelViews[index].levelText:SetText("Lv"..level);
 aaJLLevel.LevelViews[index].itemText:SetText(reward);
 if isOk == 0 then aaJLLevel.LevelViews[index].levelBtn:SetText("领取");
 aaJLLevel.LevelViews[index].levelBtn:Enable();
 elseif isOk == 1 then aaJLLevel.LevelViews[index].levelBtn:SetText("已领取");
 aaJLLevel.LevelViews[index].levelBtn:Disable();
 elseif isOk == 2 then aaJLLevel.LevelViews[index].levelBtn:SetText("未达到");
 aaJLLevel.LevelViews[index].levelBtn:Disable();
 end;
 if type == 0 then aaJLLevel.leveltitle:SetText(aaData.color_green.."等级");
 elseif type == 1 then aaJLLevel.leveltitle:SetText(aaData.color_green.."Vip等级");
 elseif type == 2 then aaJLLevel.leveltitle:SetText(aaData.color_green.."军衔等级");
 elseif type == 3 then aaJLLevel.leveltitle:SetText(aaData.color_green.."斗气等级");
 elseif type == 4 then aaJLLevel.leveltitle:SetText(aaData.color_green.."巅峰等级");
 end index = index + 1;
 else aaJLLevel.LevelViews[index]:Hide();
 end;
 end;
 end;
 local CancelButton = CreateFrame('Button', nil, aaJLLevel.mainView, 'UIPanelButtonTemplate') do 
 CancelButton:SetPoint('TOPRIGHT', aaJLLevel.mainView, 'TOPRIGHT', -30, -20);
 CancelButton:SetSize(60, 30);
 CancelButton:SetText("关闭");
 CancelButton:SetScript('OnClick', function() aaJLLevel:hide();
 end);
 end;
 function aaJLLevel:show() aaJLLevel.mainView:Show();
 aaJLLevel:reload();
 end;
 function aaJLLevel:hide() aaJLLevel.mainView:Hide();
 aaJLLevel:reload();
 end;