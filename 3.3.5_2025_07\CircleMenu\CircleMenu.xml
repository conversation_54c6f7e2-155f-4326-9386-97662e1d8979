<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/ G:\Legacy\Client\Interface\FrameXML\UI.xsd">
    
    <!-- 圆形菜单主按钮模板 -->
    <Button name="CircleMenuMainButtonTemplate" virtual="true" enableMouse="true">
        <Size x="64" y="64"/>
        <Anchors>
            <Anchor point="CENTER">
                <Offset x="0" y="0"/>
            </Anchor>
        </Anchors>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentBackground" file="Interface\AddOns\godPanel\Icons\006">
                    <Size x="64" y="64"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                </Texture>
            </Layer>
            <Layer level="HIGHLIGHT">
                <Texture name="$parentHighlight" file="Interface\Buttons\ButtonHilight-Square">
                    <Size x="64" y="64"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <Color r="1.0" g="1.0" b="1.0" a="0.5"/>
                </Texture>
            </Layer>
        </Layers>
        <Scripts>
            <OnLoad>
                CircleMenu_MainButton_OnLoad(self);
                self:RegisterForDrag("LeftButton");
            </OnLoad>
            <OnClick>
                CircleMenu_MainButton_OnClick(self);
            </OnClick>
            <OnDragStart>
                CircleMenu_MainButton_OnDragStart(self);
            </OnDragStart>
            <OnDragStop>
                CircleMenu_MainButton_OnDragStop(self);
            </OnDragStop>
            <OnEnter>
                CircleMenu_MainButton_OnEnter(self);
            </OnEnter>
            <OnLeave>
                CircleMenu_MainButton_OnLeave(self);
            </OnLeave>
        </Scripts>
    </Button>

    <!-- 圆形菜单子按钮模板 -->
    <Button name="CircleMenuSubButtonTemplate" virtual="true" hidden="true">
        <Size x="32" y="32"/>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentBackground" file="Interface\AddOns\godPanel\Icons\008">
                    <Size x="32" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                </Texture>
            </Layer>
            <Layer level="HIGHLIGHT">
                <Texture name="$parentHighlight" file="Interface\Buttons\ButtonHilight-Square">
                    <Size x="32" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <Color r="1.0" g="1.0" b="1.0" a="0.5"/>
                </Texture>
            </Layer>
        </Layers>
        <Scripts>
            <OnLoad>
                CircleMenu_SubButton_OnLoad(self);
            </OnLoad>
            <OnClick>
                CircleMenu_SubButton_OnClick(self);
            </OnClick>
            <OnEnter>
                CircleMenu_SubButton_OnEnter(self);
            </OnEnter>
            <OnLeave>
                CircleMenu_SubButton_OnLeave(self);
            </OnLeave>
        </Scripts>
    </Button>

    <!-- 圆形菜单主按钮 - 直接作为顶级组件 -->
    <Button name="CircleMenuMainButton" parent="UIParent" inherits="CircleMenuMainButtonTemplate" movable="true">
        <Anchors>
            <Anchor point="CENTER">
                <Offset x="0" y="0"/>
            </Anchor>
        </Anchors>
    </Button>

    <!-- 6个子按钮 - 直接锚定到主按钮 -->
    <Button name="CircleMenuSubButton1" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="80" y="0"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton2" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="40" y="69"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton3" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="-40" y="69"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton4" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="-80" y="0"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton5" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="-40" y="-69"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton6" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="40" y="-69"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton7" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="0" y="-80"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton8" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="-40" y="-69"/>
            </Anchor>
        </Anchors>
    </Button>

    <Button name="CircleMenuSubButton9" parent="UIParent" inherits="CircleMenuSubButtonTemplate">
        <Anchors>
            <Anchor point="CENTER" relativeTo="CircleMenuMainButton">
                <Offset x="-69" y="-40"/>
            </Anchor>
        </Anchors>
    </Button>

</Ui>
