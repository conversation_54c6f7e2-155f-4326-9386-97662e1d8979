--此文件详解创建滚动窗口步骤

--1.创建滚动主窗口Frame
---这里第四个参数是继承("inheritsFrom"),所以填nil
local scrolltest = CreateFrame("ScrollFrame","scrolltest",UIParent,nil)

--2.设置滚动主窗口的大小
scrolltest:SetSize(150, 150)

--3.设置滚动主窗口的锚点位置
scrolltest:SetPoint("CENTER")

--4.创建滚动主窗口的背景未办理 参数("textureName", "layer", "inheritsFrom")
local textest = scrolltest:CreateTexture("textest","BACKGROUND",nil)

--5.设置滚动主窗口背景大小与主窗口完全相同
---设置要放置的对象和其大小与另一个对象完全相同。<--SetAllPoints函数简介
textest:SetAllPoints(scrolltest)

--6.设置滚动主窗口背景RGB
---SetTexture有两种参数形式
---("filename",tile) e.g. texture:SetTexture("image.tga",true)
---(r, g, b[, a]) e.g. texture:SetTexture(0,0,0,1.0)
textest:SetTexture(0,0,0)

--7.创建一个新的窗口Frame
local scrollchildtest = CreateFrame("Frame")

--8.设置这个窗口为滚动窗口的子窗口
scrolltest:SetScrollChild(scrollchildtest)

--9.设置滚动子窗口的大小
scrollchildtest:SetSize(250, 250)

--10.创建滚动子窗口的纹理
local textests = scrollchildtest:CreateTexture("textests","ARTWORK",nil)

--11.设置纹理的贴图
textests:SetTexture("Interface\\ICONS\\jf1")

--12.设置纹理的大小
textests:SetSize(100, 100)

--13.设置纹理的锚点位置
textests:SetPoint("CENTER")

--14.创建滚动条
local slidertest = CreateFrame("Slider")
--15.设置滚动条的方向，横向/竖向 HORIZONTAL/VERTICAL
slidertest:SetOrientation("HORIZONTAL")
--16.设置滚动条的最小值和最大值 ====>想象成起点和终点
slidertest:SetMinMaxValues(0,100)
--17.设置滚动条的值 ==》GetValue()	===>在滚动条的哪个点
slidertest:SetValue(1)
--18.设置滚动条的步长
slidertest:SetValueStep(1.0)
--19.设置滚动条的锚点位置 （起点 对应参照窗口 终点）
---如果是竖向的那么应该是  ("LEFT","scrolltest","RIGHT")
slidertest:SetPoint("TOP","scrolltest","BOTTOM")
--20.设置滚动条的大小 
slidertest:SetSize(150,25)
--21.创建一张滚动条的纹理贴图
local tt = slidertest:CreateTexture("tt")
--22.设置问题贴图的纹理
tt:SetTexture("Interface\\Buttons\\UI-ScrollBar-Knob")
--23.设置纹理贴图的大小
tt:SetSize(25, 25)
--24.设置纹理贴图为'Thumb'贴图
local slidertesttexture = slidertest:SetThumbTexture(tt)

--25.设置滚动条鼠标左键拖移脚本
---当Value改变时,执行函数
---这个才是正确的
slidertest:SetScript("OnValueChanged",
function(self)
--SetVerticalScroll(self:GetValue())
scrolltest:SetHorizontalScroll(1 * self:GetValue())
end
)

---下面这样是不行的
--[[
slidertest:SetScript("OnValueChanged",ttt(self))
function ttt(frame)
	scrolltest:SetHorizontalScroll(1 * frame:GetValue())
end
]]--

--26.设置滚动条鼠标中键滑动脚本
scrolltest:SetScript("OnMouseWheel", 
function(self,val)  

	--往上滑 val是正数	方向：上，左
	--往下滑 val是负数	方向：下，右
	
	local s1 = slidertest	-- 滚动条
	--获取最小最大值
	local minv,maxv = s1:GetMinMaxValues()
	--获取滚动条当前值
	local cv = s1:GetValue()
	--计算新数值
	local nv = cv - ( val * 3 )
	--新数值等于 nv 和 minv中最大的那个
	nv = max(nv, minv)
	--新数值等于 nv 和 maxv中最小的那个
	nv = min(nv, maxv)
	
	--如果新值 不等于 旧值 
	--设置滚动条的值为新值  -->设置滚动到哪个点(值)
	if ( nv ~= cv ) then
	--print("3")
		s1:SetValue(nv);
	end
	
end)
