# StartTimer_SetTexNumbers 函数深度代码解析

## 函数整体功能说明

`StartTimer_SetTexNumbers`函数是WoW 3.3.5插件中负责设置倒计时数字纹理坐标和显示位置的核心函数。该函数将时间值转换为可视化的数字显示，通过纹理坐标映射实现数字的正确渲染。

**函数签名：** `StartTimer_SetTexNumbers(self, ...)`
- **参数：** self（计时器对象），...（数字纹理对象数组）
- **返回值：** 无
- **作用：** 根据当前时间值计算并设置数字纹理的坐标和位置

该函数使用纹理图集（Texture Atlas）技术，将所有数字存储在一张纹理中，通过计算每个数字的纹理坐标来实现高效的数字显示。

## 逐行代码详细解析

### 第195-207行：变量声明和初始化

```lua
function StartTimer_SetTexNumbers(self, ...)
    local digits = {...}                    -- 将可变参数转换为数字纹理对象数组
    local timeDigits = floor(self.time);    -- 将时间向下取整，获取整数秒数
    local digit;                            -- 当前处理的数字（0-9）
    local style = self.style;               -- 获取纹理样式配置（BigGold）
    local i = 1;                           -- 循环计数器，从第一个数字开始

    -- 纹理坐标计算相关变量
    local texCoW = style.w/style.texW;      -- 单个数字在纹理中的宽度比例 = 256/1024 = 0.25
    local texCoH = style.h/style.texH;      -- 单个数字在纹理中的高度比例 = 170/512 = 0.33203125
    local l,r,t,b;                         -- 纹理坐标：左、右、上、下边界
    local columns = floor(style.texW/style.w); -- 纹理中每行的数字列数 = floor(1024/256) = 4
    local numberOffset = 0;                 -- 数字总宽度偏移量，用于居中对齐
    local numShown = 0;                     -- 实际显示的数字个数
```

**关键变量解析：**
- `digits`：存储传入的数字纹理UI对象（digit1, digit2等）
- `timeDigits`：当前剩余时间的整数部分，用于数字提取
- `texCoW/texCoH`：纹理坐标系中单个数字的宽高比例
- `columns`：纹理图集的列数，决定数字在纹理中的排列方式
- `numberOffset`：用于计算数字居中显示的总宽度偏移

### 第209-230行：数字纹理坐标计算核心算法

```lua
while digits[i] do -- 遍历所有数字纹理对象
    if timeDigits > 0 then -- 如果还有数字需要显示
        digit = mod(timeDigits, 10);    -- 获取当前位的数字（个位、十位等）

        -- 计算数字的半宽度（用于精确定位）
        digits[i].hw = style.numberHalfWidths[digit+1]*digits[i].width;
        numberOffset = numberOffset + digits[i].hw; -- 累加总宽度

        -- 纹理坐标计算（关键算法）
        l = mod(digit, columns) * texCoW;    -- 左边界 = (数字 % 4) * 0.25
        r = l + texCoW;                      -- 右边界 = 左边界 + 0.25
        t = floor(digit/columns) * texCoH;   -- 上边界 = floor(数字/4) * 0.33203125
        b = t + texCoH;                      -- 下边界 = 上边界 + 0.33203125

        -- 设置纹理坐标
        digits[i]:SetTexCoord(l,r,t,b);      -- 设置数字纹理坐标
        digits[i].glow:SetTexCoord(l,r,t,b); -- 设置发光效果纹理坐标

        timeDigits = floor(timeDigits/10);   -- 移除已处理的个位数字
        numShown = numShown + 1;             -- 增加显示数字计数
    else
        -- 隐藏多余的数字纹理
        digits[i]:SetTexCoord(0,0,0,0);
        digits[i].glow:SetTexCoord(0,0,0,0);
    end
    i = i + 1; -- 处理下一个数字位
end
```

**算法核心解析：**
1. **数字提取**：使用`mod(timeDigits, 10)`提取个位数字
2. **坐标映射**：通过模运算和整除运算将数字映射到纹理网格位置
3. **纹理坐标**：将网格位置转换为0-1范围的纹理坐标
4. **递归处理**：通过`floor(timeDigits/10)`处理下一位数字

### 第232-246行：数字位置布局和显示逻辑

```lua
if numberOffset > 0 then -- 如果有数字需要显示
    -- 播放倒计时音效
    PlaySound(SOUNDKIT.UI_BATTLEGROUND_COUNTDOWN_TIMER, "SFX", SOUNDKIT_ALLOW_DUPLICATES);

    digits[1]:ClearAllPoints(); -- 清除第一个数字的锚点

    -- 根据显示模式设置第一个数字的位置
    if self.anchorCenter or C_Commentator.IsSpectating() then
        -- 居中显示模式
        digits[1]:SetPoint("CENTER", TimerTracker, "CENTER", numberOffset - digits[1].hw, 0);
    else
        -- 相对于计时器对象居中
        digits[1]:SetPoint("CENTER", self, "CENTER", numberOffset - digits[1].hw, 0);
    end

    -- 设置其他数字的相对位置（从右到左排列）
    for i=2,numShown do
        digits[i]:ClearAllPoints();
        -- 每个数字相对于前一个数字向左偏移
        digits[i]:SetPoint("CENTER", digits[i-1], "CENTER", -(digits[i].hw + digits[i-1].hw), 0)
        i = i + 1;
    end
end
```

**布局算法解析：**
- **居中对齐**：通过`numberOffset - digits[1].hw`实现整体数字的居中显示
- **相对定位**：后续数字相对于前一个数字进行定位
- **从右到左**：数字按从右到左的顺序排列（符合阅读习惯）

## 计算参数

基于 `timer.lua` 中 "BigGold" 样式的配置参数：

| 参数名称 | 数值 | 说明 |
|----------|------|------|
| `style.w` | 256 | 单个数字的宽度（像素） |
| `style.h` | 170 | 单个数字的高度（像素） |
| `style.texW` | 1024 | 纹理文件总宽度（像素） |
| `style.texH` | 512 | 纹理文件总高度（像素） |
| `texCoW` | 0.25 | 单个数字宽度占总纹理宽度的比例 (256/1024) |
| `texCoH` | 0.33203125 | 单个数字高度占总纹理高度的比例 (170/512) |
| `columns` | 4 | 纹理图集的列数 (1024/256) |

## 纹理图集布局

```
纹理图集布局（4列×3行）：
┌─────┬─────┬─────┬─────┐
│  0  │  1  │  2  │  3  │  ← 第0行 (y: 0.0 - 0.33203125)
├─────┼─────┼─────┼─────┤
│  4  │  5  │  6  │  7  │  ← 第1行 (y: 0.33203125 - 0.6640625)
├─────┼─────┼─────┼─────┤
│  8  │  9  │     │     │  ← 第2行 (y: 0.6640625 - 0.99609375)
└─────┴─────┴─────┴─────┘
  ↑     ↑     ↑     ↑
 列0   列1   列2   列3
(x:0.0-(x:0.25-(x:0.5-(x:0.75-
 0.25) 0.5)  0.75) 1.0)
```

## 完整坐标表格

| 数字 | 列位置 | 行位置 | l (左边界) | r (右边界) | t (上边界) | b (下边界) |
|------|--------|--------|------------|------------|------------|------------|
| **0** | 0 | 0 | 0.0 | 0.25 | 0.0 | 0.33203125 |
| **1** | 1 | 0 | 0.25 | 0.5 | 0.0 | 0.33203125 |
| **2** | 2 | 0 | 0.5 | 0.75 | 0.0 | 0.33203125 |
| **3** | 3 | 0 | 0.75 | 1.0 | 0.0 | 0.33203125 |
| **4** | 0 | 1 | 0.0 | 0.25 | 0.33203125 | 0.6640625 |
| **5** | 1 | 1 | 0.25 | 0.5 | 0.33203125 | 0.6640625 |
| **6** | 2 | 1 | 0.5 | 0.75 | 0.33203125 | 0.6640625 |
| **7** | 3 | 1 | 0.75 | 1.0 | 0.33203125 | 0.6640625 |
| **8** | 0 | 2 | 0.0 | 0.25 | 0.6640625 | 0.99609375 |
| **9** | 1 | 2 | 0.25 | 0.5 | 0.6640625 | 0.99609375 |

### 坐标值说明

- **l (左边界)**：数字纹理在X轴上的起始位置（0.0-1.0范围）
- **r (右边界)**：数字纹理在X轴上的结束位置（0.0-1.0范围）
- **t (上边界)**：数字纹理在Y轴上的起始位置（0.0-1.0范围）
- **b (下边界)**：数字纹理在Y轴上的结束位置（0.0-1.0范围）

## 计算公式

### Lua代码实现

```lua
-- 基础参数
local texCoW = style.w / style.texW        -- 0.25
local texCoH = style.h / style.texH        -- 0.33203125
local columns = floor(style.texW / style.w) -- 4

-- 对于任意数字 digit (0-9)
local digit = mod(timeDigits, 10)          -- 提取个位数字

-- 计算网格位置
local col = mod(digit, columns)            -- 列位置 (0-3)
local row = floor(digit / columns)         -- 行位置 (0-2)

-- 计算纹理坐标
local l = col * texCoW                     -- 左边界
local r = l + texCoW                       -- 右边界
local t = row * texCoH                     -- 上边界
local b = t + texCoH                       -- 下边界

-- 应用纹理坐标
digits[i]:SetTexCoord(l, r, t, b)
digits[i].glow:SetTexCoord(l, r, t, b)
```

### 数学公式

对于数字 `n` (0 ≤ n ≤ 9)：

```
列位置 = n mod 4
行位置 = floor(n / 4)

l = (n mod 4) × 0.25
r = l + 0.25
t = floor(n / 4) × 0.33203125
b = t + 0.33203125
```

## 使用示例

在 `StartTimer_SetTexNumbers` 函数中的实际应用：

```lua
-- 当 timeDigits = 154 时的处理过程：
-- 第1次循环：digit = 154 % 10 = 4 → 使用数字4的坐标
-- 第2次循环：digit = 15 % 10 = 5  → 使用数字5的坐标  
-- 第3次循环：digit = 1 % 10 = 1   → 使用数字1的坐标
```

## 技术优势

使用纹理图集技术的优势：

1. **性能优化**：减少纹理切换，提高渲染效率
2. **内存节省**：单张纹理存储多个数字，减少显存占用
3. **管理便利**：统一管理所有数字资源
4. **精确定位**：通过数学计算实现像素级精确定位

## 数据模拟计算（时间值1-20）

### 计算过程详解

使用timer.lua中的真实常量进行计算模拟：

**常量配置：**
```lua
TIMER_NUMBERS_SETS["BigGold"] = {
    texture = "Interface\\Timer\\BigTimerNumbers",
    w = 256, h = 170, texW = 1024, texH = 512,
    numberHalfWidths = {
        35/128, 14/128, 33/128, 32/128, 36/128, 32/128, 33/128, 29/128, 31/128, 31/128
    }
}
```

**计算参数：**
- texCoW = 256/1024 = 0.25
- texCoH = 170/512 = 0.33203125
- columns = floor(1024/256) = 4

### 完整计算结果表格（包含纹理坐标和屏幕位置坐标）

**计算参数：**
- digits[i].width = 128 (style.w/2 = 256/2)
- numberHalfWidths = {35/128, 14/128, 33/128, 32/128, 36/128, 32/128, 33/128, 29/128, 31/128, 31/128}

| 时间值 | 数字序列 | 第1位数字 | 纹理坐标(l,r,t,b) | hw值 | 第2位数字 | 纹理坐标(l,r,t,b) | hw值 | numberOffset | 第1位SetPoint(x,y) | 第2位SetPoint(x,y) |
|--------|----------|-----------|-------------------|------|-----------|-------------------|------|--------------|---------------------|---------------------|
| **1** | [1] | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | - | - | - | 14.0 | (0.0, 0) | - |
| **2** | [2] | 2 | (0.5,0.75,0.0,0.33203125) | 33.0 | - | - | - | 33.0 | (0.0, 0) | - |
| **3** | [3] | 3 | (0.75,1.0,0.0,0.33203125) | 32.0 | - | - | - | 32.0 | (0.0, 0) | - |
| **4** | [4] | 4 | (0.0,0.25,0.33203125,0.6640625) | 36.0 | - | - | - | 36.0 | (0.0, 0) | - |
| **5** | [5] | 5 | (0.25,0.5,0.33203125,0.6640625) | 32.0 | - | - | - | 32.0 | (0.0, 0) | - |
| **6** | [6] | 6 | (0.5,0.75,0.33203125,0.6640625) | 33.0 | - | - | - | 33.0 | (0.0, 0) | - |
| **7** | [7] | 7 | (0.75,1.0,0.33203125,0.6640625) | 29.0 | - | - | - | 29.0 | (0.0, 0) | - |
| **8** | [8] | 8 | (0.0,0.25,0.6640625,0.99609375) | 31.0 | - | - | - | 31.0 | (0.0, 0) | - |
| **9** | [9] | 9 | (0.25,0.5,0.6640625,0.99609375) | 31.0 | - | - | - | 31.0 | (0.0, 0) | - |
| **10** | [0,1] | 0 | (0.0,0.25,0.0,0.33203125) | 35.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 49.0 | (14.0, 0) | (-49.0, 0) |
| **11** | [1,1] | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 28.0 | (14.0, 0) | (-28.0, 0) |
| **12** | [2,1] | 2 | (0.5,0.75,0.0,0.33203125) | 33.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 47.0 | (14.0, 0) | (-47.0, 0) |
| **13** | [3,1] | 3 | (0.75,1.0,0.0,0.33203125) | 32.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 46.0 | (14.0, 0) | (-46.0, 0) |
| **14** | [4,1] | 4 | (0.0,0.25,0.33203125,0.6640625) | 36.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 50.0 | (14.0, 0) | (-50.0, 0) |
| **15** | [5,1] | 5 | (0.25,0.5,0.33203125,0.6640625) | 32.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 46.0 | (14.0, 0) | (-46.0, 0) |
| **16** | [6,1] | 6 | (0.5,0.75,0.33203125,0.6640625) | 33.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 47.0 | (14.0, 0) | (-47.0, 0) |
| **17** | [7,1] | 7 | (0.75,1.0,0.33203125,0.6640625) | 29.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 43.0 | (14.0, 0) | (-43.0, 0) |
| **18** | [8,1] | 8 | (0.0,0.25,0.6640625,0.99609375) | 31.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 45.0 | (14.0, 0) | (-45.0, 0) |
| **19** | [9,1] | 9 | (0.25,0.5,0.6640625,0.99609375) | 31.0 | 1 | (0.25,0.5,0.0,0.33203125) | 14.0 | 45.0 | (14.0, 0) | (-45.0, 0) |
| **20** | [0,2] | 0 | (0.0,0.25,0.0,0.33203125) | 35.0 | 2 | (0.5,0.75,0.0,0.33203125) | 33.0 | 68.0 | (33.0, 0) | (-68.0, 0) |

**表格说明：**
- **hw值**：数字的半宽度 = numberHalfWidths[digit+1] * 128
- **numberOffset**：所有数字hw值的累加和，用于居中对齐计算
- **第1位SetPoint(x,y)**：第一个数字的屏幕坐标 = (numberOffset - digits[1].hw, 0)
- **第2位SetPoint(x,y)**：第二个数字的相对坐标 = (-(digits[2].hw + digits[1].hw), 0)

### 详细计算步骤示例（以时间值15为例）

```lua
-- 输入：self.time = 15
local timeDigits = floor(15) = 15
local numberOffset = 0
local numShown = 0

-- 第1次循环（处理个位数字5）
digit = mod(15, 10) = 5
-- 计算hw值（半宽度）
digits[1].hw = numberHalfWidths[5+1] * digits[1].width = (32/128) * 128 = 32.0
numberOffset = numberOffset + digits[1].hw = 0 + 32.0 = 32.0

-- 计算纹理坐标
l = mod(5, 4) * 0.25 = 1 * 0.25 = 0.25
r = 0.25 + 0.25 = 0.5
t = floor(5/4) * 0.33203125 = 1 * 0.33203125 = 0.33203125
b = 0.33203125 + 0.33203125 = 0.6640625
-- 设置digit1纹理坐标：(0.25, 0.5, 0.33203125, 0.6640625)

timeDigits = floor(15/10) = 1
numShown = 1

-- 第2次循环（处理十位数字1）
digit = mod(1, 10) = 1
-- 计算hw值（半宽度）
digits[2].hw = numberHalfWidths[1+1] * digits[2].width = (14/128) * 128 = 14.0
numberOffset = numberOffset + digits[2].hw = 32.0 + 14.0 = 46.0

-- 计算纹理坐标
l = mod(1, 4) * 0.25 = 1 * 0.25 = 0.25
r = 0.25 + 0.25 = 0.5
t = floor(1/4) * 0.33203125 = 0 * 0.33203125 = 0.0
b = 0.0 + 0.33203125 = 0.33203125
-- 设置digit2纹理坐标：(0.25, 0.5, 0.0, 0.33203125)

timeDigits = floor(1/10) = 0
numShown = 2

-- 位置坐标计算阶段
-- 第一个数字的SetPoint坐标
digits[1] x坐标 = numberOffset - digits[1].hw = 46.0 - 32.0 = 14.0
digits[1]:SetPoint("CENTER", self, "CENTER", 14.0, 0)

-- 第二个数字的相对SetPoint坐标
digits[2] x坐标 = -(digits[2].hw + digits[1].hw) = -(14.0 + 32.0) = -46.0
digits[2]:SetPoint("CENTER", digits[1], "CENTER", -46.0, 0)

-- 最终结果：
-- 显示"15"，数字5在x=14.0位置，数字1在相对于数字5的x=-46.0位置
-- numberOffset总宽度：46.0像素
```

### numberHalfWidths数组详解

```lua
-- 数字0-9对应的半宽度比例（基于128像素宽度）
numberHalfWidths = {
    35/128,  -- 数字0：35像素半宽度
    14/128,  -- 数字1：14像素半宽度（最窄）
    33/128,  -- 数字2：33像素半宽度
    32/128,  -- 数字3：32像素半宽度
    36/128,  -- 数字4：36像素半宽度（最宽）
    32/128,  -- 数字5：32像素半宽度
    33/128,  -- 数字6：33像素半宽度
    29/128,  -- 数字7：29像素半宽度
    31/128,  -- 数字8：31像素半宽度
    31/128   -- 数字9：31像素半宽度
}

-- 实际hw值计算（digits[i].width = 128）
-- hw值 = (numberHalfWidths[digit+1] * 128)
-- 例如：数字5的hw值 = (32/128) * 128 = 32.0像素
```

### 位置坐标计算原理详解

#### 1. 居中对齐算法
```lua
-- 第一个数字的位置计算
-- 目标：让整个数字组合在屏幕中央对齐
digits[1] x坐标 = numberOffset - digits[1].hw

-- 原理解析：
-- numberOffset是所有数字半宽度的总和，代表整个数字组合的总宽度
-- digits[1].hw是第一个数字的半宽度
-- 通过 numberOffset - digits[1].hw 计算，使第一个数字向右偏移
-- 这样整个数字组合就能以中心点对齐
```

#### 2. 相对定位算法
```lua
-- 后续数字的相对位置计算
digits[i] x坐标 = -(digits[i].hw + digits[i-1].hw)

-- 原理解析：
-- digits[i].hw：当前数字的半宽度
-- digits[i-1].hw：前一个数字的半宽度
-- 负号表示向左偏移（从右到左排列）
-- 两个半宽度相加确保数字之间无重叠且紧密排列
```

#### 3. 数字排列顺序
- **处理顺序**：从个位到十位（从右到左处理）
- **显示顺序**：从左到右显示（符合阅读习惯）
- **定位方式**：第一个数字绝对定位，后续数字相对定位

#### 4. 实际坐标计算示例

**单位数（如：5秒）**
```
numberOffset = 32.0
第1位数字5：x = 32.0 - 32.0 = 0.0（居中显示）
```

**两位数（如：15秒）**
```
numberOffset = 32.0 + 14.0 = 46.0
第1位数字5：x = 46.0 - 32.0 = 14.0（向右偏移）
第2位数字1：x = -(14.0 + 32.0) = -46.0（相对于数字5向左）

最终显示：[1][5] 整体居中对齐
```

### 纹理坐标映射规律总结

1. **单位数（1-9）**：只使用第一个数字纹理对象，居中显示
2. **十位数（10-19）**：使用两个数字纹理对象，通过相对定位实现从右到左显示
3. **数字提取顺序**：从个位开始，逐位向高位提取
4. **坐标计算规律**：
   - **纹理坐标**：列位置 = 数字 % 4，行位置 = floor(数字 / 4)
   - **屏幕坐标**：基于numberOffset和hw值实现精确居中对齐

### 性能优化特点

1. **纹理复用**：所有数字共享同一张纹理，减少GPU状态切换
2. **精确定位**：使用numberHalfWidths数组实现像素级精确定位
3. **动态隐藏**：未使用的数字纹理设置为(0,0,0,0)坐标隐藏
4. **音效同步**：每次数字更新时播放倒计时音效

---

*本文档基于 `timer.lua` 文件深度分析生成，详细解析了StartTimer_SetTexNumbers函数的实现原理和数据计算过程。*
