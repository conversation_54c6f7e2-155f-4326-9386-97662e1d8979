<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"  xsi:schemaLocation="http://www.blizzard.com/wow/ui/ G:\Legacy\Client\Interface\FrameXML\UI.xsd">
    <!--StaticFrame-->
    <Frame name="GhostStaticFrameTemplate" parent="UIParent" virtual="true">
    	<Size x="300" y="500"/>
        <Backdrop bgFile="Interface\TutorialFrame\TutorialFrameBackground"
           edgeFile="Interface\DialogFrame\UI-DialogBox-Border" tile="true">
    	    <EdgeSize val="16"/>
    	    <TileSize val="32"/>
     	    <BackgroundInsets left="5" right="5" top="5" bottom="5"/>
            <Color a="1" r="1" g="1" b="1" />
        </Backdrop>
    	<Frames>
    		<!-- logo-->
    		<Button name="$parentLogoButton">
    			<Size x="192" y="96"/>
    			<Anchors>
    				<Anchor point="TOP">
    					<Offset x="0" y="30" />
    				</Anchor>
    			</Anchors>
    			<Layers>
    				<Layer level="OVERLAY">	
    					<FontString name = "$parentFontString" setAllPoints="true" justifyV="MIDDLE" justifyH="CENTER" inherits="ErrorFont" text="">
    						<Anchors>
    							<Anchor point="TOP">
    								<Offset x="0" y="-140" />
    							</Anchor>
    						</Anchors>
    					</FontString>
    				</Layer>
    			</Layers>
    			<Scripts>
    				<OnLoad>
    					self:SetNormalTexture("Interface\\TUTORIALFRAME\\UI-TutorialFrame-Logo");
    				</OnLoad>
    			</Scripts>
    		</Button>
    	</Frames>
    </Frame>
    <!--Basic-->
    <Button name="GhostBasicButtonTemplate" virtual="true">
        <Size>
            <AbsDimension x="100" y="100"/>
        </Size>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentIcon" file="Interface\ICONS\INV_MISC_QUESTIONMARK">
                    <Size x="60" y="60"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0.1" right="0.9" top="0.1" bottom="0.9"/>
                </Texture>
            </Layer>
            <Layer level="BORDER">
                <Texture name="$parentBorder" file="Interface\Addons\GhostPanel\Asset\Image\AbilityBorderHex">
                    <Size x="100" y="100"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <Texture name="$parentHighlight" file="Interface\Addons\GhostPanel\Asset\Image\AbilityBorderHexHighlight" hidden="true">
                    <Size x="100" y="100"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                    <Color r="1" g="1" b="0" a="0.5"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <FontString name="$parentTitle" inherits="GameFontNormal" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentDesc" inherits="GameFontHighlightSmall" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="-10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentTText" inherits="GameFontNormal" text="LEGACY_TEMP" hidden = "true">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentMText" inherits="GameFontNormal" text="LEGACY_TEMP" hidden = "true">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentBText" inherits="GameFontHighlightSmall" text="LEGACY_TEMP" hidden = "true">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="-10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
            </Layer>
        </Layers>
    </Button>
    <Button name="GhostBasicPopButtonTemplate" virtual="true">
        <Size>
            <AbsDimension x="50" y="50"/>
        </Size>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentIcon" file="Interface\ICONS\INV_MISC_QUESTIONMARK">
                    <Size x="30" y="30"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0.1" right="0.9" top="0.1" bottom="0.9"/>
                </Texture>
            </Layer>
            <Layer level="BORDER">
                <Texture name="$parentBorder" file="Interface\Addons\GhostPanel\Asset\Image\AbilityBorderHex">
                    <Size x="50" y="50"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <Texture name="$parentHighlight" file="Interface\Addons\GhostPanel\Asset\Image\AbilityBorderHexHighlight" hidden="true">
                    <Size x="50" y="50"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                    <Color r="1" g="1" b="0" a="0.5"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <FontString name="$parentTitle" inherits="GameFontNormal" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentDesc" inherits="GameFontHighlightSmall" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="-10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentMText" inherits="GameFontNormal" text="LEGACY_TEMP" hidden = "true">
                    <Size x="256" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="120" y="0"/>
                        </Anchor>
                    </Anchors>
                </FontString>
            </Layer>
        </Layers>
    </Button>
    <Button name="GhostBasicPopConfirmCancelButtonTemplate" virtual="true">
        <Size>
            <AbsDimension x="50" y="50"/>
        </Size>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentIcon" file="Interface\ICONS\INV_MISC_QUESTIONMARK">
                    <Size x="30" y="30"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0.1" right="0.9" top="0.1" bottom="0.9"/>
                </Texture>
            </Layer>
            <Layer level="BORDER">
                <Texture name="$parentBorder" file="Interface\Addons\GhostPanel\Asset\Image\AbilityBorderHex" hidden="true">
                    <Size x="50" y="50"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <Texture name="$parentHighlight" file="Interface\BUTTONS\CheckButtonGlow" hidden="true">
                    <Size x="50" y="50"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                    <TexCoords left="0" right="1" top="0" bottom="1"/>
                    <Color r="1" g="1" b="0" a="0.5"/>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
                <FontString name="$parentTitle" inherits="GameFontNormal" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentDesc" inherits="GameFontHighlightSmall" text="LEGACY_TEMP">
                    <Size x="64" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="0" y="-10"/>
                        </Anchor>
                    </Anchors>
                </FontString>
                <FontString name="$parentMText" inherits="GameFontNormal" text="LEGACY_TEMP" hidden = "true">
                    <Size x="256" y="32"/>
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parent" relativePoint="CENTER">
                            <Offset x="120" y="0"/>
                        </Anchor>
                    </Anchors>
                </FontString>
            </Layer>
        </Layers>
    </Button>
    <!--HomeNav Arrow Confirm/Cancel--> 
    <Button name="GhostUpArrowTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["ARROW_UP"]);
            </OnLoad>
        </Scripts>
    </Button>
    <Button name="GhostDownArrowTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["ARROW_DOWN"]);
            </OnLoad>
        </Scripts>
    </Button>
    <Button name="GhostLeftArrowTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["ARROW_LEFT"]);
            </OnLoad>
        </Scripts>
    </Button>
    <Button name="GhostRightArrowTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["ARROW_RIGHT"]);
            </OnLoad>
        </Scripts>
    </Button>
    <Button name="GhostConfirmTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
         <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["CONFIRM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostCancelTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
         <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["CANCEL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>  
    <!--HomeNav-->
    <Button name="GhostHomeNavTokenTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_HomeNav_OnLoad(self);
            </OnLoad>
            <OnEnter>
                GhostPanel_HomeNav_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_HomeNav_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_HomeNav_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--Rune-->
    <Button name="GhostRuneNavTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>      
            </OnLoad>
			<OnShow>
				GhostPanel_RuneNav_OnShow(self);
			</OnShow>
            <OnEnter>
                GhostPanel_RuneNav_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_RuneNav_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_RuneNav_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRuneButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["RUNE"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--TransMog-->
    <Button name="GhostTransMogNavTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_TransMogNav_OnLoad(self);      
            </OnLoad>
            <OnEnter>
                GhostPanel_TransMogNav_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_TransMogNav_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_TransMogNav_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostTransMogButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["TRANSMOG"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--BlackMarket-->
    <Button name="GhostBlackMarketButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["BLACKMARKET"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>

    <!--Talent-->
    <Button name="GhostTalentButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["TALENT"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>

    <!--LuckDraw-->
    <Button name="GhostLuckDrawButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["LUCKDRAW"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
        </Scripts>
    </Button>
    <Button name="GhostLuckDrawRewButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["LUCKDRAW_REW"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
        </Scripts>
    </Button>
    <Button name="GhostLuckDrawActionButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["LUCKDRAW_ACTION"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--VIP-->
    <Button name="GhostVIPButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["VIP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--HR-->
    <Button name="GhostHRButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["HR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>

    <!--ReqPanel-->
    <Button name="GhostReqItemButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_ITEM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqLevelButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_LEVEL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqVIPButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_VIP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqHRButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_HR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqFactionButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_FACTION"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqRankButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_RANK"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqReincarnationButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_REINCARNATION"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqAchievementPointsButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_ACHIEVEMENTPOINTS"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqGoldButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_GOLD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqTokenButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_TOKEN"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqXPButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_XP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqHonorButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_HONOR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqArenaButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_ARENA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqSpiritPowerButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_SPIRITPOWER"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqMapDataButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_MAP_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqQuestDataButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_QUEST_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqAchievementDataButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_ACHIEVEMENT_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqSpellDataButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_SPELL_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostReqCMDButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REQ_CMD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!-- RewPanel-->
    <Button name="GhostRewGoldButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_GOLD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewTokenButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_TOKEN"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewXPButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_XP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewHonorButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_HONOR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewArenaButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_ARENA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewStatPointButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_STATPOINT"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewItemButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_ITEM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewSpellButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_SPELL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewAuraButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_AURA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostRewCMDButtonTemplate" inherits="GhostBasicButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["REW_CMD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--ReqRewPop Req-->
    <Button name="GhostPopReqItemButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_ITEM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqLevelButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_LEVEL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqVIPButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_VIP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqHRButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_HR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqFactionButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_FACTION"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqRankButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_RANK"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqReincarnationButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_REINCARNATION"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqAchievementPointsButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_ACHIEVEMENTPOINTS"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqGoldButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_GOLD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqTokenButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_TOKEN"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqXPButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_XP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqHonorButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_HONOR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqArenaButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_ARENA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqSpiritPowerButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_SPIRITPOWER"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqMapDataButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_MAP_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqQuestDataButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_QUEST_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqAchievementDataButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_ACHIEVEMENT_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqSpellDataButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_SPELL_DATA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopReqCMDButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REQ_CMD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewGoldButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_GOLD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--ReqRewPop Rew-->
    <Button name="GhostPopRewTokenButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_TOKEN"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewXPButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_XP"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewStatPointButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_STATPOINT"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewHonorButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_HONOR"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewArenaButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_ARENA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewAuraButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_AURA"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewSpellButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_SPELL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewItemButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_ITEM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopRewCMDButtonTemplate" inherits="GhostBasicPopButtonTemplate" virtual="true">
        <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_REW_CMD"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <!--ReqRewPop confirm cancel-->
    <Button name="GhostPopConfirmButtonTemplate" inherits="GhostBasicPopConfirmCancelButtonTemplate" virtual="true">
         <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_CONFIRM"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>
    <Button name="GhostPopCancelButtonTemplate" inherits="GhostBasicPopConfirmCancelButtonTemplate" virtual="true">
         <Scripts>
            <OnLoad>
                GhostPanel_Button_OnLoad(self, GHOST_BUTTON_TYPE["POP_CANCEL"]);
            </OnLoad>
            <OnEnter>
                GhostPanel_Button_OnEnter(self);
            </OnEnter>
            <OnLeave>
                GhostPanel_Button_OnLeave(self);
            </OnLeave>
            <OnClick>
                GhostPanel_Button_OnClick(self, button, down);
            </OnClick>
        </Scripts>
    </Button>

<!--
    <Frame name="GhostLootFrameTemplate" parent="UIParent" frameStrata="BACKGROUND">
	    <Size>
            <AbsDimension x="128" y="18"/>
        </Size>
        <Anchors>
            <Anchor point="CENTER" relativePoint="CENTER" relativeTo="$parent">
                <Offset>
                    <RelDimension x="0" y="0"/>
                </Offset>
            </Anchor>
        </Anchors>
        <Layers>
            <Layer level="BACKGROUND">
                <Texture name="$parentIcon" file="Interface\ICONS\Ability_Mount_CelestialHorse">
                    <Size x="18" y="18"/>
                    <Anchors>
                        <Anchor point="CENTER">
                            <Offset x="0" y="0"/>
                        </Anchor>
                    </Anchors>
                </Texture>
            </Layer>
            <Layer level="OVERLAY">
				<FontString name="$parentTitle" inherits="GameFontNormal" text = "测试">
                    <Anchors>
                        <Anchor point="CENTER" relativeTo="$parentIcon" relativePoint="CENTER">
                            <Offset x="18" y="0"/>
                        </Anchor>
                    </Anchors>
				</FontString>
			</Layer>
        </Layers>
    </Frame>
    -->
</Ui>