/*
SQLyog Ultimate
MySQL - 5.7.35-log 
*********************************************************************
*/
/*!40101 SET NAMES utf8 */;

create table `store_boxes_buttons` (
	`Box_Name` varchar (765),
	`Box_Coord_Y` float ,
	`Box_Coord_X` float ,
	`InsideBox_Name` text ,
	`Tooltip_Name` text ,
	`Tooltip_Type` varchar (765),
	`Tooltip_Text` text ,
	`ParentFrame` varchar (765),
	`ICON` varchar (765),
	`Service_Price` float ,
	`Service_Function` varchar (765),
	`HyperLink_ID` float ,
	`Service_ID` int (11),
	`Model_Type` varchar (300),
	`Creature_DisplayID` float ,
	`Currency_Type` varchar (300),
	`Discount_Price` float ,
	`Discount_Percent` float ,
	`Discount_Color` varchar (300),
	`Flags` float ,
	`Confirmation_ID` float 
); 
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Boost','0','0','   Level 10\r\nCharacter Boost','Character Boost','','','SERVICES_FRAME','level50','40','Boost','0','1','0','0','VT','0','0','|cff00ffff','0','1');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Faction','0','1','Faction Change','Faction Change','','','SERVICES_FRAME','VAS_FactionChange','5','FactionChange','0','2','0','0','DT','0','0','|cff00ffff','0','2');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Race','0','2','Race Change','Race Change','','','SERVICES_FRAME','VAS_RaceChange','10','RaceChange','0','3','0','0','DT','0','0','|cff00ffff','0','3');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Name','0','3','Name Change','Name Change','','','SERVICES_FRAME','VAS_NameChange','5','ServiceRename','0','4','0','0','DT','0','0','|cff00ffff','0','4');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Shadowmourne','0','0','Shadowmourne\r\n(Two-Hand Axe)','','item','','AXE_TWOHAND_FRAME','INV_Axe_113','10','ItemsFunction','49623','5','1','0','DT','0','0','|cff00ffff','1','5');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('TusksOfMannoroth','0','0','Tusks of Mannoroth\r\n(Shoulder)','','item','','TRANSMOG_FRAME','inv_shoulder_plate_garrosh_d_01','30','ItemsFunction','103785','6','1','0','DT','40','25','|cff00ffff','1','6');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('ScytheoftheUnmaker','0','1','Scythe of the Unmaker\r\n(Polearm)','','item','','TRANSMOG_FRAME','inv_polearm_2h_titanargus_d_01','50','ItemsFunction','153115','0','1','0','DT','100','50','|cff00ffff','1','7');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Invincible','0','0','Invincible\'s Reins\r\n(Mount)','','spell','','MOUNTS_FRAME','ability_mount_pegasus','30','Mounts','72286','7','1','38545','DT','0','0','|cff00ffff','3','8');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('Bolvar\'s Mace','0','2','Bolvar\'s Mace\r\n(Mace Two Hand)','Bolvar\'s Mace','item','','TRANSMOG_FRAME','achievement_dungeon_icecrown_frostmourne','30','ItemsFunction','177838','8','1','0','DT','0','0','|cff00ffff','1','9');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('WarglaivesOfAzzinoth','0','3','Warglaives of Azzinoth\r\n(Warglaives)','Arsenal: The Warglaives of Azzinoth','','Collect the appearances of the \r\nWarglaives of Azzinoth.','TRANSMOG_FRAME','Warglaives','150','SetsFunction','32838','9','1','0','DT','165','10','|cff00ffff','2','10');
insert into `store_boxes_buttons` (`Box_Name`, `Box_Coord_Y`, `Box_Coord_X`, `InsideBox_Name`, `Tooltip_Name`, `Tooltip_Type`, `Tooltip_Text`, `ParentFrame`, `ICON`, `Service_Price`, `Service_Function`, `HyperLink_ID`, `Service_ID`, `Model_Type`, `Creature_DisplayID`, `Currency_Type`, `Discount_Price`, `Discount_Percent`, `Discount_Color`, `Flags`, `Confirmation_ID`) values('VipSilver','1','0','Vip Silver\r\n(30 Days)','VIP: Silver','','Testing Line 1 |TInterface/Icons/ability_racial_avatar:25:25:0:0|t\r\nTesting Line 2','VIP_FRAME','vipsilver_el','100','VipSilver','0','11','1','0','DT','90','10','|cff00ffff','2','12');
