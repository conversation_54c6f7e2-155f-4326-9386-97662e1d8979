<Ui xmlns="http://www.blizzard.com/wow/ui/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.blizzard.com/wow/ui/
..\FrameXML\UI.xsd">
	<Script file="MerchantFrame.lua"/>
	<Frame name="MerchantItemTemplate" virtual="true">	
		<Size>
			<AbsDimension x="153" y="44"/>
		</Size>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="$parentSlotTexture" file="Interface\Buttons\UI-EmptySlot">
					<Size>
						<AbsDimension x="64" y="64"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="-13" y="13"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<Texture name="$parentNameFrame" file="Interface\MerchantFrame\UI-Merchant-LabelSlots">
					<Size>
						<AbsDimension x="128" y="78"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-9" y="-18"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<FontString name="$parentName" inherits="GameFontNormalSmall" text="Item Name" justifyH="LEFT">
					<Size>
						<AbsDimension x="100" y="30"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT">
							<Offset>
								<AbsDimension x="-5" y="7"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
			</Layer>
		</Layers>
		<Frames>
			<Button name="$parentItemButton" inherits="ItemButtonTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="$parent"/>
				</Anchors>
				<Scripts>
					<OnClick>
						if ( IsModifiedClick() ) then
							MerchantItemButton_OnModifiedClick(self, button);
						else
							MerchantItemButton_OnClick(self, button);
						end
					</OnClick>
					<OnLoad>
						MerchantItemButton_OnLoad(self);
					</OnLoad>
					<OnDragStart>
						MerchantItemButton_OnClick(self, "LeftButton");
					</OnDragStart>
					<OnEnter>
						MerchantItemButton_OnEnter(self, motion);
					</OnEnter>
					<OnLeave>
						GameTooltip:Hide();
						ResetCursor();
						MerchantFrame.itemHover = nil;
					</OnLeave>
					<OnHide>
						if ( self.hasStackSplit == 1 ) then
							StackSplitFrame:Hide();
						end
					</OnHide>
				</Scripts>
			</Button>
			<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="2" y="31"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Scripts>
					<OnLoad>
						SmallMoneyFrame_OnLoad(self);
						MoneyFrame_SetType(self, "STATIC");
					</OnLoad>
				</Scripts>
			</Frame>
			<Frame name="$parentAltCurrencyFrame" inherits="SmallAlterateCurrencyFrameTemplate" hidden="true">
				<Anchors>
					<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="2" y="31"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
		</Frames>
	</Frame>
	<Frame name="MerchantFrame" toplevel="true" parent="UIParent" movable="true" enableMouse="true" hidden="true">
		<Size>
			<AbsDimension x="384" y="512"/>
		</Size>
		<Anchors>
			<Anchor point="TOPLEFT">
				<Offset>
					<AbsDimension x="0" y="-104"/>
				</Offset>
			</Anchor>
		</Anchors>
		<HitRectInsets>
			<AbsInset left="0" right="35" top="0" bottom="61"/>
		</HitRectInsets>
		<Layers>
			<Layer level="BACKGROUND">
				<Texture name="MerchantFramePortrait">
                    <Size>
                        <AbsDimension x="60" y="60"/>
                    </Size>
                    <Anchors>
                        <Anchor point="TOPLEFT">
                            <Offset>
                                <AbsDimension x="7" y="-6"/>
                            </Offset>
                        </Anchor>
                    </Anchors>
                </Texture>
            </Layer>
			<Layer level="BORDER">
				<Texture file="Interface\MerchantFrame\UI-Merchant-TopLeft">
					<Size>
						<AbsDimension x="256" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT"/>
					</Anchors>
				</Texture>		
				<Texture file="Interface\MerchantFrame\UI-Merchant-TopRight">
					<Size>
						<AbsDimension x="128" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\MerchantFrame\UI-Merchant-BotLeft">
					<Size>
						<AbsDimension x="256" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture file="Interface\MerchantFrame\UI-Merchant-BotRight">
					<Size>
						<AbsDimension x="128" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
				 <FontString name="MerchantNameText" inherits="GameFontNormal" text="Merchant Name">
					<Anchors>
						<Anchor point="TOP" relativeTo="MerchantFrame" relativePoint="TOP">
							<Offset>
								<AbsDimension x="0" y="-17"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="MerchantPageText" inherits="GameFontNormal" text="Page">
					<Size>
						<AbsDimension x="104" y="0"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOM">
							<Offset>
								<AbsDimension x="-14" y="150"/>
							</Offset>
						</Anchor>
					</Anchors>
				</FontString>
				<FontString name="MerchantRepairText" inherits="GameFontHighlightSmall" text="REPAIR_ITEMS">
 					<Anchors>
 						<Anchor point="BOTTOMLEFT">
 							<Offset>
 								<AbsDimension x="26" y="103"/>
 							</Offset>
 						</Anchor>
 					</Anchors>
 				</FontString>
			</Layer>
			<Layer level="ARTWORK">
				<Texture name="BuybackFrameTopLeft" file="Interface\MerchantFrame\UI-BuyBack-TopLeft">
					<Size>
						<AbsDimension x="256" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT">
							<Offset>
								<AbsDimension x="19" y="-71"/>
							</Offset>
						</Anchor>
					</Anchors>
				</Texture>
				<Texture name="BuybackFrameTopRight" file="Interface\MerchantFrame\UI-BuyBack-TopRight">
					<Size>
						<AbsDimension x="64" y="256"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="BuybackFrameTopLeft" relativePoint="TOPRIGHT"/>
					</Anchors>
				</Texture>
				<Texture name="BuybackFrameBotLeft" file="Interface\MerchantFrame\UI-BuyBack-BotLeft">
					<Size>
						<AbsDimension x="256" y="128"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="BuybackFrameTopLeft" relativePoint="BOTTOMLEFT"/>
					</Anchors>
				</Texture>
				<Texture name="BuybackFrameBotRight" file="Interface\MerchantFrame\UI-BuyBack-BotRight">
					<Size>
						<AbsDimension x="64" y="128"/>
					</Size>
					<Anchors>
						<Anchor point="TOPLEFT" relativeTo="BuybackFrameTopLeft" relativePoint="BOTTOMRIGHT"/>
					</Anchors>
				</Texture>
			</Layer>
			<Layer level="OVERLAY">
				<Texture name="MerchantFrameBottomLeftBorder" file="Interface\MerchantFrame\UI-Merchant-BottomBorder">
					<Size>
						<AbsDimension x="256" y="61"/>
					</Size>
					<Anchors>
						<Anchor point="BOTTOMLEFT">
							<Offset>
								<AbsDimension x="14" y="85"/>
							</Offset>
						</Anchor>
					</Anchors>
					<TexCoords left="0" right="1" top="0" bottom="0.4765625"/>
				</Texture>
				<Texture name="MerchantFrameBottomRightBorder" file="Interface\MerchantFrame\UI-Merchant-BottomBorder">
					<Size>
						<AbsDimension x="76" y="61"/>
					</Size>
					<Anchors>
						<Anchor point="LEFT" relativeTo="MerchantFrameBottomLeftBorder" relativePoint="RIGHT"/>
					</Anchors>
					<TexCoords left="0" right="0.296875" top="0.4765625" bottom="0.953125"/>
				</Texture>
			</Layer>
		</Layers>
		<Frames>
			<Frame name="MerchantItem1" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT">
						<Offset>
							<AbsDimension x="24" y="-80"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem2" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem1" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem3" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem1" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem4" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem3" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem5" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem3" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem6" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem5" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem7" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem5" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem8" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem7" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem9" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem7" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem10" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem9" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem11" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem9" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-15"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Frame name="MerchantItem12" inherits="MerchantItemTemplate">
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem11" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="12" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Button name="MerchantRepairAllButton">
				<Size>
					<AbsDimension x="36" y="36"/>
				</Size>
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="172" y="91"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="MerchantRepairAllIcon" file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0.28125" right="0.5625" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local repairAllCost, canRepair = GetRepairAllCost();
						if ( canRepair and (repairAllCost > 0) ) then
							GameTooltip:SetText(REPAIR_ALL_ITEMS);
							SetTooltipMoney(GameTooltip, repairAllCost);
						end
						GameTooltip:Show();
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						RepairAllItems();
						PlaySound("ITEM_REPAIR");
						GameTooltip:Hide();
					</OnClick>
					<OnEvent>
						local _, canRepair = GetRepairAllCost();
						if ( not canRepair ) then
							SetDesaturation(MerchantRepairAllIcon, 1);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, 1);
							MerchantGuildBankRepairButton:Disable();
							self:Disable();
						else
							SetDesaturation(MerchantRepairAllIcon, nil);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, nil);
							self:Enable();
							MerchantGuildBankRepairButton:Enable();
						end
					</OnEvent>
					<OnLoad>
						self:RegisterEvent("UPDATE_INVENTORY_DURABILITY");
					</OnLoad>
				</Scripts>
				<NormalTexture file=""/>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantRepairItemButton">
				<Size>
					<AbsDimension x="36" y="36"/>
				</Size>
				<Anchors>
					<Anchor point="RIGHT" relativeTo="MerchantRepairAllButton" relativePoint="LEFT">
						<Offset>
							<AbsDimension x="-2" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0" right="0.28125" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText(REPAIR_AN_ITEM);
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						if ( InRepairMode() ) then
							MerchantFrame:UnregisterEvent("PLAYER_MONEY");
							HideRepairCursor();
						else
							MerchantFrame:RegisterEvent("PLAYER_MONEY");
							ShowRepairCursor();
						end
					</OnClick>
				</Scripts>
				<NormalTexture file=""/>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantGuildBankRepairButton" hidden="true">
				<Size>
					<AbsDimension x="32" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="LEFT" relativeTo="MerchantRepairAllButton" relativePoint="RIGHT">
						<Offset>
							<AbsDimension x="4" y="0"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BORDER">
						<Texture name="MerchantGuildBankRepairButtonIcon" file="Interface\MerchantFrame\UI-Merchant-RepairIcons">
							<TexCoords left="0.5625" right="0.84375" top="0" bottom="0.5625"/>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						local repairAllCost, canRepair = GetRepairAllCost();
						if ( canRepair and (repairAllCost > 0) ) then
							GameTooltip:SetText(REPAIR_ALL_ITEMS);
							SetTooltipMoney(GameTooltip, repairAllCost);
							local amount = GetGuildBankWithdrawMoney();
							local guildBankMoney = GetGuildBankMoney();
							if ( amount == -1 ) then
								-- Guild leader shows full guild bank amount
								amount = guildBankMoney;
							else
								amount = min(amount, guildBankMoney);
							end
							GameTooltip:AddLine(GUILDBANK_REPAIR, nil, nil, nil, 1);
							SetTooltipMoney(GameTooltip, amount, "GUILD_REPAIR");
							GameTooltip:Show();
						end
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						--FIXME!!! Need actual amount of guild money left to withdraw
						if(CanGuildBankRepair()) then
							RepairAllItems(1);
							PlaySound("ITEM_REPAIR");
						end
						GameTooltip:Hide();
					</OnClick>
					<OnEvent>
						local _, canRepair = GetRepairAllCost();
						if ( not canRepair ) then
							SetDesaturation(MerchantRepairAllIcon, 1);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, 1);
							MerchantGuildBankRepairButton:Disable();
							MerchantRepairAllButton:Disable();
						else
							SetDesaturation(MerchantRepairAllIcon, nil);
							SetDesaturation(MerchantGuildBankRepairButtonIcon, nil);
							self:Enable();
							MerchantRepairAllButton:Enable();
						end
					</OnEvent>
					<OnLoad>
						self:RegisterEvent("UPDATE_INVENTORY_DURABILITY");
					</OnLoad>
				</Scripts>
				<NormalTexture file=""/>
				<PushedTexture file="Interface\Buttons\UI-Quickslot-Depress"/>
				<HighlightTexture file="Interface\Buttons\ButtonHilight-Square" alphaMode="ADD"/>
			</Button>
			<Frame name="MerchantBuyBackItem">	
				<Size>
					<AbsDimension x="153" y="37"/>
				</Size>
				<Anchors>
					<Anchor point="TOPLEFT" relativeTo="MerchantItem10" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="0" y="-53"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<Texture name="$parentSlotTexture" file="Interface\Buttons\UI-EmptySlot">
							<Size>
								<AbsDimension x="64" y="64"/>
							</Size>
							<Anchors>
								<Anchor point="TOPLEFT">
									<Offset>
										<AbsDimension x="-13" y="13"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
						<Texture name="$parentNameFrame" file="Interface\MerchantFrame\UI-Merchant-LabelSlots">
							<Size>
								<AbsDimension x="128" y="64"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT">
									<Offset>
										<AbsDimension x="-9" y="-10"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
						<FontString name="$parentName" inherits="GameFontNormalSmall" text="Item Name" justifyH="LEFT">
							<Size>
								<AbsDimension x="90" y="30"/>
							</Size>
							<Anchors>
								<Anchor point="LEFT" relativeTo="$parentSlotTexture" relativePoint="RIGHT">
									<Offset>
										<AbsDimension x="-5" y="10"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
					</Layer>
				</Layers>
				<Frames>
					<Button name="$parentItemButton" inherits="ItemButtonTemplate">
						<Anchors>
							<Anchor point="TOPLEFT" relativeTo="$parent"/>
						</Anchors>
						<Scripts>
							<OnLoad function="MerchantItemBuybackButton_OnLoad"/>
							<OnEvent>
								if ( event == "MERCHANT_UPDATE" ) then
									if ( GameTooltip:IsOwned(self) ) then
										GameTooltip:SetBuybackItem(GetNumBuybackItems());
										GameTooltip:Show();
									end
								end
							</OnEvent>
							<OnClick>
								BuybackItem(GetNumBuybackItems());
							</OnClick>
							<OnEnter>
								GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
								GameTooltip:SetBuybackItem(GetNumBuybackItems());
								ShowMerchantSellCursor(self:GetID());
							</OnEnter>
							<OnLeave function="GameTooltip_HideResetCursor"/>
						</Scripts>
					</Button>
					<Frame name="$parentMoneyFrame" inherits="SmallMoneyFrameTemplate">
						<Anchors>
							<Anchor point="BOTTOMLEFT" relativeTo="$parentNameFrame" relativePoint="BOTTOMLEFT">
								<Offset>
									<AbsDimension x="0" y="25"/>
								</Offset>
							</Anchor>
						</Anchors>
						<Scripts>
							<OnLoad>
								SmallMoneyFrame_OnLoad(self);
								MoneyFrame_SetType(self, "STATIC");
							</OnLoad>
						</Scripts>
					</Frame>
				</Frames>
			</Frame>
			<Frame name="MerchantMoneyFrame" inherits="SmallMoneyFrameTemplate">
				<Anchors>
					<Anchor point="BOTTOMRIGHT" relativeTo="MerchantFrame" relativePoint="BOTTOMRIGHT">
						<Offset>
							<AbsDimension x="-40" y="67"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Frame>
			<Button name="MerchantPrevPageButton">
				<Size>
					<AbsDimension x="32" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" relativeTo="MerchantFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="37" y="156"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormal" justifyH="LEFT" text="PREV">
							<Anchors>
								<Anchor point="LEFT" relativeTo="MerchantPrevPageButton" relativePoint="RIGHT"/>
							</Anchors>
						</FontString>
						<Texture file="Interface\Buttons\UI-PageButton-Background">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="0" y="1"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick function="MerchantPrevPageButton_OnClick"/>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-PrevPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantNextPageButton">
				<Size>
					<AbsDimension x="32" y="32"/>
				</Size>
				<Anchors>
					<Anchor point="CENTER" relativeTo="MerchantFrame" relativePoint="BOTTOMLEFT">
						<Offset>
							<AbsDimension x="324" y="156"/>
						</Offset>
					</Anchor>
				</Anchors>
				<Layers>
					<Layer level="BACKGROUND">
						<FontString inherits="GameFontNormal" justifyH="RIGHT" text="NEXT">
							<Anchors>
								<Anchor point="RIGHT" relativeTo="MerchantNextPageButton" relativePoint="LEFT">
									<Offset>
										<AbsDimension x="-3" y="0"/>
									</Offset>
								</Anchor>
							</Anchors>
						</FontString>
						<Texture file="Interface\Buttons\UI-PageButton-Background">
							<Size>
								<AbsDimension x="32" y="32"/>
							</Size>
							<Anchors>
								<Anchor point="CENTER">
									<Offset>
										<AbsDimension x="0" y="1"/>
									</Offset>
								</Anchor>
							</Anchors>
						</Texture>
					</Layer>
				</Layers>
				<Scripts>
					<OnClick function="MerchantNextPageButton_OnClick"/>
				</Scripts>
				<NormalTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Up"/>
				<PushedTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Down"/>
				<DisabledTexture file="Interface\Buttons\UI-SpellbookIcon-NextPage-Disabled"/>
				<HighlightTexture file="Interface\Buttons\UI-Common-MouseHilight" alphaMode="ADD"/>
			</Button>
			<Button name="MerchantFrameCloseButton" inherits="UIPanelCloseButton">
				<Anchors>
					<Anchor point="TOPRIGHT" relativeTo="MerchantFrame" relativePoint="TOPRIGHT">
						<Offset>
							<AbsDimension x="-30" y="-8"/>
						</Offset>
					</Anchor>
				</Anchors>
			</Button>
			<Button name="MerchantFrameTab1" inherits="CharacterFrameTabButtonTemplate" id="1" text="MERCHANT">
                <Anchors>
                    <Anchor point="CENTER" relativePoint="BOTTOMLEFT">
                        <Offset>
                            <AbsDimension x="60" y="46"/>
                        </Offset>
                    </Anchor>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText("", 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						PanelTemplates_SetTab(MerchantFrame, self:GetID());
						MerchantFrame_Update();
					</OnClick>
                </Scripts>
            </Button>
            <Button name="MerchantFrameTab2" inherits="CharacterFrameTabButtonTemplate" id="2" text="BUYBACK">
                <Anchors>
                   <Anchor point="LEFT" relativeTo="MerchantFrameTab1" relativePoint="RIGHT">
                        <Offset>
                            <AbsDimension x="-16" y="0"/>
                        </Offset>
                    </Anchor>
                </Anchors>
                <Scripts>
					<OnEnter>
						GameTooltip:SetOwner(self, "ANCHOR_RIGHT");
						GameTooltip:SetText("", 1.0,1.0,1.0 );
					</OnEnter>
					<OnLeave function="GameTooltip_Hide"/>
					<OnClick>
						PanelTemplates_SetTab(MerchantFrame, self:GetID());
						MerchantFrame_Update();
					</OnClick>
                </Scripts>
            </Button>
		</Frames>
		<Scripts>
			<OnHide function="MerchantFrame_OnHide"/>
			<OnLoad function="MerchantFrame_OnLoad"/>
			<OnShow function="MerchantFrame_OnShow"/>
			<OnEvent function="MerchantFrame_OnEvent"/>
			<OnUpdate>
				if ( MerchantFrame.itemHover ) then
					if ( IsModifiedClick("DRESSUP") ) then
						ShowInspectCursor();
					else
						ShowMerchantSellCursor(MerchantFrame.itemHover);
					end
				end
				if ( MerchantRepairItemButton:IsShown() ) then
					if ( InRepairMode() ) then
						MerchantRepairItemButton:LockHighlight();
					else
						MerchantRepairItemButton:UnlockHighlight();
					end
				end
			</OnUpdate>
			<OnMouseUp>
				if ( MerchantFrame.refundItem ) then
					if ( ContainerFrame_GetExtendedPriceString(MerchantFrame.refundItem, MerchantFrame.refundItemEquipped)) then
						-- a confirmation dialog has been shown
						return;
					end
				end
				PickupMerchantItem(0);
			</OnMouseUp>
			<OnReceiveDrag>
				MerchantItemButton_OnClick(self, "LeftButton");
			</OnReceiveDrag>
		</Scripts>
	</Frame>
</Ui>
