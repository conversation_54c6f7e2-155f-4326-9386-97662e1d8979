﻿#include "Chat.h"
#include "ScriptMgr.h"
#include "AccountMgr.h"
#include "ArenaTeamMgr.h"
#include "CellImpl.h"
#include "GridNotifiers.h"
#include "Group.h"
#include "GuildMgr.h"
#include "InstanceSaveMgr.h"
#include "Language.h"
#include "MovementGenerator.h"
#include "ObjectAccessor.h"
#include "Opcodes.h"
#include "SpellAuras.h"
#include "TargetedMovementGenerator.h"
#include "WeatherMgr.h"
#include "ace/INET_Addr.h"
#include "Player.h"
#include "Pet.h"
#include "LFG.h"
#include "GroupMgr.h"
#include "BattlegroundMgr.h"
#include "MapManager.h"
#include <iostream>

using namespace std;


#define MAX_MALL_SELL_REQITEM_VAL 5


struct MallData
{
    //uint32 SerialNumber;
    uint32 ItemId;                  //物品ID
    string P_Class;                 //父类
    string S_Class;                 //子类
    uint32 Req_VipLevel;            //需求vip等级
    vector<uint32> Req_ItemId;      //消耗物品ID 最大五组
    vector<uint32> Req_ItemVal;     //消耗物品数量 最大五组
    uint32 Req_Jf;                  //消耗积分
    uint32 Req_Money;               //消耗金币
    uint32 Req_Exp;                 //消耗经验值
    uint32 Req_Honor_value;         //消耗荣誉值
    uint32 Req_Arena_Point;         //消耗竞技点
    string IsHomeShow;              //是否首页展示
    string Promotion_Type;          //促销类型 热卖 新品 店长推荐
    uint32 Discount;                //折扣 在LUA做计算 0~100
    time_t s_time;                  //活动开始时间
    time_t e_time;                  //活动结束时间
    uint32 Ppos;                    //贩卖物品在父类按钮按下界面显示位置
    uint32 Spos;                    //贩卖物品在子类按钮按下界面显示位置
    uint32 Hpos;                    //贩卖物品在主页的相对位置
    uint32 ElementNum;              //贩卖物品窗口内元素个数 自动统计
    string tex; //测试用


    //uint32 Req_charTitles;
};

struct MallClass
{
    string P_Class;
    string S_Class;
};


class Mall
{
public:
    static Mall * instance()
    {
        static Mall instance;
        return &instance;
    }
	void Load(bool online);
    void LoadUIMallData();

    std::string GetItemLink(uint32 entry);

    uint32 GetItemPosOfPclass(string pclass);                   //贩卖物品在父类窗口展示的位置
    uint32 GetItemPosOfSclass(string pclass, string sclass);    //贩卖物品在子类窗口展示的位置
    uint32 GetItemInThePClassWithCount(string pclass);
    void SendHomePclassPos(Player * player);                    //父类在主页展示的位置
    uint32 GetHomeItemPos(string pclass);                       //贩卖物品在主页展示的位置
    void SendMallClass(Player * player);
    void SendMallUIData(Player * player);
    void SendMallTestData(Player* player);
    bool IsIfLimit(Player* player);                             //判断是被限制交互 默认一次
    void EraseLimit(Player * player);                           //移除交互限制
    void SetLimit(Player * player);                             //添加交互限制
	bool check(Player * player, uint32 id);		
	bool IsSellLimit(Player * player);
	void DesItemSellReq(Player * player,uint32 id);
	void SetSellLimit(Player * player, bool b);
	void AddMallItem(Player * player, uint32 id);



    typedef map<Player * , uint32> ReceiveRequestMap;
    typedef map<uint32/*SerialNumber*/, MallData> MallDataMap;
    typedef vector<string /*pclass*/> TemporaryPClassVec;
    typedef vector<MallClass> MallClassVec;
    typedef vector<string> TemporaryClassVec;
    typedef vector<string /*pclass*/> ItemInHomePosVec;
    typedef vector<string /*pclass*/> PclassInHomePosVec;
	typedef map<Player *, bool> SellItemLimit;
    
    std::string Splice = ";:;";
    //uint32 Request = 0;

private:
    MallDataMap _MallDataMap;                   //用于存储所有商城的数据,处理后发送给客户端
    TemporaryPClassVec _TemporaryPClassVec;     //用于存储父级类别
    MallClassVec _MallClass;                    //用于存储父级类别和子级类别的结构性数据
    TemporaryClassVec _TemporaryClassMap;       //用于存储父级类别和子级类别的字符串数据
    ItemInHomePosVec _ItemInHomePosVec;         //贩卖物品在主页的相对位置(以父类按钮为相对目标)
    PclassInHomePosVec _PclassInHomePosVec;     //父类目标在主页的位置
    ReceiveRequestMap _ReceiveRequestMap;       //玩家请求查询限制?
	SellItemLimit _SellItemLimit;
};

#define sMall Mall::instance()
