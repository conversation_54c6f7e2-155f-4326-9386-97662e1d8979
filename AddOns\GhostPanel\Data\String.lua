BINDING_HEADER_LEGACYPANEL = "遗产面板";
BINDING_NAME_LEGACYPANEL = "角色成长";
LEGACY_PANEL = "遗产面板(开发)";
LEGACY_PANEL_STAT = "属性加点";
LEGACY_PANEL_STAT_SUMMERY = "数据总览";
LEGACY_PANEL_TOTAL_EXP_GAINED = "成长点数";
LEGACY_PANEL_TOTAL_EXP_COST = "花费点数";
LEGACY_PANEL_SPELL_EXP_COST = "技能花费";
LEGACY_PANEL_LEVEL_EXP_COST = "等级花费";
LEGACY_PANEL_STAT_EXP_COST = "精通花费";
LEGACY_PANEL_AVAILABLE_EXP = "可用点数";
LEGACY_REQUIRED_LEVEL = "需要等级: %d";
LEGACY_REQUIRED_LEVEL_NOT_REACH = "需要等级: |cffff0000%d|r";
LEGACY_REQUIRED_XP_NOT_REACH = "成长点数: |cffff0000%d|r";
LEGACY_REQUIRED_XP = "成长点数: %d";
LEGACY_SPELL_RANGE = "射程: %s码";
LEGACY_SPELL_COOLDOWN = "冷却时间: %s";
LEGACY_POWER_COST = "能量消耗: %d %s";
LEGACY_INSTANT_CAST = "瞬发";
LEGACY_TIMESTAMP_SECOND = "%d秒";
LEGACY_SPELL_CASTTIME = "%s"
LEGACY_YARD = "码";
LEGACY_DAY = "%.1f天";
LEGACY_HOUR = "%.1f小时";
LEGACY_MINUTE = "%.1f分钟";
LEGACY_SECOND = "%.1f秒";
LEGACY_POWER_HEALTH = "生命值";
LEGACY_POWER_MANA = "法力值";
LEGACY_POWER_RAGE = "怒气";
LEGACY_POWER_FOCUS = "集中值";
LEGACY_POWER_ENERGY = "能量";
LEGACY_POWER_HAPPINESS = "快乐值";
LEGACY_POWER_RUNE = "符文";
LEGACY_POWER_RUNIC_POWER = "符文能量";
LEGACY_EXP = "经验值";
LEGACY_ACQUIRABLE_SPELL = "可获取技能";
LEGACY_SPELL_DETAIL = "技能详情";
LEGACY_TEMP = "测试 6666666666666666666";
LEGACY_TEMP2 = "占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234占位文本1234占位文本 1234";
LEGACY_TEST_STR1 = "爆击几率提高|cff00ff001%|r";
LEGACY_TEST_STR2 = "|cff00ff00+1%|r";
LEGACY_TEST_STR3 = "射程提高1码";
LEGACY_LEARN = "学习";
LEGACY_RETURN = "返回";
LEGACY_PASSIVE = "被动";
LEGACY_INSTANT_CAST = "瞬发";
LEGACY_CHANNEL_SPELL = "需引导";
LEGACY_SPELL_LEARNED = "已习得";
LEGACY_LEVEL_X = "等级 %s";
LEGACY_MAX_LEVEL_REACHED = "已达到最高等级";
LEGACY_EXP_X = "%s经验值";
LEGACY_RESET_STATS_DEV = "重置属性(开发)";
LEGACY_RESET_SPELLS_DEV = "重置技能(开发)";
LEGACY_RESET_SPELL_CACHE_DEV = "重置法术缓存(开发)";
LEGACY_PREREQUISITE_SPELLS = "前置技能";
LEGACY_LEVEL_UP = "获取等级";
LEGACY_PREQ_SPELLS = "前置技能";
LEGACY_SPELL_TYPE_TEXTURE0 = "Interface\\ITEMSOCKETINGFRAME\\UI-EmptySocket-Meta";
LEGACY_SPELL_TYPE_TEXTURE1 = "Interface\\ITEMSOCKETINGFRAME\\UI-EmptySocket-Red";
LEGACY_SPELL_TYPE_TEXTURE2 = "Interface\\ITEMSOCKETINGFRAME\\UI-EmptySocket-Blue";
LEGACY_SPELL_TYPE_TEXTURE3 = "Interface\\ITEMSOCKETINGFRAME\\UI-EmptySocket-Yellow";
LEGACY_SPELL_TYPE_TEXTURE4 = "Interface\\ITEMSOCKETINGFRAME\\UI-EMPTYSOCKET";
LEGACY_LEVEL = "等级";
LEGACY_MAGE_SPELL_SPEC1 = "火焰精通";
LEGACY_MAGE_SPELL_SPEC2 = "冰霜精通";
LEGACY_MAGE_SPELL_SPEC3 = "奥术精通";
LEGACY_SPEC_FORMAT = "|cff00ff00+%d|r%s";
LEGACY_WARRIOR_SPELL_SPEC1 = "武器精通";
LEGACY_WARRIOR_SPELL_SPEC2 = "狂暴精通";
LEGACY_WARRIOR_SPELL_SPEC3 = "防御精通";
LEGACY_PRIEST_SPELL_SPEC1 = "神圣精通";
LEGACY_PRIEST_SPELL_SPEC2 = "戒律精通";
LEGACY_PRIEST_SPELL_SPEC3 = "暗影精通";
LEGACY_PALADIN_SPELL_SPEC1 = "神圣精通";
LEGACY_PALADIN_SPELL_SPEC2 = "防护精通";
LEGACY_PALADIN_SPELL_SPEC3 = "惩戒精通";
LEGACY_ROGUE_SPELL_SPEC1 = "刺杀精通";
LEGACY_ROGUE_SPELL_SPEC2 = "战斗精通";
LEGACY_ROGUE_SPELL_SPEC3 = "敏锐精通";
LEGACY_GROWTH_POINT_SHORT = "GP |cff00dd00+%.3f%%|r";
LEGACY_GROWTH_STATUS = "成长";
LEGACY_MF = "MF";
LEGACY_UNGATHERED_REWARD = "|cff00ff00新奖励可用|r";
LEGACY_GATHER_REWARD = "查看";
LEGACY_REWARD_LEFT = "|cffff00ff剩余数量: %d|r";
LEGACY_REWARD_LEFT_ACC = "|cffffff00剩余数量: %d|r";
LEGACY_REWARD_LEFT_SHARED = "|cff00ffff剩余数量: %d";
LEGACY_SPECIALTY_MARTIAL = "武学精通";
LEGACY_SPECIALTY_MAGICAL = "魔法精通";
LEGACY_SPECIALTY_TANACITY = "韧性";
LEGACY_SPECIALTY_PHYSICAL_DEFENSE = "物理防御";
LEGACY_SPECIALTY_MAGICAL_DEFENSE = "魔法防御";
LEGACY_SPECIALTY1_EFFECT = "|cff00ff00+%.3f%%|r|cffcccccc物理伤害/治疗|r";
LEGACY_SPECIALTY2_EFFECT = "|cff00ff00+%.3f%%|r|cffcccccc魔法伤害/治疗|r";
LEGACY_SPECIALTY3_EFFECT = "|cff00ff00+%.3f%%|r|cffcccccc物理防御力|r";
LEGACY_SPECIALTY4_EFFECT = "|cff00ff00+%.3f%%|r|cffcccccc魔法防御力|r";
LEGACY_SPECIALTY5_EFFECT = "|cff00ff00+%.3f%%|r|cffcccccc生命值|r";
LEGACY_SPECIALTY6_EFFECT = "|cff00ff00+%.3f%%|r|cffccccccMF|r";
LEGACY_SPECIALTY_ADDER_STR = "+%d精通";
LEGACY_PANEL_SPELLMOD_EXP_COST = "修研花费";
LEGACY_COLOR_CODE_GREEN = "|cff00ff00";
LEGACY_GIVE_KGP_DEV = "获取1000GP";
LEGACY_STAT_SUM_FORMAT_1 = "+%d 力量";
LEGACY_STAT_SUM_FORMAT_2 = "+%d 敏捷";
LEGACY_STAT_SUM_FORMAT_3 = "+%d 耐力";
LEGACY_STAT_SUM_FORMAT_4 = "+%d 智力";
LEGACY_STAT_SUM_FORMAT_5 = "+%d 精神";
LEGACY_STAT_FORMAT_1 = "+%d 力量 (%d|cff00ff00+%d|r)";
LEGACY_STAT_FORMAT_2 = "+%d 敏捷 (%d|cff00ff00+%d|r)";
LEGACY_STAT_FORMAT_3 = "+%d 耐力 (%d|cff00ff00+%d|r)";
LEGACY_STAT_FORMAT_4 = "+%d 智力 (%d|cff00ff00+%d|r)";
LEGACY_STAT_FORMAT_5 = "+%d 精神 (%d|cff00ff00+%d|r)";
LEGACY_STAT_BFORMAT_1 = "+%d 力量 (|cff00ff00+%d|r)";
LEGACY_STAT_BFORMAT_2 = "+%d 敏捷 (|cff00ff00+%d|r)";
LEGACY_STAT_BFORMAT_3 = "+%d 耐力 (|cff00ff00+%d|r)";
LEGACY_STAT_BFORMAT_4 = "+%d 智力 (|cff00ff00+%d|r)";
LEGACY_STAT_BFORMAT_5 = "+%d 精神 (|cff00ff00+%d|r)";
LEGACY_STAT_RANGE = "|cff7fff7f<%d-%d>|r";
LEGACY_STAT_RANGE_FORMAT_1 = "+%d%s 力量";
LEGACY_STAT_RANGE_FORMAT_2 = "+%d%s 敏捷";
LEGACY_STAT_RANGE_FORMAT_3 = "+%d%s 耐力";
LEGACY_STAT_RANGE_FORMAT_4 = "+%d%s 智力";
LEGACY_STAT_RANGE_FORMAT_5 = "+%d%s 精神";
LEGACY_REGEX_STAT1 = "^%+%d+%s力量";
LEGACY_REGEX_STAT2 = "^%+%d+%s敏捷";
LEGACY_REGEX_STAT3 = "^%+%d+%s耐力";
LEGACY_REGEX_STAT4 = "^%+%d+%s智力";
LEGACY_REGEX_STAT5 = "^%+%d+%s精神";
LEGACY_REGEX_DMG = "(%d+)%-(%d+) ([神圣|火焰|冰霜|自然|暗影|奥术]*)伤害";
LEGACY_REGEX_DMG_SINGLE = "(%d+) ([神圣|火焰|冰霜|自然|暗影|奥术]*)伤害";
LEGACY_REGEX_DPS = "（每秒伤害([%s%S]*)）";
LEGACY_REGEX_ARMOR = "(%d+) 护甲";
LEGACY_REGEX_BLOCK = "+(%d+)%% 格挡";
LEGACY_DMG_FORMAT = "+%d-%d %s伤害";
LEGACY_DMG_SINGLE_FORMAT = "+%d %s伤害";
LEGACY_DMG_FORMAT_RANGE = "%d%s-%d%s %s伤害";
LEGACY_DMG_RANGE = "|cffffff7f<%d-%d>|r";
LEGACY_ARMOR_FORMAT = "%d 护甲";
LEGACY_BLOCK_FORMAT = "+%d%% 格挡";
LEGACY_ARMOR_FORMAT_RANGE = "%d%s 护甲";
LEGACY_DPS_FORMAT = "+%.1f 每秒伤害";
LEGACY_RAND_QUALITY_1 = "|cffcccccc沧桑|r";
LEGACY_RAND_QUALITY_2 = "|cffffffff精制|r";
LEGACY_RAND_QUALITY_3 = "|cff1eff00触魔|r";
LEGACY_RAND_QUALITY_4 = "|cff0070dd强效|r";
LEGACY_RAND_QUALITY_5 = "|cffa335ee高能|r";
LEGACY_RAND_QUALITY_6 = "|cffff8000圣化|r";
LEGACY_RAND_QUALITY_7 = "|cffff0000遗产|r";
LEGACY_REGEX_RES1 = "^+%d+%s火焰抗性";
LEGACY_REGEX_RES2 = "^+%d+%s自然抗性";
LEGACY_REGEX_RES3 = "^+%d+%s冰霜抗性";
LEGACY_REGEX_RES4 = "^+%d+%s暗影抗性";
LEGACY_REGEX_RES5 = "^+%d+%s奥术抗性";
LEGACY_REGEX_RES0 = "^+%d+%s魔法抗性";
LEGACY_RES_FORMAT1 = "+%d 火焰抗性";
LEGACY_RES_FORMAT2 = "+%d 自然抗性";
LEGACY_RES_FORMAT3 = "+%d 冰霜抗性";
LEGACY_RES_FORMAT4 = "+%d 暗影抗性";
LEGACY_RES_FORMAT5 = "+%d 奥术抗性";
LEGACY_RES_FORMAT0 = "+%d 魔法抗性";
LEGACY_RES_FORMAT_RANGE1 = "+%d%s 火焰抗性";
LEGACY_RES_FORMAT_RANGE2 = "+%d%s 自然抗性";
LEGACY_RES_FORMAT_RANGE3 = "+%d%s 冰霜抗性";
LEGACY_RES_FORMAT_RANGE4 = "+%d%s 暗影抗性";
LEGACY_RES_FORMAT_RANGE5 = "+%d%s 奥术抗性";
LEGACY_RES_FORMAT_RANGE0 = "+%d%s 魔法抗性";
LEGACY_STRING_WEAPON = "武器";
LEGACY_STRING_ARMOR = "护甲";
LEGACY_REGEX_SPELLPOWER = "装备: 法术强度提高(%d+)点";
LEGACY_REGEX_SPELLDAMAGEPOWER = "装备: 法术伤害强度提高(%d+)点";
LEGACY_REGEX_SPELLHEALINGPOWER = "装备: 法术治疗强度提高(%d+)点";
LEGACY_SPELLPOWER_FORMAT = "装备: 法术强度提高%d点。";
LEGACY_REGEX_SPELLDAMAGEPOWER = "装备: 法术伤害强度提高%d点。";
LEGACY_REGEX_SPELLHEALINGPOWER = "装备: 法术治疗强度提高%d点。";
LEGACY_SPECIALTY_MF = "寻宝精通";
LEGACY_ABILITY_LOCKED_SLOT_ACTIVE = "T%d主动技能槽";
LEGACY_ABILITY_LOCKED_SLOT_PASSIVE = "T%d被动技能槽";
LEGACY_ABILITY_LOCKED_SLOT_TALENT = "T%d天赋技能槽";
LEGACY_ABILITY_UNLOCK_AT_LEVEL = "到达%d级解锁。";
LEGACY_ABILITY_NO_AVAILABLE_SPELL = "无可用技能。";
LEGACY_ABILITY_LEFT_CLICK_HINT = "|cff00ff00左键: 选择|r";
LEGACY_ABILITY_RIGHT_CLICK_HINT = "|cff00ff00右键: 修研|r";
LEGACY_ABILITY_NOT_ACQUIRED = "|cffff0000未领悟|r";
LEGACY_ABILITY_RETURN_TO_MAIN_FRAME_HINT = "|cff00ff00左键: 返回";
LEGACY_ABILITY_LOCK_ICON = "Interface\\Icons\\Inv_Misc_SummerFest_BrazierOrange";
LEGACY_ABILITY_EMPTY_ICON = "Interface\\Icons\\Inv_Misc_SummerFest_BrazierOrange";
LEGACY_ABILITY_ACTIVE_SLOT_EMPTY = "T%d主动技能槽|cff00ff00(空)|r";
LEGACY_ABILITY_PASSIVE_SLOT_EMPTY = "T%d被动技能槽|cff00ff00(空)|r";
LEGACY_ABILITY_TALENT_SLOT_EMPTY = "T%d天赋技能槽|cff00ff00(空)|r";
LEGACY_ABILITY_CLASS_SLOT_EMPTY = "特性技能槽|cff00ff00(空)|r";
LEGACY_ABILITY_ACTIVE_SLOT_EMPTY_HINT = "|cff00ff00左键: 选择技能";
LEGACY_ABILITY_PASSIVE_SLOT_EMPTY_HINT = "|cff00ff00左键: 选择技能";
LEGACY_ICON_PATH = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\";
LEGACY_ABILITY_SPELLMOD_ICON = {
    [0] = LEGACY_ICON_PATH.."OW\\flashbang", -- damage
    [1] = LEGACY_ICON_PATH.."OW\\deadeye", -- crit chance
    [2] = LEGACY_ICON_PATH.."OW\\deathblossom", -- crit dmg
    [3] = LEGACY_ICON_PATH.."OW\\earthshatter", -- dot
    [4] = LEGACY_ICON_PATH.."OW\\ampitup", -- cooldown
    [5] = LEGACY_ICON_PATH.."OW\\sprint", -- range
    [6] = LEGACY_ICON_PATH.."OW\\charge", -- cast time
	[7] = LEGACY_ICON_PATH.."OW\\projectedbarrier", -- cost
	[8] = LEGACY_ICON_PATH.."OW\\wholehog", -- threat
	[9] = LEGACY_ICON_PATH.."OW\\dragonblade", -- arp
	[10] = LEGACY_ICON_PATH.."OW\\swiftstrike", -- hit chance
	[11] = LEGACY_ICON_PATH.."OW\\bioticfield", -- heal
	[12] = LEGACY_ICON_PATH.."OW\\orbofharmony", -- hot
	[13] = LEGACY_ICON_PATH.."OW\\barriorprojector", -- absorbtion
	[14] = LEGACY_ICON_PATH.."OW\\sonicarrow", -- radius
	[15] = LEGACY_ICON_PATH.."OW\\reconfigure", -- amplitude
	[16] = LEGACY_ICON_PATH.."OW\\firestrike", -- gcd
	[17] = LEGACY_ICON_PATH.."OW\\gravitonsurge" -- duration
};
LEGACY_POLARITY_ICON = {
	[1] = { a = LEGACY_ICON_PATH.."LegacyPolarity1", d = LEGACY_ICON_PATH.."LegacyPolarity1d", n = "A", r = 1, g = 0, b = 0 },
	[2] = { a = LEGACY_ICON_PATH.."LegacyPolarity2", d = LEGACY_ICON_PATH.."LegacyPolarity2d", n = "C", r = 1, g = 0.5, b = 0 },
	[3] = { a = LEGACY_ICON_PATH.."LegacyPolarity3", d = LEGACY_ICON_PATH.."LegacyPolarity3d", n = "E", r = 1, g = 1, b = 0 },
	[4] = { a = LEGACY_ICON_PATH.."LegacyPolarity4", d = LEGACY_ICON_PATH.."LegacyPolarity4d", n = "N", r = 0.5, g = 1, b = 0 },
	[5] = { a = LEGACY_ICON_PATH.."LegacyPolarity5", d = LEGACY_ICON_PATH.."LegacyPolarity5d", n = "O", r = 0, g = 0.5, b = 1 },
	[6] = { a = LEGACY_ICON_PATH.."LegacyPolarity6", d = LEGACY_ICON_PATH.."LegacyPolarity6d", n = "R", r = 0, g = 1, b = 1 },
	[7] = { a = LEGACY_ICON_PATH.."LegacyPolarity7", d = LEGACY_ICON_PATH.."LegacyPolarity7d", n = "U", r = 0.5, g = 0.5, b = 1 },
	[8] = { a = LEGACY_ICON_PATH.."LegacyPolarity8", d = LEGACY_ICON_PATH.."LegacyPolarity8d", n = "X", r = 0.5, g = 0, b = 1 },
	[9] = { a = LEGACY_ICON_PATH.."LegacyPolarity9", d = LEGACY_ICON_PATH.."LegacyPolarity9d", n = "Z", r = 0, g = 1, b = 0 },
}
LEGACY_ABILITY_LEARNED = "|cff00ff00已习得|r";
LEGACY_ABILITY_RIGHT_CLICK_TO_LEARN = "|cff00ff00右键: 学习|r (%dTP)";
LEGACY_ABILITY_LEFT_CLICK_TO_CANCEL_UPGRADE = "|cff00ff00左键: 取消强化|r";
LEGACY_ABILITY_RIGHT_CLICK_TO_SELECT_UPGRADE = "|cff00ff00右键: 强化|r";
LEGACY_ABILITY_LEARN_SPECIALTY = "|cff00ff00右键: 修研|r";
LEGACY_ABILITY_LEARN = "修研%s。|n|n花费: %d修研点数";
LEGACY_CONFIRM = "确认";
LEGACY_CANCEL = "取消";
LEGACY_NOT_ENOUGH_GP_FORMAT = "你没有足够的修研点数来解锁%s。";
LEGACY_DISCARD_ABILITY_UPGRADE_CONFIRM = "%s的以下强化项目仍未应用: |n|n%s|n|n确认返回吗？";
LEGACY_APPLY_UPGRADES_AND_RETURN = "应用修改";
LEGACY_DISCARD_UPGRADES_AND_RETURN = "放弃修改";
LEGACY_ABILITY_UPGRADE_STEP_FOR_NEXT_LEVEL = "|cffffffff下一级: |r|cff00ff00%s|r";
LEGACY_ABILITY_UPGRADE_LEVEL_FORMAT = "|cff00ff00%s/%s|r";
LEGACY_ABILITY_UPGRADE_LEVEL_MAXED_FORMAT = "|cffffff00%s/%s|r";
LEGACY_INFINITE_MARK = "∞";
LEGACY_ABILITY_GP_COST = "(%dGP)";
LEGACY_ABILITY_TP_COST = "(%dTP)";
LEGACY_STRING_FORMAT_GREEN = "|cff00ff00%s|r";
LEGACY_STRING_FORMAT_RED = "|cffff0000%s|r";
LEGACY_STRING_FORMAT_YELLOW = "|cffffff00%s|r";
LEGACY_STRING_FORMAT_LIGHTBLUE = "|cff6666ff%s|r";
LEGACY_GP_ITEM_HINT = "|cff00ff00右键: 重置|r";
LEGACY_GP_AVAILABLE = "可用战争点数";
LEGACY_GP_TOTAL_GAINED = "获取点数总额";
LEGACY_GP_TOTAL_COST = "花费点数总额";
LEGACY_GP_COST_SPELL = "·技能学习";
LEGACY_GP_COST_MOD = "·技能修研";
LEGACY_GP_COST_STAT = "·精通强化";
LEGACY_SPECIALTY_DESC = 
{
    [1] = { Title = "老兵", Tip = "老兵 |cffffffff%d|r", Pos = "物理防御力", Neg = "物理伤害力", Icon = "Interface\\Icons\\ABILITY_WARRIOR_STRENGTHOFARMS" },
    [2] = { Title = "狂徒", Tip = "狂徒 |cffffffff%d|r", Pos = "物理伤害力", Neg = "物理防御力", Icon = "Interface\\Icons\\ABILITY_ROGUE_MASTEROFSUBTLETY" },
    [3] = { Title = "贤者", Tip = "贤者 |cffffffff%d|r", Pos = "生命和能量上限", Neg = "生命和能量恢复", Icon = "Interface\\Icons\\ACHIEVEMENT_BG_KILLFLAGCARRIERS_GRABFLAG_CAPIT" },
    [4] = { Title = "魔瘾", Tip = "魔瘾 |cffffffff%d|r", Pos = "魔法伤害力", Neg = "魔法防御和治疗力", Icon = "Interface\\Icons\\ABILITY_MAGE_GREATERINVISIBILITY" },
    [5] = { Title = "信仰", Tip = "信仰 |cffffffff%d|r", Pos = "魔法防御和治疗力", Neg = "魔法伤害力", Icon = "Interface\\Icons\\ABILITY_ROGUE_IMROVEDRECUPERATE" },
    [6] = { Title = "修行", Tip = "修行 |cffffffff%d|r", Pos = "生命和能量恢复", Neg = "生命和能量上限", Icon = "Interface\\Icons\\ABILITY_SHAMAN_ANCESTRALGUIDANCE" },
};
LEGACY_SPECIALTY_DESC_PSYCHE = "|cff00ff00+%.2f%%|r |cffffffff魔法精准率|r";
LEGACY_GUILD_RANK =
{
	COLOR = 
	{
		HORDE = { r = 1, g = 0.35, b = 0.35 },
		ALLIANCE = { r = 0.35, g = 0.35, b = 1 },
	},
	ICON =
	{
		NOTSTARTED = "Interface\\Icons\\ACHIEVEMENT_QUESTS_COMPLETED_DAILY_04";
		INPROGRESS = "Interface\\Icons\\ACHIEVEMENT_QUESTS_COMPLETED_07",
		FINISHED = "Interface\\Icons\\ACHIEVEMENT_QUESTS_COMPLETED_07",
		BONUS_UNASSIGNED = "Interface\\Icons\\ABILITY_VEHICLE_SHELLSHIELDGENERATOR",
		HORDE = {
			[0] = "Interface\\Icons\\pvpcurrency-honor-horde",
			[1] = "Interface\\Icons\\Achievement_PVP_H_01",
			[2] = "Interface\\Icons\\Achievement_PVP_H_02",
			[3] = "Interface\\Icons\\Achievement_PVP_H_03",
			[4] = "Interface\\Icons\\Achievement_PVP_H_04",
			[5] = "Interface\\Icons\\Achievement_PVP_H_05",
			[6] = "Interface\\Icons\\Achievement_PVP_H_06",
			[7] = "Interface\\Icons\\Achievement_PVP_H_07",
			[8] = "Interface\\Icons\\Achievement_PVP_H_08",
			[9] = "Interface\\Icons\\Achievement_PVP_H_09",
			[10] = "Interface\\Icons\\Achievement_PVP_H_10",
			[11] = "Interface\\Icons\\Achievement_PVP_H_11",
			[12] = "Interface\\Icons\\Achievement_PVP_H_12",
			[13] = "Interface\\Icons\\Achievement_PVP_H_13",
			[14] = "Interface\\Icons\\Achievement_PVP_H_14",
			[15] = "Interface\\Icons\\Achievement_PVP_H_H",
		},
		ALLIANCE = {
			[0] = "Interface\\Icons\\pvpcurrency-honor-alliance",
			[1] = "Interface\\Icons\\Achievement_PVP_A_01",
			[2] = "Interface\\Icons\\Achievement_PVP_A_02",
			[3] = "Interface\\Icons\\Achievement_PVP_A_03",
			[4] = "Interface\\Icons\\Achievement_PVP_A_04",
			[5] = "Interface\\Icons\\Achievement_PVP_A_05",
			[6] = "Interface\\Icons\\Achievement_PVP_A_06",
			[7] = "Interface\\Icons\\Achievement_PVP_A_07",
			[8] = "Interface\\Icons\\Achievement_PVP_A_08",
			[9] = "Interface\\Icons\\Achievement_PVP_A_09",
			[10] = "Interface\\Icons\\Achievement_PVP_A_10",
			[11] = "Interface\\Icons\\Achievement_PVP_A_11",
			[12] = "Interface\\Icons\\Achievement_PVP_A_12",
			[13] = "Interface\\Icons\\Achievement_PVP_A_13",
			[14] = "Interface\\Icons\\Achievement_PVP_A_14",
			[15] = "Interface\\Icons\\Achievement_PVP_A_A",
		},
	},
};
LEGACY_SELECT_GUILD_SPELL_CONFIRM = "你的公会将获得%s的增益，这项增益需要花费|cff00ff00%d影响力 / 天|r来维护。确定继续吗？"
LEGACY_GUILD_RANK_STRING = "等级 %d";
LEGACY_UNLOCK_AT_LEVEL = "需要公会等级：%d";
LEGACY_GUILD_BONUS_EMPTY = "阵营增益 (空)";
LEGACY_GUILD_BONUS_CLICK_TO_SELECT = "|cff00ff00点击查看可用的阵营增益。|r";
LEGACY_GUILD_REPLACE_BONUS_CLICK_TO_RETURN = "|cff00ff00点击以返回。|r";
LEGACY_ACCOUNT_LEVEL = "等级";
LEGACY_ACCOUNT_REPUTATION = "贡献值";
LEGACY_ACCOUNT_FUND = "|cffffffff资金|r";
LEGACY_UNIDENTIFIED = "|cffff0000未鉴定|r";
LEGACY_IDENTIFY_INFO = "|cffffffff鉴定信息|r";
LEGACY_ITEM_MAGIC_LEVEL = "|cffffffff·品级:|r |cff00ff00%d|r";
LEGACY_ITEM_STAT_RATIO = "·属性分布";
LEGACY_ITEM_STRENGTH_LEVEL = "|cffffffff·力量:|r |cff00ff00%.2f%%|r";
LEGACY_ITEM_AGILITY_LEVEL = "|cffffffff·敏捷:|r |cff00ff00%.2f%%|r";
LEGACY_ITEM_STAMINA_LEVEL = "|cffffffff·耐力:|r |cff00ff00%.2f%%|r";
LEGACY_ITEM_INTELLECT_LEVEL = "|cffffffff·智力:|r |cff00ff00%.2f%%|r";
LEGACY_ITEM_SPIRIT_LEVEL = "|cffffffff·精神:|r |cff00ff00%.2f%%|r";
LEGACY_EQUIP_REQUIREMENT = "|cffffffff装备需求|r";
LEGACY_SPEC_REQUIREMENT =
{
    [1] = "|cffffffff·武力 %d|r",
    [2] = "|cffffffff·直觉 %d|r",
    [3] = "|cffffffff·意志 %d|r",
    [4] = "|cffffffff·心智 %d|r",
    [5] = "|cffffffff·灵魂 %d|r",
    [6] = "|cffffffff·阅历 %d|r"
};
LEGACY_SPEC_XREQUIREMENT =
{
    [1] = "|cffff0000·武力 %d|r",
    [2] = "|cffff0000·直觉 %d|r",
    [3] = "|cffff0000·意志 %d|r",
    [4] = "|cffff0000·心智 %d|r",
    [5] = "|cffff0000·灵魂 %d|r",
    [6] = "|cffff0000·阅历 %d|r"
};
LEGACY_STAT_SUFFIX_HEADER = "|cffffffff魔法属性|r";
LEGACY_STAT_SUFFIX_FORMAT_1 = "|cffffffff·力量 %d|r";
LEGACY_STAT_SUFFIX_FORMAT_2 = "|cffffffff·敏捷 %d|r";
LEGACY_STAT_SUFFIX_FORMAT_3 = "|cffffffff·耐力 %d|r";
LEGACY_STAT_SUFFIX_FORMAT_4 = "|cffffffff·智力 %d|r";
LEGACY_STAT_SUFFIX_FORMAT_5 = "|cffffffff·精神 %d|r";
LEGACY_NAV_DATA = 
{
	[0] = { Name = "技能", Desc = "可以丢的法术", Icon = "Interface\\Icons\\ACHIEVEMENT_DUNGEON_GLORYOFTHERAIDER" },
    [1] = { Name = "BUFF", Desc = "精通和符文", Icon = "Interface\\Icons\\ABILITY_DEATHKNIGHT_HEMORRHAGICFEVER" },
	[2] = { Name = "幻化", Desc = "变个样子！", Icon = "Interface\\Icons\\ACHIEVEMENT_DUNGEON_ICECROWN_FROSTMOURNE" },
	[3] = { Name = "公会", Desc = "你的团队和未来", Icon = "Interface\\Icons\\ABILITY_SIEGE_ENGINEER_PATTERN_RECOGNITION" },
    [4] = { Name = "遗产", Desc = "唯有时光可以道来的故事", Icon = "Interface\\Icons\\INV_LEGENDARY_SWORD" },
	[5] = { Name = "市场", Desc = "一些可以悄悄买到的东东",  Icon = "Interface\\Icons\\INV_MISC_COINBAG_SPECIAL" },
    [6] = { Name = "声望", Desc = "你的社区贡献和奖励", Icon = "Interface\\Icons\\ABILITY_PALADIN_VENERATION" },
	[7] = { Name = "奖励", Desc = "你的宝贝", Icon = "Interface\\Icons\\INV_MISC_CODEXOFXERRATH_CHAINS" },
	[8] = { Name = "符文", Desc = "新增的部分", Icon = "Interface\\Icons\\INV_MISC_CODEXOFXERRATH_CHAINS" },
};
LEGACY_ITEM_INHERITED = "|cffcccccc已被继承|r";
LEGACY_ITEM_INHERIT = "|cff00ff00右键：继承此遗产|r";
LEGACY_ITEM_INHERIT_PRICELESS = "|cffee3333特定继承人|r";
LEGACY_ITEM_INHERIT_COST = "|cffffff00·%d信念徽章|r";
LEGACY_TOKEN_ICON = "Interface\\Icons\\Inv_Legacy_gem_7";
LEGACY_ITEM_COST_PRICELESS = "|cffffff00·无价|r";
LEGACY_FETCH_LEGACY_ITEM_CONFIRM = "获取遗产的提示|n物品：%s|n需要：%d信念徽章";
LEGACY_FETCH_MARKET_ITEM_CONFIRM = "你即将购买一件物品：%s|n花费：%d资金";
LEGACY_FETCH_MARKET_SPELL_CONFIRM = "你即将购买一件|cff00ff00账号通用|r物品：%s|n花费：%d资金";
LEGACY_TP_AVAILABLE = "可用修研点数";
LEGACY_TP_TOTAL_GAINED = "获取点数总额";
LEGACY_TP_TOTAL_COST = "花费点数总额";
LEGACY_RIGHT_CLICK = "|cff00ff00右键:|r ";
LEGACY_CURRENCY_TO_GOLD = "兑换|cff00ff00全额|r资金"
LEGACY_CURRENT_RATIO = "当前汇率: ";
LEGACY_CURRENCY_TO_GOLD_RATIO = "|cff00ff00%d|r|cffffffff资金|r = |cff00ff00%d|r|TInterface\\Icons\\INV_MISC_COIN_17:12:12:3:0|t |cff00ff00%d|r|TInterface\\Icons\\INV_MISC_COIN_18:12:12:3:0|t |cff00ff00%d|r|TInterface\\Icons\\INV_MISC_COIN_19:12:12:3:0|t";
LEGACY_BUY_GOLD_CONFIRM = "将|cff00ff00%d|r资金兑换为|cff00ff00%s|r。确认继续吗？";
LEGACY_GOLD_COIN_ICON = "Interface\\Icons\\INV_MISC_COIN_02";
LEGACY_BUY_GOLD_AMOUNT_TITLE = "兑换|cff00ff00%d|r资金";
LEGACY_MONEY_GOLD_ICON = "|TInterface\\Icons\\INV_MISC_COIN_17:12:12:3:0|t";
LEGACY_MONEY_SILVER_ICON = "|TInterface\\Icons\\INV_MISC_COIN_18:12:12:3:0|t";
LEGACY_MONEY_COPPER_ICON = "|TInterface\\Icons\\INV_MISC_COIN_19:12:12:3:0|t";
LEGACY_ABILITY_RIGHT_CLICK_HINT_COMPREHEND = "|cff00ff00右键：领悟";
LEGACY_ABILITY_COMPREHEND = "|cffffffff%d修研点数|r";
LEGACY_ABILITY_COMPREHEND_X = "|cffff0000%d修研点数|r";
LEGACY_ABILITY_UNLOCK_BY_BOOK = "|cffff0000由典籍领悟|r";
LEGACY_REFORGABLE = "|cff00ff00可重铸 (%d/%d)|r";
LEGACY_REFORGABLE_MAX = "|cffffff00可重铸 (%d/%d)|r";
LEGACY_CURRENT_TRANSMOG = "当前幻化:";
LEGACY_TRANSMOG_SLOT_LEFT_CLICK = "|cff00ff00左键: 选择|r";
LEGACY_TRANSMOG_SLOT_RIGHT_CLICK = "|cff00ff00右键: 预览|r";
LEGACY_NAV_ARROW = 
{
	[1] = "Interface\\Icons\\misc_arrowleft",
	[2] = "Interface\\Icons\\misc_arrowright"
};
LEGACY_CLASS_ICON = 
{
	[0] = 
	{
		["WARRIOR"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARRIOR_DESATURATED",
		["PALADIN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PALADIN_DESATURATED",
		["ROGUE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_ROGUE_DESATURATED",
		["HUNTER"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_HUNTER_DESATURATED",
		["SHAMAN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_SHAMAN_DESATURATED",
		["MAGE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_MAGE_DESATURATED",
		["PRIEST"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PRIEST_DESATURATED",
		["WARLOCK"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARLOCK_DESATURATED",
		["DRUID"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DRUID_DESATURATED",
		["DEATHKNIGHT"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DEATHKNIGHT_DESATURATED",
	},
	[1] = 
	{
		["WARRIOR"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARRIOR_DESATURATED",
		["PALADIN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PALADIN_DESATURATED",
		["ROGUE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_ROGUE_DESATURATED",
		["HUNTER"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_HUNTER_DESATURATED",
		["SHAMAN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_SHAMAN_DESATURATED",
		["MAGE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_MAGE_DESATURATED",
		["PRIEST"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PRIEST_DESATURATED",
		["WARLOCK"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARLOCK_DESATURATED",
		["DRUID"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DRUID_DESATURATED",
		["DEATHKNIGHT"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DEATHKNIGHT_DESATURATED",
	},
	[2] = 
	{
		["WARRIOR"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARRIOR_DESATURATED",
		["PALADIN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PALADIN_DESATURATED",
		["ROGUE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_ROGUE_DESATURATED",
		["HUNTER"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_HUNTER_DESATURATED",
		["SHAMAN"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_SHAMAN_DESATURATED",
		["MAGE"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_MAGE_DESATURATED",
		["PRIEST"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_PRIEST_DESATURATED",
		["WARLOCK"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_WARLOCK_DESATURATED",
		["DRUID"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DRUID_DESATURATED",
		["DEATHKNIGHT"] = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\CLASSICON_DEATHKNIGHT_DESATURATED",
	},
};
LEGACY_TRANSMOG_SLOT_SELECT = "|cff00ff00为%s装备选择幻化源|r";
LEGACY_TRANSMOG_FOR_SLOT_COUNT = "%d 可用幻化源";
LEGACY_NO_TRANSMOG_FOR_SLOT = "|cffffffff你还未收藏过%s装备的幻化源|r";
LEGACY_TRANSMOG_ITEM_LEFT_CLICK = "|cff00ff00左键: 预览|r";
LEGACY_TRANSMOG_ITEM_RIGHT_CLICK = "|cff00ff00右键: 选择|r";
LEGACY_ITEM_QUERYING = "|cffff0000正在查询...|r";
LEGACY_TRANSMOG_SLOT_CONFIRM = "你将选择%s成为%s幻化源。|n确定继续吗？";
LEGACY_MAINHAND_ONLY = " |cff666666(主手限定)|r";
LEGACY_OFFHAND_ONLY = " |cff666666(副手限定)|r";
LEGACY_TRANSMOG_SLOT_CLICK_HINT = "|cff00ff00点击: 更换幻化源|r";
LEGACY_TRANSMOG_POWER = "|cffffffff幻化能量|r";
LEGACY_REMOVE_ALL_TRANSMOG_HINT = "|cff00ff00右键: 移除所有幻化|r";
LEGACY_REMOVE_ALL_TRANSMOG_CONFIRM = "即将移除所有装备部位的幻化效果。|n确定继续吗？";
LEGACY_CURRENT_TRANSMOG = "|cffffff00当前幻化|r";
LEGACY_DROP_LEVEL = "|cff00eeee魔法等级 %d|r";
LEGACY_MF_DESC = "|cff00eeee阅历|r|cffffffff将提高你在物品中发现|r|cff00eeee魔法能量|r|n|cffffffff的|r|cff00ff00几率|r|cffffffff和|r|cff00ff00效果强度|r|cffffffff。|r";
LEGACY_MF_STATE_RECORD = "|n|cffffffff当前状态：|r|cff00ff00记录|r|n|cff00eeee探索发现魔法物品将提高阅历等级|r|n|n|cff00ff00右键点击：切换状态|r";
LEGACY_MF_STATE_SEALED = "|n|cffffffff当前状态：|r|cffffff00封笔|r|n|cff00eeee提高下一次发现魔法物品的几率和魔法强度|r|n|n|cff00ff00右键点击：切换状态|r";
LEGACY_MF_BONUS_FORMAT = "|cff00ff00+%d|r |cff00eeee阅历等级|r";
LEGACY_MARKET_SPELL_POSSESSED = "|cff00ff00已获取|r";
LEGACY_MARKET_SPELL_NOT_POSSESSED = "|cffcccccc未获取|r";
LEGACY_MARKET_REPUTATION_REWARDED = "|cff00ff00已获取|r";
LEGACY_MARKET_REPUTATION_NOT_REWARDED = "|cffcccccc未获取|r";
LEGACY_MARKET_REPUTATION_REWARDED_PERIODIC = "|cff00ff00周期奖励|r";
LEGACY_MARKET_REPUTATION_NOT_REWARDED_PERIODIC = "|cffcccccc周期奖励|r";
LEGACY_MARKET_PRICE = "价格";
LEGACY_MARKET_EVENT_PRICE = "|cff00ff00促销|r";
LEGACY_MARKET_PRICE_FORMAT = "|cffffffff%s|r";
LEGACY_MARKET_DISCOUNT_FORMAT = "|cff00ff00%s (-%s%%)|r";
LEGACY_COMMUNITY_REPUTATION = "|cffffff00声望奖励|r";
LEGACY_MARKET_REPUTATION_REWARD = "|cffffff00%d级|r";
LEGACY_MARKET_REPUTATION_RANK_FORMAT = "|cffffff00R%d|r";
LEGACY_MF = "|cff00eeee阅历等级|r";
LEGACY_MF_TIP_FORMAT = "|cff00eeee%d|r |cff00ff00(+%d/%d)|r";
LEGACY_RUNE_TIER =
{
	[1] = { name = "灵魂符文", lvl = 8 },
	[2] = { name = "灵魂符文", lvl = 16 },
	[3] = { name = "灵魂符文", lvl = 24 },
	[4] = { name = "灵魂符文", lvl = 32 },
	[5] = { name = "灵魂符文", lvl = 40 },
	[6] = { name = "灵魂符文", lvl = 48 },
	[7] = { name = "灵魂符文", lvl = 56 },
	[8] = { name = "灵魂符文", lvl = 64 },
	[9] = { name = "灵魂符文", lvl = 72 },
	[10] = { name = "灵魂符文", lvl = 80 }
};
LEGACY_RUNE_ACTIVE = "|cffff7fff(激活)";
LEGACY_RUNE_EMPTY = "(空)";
LEGACY_ACTIVE_SPELL_TIER =
{
	[1] = { name = "生命符文", lvl = 8 },
	[2] = { name = "生命符文", lvl = 16 },
	[3] = { name = "生命符文", lvl = 24 },
	[4] = { name = "生命符文", lvl = 32 },
	[5] = { name = "生命符文", lvl = 40 },
	[6] = { name = "生命符文", lvl = 48 },
	[7] = { name = "生命符文", lvl = 56 },
	[8] = { name = "生命符文", lvl = 64 },
	[9] = { name = "生命符文", lvl = 72 },
	[10] = { name = "生命符文", lvl = 80 }
};
LEGACY_PASSIVE_SPELL_TIER =
{
	[1] = { name = "元素符文", lvl = 8 },
	[2] = { name = "元素符文", lvl = 16 },
	[3] = { name = "元素符文", lvl = 24 },
	[4] = { name = "元素符文", lvl = 32 },
	[5] = { name = "元素符文", lvl = 40 },
	[6] = { name = "元素符文", lvl = 48 },
	[7] = { name = "元素符文", lvl = 56 },
	[8] = { name = "元素符文", lvl = 64 },
	[9] = { name = "元素符文", lvl = 72 },
	[10] = { name = "元素符文", lvl = 80 }
};
LEGACY_RUNE_UNLOCK_LEVEL =
{
	[1] = 8,
	[2] = 16,
	[3] = 24,
	[4] = 32,
	[5] = 40,
	[6] = 48,
	[7] = 56,
	[8] = 64,
	[9] = 72,
	[10] = 80
};
LEGACY_ACTIVE_SPELL_COLOR = { r = 0.5, g = 1, b = 0.5, a = 1 };
LEGACY_PASSIVE_SPELL_COLOR = { r = 0.5, g = 1, b = 1, a = 1 };
LEGACY_RUNE_SPELL_COLOR = { r = 0.5, g = 0.5, b = 1, a = 1 };
LEGACY_SPELL_SLOT_COLOR =
{
	[1] = { r = 0.1176, g = 1, b = 0, a = 0.75 },
	[2] = { r = 0.1176, g = 1, b = 0, a = 0.75 },
	[3] = { r = 0.1176, g = 1, b = 0, a = 0.75 },
	[4] = { r = 0, g = 0.4392, b = 0.8667, a = 0.75 },
	[5] = { r = 0, g = 0.4392, b = 0.8667, a = 0.75 },
	[6] = { r = 0, g = 0.4392, b = 0.8667, a = 0.75 },
	[7] = { r = 0.6392, g = 0.2078, b = 0.9333, a = 0.75 },
	[8] = { r = 0.6392, g = 0.2078, b = 0.9333, a = 0.75 },
	[9] = { r = 0.6392, g = 0.2078, b = 0.9333, a = 0.75 },
	[10] = { r = 1, g = 0.502, b = 0, a = 0.75 },
};
LEGACY_CLICK_TO_EXPAND = "点击: 展开子项 (%d项)";
LEGACY_SPELL_UNLOCK_AT_LEVEL = "|cffdddddd等级%d解锁|r";
LEGACY_RUNE_SEALED = "(封印)";
LEGACY_SPELL_AVAILABLE = "|cff00ff00可用|r";
LEGACY_GUILD_BONUS_COST = "|cff00ff00维护：|r";
LEGACY_GUILD_BONUS_COST_VALUE = "|cff00ff00%d影响力 / 天|r";
LEGACY_GUILD_INFLUENCE = "|cff00ff00影响力：%d / %d|r";
LEGACY_MF_STATE = 
{
	[0] = "|cff00ff00记录|r",
	[1] = "|cffffff00封笔|r",
};
LEGACY_SET_MF_STATE = "将阅历状态改为%s。|n|n在这种状态下，%s。|n|n确定继续吗？";
LEGACY_SET_MF_STATE_RECORD = "在战斗和探索中获取的魔法物品将提高你的阅历等级";
LEGACY_SET_MF_STATE_SEAL = "下一次制造或发现战利品中魔法的几率和强度将由已积累的阅历等级提升";
LEGACY_REQ_GUILD_LEVEL = "需要公会等级";
LEGACY_LOCKED_ICON = "Interface\\ICONS\\SPELL_FROST_STUN";
LEGACY_REMOVE_RUNE_CONFIRM = "即将移除%s符文。确定继续吗？";
LEGACY_REMOVE_RUNE_HINT = "|cff00ff00右键：移除符文|r";
LEGACY_STAT_DESC = 
{
	[1] = { p0 = "|cff00ff00+%.2f%%|r武器命中率 (对抗%d级敌人)", p1 = "|cff00ff00+%.1f点|r物理防御", f0 = 50, f1 = 0.5, d0 = false, d1 = false },
	[2] = { p0 = "|cff00ff00+%.2f%%|r武器爆击率 (对抗%d级敌人)", p1 = "|cff00ff00+%.1f%%|r物理防御", f0 = 50, f1 = 0.25, d0 = false, d1 = true },
	[3] = { p0 = "|cff00ff00+%d点|r生命值", p1 = "|cff00ff00+%.2f%%|r原力效能", f0 = 5, f1 = 0.25, d0 = false, d1 = false },
	[4] = { p0 = "|cff00ff00+%.2f%%|r法术命中率 (对抗%d级敌人)", p1 = "|cff00ff00+%.1f%%|r魔法防御", f0 = 50, f1 = 0.25, d0 = false, d1 = true },
	[5] = { p0 = "|cff00ff00+%.2f%%|r法术爆击率 (对抗%d级敌人)", p1 = "|cff00ff00+%.1f点|r魔法防御", f0 = 50, f1 = 0.5, d0 = false, d1 = false },
};
LEGACY_ARMOR_REDUCTION = "|cff00ff00+%.2f%%|r物理伤害减免 (对抗%d级敌人)|r";
LEGACY_RES_REDUCTION = "|cff00ff00+%.2f%%|r%s伤害减免 (对抗%d级敌人)";
LEGACY_RESET_SPECIALTY_CONFIRM = "你即将重置战斗精通，将花费[魔法碎片]x%d。|n|n确定继续吗？";
LEGACY_ACTIVATION_KEY_ICON = "Interface\\Icons\\INV_MISC_CODEXOFXERRATH_NOCHAINS";
LEGACY_MARKET_BUFF_POSSESSED = "|cff00ff00%s|r";
LEGACY_MARKET_BUFF_NOT_POSSESSED = "|cffcccccc未获取|r";
LEGACY_MARKET_BUFF_BUY_CONFIRM = "你即将为账号添加%s的%s效果。|n花费: %d资金";
LEGACY_MARKET_BUFF_ADDTIME = "|cff00ff00添加%s|r";
LEGACY_MARKET_FETCH = "|cff00ff00获取|r";
LEGACY_STAT_TOOLTIP_FMT_D = "|cffffffff%s %s|r";
LEGACY_STAT_TEXT_FMT_D = "|cff666666(%.d)|r %d";
LEGACY_STAT_TEXT_FMT_F2 = "|cff666666(%.2f%%)|r %d";
LEGACY_STAT_WEAPON_SPEED_FMT = "|cff666666(%.1fDPS)|r %.2f";
LEGACY_STAT_NAME = 
{
	[LEGACY_STAT_STRENGTH] = { n = "力量", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_STRENGTH" },
	[LEGACY_STAT_AGILITY] = { n = "敏捷", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_AGILITY" },
	[LEGACY_STAT_STAMINA] = { n = "耐力", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_STAMINA" },
	[LEGACY_STAT_INTELLECT] = { n = "智力", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_INTELLECT" },
	[LEGACY_STAT_SPIRIT] = { n = "精神", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_SPIRIT" },
	[LEGACY_STAT_POWER_MELEE] = { n = "武器攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MELEE" },
	[LEGACY_STAT_POWER_RANGED] = { n = "远程攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_RANGED" },
	[LEGACY_STAT_POWER_HOLY] = { n = "神圣攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_HOLY", d = "+%.1f 自火焰攻击强度|n+%.1f 自自然攻击强度|n-%.1f 自奥术攻击强度|n-%.1f 自冰霜攻击强度|n-%.1f 自暗影攻击强度" },
	[LEGACY_STAT_POWER_FIRE] = { n = "火焰攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_FIRE", d = "+%.1f 自奥术攻击强度|n+%.1f 自神圣攻击强度|n-%.1f 自暗影攻击强度|n-%.1f 自自然攻击强度|n-%.1f 自冰霜攻击强度" },
	[LEGACY_STAT_POWER_NATURE] = { n = "自然攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_NATURE", d = "+%.1f 自神圣攻击强度|n+%.1f 自冰霜攻击强度|n-%.1f 自火焰攻击强度|n-%.1f 自暗影攻击强度|n-%.1f 自奥术攻击强度" },
	[LEGACY_STAT_POWER_FROST] = { n = "冰霜攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_FROST", d = "+%.1f 自自然攻击强度|n+%.1f 自暗影攻击强度|n-%.1f 自神圣攻击强度|n-%.1f 自奥术攻击强度|n-%.1f 自火焰攻击强度" },
	[LEGACY_STAT_POWER_SHADOW] = { n = "暗影攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_SHADOW", d = "+%.1f 自冰霜攻击强度|n+%.1f 自奥术攻击强度|n-%.1f 自自然攻击强度|n-%.1f 自火焰攻击强度|n-%.1f 自神圣攻击强度" },
	[LEGACY_STAT_POWER_ARCANE] = { n = "奥术攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_ARCANE", d = "+%.1f 自暗影攻击强度|n+%.1f 自火焰攻击强度|n-%.1f 自冰霜攻击强度|n-%.1f 自神圣攻击强度|n-%.1f 自自然攻击强度" },
	[LEGACY_STAT_POWER_HEALING] = { n = "治疗法术强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_HEALING" },
	[LEGACY_STAT_ARMOR] = { n = "物理防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\ARMOR" },
	[LEGACY_STAT_RES_HOLY] = { n = "神圣防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_HOLY" },
	[LEGACY_STAT_RES_FIRE] = { n = "火焰防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_FIRE" },
	[LEGACY_STAT_RES_NATURE] = { n = "自然防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_NATURE" },
	[LEGACY_STAT_RES_FROST] = { n = "冰霜防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_FROST" },
	[LEGACY_STAT_RES_SHADOW] = { n = "暗影防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_SHADOW" },
	[LEGACY_STAT_RES_ARCANE] = { n = "奥术防御强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RES_ARCANE" },
	[LEGACY_STAT_WEAPON_DAMAGE_BASE] = { n = "主手物理攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\MELEE_BASE" },
	[LEGACY_STAT_WEAPON_DAMAGE_OFF] = { n = "副手物理攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\MELEE_OFF" },
	[LEGACY_STAT_WEAPON_DAMAGE_RANGED] = { n = "远程物理攻击强度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\RANGED" },
	[LEGACY_STAT_WEAPON_SPEED_BASE] = { n = "主手物理攻击速度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\SPEED_MELEE_BASE" },
	[LEGACY_STAT_WEAPON_SPEED_OFF] = { n = "副手物理攻击速度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\SPEED_MELEE_OFF" },
	[LEGACY_STAT_WEAPON_SPEED_RANGED] = { n = "远程物理攻击速度", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\SPEED_RANGED" },
	[LEGACY_STAT_MANA_REGEN] = { n = "能量恢复速率(战斗)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_MANAREGEN" },
	[LEGACY_STAT_MANA_REGEN_RESTING] = { n = "能量恢复速率(修整)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\STAT_MANAREGEN" },
	[LEGACY_STAT_PARRY_CHANCE] = { n = "招架几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PARRY"},
	[LEGACY_STAT_DODGE_CHANCE] = { n = "躲闪几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_DODGE"},
	[LEGACY_STAT_BLOCK_CHANCE] = { n = "格挡几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_BLOCK"},
	[LEGACY_STAT_RESIST_CHANCE] = { n = "抵抗几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_RESIST"},
	[LEGACY_STAT_MELEE_CRIT_CHANCE] = { n = "近战爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MELEE"},
	[LEGACY_STAT_RANGED_CRIT_CHANCE] = { n = "远程爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_RANGED"},
	[LEGACY_STAT_HOLY_CRIT_CHANCE] = { n = "神圣爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_HOLY"},
	[LEGACY_STAT_FIRE_CRIT_CHANCE] = { n = "火焰爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_FIRE"},
	[LEGACY_STAT_NATURE_CRIT_CHANCE] = { n = "自然爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_NATURE"},
	[LEGACY_STAT_FROST_CRIT_CHANCE] = { n = "冰霜爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_FROST"},
	[LEGACY_STAT_SHADOW_CRIT_CHANCE] = { n = "暗影爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_SHADOW"},
	[LEGACY_STAT_ARCANE_CRIT_CHANCE] = { n = "奥术爆击几率", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_ARCANE"},
	[LEGACY_STAT_PHYSICAL_CRIT_POWER] = { n = "物理爆击强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PHYSICAL_CRIT", d = "+%d%%物理攻击爆击几率", b = "+%d%%物理攻击爆击伤害", p = 10 },
	[LEGACY_STAT_MAGICAL_CRIT_POWER] = { n = "魔法爆击强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MAGICAL_CRIT", d = "+%d%%魔法攻击爆击几率", b = "+%d%%魔法攻击爆击伤害", p = 10 },
	[LEGACY_STAT_PHYSICAL_HIT_POWER] = { n = "物理命中强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PHYSICAL_HIT", d = "+%d%%物理命中几率", b = "+%d%%物理攻击精准", p = 1 },
	[LEGACY_STAT_MAGICAL_HIT_POWER] = { n = "魔法命中强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MAGICAL_HIT", d = "+%d%%魔法命中几率", b = "+%d%%魔法攻击精准", p = 2 },
	[LEGACY_STAT_PHYSICAL_HASTE_POWER] = { n = "物理急速强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PHYSICAL_HASTE", d = "+%d%%武器攻击速度", b = "+%d%%几率物理攻击不触发公共冷却", p = 5 },
	[LEGACY_STAT_MAGICAL_HASTE_POWER] = { n = "魔法急速强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MAGICAL_HASTE", d = "+%d%%施法速度", b = "+%d%%几率于成功施法后令强度加倍", p = 5 },
	[LEGACY_STAT_PHYSICAL_CRIT_DEFENSE_POWER] = { n = "物理爆击防御强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PHYSICAL_CRIT_DEFENSE", d = "-%d%%承受物理爆击几率", b = "-%d%%承受物理爆击伤害", p = 10 },
	[LEGACY_STAT_MAGICAL_CRIT_DEFENSE_POWER] = { n = "魔法爆击防御强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MAGICAL_CRIT_DEFENSE", d = "-%d%%承受魔法爆击几率", b = "-%d%%承受魔法爆击伤害", p = 10 },
	[LEGACY_STAT_PHYSICAL_HIT_DEFENSE_POWER] = { n = "物理命中防御强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PHYSICAL_HIT_DEFENSE", d = "-%d%%被物理攻击命中几率", b = "+%d%%几率于被物理攻击命中后令强度加倍", p = 2 },
	[LEGACY_STAT_MAGICAL_HIT_DEFENSE_POWER] = { n = "魔法命中防御强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_MAGICAL_HIT_DEFENSE", d = "-%d%%被魔法攻击命中几率", b = "+%d%%几率于被魔法攻击命中后令强度加倍", p = 2 },
	[LEGACY_STAT_DODGE_POWER] = { n = "躲闪强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_DODGE", d = "-%d%%躲闪几率", b = "+%d%%移动速度于躲闪后", p = 6 },
	[LEGACY_STAT_PARRY_POWER] = { n = "招架强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_PARRY", d = "-%d%%招架几率", b = "+%d%%物理攻击速度于招架后", p = 6 },
	[LEGACY_STAT_BLOCK_POWER] = { n = "格挡强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_BLOCK", d = "-%d%%格挡几率", b = "+%d%%格挡的伤害反弹至攻击者", p = 20 },
	[LEGACY_STAT_RESIST_POWER] = { n = "魔法抵抗强度 %d (精通 %d)", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_RESIST", d = "-%d%%魔法抵抗几率", b = "+%d%%最大能量恢复于成功抵抗后", p = 2 },
	[LEGACY_STAT_CHAOS_POWER] = { n = "混乱能量 %d", i = "Interface\\Addons\\LegacyPanel\\Asset\\Image\\Icon\\POWER_SHADOW", d = "-%d%%魔法抵抗几率", b = "+%d%%最大能量恢复于成功抵抗后", p = 2 },
};

LEGACY_SKILL_NAME =
{
	[1] = { Name = "火焰", Icon = "Interface\\Icons\\INV_ELEMENTAL_MOTE_FIRE01", Class = "MAGE", Shade = "FD5E10" },
	[2] = { Name = "冰霜", Icon = "Interface\\Icons\\SPELL_FROST_FROSTBOLT", Class = "MAGE", Shade = "4CCAE9" },
	[3] = { Name = "奥术", Icon = "Interface\\Icons\\SPELL_ARCANE_BLAST", Class = "MAGE", Shade = "8F64E4" },
	[4] = { Name = "刺杀", Icon = "Interface\\Icons\\ABILITY_ROGUE_SEALFATE", Class = "ROGUE", Shade = "C41008" },
	[5] = { Name = "战斗", Icon = "Interface\\Icons\\ABILITY_ROGUE_TURNTHETABLES", Class = "ROGUE", Shade = "F3F24A" },
	[6] = { Name = "敏锐", Icon = "Interface\\Icons\\ABILITY_ROGUE_MASTEROFSUBTLETY", Class = "ROGUE", Shade = "7D0ABC" },
	[7] = { Name = "雷霆", Icon = "Interface\\Icons\\SHAMAN_TALENT_ELEMENTALBLAST", Class = "SHAMAN", Shade = "605DD2" },
	[8] = { Name = "风暴", Icon = "Interface\\Icons\\SPELL_SHAMAN_MAELSTROMWEAPON", Class = "SHAMAN", Shade = "3632B7" },
	[9] = { Name = "潮汐", Icon = "Interface\\Icons\\ABILITY_SHAMAN_ANCESTRALGUIDANCE", Class = "SHAMAN", Shade = "00AC87" },
	[10] = { Name = "武器", Icon = "Interface\\Icons\\ABILITY_WARRIOR_BLADESTORM", Class = "WARRIOR", Shade = "BA8475" },
	[11] = { Name = "狂暴", Icon = "Interface\\Icons\\ABILITY_WARRIOR_RAMPAGE", Class = "WARRIOR", Shade = "EC4726" },
	[12] = { Name = "防御", Icon = "Interface\\Icons\\ABILITY_WARRIOR_VIGILANCE", Class = "WARRIOR", Shade = "EDE9D0" },
	[13] = { Name = "黑暗", Icon = "Interface\\Icons\\SPELL_SHADOW_SUMMONVOIDWALKER", Class = "PRIEST", Shade = "474554" },
	[14] = { Name = "光明", Icon = "Interface\\Icons\\SPELL_HOLY_HOLYNOVA", Class = "PRIEST", Shade = "FFFFFF" },
	[15] = { Name = "戒律", Icon = "Interface\\Icons\\SPELL_HOLY_HOPEANDGRACE", Class = "PRIEST", Shade = "F2ECFF" },
	[16] = { Name = "射手", Icon = "Interface\\Icons\\ABILITY_HUNTER_BLINDINGSHOT", Class = "HUNTER", Shade = "90A875" },
	[17] = { Name = "兽王", Icon = "Interface\\Icons\\ABILITY_HUNTER_KILLCOMMAND", Class = "HUNTER", Shade = "AB6629" },
	[18] = { Name = "生存", Icon = "Interface\\Icons\\ABILITY_HUNTER_BEASTWITHIN", Class = "HUNTER", Shade = "96EE43" },
	[19] = { Name = "苦痛", Icon = "Interface\\Icons\\ABILITY_WARLOCK_BACKDRAFTGREEN", Class = "WARLOCK", Shade = "007E6D" },
	[20] = { Name = "恶魔", Icon = "Interface\\Icons\\ABILITY_WARLOCK_SHADOWFURYTGA", Class = "WARLOCK", Shade = "461376" },
	[21] = { Name = "毁灭", Icon = "Interface\\Icons\\ABILITY_WARLOCK_BANEOFHAVOC", Class = "WARLOCK", Shade = "AB3719" },
	[22] = { Name = "神圣", Icon = "Interface\\Icons\\SPELL_PALADIN_DIVINECIRCLE", Class = "PALADIN", Shade = "FFD45D" },
	[23] = { Name = "律令", Icon = "Interface\\Icons\\ABILITY_PALADIN_TOUCHEDBYLIGHT", Class = "PALADIN", Shade = "B8A6B3" },
	[24] = { Name = "惩戒", Icon = "Interface\\Icons\\SPELL_HOLY_RETRIBUTIONAURA", Class = "PALADIN", Shade = "F565D7" },
	[25] = { Name = "梦境", Icon = "Interface\\Icons\\ABILITY_DRUID_DREAMSTATE", Class = "DRUID", Shade = "65F5D5" },
	[26] = { Name = "平衡", Icon = "Interface\\Icons\\ABILITY_DRUID_NATURALPERFECTION", Class = "DRUID", Shade = "E4AAFF" },
	[27] = { Name = "野性", Icon = "Interface\\Icons\\ABILITY_DRUID_PREDATORYINSTINCTS", Class = "DRUID", Shade = "E0BF53" },
	[28] = { Name = "鲜血", Icon = "Interface\\Icons\\SPELL_DEATHKNIGHT_BLOODPRESENCE", Class = "DEATHKNIGHT", Shade = "FF0000" },
	[29] = { Name = "冰河", Icon = "Interface\\Icons\\ACHIEVEMENT_ZONE_ICECROWN_09", Class = "DEATHKNIGHT", Shade = "008BE4" },
	[30] = { Name = "瘟疫", Icon = "Interface\\Icons\\SPELL_DEATHKNIGHT_EMPOWERRUNEBLADE", Class = "DEATHKNIGHT", Shade = "009700" },
};

LEGACY_BUILD_NAME =
{
	[1] = { n = "1", i = "Interface\\Icons\\ABILITY_WINTERGRASP_RANK1" },
	[2] = { n = "1", i = "Interface\\Icons\\ABILITY_WINTERGRASP_RANK2" },
	[3] = { n = "1", i = "Interface\\Icons\\ABILITY_WINTERGRASP_RANK3" },
}

LEGACY_ORIGIN_MASTERY = "源始精通";
LEGACY_SKILL_MASTERY_RANK = "精通";
LEGACY_MEMORY_POINT_COST = "记忆点数";
LEGACY_SKILL_MASTERY_AVAILABLE = "可用精通点数";
LEGACY_SKILL_MASTERY_TOTAL = "获取的精通点数";
LEGACY_MEMORY_POINT_AVAILABLE = "可用修研点数";
LEGACY_MEMORY_POINT_TOTAL = "获取的修研点数";
LEGACY_MEMORY_POINT_COST_MOD = "技能强化消耗";
LEGACY_MEMORY_POINT_COST_SLOT = "技能栏位消耗";
LEGACY_MEMORY_POINT_COST_SPELL = "技能链接消耗";
LEGACY_SKILL_MASTERY_LEFT_CLICK_HINT = "左键: 选择";
LEGACY_SKILL_MASTERY_RIGHT_CLICK_HINT = "右键: 提升(%dMP)";
LEGACY_CLASS_SKILL_BONUS = "精通上限加成: ";

LEGACY_MEMORY_LEFT_CLICK_HINT = "|cff00ff00左键: 技能强化|r";
LEGACY_MEMORY_RIGHT_CLICK_HINT = "右键: 激活技能(%dTP)";
LEGACY_MEMORY_NOT_AVAILABLE = "|cff999999未记忆|r";
LEGACY_RUNE_NAME_T0 = "生命符文";
LEGACY_RUNE_NAME_T1 = "元素符文";
LEGACY_RUNE_NAME_T2 = "灵魂符文";
LEGACY_SPELL_SLOT_EMPTY = "技能槽(空)";
LEGACY_SPELL_SLOT_HINT = "激活已记忆的法术来获取更多技能";
LEGACY_SPELL_SLOT_SEALED = "技能槽(封印)";

LEGACY_SPELLMOD_NEXT_RANK = "下一等级: ";
LEGACY_SPELLMOD_COST = "花费: %dTP";
LEGACY_SPELLMOD_RIGHT_CLICK_HINT = "右键: 强化";
LEGACY_SPELLMOD_MAX_RANK_EXCEEDED = "已达到最高强化等级";

LEGACY_ACTIVATE_CLASS_SPELL = "激活技能：%s|n花费: %dTP";
LEGACY_REMOVE_ACTIVE_SPELL_RIGHT_CLICK_HINT = "右键: 移除技能";
LEGACY_REMOVE_CLASS_SPELL = "移除技能: %s并返还修研点数消耗。|n花费: %d金币";

LEGACY_LEARN_CLASS_SKILL = "|cff%s[%s]|r精通将由%d级提升至%d级|n花费: %dMP";
LEGACY_CLASS_SKILL_BONUS_RANK = "精通";
LEGACY_COMPLETED = "(已完成)";
LEGACY_INCOMPLETED = "(未完成)";
LEGACY_CLASS_SPELL_ORIGIN = "源始";
LEGACY_EMPTY_TRANSMOG_SLOT = "无幻化";
LEGACY_CLICK = "点击:";
LEGACY_EXPAND_TRANSMOG_COLLECTION = "查看收藏";
LEGACY_TRANSMOG_COLLECTED = "已收藏";
LEGACY_TRANSMOG_NOT_COLLECTED = "未收藏";
LEGACY_TRANSMOG_COLLECT_RIGHT_CLICK_HINT = "右键: 收藏";
LEGACY_TRANSMOG_COLLECT_COST_HINT = "花费: %d资金";
LEGACY_ACTIVATED_TRANSMOG = "激活中的幻化";
LEGACY_ACTIVATE_TRANSMOG_RIGHT_CLICK_HINT = "右键: 激活幻化";
LEGACY_TRANSMOG_PREVIEW_LEFT_CLICK_HINT = "左键: 预览";
LEGACY_COLLECT_TRANSMOG = "将%s加入你的幻化收藏。|n花费: %d资金";
LEGACY_RUNE_SOCKETED = "已激活符文";
LEGACY_TRANSMOG_SOURCE_COLLECTED = "已收藏幻化源";
LEGACY_EXPAND_TRANSMOG_COLLECTION_CLICK_HINT = "点击: 查看收藏";
LEGACY_CHRONO_POINT_AVAILABLE = "可用时空点数";
LEGACY_CHRONO_POINT_CAP = "时空点数上限";
LEGACY_CHRONO_POINT_HINT = "在到达60级前，只要处于休息状态即可无成本地重置技能精通。到达60级之后，这一行为还将花费一定数量的金币——每重置1点精通将消耗1金币。你的已激活技能皆需要处于冷却完毕状态才能进行重置。|n|n重置精通将移除所有已激活的技能。";
LEGACY_CHRONO_POINT_CLEAR_HINT = "右键: 重置技能精通";
LEGACY_CHRONO_POINT_CLEAR_HINT_RIGHT = "花费: %d金币";

LEGACY_ATTACK_SPEED_MAINHAND = "主手攻击速度 %.2f";
LEGACY_ATTACK_SPEED_OFFHAND = "副手攻击速度 %.2f";
LEGACY_ATTACK_SPEED_RANGED = "远程攻击速度 %.2f";
LEGACY_CRIT_CHANCE = "%.2f%% 爆击率";
LEGACY_POWER_REGEN_RATE = "休息状态能量恢复速率 %.1f/秒|n战斗状态能量恢复速率 %.1f/秒";
LEGACY_MEMORIZE_SPELL_PTR = "|cff00ff00右键: 记忆技能(PTR)|r";
LEGACY_RESET_CLASSSKILL = "你即将重置技能精通和已激活的技能，需要花费%d金币。|r确定继续吗？";
LEGACY_PRICELESS = "无价";

LEGACY_REP_SPELL_RIGHT_CLICK_HINT = "右键: 获取";
LEGACY_OWNED = "已获取";
LEGACY_NOT_OWNED = "未获取";
LEGACY_REQUIRE_REPUTATION_POINT = "%d 贡献值";
LEGACY_FETCH_REPUTATION_ITEM_CONFIRM = "将%d点贡献值兑换为%s。确定继续吗？";
LEGACY_FETCH_REPUTATION_SPELL_CONFIRM = "将%d点贡献值兑换为%s（账号通用）。确定继续吗？";