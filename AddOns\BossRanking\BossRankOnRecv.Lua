BossRankColor = {}
BossRankData = {}
BossRankMyData = {}
BossName = nil;
BossRankMinDamageLimitVal = nil;
TotalNumber = nil;

function BossRankSplit(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end

local function BossRankEvent(self, event, h, msg, classtype, sender)
	if event == "CHAT_MSG_ADDON" then
		if h == "SERVER_RANK_ALLDATA" then	
			local list = BossRankSplit(msg,"#")
			local rank,damage,rewDes,plrname,preDamage,plrClass = tonumber(list[1]),tonumber(list[2]),list[3],list[4],tonumber(list[5]),tonumber(list[6]);
			if BossRankData[rank] == nil then
				BossRankData[rank] = {};
			end
			BossRankData[rank].damage = damage;
			BossRankData[rank].name = plrname;
			BossRankData[rank].rewDes = rewDes;
			BossRankData[rank].preDamage = preDamage;
			BossRankData[rank].plrClass = plrClass;
			-- print("player is name = "..plrname.." ;Is Rank = "..rank.." ;Is Damage = "..damage.." ; RewItem = "..rewDes.." ; Pre is"..preDamage.." ;Class = "..plrClass)
		end
		
		if h == "SERVER_RANK_MYDATA" then
			local list = BossRankSplit(msg,"#")
			local rank,damage,rewDes,plrname,preDamage,plrClass,guid = tonumber(list[1]),tonumber(list[2]),list[3],list[4],tonumber(list[5]),tonumber(list[6]),tonumber(list[7]);
			-- print("player is name = "..plrname.." ;Is Rank = "..rank.." ;Is Damage = "..damage.." ; RewItem = "..rewDes.." ; Pre is"..preDamage.." ; guid ="..guid)
			if BossRankMyData[guid] == nil then
				BossRankMyData[guid] = {};
			end
			BossRankMyData[guid].rank = rank;
			BossRankMyData[guid].damage = damage;
			BossRankMyData[guid].rewDes = rewDes;
			BossRankMyData[guid].name = plrname;
			BossRankMyData[guid].preDamage = preDamage;
			BossRankMyData[guid].plrClass = plrClass
			-- print("player is name = "..BossRankMyData[guid].name.." ;Is Rank = "..BossRankMyData[guid].preDamage.." ;Is Damage = "..BossRankMyData[guid].damage.." ; RewItem = "..BossRankMyData[guid].rewDes.." ; Pre is"..BossRankMyData[guid].preDamage.." ; guid ="..guid)
			-- print("msg = "..msg)
		end
		
		if h == "SERVER_RANK_NOW_BOSSNAME" then
			BossName = msg;
		end
		
		if h == "SERVER_RANK_START" and msg == "OPEN_UI" then
			BossRank.mainView:Show();
		end
		
		if h == "SERVER_RANK_END" and msg == "CLOSE_UI" then
			BossRank.mainView:Hide();
		end
		
		if h == "SERVER_RANK_CLEAR" and msg == "ALL_DATA" then
			BossRankMyData = nil
			BossRankMyData = {}
			for i=1,10 do
				BossRankData[i] = nil;
			end
		end
		
		if h == "SERVER_RANK_MINDAMAGE" then
			BossRankMinDamageLimitVal = msg;
		end
		
		if h == "SERVER_TOTAL_NUMBER" then
			TotalNumber = msg;
		end
	end
end


local BossRankReceiveMsg = CreateFrame("Frame")
BossRankReceiveMsg:RegisterEvent("CHAT_MSG_ADDON")
BossRankReceiveMsg:SetScript("OnEvent", BossRankEvent)

local BossRankLoad = CreateFrame("Frame")
BossRankLoad:RegisterEvent("PLAYER_LOGIN")
BossRankLoad:SetScript("OnEvent", function() SendAddonMessage("CLIENT_BOSSRANK_REQDATA","","GUILD", UnitName("player")) end)