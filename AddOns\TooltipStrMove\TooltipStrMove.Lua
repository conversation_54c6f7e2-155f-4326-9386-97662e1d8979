local stat = {}
stat[0] =  "防御等级提高+%d+"
stat[1] =  "使你的躲闪等级提高+%d+"
stat[2] =  "使你的招架等级提高+%d+"
stat[3] =  "使你的盾牌格挡等级提高+%d+"
stat[4] =  "近战命中等级提高+%d+"
stat[5] =  "远程命中等级提高+%d+"
stat[6] =  "法术命中等级提高+%d+"
stat[7] =  "近战爆击等级提高+%d+"
stat[8] =  "远程爆击等级提高+%d+"
stat[9] =  "法术爆击等级提高+%d+"
stat[10] =  "近战命中躲闪等级提高+%d+"
stat[11] =  "远程命中躲闪等级提高+%d+"
stat[12] =  "法术命中躲闪等级提高+%d+"
stat[13] =  "近战爆击躲闪等级提高+%d+"
stat[14] =  "远程爆击躲闪等级提高+%d+"
stat[15] =  "法术爆击躲闪等级提高+%d+"
stat[16] =  "近战急速等级提高+%d+"
stat[17] =  "远程急速等级提高+%d+"
stat[18] =  "法术急速等级提高+%d+"
stat[19] =  "命中等级提高+%d+"
stat[20] =  "爆击等级提高+%d+"
stat[21] =  "命中躲闪等级提高+%d+"
stat[22] =  "爆击躲闪等级提高+%d+"
stat[23] =  "韧性等级提高+%d+"
stat[24] =  "急速等级提高+%d+"
stat[25] =  "使你的精准等级提高+%d+"
stat[26] =  "攻击强度提高+%d+"
stat[27] =  "远程攻击强度提高+%d+"
stat[28] =  "法术和魔法效果的治疗量提高最多+%d+"
stat[29] =  "法术和魔法效果的伤害量提高最多+%d+"
stat[30] =  "每5秒恢复+%d+点法力值"
stat[31] =  "使你的护甲穿透等级提高+%d+"
stat[32] =  "法术强度提高+%d+"
stat[33] =  "每5秒恢复+%d+点生命值"
stat[34] =  "法术穿透提高+%d+"
stat[35] =  "使你的盾牌格挡值提高+%d+"

local cstat = {}
cstat[0] = " 防御等级"
cstat[1] = " 躲闪等级"
cstat[2] = " 招架等级"
cstat[3] = " 盾牌格挡等级"
cstat[4] = " 近战命中等级"
cstat[5] = " 远程命中等级"
cstat[6] = " 法术命中等级"
cstat[7] = " 近战爆击等级"
cstat[8] = " 远程爆击等级"
cstat[9] = " 法术爆击等级"
cstat[10] = " 近战命中躲闪等级"
cstat[11] = " 远程命中躲闪等级"
cstat[12] = " 法术命中躲闪等级"
cstat[13] = " 近战爆击躲闪等级"
cstat[14] = " 远程爆击躲闪等级"
cstat[15] = " 法术爆击躲闪等级"
cstat[16] = " 近战急速等级"
cstat[17] = " 远程急速等级"
cstat[18] = " 法术急速等级"
cstat[19] = " 命中等级"
cstat[20] = " 爆击等级"
cstat[21] = " 命中躲闪等级"
cstat[22] = " 爆击躲闪等级"
cstat[23] = " 韧性等级"
cstat[24] = " 急速等级"
cstat[25] = " 精准等级"
cstat[26] = " 攻击强度"
cstat[27] = " 远程攻击强度"
cstat[28] = " 法术和魔法效果的治疗量"
cstat[29] = " 法术和魔法效果的伤害量"
cstat[30] = " 点每5秒恢复魔法值"
cstat[31] = " 护甲穿透等级"
cstat[32] = " 法术强度提高"
cstat[33] = " 点每5秒恢复生命值"
cstat[34] = " 法术穿透提高"
cstat[35] = " 盾牌格挡值"

local pstat = {}

pstat[0] = "力量"
pstat[1] = "智力"
pstat[2] = "敏捷"
pstat[3] = "耐力"
pstat[4] = "精神"
pstat[5] = "生命值"
pstat[6] = "魔法值"

local function Source2Custom(str)
	
	local num = -1
	local msg = ""
	for k,v in pairs (stat) do
		if string.find(str,v) then
			num = num + 1 + k
			break
		end
	end
	if num ~= -1 then
		local a,b =	string.find(str,"[一-龥]+%d+")
		local c,d =	string.find(str,"[一-龥]+")
		local val = string.sub(str,d+1,b)
		msg = "|cFF33FF00".."+"..val..cstat[num]
	end
	
	return msg
end

local function IsSource(str)
	
	local bool = false
	for k,v in pairs (stat) do
		if string.find(str,v) then
			bool = true
			break
		end
	end
	
	return bool
end

local function IsPSource(str)
	
	local bool = false
	for k,v in pairs (pstat) do
		if string.find(str,v) then
			bool = true
			break
		end
	end
	
	return bool
end

local function movestr(self)
local _, itemLink = self:GetItem()
	if itemLink then
		local _,_,_,_, itemId, enchantId, jewelId1, jewelId2, jewelId3, jewelId4, suffixId, uniqueId,linkLevel,_ = string.find(itemLink,"|?c?f?f?(%x*)|?H?([^:]*):?(%d+):?(%d*):?(%d*):?(%d*):?(%d*):?(%d*):?(%-?%d*):?(%-?%d*):?(%d*):?(%d*):?(%-?%d*)|?h?%[?([^%[%]]*)%]?|?h?|?r?")

		local text = ""
		for i=1,self:NumLines() do
			local s = _G[self:GetName().."TextLeft"..i]:GetText()
			if s then 
				-- if string.find(s, "^|TInterface/ICONS/1008:30:30|t") and string.find(s,"法术穿透提高+%d+") then
				if string.find(s, "^|TInterface/ICONS/1008:30:30|t") and IsSource(s) then			
					local msg = Source2Custom(s)
					text = text..msg.."\n"			
				end
			end
		end
		
		local bool = false
		for i=1,self:NumLines() do
			local s = _G[self:GetName().."TextLeft"..i]:GetText()
			if s then 
				if string.find(s, "^[\+\d+]")  then
				bool = true;
				end
				
				-- if bool and string.find(s, "^[\+\d+]") == nil then
					-- if string.find(s,"插槽") then
					-- _G[self:GetName().."TextLeft"..(i-1)]:SetText(text)
					-- else
					-- _G[self:GetName().."TextLeft"..i]:SetText(text.."|cFFFFFFFF"..s)
					-- end
					-- bool = false
				-- end
				if bool and  string.find(s,"^[\+\d+]") == nil and string.find(s,"插槽") then
					local f = _G[self:GetName().."TextLeft"..(i-1)]:GetText()
					if string.find(f,"插槽") == nil then
					_G[self:GetName().."TextLeft"..(i-1)]:SetText(f.."\n"..text)
					bool = false
					end
				end
				
				if string.find(s, "^|TInterface/ICONS/1008:30:30|t") and IsSource(s) then
					_G[self:GetName().."TextLeft"..i]:SetText("")
				end
				
				if string.find(s,"买端联系QQ") then
					_G[self:GetName().."TextLeft"..i]:SetText("")
				end
			end	
		end
	end
	
end

GameTooltip:HookScript("OnShow", movestr);
ItemRefTooltip:HookScript("OnShow", movestr);