#include "ScriptMgr.h"
#include "Player.h"
#include "Config.h"
#include "Chat.h"


struct Laboratory_UI
{
    string str;
};


class Laboratory
{
public:
    static Laboratory * instance()
    {
        static Laboratory instance;
        return &instance;
    }

    void LoadUIInteractionData();
    void SendUIInteractionData(Player * player);

    typedef std::vector<string> UIvec;

private:
    UIvec _UIvec;

};

#define sLaboratory Laboratory::instance()
