 aaBoss = {};
 aaBoss.width = 400;
 aaBoss.height = 380;
 aaBoss.paihang = {};
 local GetSpellIcon = function(index) local _, _, icon, _, _, _ = GetSpellInfo(index);
 return icon;
 end;
 aaBoss.mainView = CreateFrame("Frame",nil,UIParent) do 
 aaBoss.mainView:SetFrameStrata("TOOLTIP");
 aaBoss.mainView:SetBackdrop ({bgFile = "Interface\\Tooltips\\UI-Tooltip-Background",edgeFile="Interface\\Tooltips\\UI-Tooltip-Border" ,edgeSize="16", tile = true});
 aaBoss.mainView:SetBackdropColor(0,0,0,0.5);
 aaBoss.mainView:SetWidth(aaBoss.width);
 aaBoss.mainView:SetHeight(aaBoss.height);
 aaBoss.mainView:SetPoint("RIGHT",0,0);
 aaBoss.mainView:SetMovable(1);
 aaBoss.mainView:EnableMouse();
 aaBoss.mainView:SetScript("OnMouseDown",function() this:StartMoving();
 end);
 aaBoss.mainView:SetScript("OnMouseUp",function() this:StopMovingOrSizing();
 end);
 aaBoss.mainView:Hide();
 end;
 function aaBoss:CreateCell(index,x,y,colorstr,iconstr,namestr,dmgstr,rewardstr) 
 local menuView = CreateFrame("Frame",nil,aaBoss.mainView) do 
 menuView:SetFrameStrata("TOOLTIP");
 menuView:SetWidth(aaBoss.width);
 menuView:SetHeight(24);
 menuView:SetPoint("TOPLEFT",aaBoss.mainView,'TOPLEFT',x,y);
 menuView:Show();
 end;
 menuView.noText = menuView:CreateFontString("menuView.noText", "OVERLAY", "GameFontNormal") do 
 menuView.noText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 16);
 menuView.noText:SetPoint("TOPLEFT", menuView, "TOPLEFT", 0,-2);
 menuView.noText:SetWidth(30);
 menuView.noText:SetHeight(20);
 menuView.noText:SetText("");
 end;
 menuView.iconView = CreateFrame('Button', nil, menuView, '') do 
 menuView.iconView:SetPoint('TOPLEFT', menuView, 'TOPLEFT', 20+10, -4);
 menuView.iconView:SetButtonState('NORMAL');
 menuView.iconView:IsEnabled();
 menuView.iconView:SetNormalTexture(iconstr);
 menuView.iconView:SetDisabledTexture(iconstr);
 menuView.iconView:SetHighlightTexture(iconstr);
 menuView.iconView:SetPushedTexture(iconstr);
 menuView.iconView:SetWidth(16);
 menuView.iconView:SetHeight(16);
 end;
 menuView.nameText = menuView:CreateFontString("menuView.nameText", "OVERLAY", "GameFontNormal") do 
 menuView.nameText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 16);
 menuView.nameText:SetPoint("TOPLEFT", menuView, "TOPLEFT", 20+10+18+2,-2);
 menuView.nameText:SetHeight(20);
 menuView.nameText:SetText("");
 end;
 menuView.dmgText = menuView:CreateFontString("menuView.dmgText", "OVERLAY", "GameFontNormal") do 
 menuView.dmgText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 16);
 menuView.dmgText:SetPoint("TOPLEFT", menuView, "TOPLEFT", aaBoss.width/3,-2);
 menuView.dmgText:SetWidth(aaBoss.width/3);
 menuView.dmgText:SetHeight(20);
 menuView.dmgText:SetText("");
 end;
 menuView.rewardText = menuView:CreateFontString("menuView.rewardText", "OVERLAY", "GameFontNormal") do 
 menuView.rewardText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 16);
 menuView.rewardText:SetPoint("TOPLEFT", menuView, "TOPLEFT", aaBoss.width/3*2,-2);
 menuView.rewardText:SetWidth(aaBoss.width/3);
 menuView.rewardText:SetHeight(20);
 menuView.rewardText:SetText("");
 end;
 return menuView;
 end;
 aaBoss.CancelButton = CreateFrame('Button', nil, aaBoss.mainView, 'UIPanelButtonTemplate') do 
 aaBoss.CancelButton:SetPoint('BOTTOMRIGHT', aaBoss.mainView, 'TOPRIGHT', 0, -30);
 aaBoss.CancelButton:SetSize(80, 30);
 aaBoss.CancelButton:SetText("关闭");
 aaBoss.CancelButton:SetScript('OnClick', function() aaBoss:hide();
 end);
 end;
 aaBoss.timeText = aaBoss.mainView:CreateFontString("timeText", "OVERLAY", "GameFontNormal") do 
 aaBoss.timeText:SetText(aaData.color_white.."挑战剩余时间：0分钟");
 aaBoss.timeText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.timeText:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", 10, -10);
 aaBoss.timeText:SetHeight(20);
 end;
 aaBoss.peopleText = aaBoss.mainView:CreateFontString("peopleText", "OVERLAY", "GameFontNormal") do 
 aaBoss.peopleText:SetText(aaData.color_white.."参与人数：0");
 aaBoss.peopleText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.peopleText:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", 10, -10-20-10);
 aaBoss.peopleText:SetHeight(20);
 end;
 aaBoss.dmgText = aaBoss.mainView:CreateFontString("dmgText", "OVERLAY", "GameFontNormal") do 
 aaBoss.dmgText:SetText(aaData.color_yellows.."你未造成伤害");
 aaBoss.dmgText:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 20);
 aaBoss.dmgText:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", 10, -10-20-10-20-10);
 aaBoss.dmgText:SetHeight(20);
 end;
 aaBoss.lineFrame = CreateFrame("Frame",nil,aaBoss.mainView) do 
 aaBoss.lineFrame:SetFrameStrata("TOOLTIP");
 aaBoss.lineFrame:SetBackdrop ({bgFile="Interface\\TutorialFrame\\TutorialFrameBackground"});
 aaBoss.lineFrame:SetBackdropColor(1, 1, 1, 1);
 aaBoss.lineFrame:SetWidth(aaBoss.width-8);
 aaBoss.lineFrame:SetHeight(2);
 aaBoss.lineFrame:SetPoint("TOPLEFT",4,-10-20-10-20-10-20-10);
 end;
 aaBoss.leveltitle = aaBoss.mainView:CreateFontString("aaBoss.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaBoss.leveltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.leveltitle:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", 0,-10-20-10-20-10-20-10-2);
 aaBoss.leveltitle:SetWidth(aaBoss.width/3);
 aaBoss.leveltitle:SetHeight(20);
 aaBoss.leveltitle:SetSpacing(5);
 aaBoss.leveltitle:SetText(aaData.color_green.."角色名");
 end;
 aaBoss.jltitle = aaBoss.mainView:CreateFontString("aaBoss.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaBoss.jltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.jltitle:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", aaBoss.width/3,-10-20-10-20-10-20-10-2);
 aaBoss.jltitle:SetWidth(aaBoss.width/3);
 aaBoss.jltitle:SetHeight(20);
 aaBoss.jltitle:SetSpacing(5);
 aaBoss.jltitle:SetText(aaData.color_green.."伤害值");
 end;
 aaBoss.zttitle = aaBoss.mainView:CreateFontString("aaBoss.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaBoss.zttitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.zttitle:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", aaBoss.width/3*2,-10-20-10-20-10-20-10-2);
 aaBoss.zttitle:SetWidth(aaBoss.width/3);
 aaBoss.zttitle:SetHeight(20);
 aaBoss.zttitle:SetSpacing(5);
 aaBoss.zttitle:SetText(aaData.color_green.."获得奖励");
 end;
 aaBoss.leveltitle = aaBoss.mainView:CreateFontString("aaBoss.leveltitle", "OVERLAY", "GameFontNormal") do 
 aaBoss.leveltitle:SetFont("Interface\\AddOns\\aaAddon\\Font\\aaFont.TTF", 18);
 aaBoss.leveltitle:SetPoint("TOPLEFT", aaBoss.mainView, "TOPLEFT", 0,-10-20-10-20-10-20-10-2);
 aaBoss.leveltitle:SetWidth(aaBoss.width/3);
 aaBoss.leveltitle:SetHeight(20);
 aaBoss.leveltitle:SetSpacing(5);
 aaBoss.leveltitle:SetText(aaData.color_green.."角色名");
 end;
 aaBoss.paihang[1] = aaBoss:CreateCell(1,0,-10-20-10-20-10-20-10-2-20,aaData.color_yellows,GetSpellIcon(0),"","","");
 aaBoss.paihang[2] = aaBoss:CreateCell(2,0,-10-20-10-20-10-20-10-2-20-24*1,aaData.color_blues,GetSpellIcon(0),"","","");
 aaBoss.paihang[3] = aaBoss:CreateCell(3,0,-10-20-10-20-10-20-10-2-20-24*2,aaData.color_green,GetSpellIcon(0),"","","");
 aaBoss.paihang[4] = aaBoss:CreateCell(4,0,-10-20-10-20-10-20-10-2-20-24*3,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[5] = aaBoss:CreateCell(5,0,-10-20-10-20-10-20-10-2-20-24*4,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[6] = aaBoss:CreateCell(6,0,-10-20-10-20-10-20-10-2-20-24*5,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[7] = aaBoss:CreateCell(7,0,-10-20-10-20-10-20-10-2-20-24*6,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[8] = aaBoss:CreateCell(8,0,-10-20-10-20-10-20-10-2-20-24*7,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[9] = aaBoss:CreateCell(9,0,-10-20-10-20-10-20-10-2-20-24*8,aaData.color_white,GetSpellIcon(0),"","","");
 aaBoss.paihang[10] = aaBoss:CreateCell(10,0,-10-20-10-20-10-20-10-2-20-24*9,aaData.color_white,GetSpellIcon(0),"","","");
 function aaBoss:reload() local rewardkey = nil;
 if aa_bosstitle then if aa_bosstitle[2] then aaBoss.timeText:SetText("挑战剩余时间："..aa_bosstitle[2].."分钟");
 else aaBoss.timeText:SetText("挑战剩余时间：0分钟");
 end;
 if aa_bosstitle[3] then aaBoss.peopleText:SetText("参与人数："..aa_bosstitle[3]);
 else aaBoss.peopleText:SetText("参与人数：0");
 end;
 aaBoss.dmgText:SetText("你未造成伤害");
 if aa_bosstitle[4] and aa_bosstitle[5] then if aa_bosstitle[4]+0 > 0 then aaBoss.dmgText:SetText("你造成的伤害："..aa_bosstitle[4].."("..aa_bosstitle[5].."%)");
 end;
 end;
 if aa_bosstitle[6] then rewardkey = aaData:toVectorInt2(aa_bosstitle[6]);
 end;
 end;
 if aa_bossdetail then for i=1,10 do 
 local color = aaData.color_white;
 if i == 1 then color = aaData.color_yellows;
 end;
 if i == 2 then color = aaData.color_blues;
 end;
 if i == 3 then color = aaData.color_green;
 end;
 if aa_bossdetail[i] then local class1 = aa_bossdetail[i][2];
 local iconint = aaData.class_spellicon[class1]+0;
 local icon = GetSpellIcon(iconint);
 aaBoss.paihang[i].noText:SetText(color..aa_bossdetail[i][1]..".");
 aaBoss.paihang[i].iconView:SetNormalTexture(icon);
 aaBoss.paihang[i].iconView:SetDisabledTexture(icon);
 aaBoss.paihang[i].iconView:SetHighlightTexture(icon);
 aaBoss.paihang[i].iconView:SetPushedTexture(icon);
 aaBoss.paihang[i].iconView:Show();
 aaBoss.paihang[i].nameText:SetText(color..aa_bossdetail[i][3]);
 aaBoss.paihang[i].dmgText:SetText(color..aa_bossdetail[i][4].."("..aa_bossdetail[i][5].."%)");
 aaBoss.paihang[i].rewardText:SetText("");
 if rewardkey ~= nil then if rewardkey[i] then local needid = rewardkey[i]+0;
 local need = aaData:getReward(needid);
 if need ~= "" then aaBoss.paihang[i].rewardText:SetText(need);
 end;
 end;
 end;
 else aaBoss.paihang[i].noText:SetText("");
 aaBoss.paihang[i].iconView:Hide();
 aaBoss.paihang[i].nameText:SetText("");
 aaBoss.paihang[i].dmgText:SetText("");
 aaBoss.paihang[i].rewardText:SetText("");
 end;
 end;
 end;
 end;
 function aaBoss:show() aaBoss.mainView:Show();
 aaBoss:reload();
 end;
 function aaBoss:hide() aaBoss.mainView:Hide();
 aaBoss:reload();
 aa_bossdetail = {};
 aa_bosstitle = {};
 end;
