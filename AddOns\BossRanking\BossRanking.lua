BossRank={};
BossRank.width=600;
BossRank.height=380;
BossRank.paihang={};

ClassIcon = {
"Interface\\icons\\INV_Sword_27",
"Interface\\icons\\INV_Hammer_01",
"Interface\\icons\\INV_Weapon_Bow_07",
"Interface\\icons\\INV_ThrowingKnife_04",
"Interface\\icons\\INV_Staff_30",
"Interface\\icons\\Spell_Deathknight_ClassIcon",
"Interface\\icons\\Spell_Nature_BloodLust",
"Interface\\icons\\INV_Staff_13",
"Interface\\icons\\Spell_Nature_FaerieFire",
"",
"Interface\\icons\\Ability_Druid_Maul"
}

function Getys(id)
	if id == 1 then
		return "|cFF000000";
	end
	if id == 2 then
		return "|cFF000000";
	end
end

 local GetSpellIcon = function(index) 
	local _, _, icon, _, _, _ = GetSpellInfo(index);
	return icon;
 end;

local GetClassIcon = function(index)
	if ClassIcon[index] ~= nil then
		return ClassIcon[index];
	else
		return "";
	end
end

BossRank.mainView=CreateFrame("Frame",nil,UIParent)
BossRank.mainView:SetFrameStrata("TOOLTIP");
BossRank.mainView:SetBackdrop({bgFile="Interface\\Tooltips\\UI-Tooltip-Background",edgeFile="Interface\\Tooltips\\UI-Tooltip-Border",edgeSize="16",tile=true});
BossRank.mainView:SetBackdropColor(0,0,0,0.5);
BossRank.mainView:SetWidth(BossRank.width);
BossRank.mainView:SetHeight(BossRank.height);
BossRank.mainView:SetPoint("RIGHT",0,0);
BossRank.mainView:SetMovable(1);
BossRank.mainView:EnableMouse();
BossRank.mainView:SetScript("OnMouseDown",function()this:StartMoving();end);
BossRank.mainView:SetScript("OnMouseUp",function()this:StopMovingOrSizing();end);
BossRank.mainView:Hide();


function BossRank:CreateCell(index,x,y,colorstr,iconstr,namestr,dmgstr,rewardstr)

local menuView=CreateFrame("Frame",nil,BossRank.mainView) 
menuView:SetFrameStrata("TOOLTIP");
menuView:SetWidth(BossRank.width);
menuView:SetHeight(24);
menuView:SetPoint("TOPLEFT",BossRank.mainView,'TOPLEFT',x,y);
menuView:Show();


menuView.noText=menuView:CreateFontString("menuView.noText","OVERLAY","GameFontNormal") 
menuView.noText:SetFont("Interface\\Fonts\\FZZY.TTF",16);
menuView.noText:SetPoint("TOPLEFT",menuView,"TOPLEFT",0,-2);
menuView.noText:SetWidth(30);
menuView.noText:SetHeight(20);
menuView.noText:SetText("");


menuView.iconView=CreateFrame('Button',nil,menuView,'')
menuView.iconView:SetPoint('TOPLEFT',menuView,'TOPLEFT',20+10,-4);
menuView.iconView:SetButtonState('NORMAL');
menuView.iconView:IsEnabled();
menuView.iconView:SetNormalTexture(iconstr);
menuView.iconView:SetDisabledTexture(iconstr);
menuView.iconView:SetHighlightTexture(iconstr);
menuView.iconView:SetPushedTexture(iconstr);
menuView.iconView:SetWidth(24);
menuView.iconView:SetHeight(24);


menuView.nameText=menuView:CreateFontString("menuView.nameText","OVERLAY","GameFontNormal")
menuView.nameText:SetFont("Interface\\Fonts\\FZZY.TTF",16);
menuView.nameText:SetPoint("TOPLEFT",menuView,"TOPLEFT",20+10+18+2,-2);
menuView.nameText:SetHeight(20);
menuView.nameText:SetText("");


menuView.dmgText=menuView:CreateFontString("menuView.dmgText","OVERLAY","GameFontNormal")
menuView.dmgText:SetFont("Interface\\Fonts\\FZZY.TTF",16);
menuView.dmgText:SetPoint("TOPLEFT",menuView,"TOPLEFT",BossRank.width/3,-2);
menuView.dmgText:SetWidth(BossRank.width/3);
menuView.dmgText:SetHeight(20);
menuView.dmgText:SetText("");


menuView.rewardText=menuView:CreateFontString("menuView.rewardText","OVERLAY","GameFontNormal")
menuView.rewardText:SetFont("Interface\\Fonts\\FZZY.TTF",16);
menuView.rewardText:SetPoint("TOPLEFT",menuView,"TOPLEFT",BossRank.width/3*2,-2);
menuView.rewardText:SetWidth(BossRank.width/3);
menuView.rewardText:SetHeight(20);
menuView.rewardText:SetText("");


-- menuView.Bar = CreateFrame("StatusBar", "menuView.Bar", menuView, nil)
-- menuView.Bar:SetSize(580, 16)
-- menuView.Bar:SetStatusBarTexture("Interface\\TargetingFrame\\UI-StatusBar")
-- menuView.Bar:SetStatusBarColor(51/255, 102/255, 255/255, 1)
-- menuView.Bar:SetPoint("CENTER")
-- menuView.Bar:SetMinMaxValues(0, 100)
-- menuView.Bar:SetFrameStrata("LOW")
return menuView;
end;

BossRank.CloseButton = CreateFrame('Button', nil, BossRank.mainView, 'UIPanelButtonTemplate') 
BossRank.CloseButton:SetPoint('BOTTOMRIGHT', BossRank.mainView, 'TOPRIGHT', 0, -30);
BossRank.CloseButton:SetSize(80, 30);
BossRank.CloseButton:SetText("关闭");
BossRank.CloseButton:SetScript('OnClick', function() BossRank.mainView:Hide();SendAddonMessage("CLIENT_BOSSRANK_UICLOSE","","GUILD", UnitName("player")); end);


BossRank.title = BossRank.mainView:CreateFontString("title", "OVERLAY", "GameFontNormal") 
BossRank.title:SetText("首领伤害排行");
BossRank.title:SetFont("Interface\\Fonts\\FZZY.TTF", 22);
BossRank.title:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", 220, -10);
BossRank.title:SetHeight(20);


BossRank.BossName = BossRank.mainView:CreateFontString("BossName", "OVERLAY", "GameFontNormal") 
BossRank.BossName:SetText("未有对某个首领造成伤害");
BossRank.BossName:SetFont("Interface\\Fonts\\FZZY.TTF", 18);
BossRank.BossName:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", 10, -10-20-10);
BossRank.BossName:SetHeight(20);

 

BossRank.dmgText = BossRank.mainView:CreateFontString("dmgText", "OVERLAY", "GameFontNormal") 
BossRank.dmgText:SetText("你未造成伤害");
BossRank.dmgText:SetFont("Interface\\Fonts\\FZZY.TTF", 20);
BossRank.dmgText:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", 10, -10-20-10-20-10);
BossRank.dmgText:SetHeight(20);


BossRank.lineFrame = CreateFrame("Frame",nil,BossRank.mainView) 
BossRank.lineFrame:SetFrameStrata("TOOLTIP");
BossRank.lineFrame:SetBackdrop ({bgFile="Interface\\TutorialFrame\\TutorialFrameBackground"});
BossRank.lineFrame:SetBackdropColor(1, 1, 1, 1);
BossRank.lineFrame:SetWidth(BossRank.width-8);
BossRank.lineFrame:SetHeight(2);
BossRank.lineFrame:SetPoint("TOPLEFT",4,-10-20-10-20-10-20-10);


BossRank.leveltitle = BossRank.mainView:CreateFontString("BossRank.leveltitle", "OVERLAY", "GameFontNormal") 
BossRank.leveltitle:SetFont("Interface\\Fonts\\FZZY.TTF", 18);
BossRank.leveltitle:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", 0,-10-20-10-20-10-20-10-2);
BossRank.leveltitle:SetWidth(BossRank.width/3);
BossRank.leveltitle:SetHeight(20);
BossRank.leveltitle:SetSpacing(5);
BossRank.leveltitle:SetText("|cFFFF0033角色名");


BossRank.jltitle = BossRank.mainView:CreateFontString("BossRank.leveltitle", "OVERLAY", "GameFontNormal") 
BossRank.jltitle:SetFont("Interface\\Fonts\\FZZY.TTF", 18);
BossRank.jltitle:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", BossRank.width/3,-10-20-10-20-10-20-10-2);
BossRank.jltitle:SetWidth(BossRank.width/3);
BossRank.jltitle:SetHeight(20);
BossRank.jltitle:SetSpacing(5);
BossRank.jltitle:SetText("伤害值");


BossRank.zttitle = BossRank.mainView:CreateFontString("BossRank.leveltitle", "OVERLAY", "GameFontNormal") 
BossRank.zttitle:SetFont("Interface\\Fonts\\FZZY.TTF", 18);
BossRank.zttitle:SetPoint("TOPLEFT", BossRank.mainView, "TOPLEFT", BossRank.width/3*2,-10-20-10-20-10-20-10-2);
BossRank.zttitle:SetWidth(BossRank.width/3);
BossRank.zttitle:SetHeight(20);
BossRank.zttitle:SetSpacing(5);
BossRank.zttitle:SetText("获得奖励");


BossRank.paihang[1] = BossRank:CreateCell(1,0,-10-20-10-20-10-20-10-2-20,nil,GetSpellIcon(0),"","","");
BossRank.paihang[2] = BossRank:CreateCell(2,0,-10-20-10-20-10-20-10-2-20-24*1,nil,GetSpellIcon(0),"","","");
BossRank.paihang[3] = BossRank:CreateCell(3,0,-10-20-10-20-10-20-10-2-20-24*2,nil,GetSpellIcon(0),"","","");
BossRank.paihang[4] = BossRank:CreateCell(4,0,-10-20-10-20-10-20-10-2-20-24*3,nil,GetSpellIcon(0),"","","");
BossRank.paihang[5] = BossRank:CreateCell(5,0,-10-20-10-20-10-20-10-2-20-24*4,nil,GetSpellIcon(0),"","","");
BossRank.paihang[6] = BossRank:CreateCell(6,0,-10-20-10-20-10-20-10-2-20-24*5,nil,GetSpellIcon(0),"","","");
BossRank.paihang[7] = BossRank:CreateCell(7,0,-10-20-10-20-10-20-10-2-20-24*6,nil,GetSpellIcon(0),"","","");
BossRank.paihang[8] = BossRank:CreateCell(8,0,-10-20-10-20-10-20-10-2-20-24*7,nil,GetSpellIcon(0),"","","");
BossRank.paihang[9] = BossRank:CreateCell(9,0,-10-20-10-20-10-20-10-2-20-24*8,nil,GetSpellIcon(0),"","","");
BossRank.paihang[10] = BossRank:CreateCell(10,0,-10-20-10-20-10-20-10-2-20-24*9,nil,GetSpellIcon(0),"","","");


function BossRank:reload() 
	
	if BossRank.mainView:IsShown() then
		-- print("dakaide")
	else
		return
	end
	
	if BossRankMyData ~= nil then
		local guid = tonumber(UnitGUID("player"));
		if next(BossRankMyData[guid]) ~= nil then
			BossRank.dmgText:SetText("你造成的伤害："..(BossRankMyData[guid].damage).."("..(BossRankMyData[guid].preDamage).."%)   当前排名：第"..BossRankMyData[guid].rank.."名".."  参与总人数："..TotalNumber.."人");
		end
	end
	
	if BossName ~= nil then
		BossRank.BossName:SetText("当前首领： "..BossName);
		else
		BossRank.BossName:SetText("")
	end
	
	for i=1,10 do
		if BossRankData[i] ~=nil then
			local classid = BossRankData[i].plrClass
			-- print("classid = "..classid)
			BossRank.paihang[i].nameText:SetText(" 第"..i.."名：   "..BossRankData[i].name);
			BossRank.paihang[i].dmgText:SetText(BossRankData[i].damage.."("..(BossRankData[i].preDamage).."%)");
			BossRank.paihang[i].rewardText:SetText("");
			BossRank.paihang[i].rewardText:SetText(BossRankData[i].rewDes);
			BossRank.paihang[i].iconView:SetNormalTexture(GetClassIcon(BossRankData[i].plrClass));
			BossRank.paihang[i].iconView:SetDisabledTexture(GetClassIcon(BossRankData[i].plrClass));
			BossRank.paihang[i].iconView:SetHighlightTexture(GetClassIcon(BossRankData[i].plrClass));
			BossRank.paihang[i].iconView:SetPushedTexture(GetClassIcon(BossRankData[i].plrClass));
			BossRank.paihang[i].iconView:Show();
			-- BossRank.paihang[i].Bar:SetValue(BossRankData[i].preDamage);
			else
			BossRank.paihang[i].nameText:SetText("");
			BossRank.paihang[i].dmgText:SetText("");
			BossRank.paihang[i].rewardText:SetText("");
			BossRank.paihang[i].iconView:Hide();
		end
	end
end


local BossRankOnUpdate = CreateFrame("Frame")
BossRankOnUpdate:SetScript("OnUpdate", function() BossRank:reload() end)

