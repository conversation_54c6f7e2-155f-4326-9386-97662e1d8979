local HSA

local SAB = {}

local SAButton = {}

local HSAB = {}

function SAGetItemInSlotText(button)
	if button.itemlink ~= nil then
	return ""
	else
		if button.Act then
			return "未镶嵌"
		else
			return "未解封\n点击解封"
		end
	end
end

function SASplit(szFullString, szSeparator)
	local nFindStartIndex = 1
	local nSplitIndex = 1
	local nSplitArray = {}
	while true do
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)
		   if not nFindLastIndex then
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))
			break
		   end
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)
		   nSplitIndex = nSplitIndex + 1
	end
	return nSplitArray
end

function SAPrepareScript(object, text, script, itemlink)
	  if text then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end

	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
	  end

	  if type(script) == "function" then
      object:SetScript("OnClick", script)
	elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end

function SAHandleButtonScript(Button,Type)

	if Type == "GLMENU" then
		if Button.ON:IsShown() then
			Button.ON:Hide()
			Button.OFF:Show()
			HSA:Show()
		else
			Button.ON:Show()
			Button.OFF:Hide()
			HSA:Hide()
		end
	end

	if Type == "CLOSE" then
		HSAB.ON:Show()
		HSAB.OFF:Hide()
		HSA:Hide()
	end
end

function CreateHSA(Name)
local F = CreateFrame("Frame",Name,UIParent)
F:SetSize(1024, 768);
F:SetPoint("CENTER",0,30)
local t = F:CreateTexture("$parent".."Background","ARTWORK")
t:SetTexture("Interface\\Icons\\Soul_array1024")
t:SetSize(1024, 768)
t:SetPoint("CENTER",0,0)
F:Hide()
return F
end

function CreateSAButton(Parent,Point,Name,x,y,num)
local B1 = CreateFrame("Button","$parent"..Name,Parent)
B1:SetSize(64, 64);
B1:SetPoint(Point,x,y)
if num < 10 then
	B1:SetNormalTexture("Interface\\Icons\\little_seal_blue")
elseif num >10 and num < 20 then
	B1:SetNormalTexture("Interface\\Icons\\little_seal_yellow")
elseif num > 20 and num < 30 then
	B1:SetNormalTexture("Interface\\Icons\\little_seal_red")
end
-- B1:SetNormalTexture("Interface\\Icons\\FWK")	--封印法阵图标
B1:SetHighlightTexture("Interface\\Icons\\YXGL")
B1:Show()
return B1
end

function CreateSAButtonX(Parent,Point,Name,x,y,num)
local B1 = CreateFrame("Button","$parent"..Name,Parent)
B1:SetSize(100, 100);
B1:SetPoint(Point,x,y)
if num == 10 then
	B1:SetNormalTexture("Interface\\Icons\\big_seal_blue")
elseif num == 20 then
	B1:SetNormalTexture("Interface\\Icons\\big_seal_yellow")
elseif num == 30 then
	B1:SetNormalTexture("Interface\\Icons\\big_seal_red")
end
-- B1:SetNormalTexture("Interface\\Icons\\FWK")	--封印法阵图标
B1:SetHighlightTexture("Interface\\Icons\\YXGL")
B1:Show()
return B1
end

function SACreateXXButton(Parent,Name)
local Button = CreateFrame("Button","$parent"..Name,Parent)
Button:SetSize(32,32)
Button:SetPoint("TOPRIGHT",0,0)
Button:SetFrameStrata("HIGH")
Button:SetNormalTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Up")
Button:SetPushedTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Down")
Button:SetDisabledTexture("Interface\\Buttons\\UI-Panel-MinimizeButton-Highlight","ADD")
Button:Show()
return Button
end

function SACreateMenuButton(Name,x,y,Tex)
local Button = CreateFrame("Button","$parent"..Name,UIParent)
Button:SetSize(64,64)
Button:SetPoint("BOTTOMRIGHT",x,y)
Button:SetNormalTexture(Tex)
Button:SetHighlightTexture("Interface\\BUTTONS\\CheckButtonHilight")
Button:Show()
return Button
end

function SACreateActTexs(Parent,Name,num)
if num < 10 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_little_blue")	--解封法阵图标
elseif num == 10 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_blue")	--解封法阵图标
elseif num > 10 and num < 20 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_little_yellow")	--解封法阵图标
elseif num == 20 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_yellow")	--解封法阵图标
elseif num > 20 and num < 30 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_little_red")	--解封法阵图标
elseif num == 30 then
	Parent:SetNormalTexture("Interface\\Icons\\unseal_red")	--解封法阵图标
end
-- Parent:SetNormalTexture("Interface\\Icons\\1008")	--解封法阵图标
Parent.Act = true
SAPrepareScript(Parent,SAGetItemInSlotText(Parent),SABScriptHandle,Parent.itemlink)
end

function SACreateItemTexs(Parent,Name,Tex)
local bt1 = Parent:CreateTexture("$parent"..Name,"TOOLTIP")
bt1:SetSize(30, 30)
bt1:SetPoint("CENTER",0,0)
SetPortraitToTexture(bt1,Tex)
local bt2 = Parent:CreateTexture("$parent".."quanquan","TOOLTIP")
bt2:SetSize(35, 35)
bt2:SetPoint("CENTER",0,0)
bt2:SetTexture("Interface\\Icons\\Circle")
SAPrepareScript(Parent,SAGetItemInSlotText(Parent),SABScriptHandle,Parent.itemlink)
return bt1
end

function SAGenerateSAButtons()
	local Lx,Ly,Rx,Ry,Bx,By = 115,-115,-115,-125,-110,50
	for i=1, 30 do
		if i < 10 then
			if i <= 3 then
				local x = Lx + (i - 1) * 70;
				local y = Ly;
				SAButton[i] = CreateSAButton(HSA,"TOPLEFT","SAButton"..i,x,y,i)
			end
			if i > 3 and i <= 6 then
				local x = Lx + 35 + (i - 4) * 70;
				local y = Ly - 70;
				SAButton[i] = CreateSAButton(HSA,"TOPLEFT","SAButton"..i,x,y,i)
			end
			if i > 6 and i <= 9 then
				local x = Lx + 70 + (i - 7) * 70;
				local y = Ly - 140;
				SAButton[i] = CreateSAButton(HSA,"TOPLEFT","SAButton"..i,x,y,i)
			end
		end

		if i == 10 then
			SAButton[i] = CreateSAButtonX(HSA,"TOPLEFT","SAButton"..i,375,-305,i)
		end

		if i < 20 and i > 10 then
			if i <= 13 then
				local x = Rx - (i - 11) * 70;
				local y = Ry;
				SAButton[i] = CreateSAButton(HSA,"TOPRIGHT","SAButton"..i,x,y,i)
			end

			if i > 13 and i <= 16 then
				local x = Rx - 35 - (i - 14) * 70;
				local y = Ry - 70;
				SAButton[i] = CreateSAButton(HSA,"TOPRIGHT","SAButton"..i,x,y,i)
			end

			if i > 16 and i <= 19 then
				local x = Rx - 70 - (i - 17) * 70;
				local y = Ry - 140;
				SAButton[i] = CreateSAButton(HSA,"TOPRIGHT","SAButton"..i,x,y,i)
			end
		end

		if i == 20 then
			SAButton[i] = CreateSAButtonX(HSA,"TOPRIGHT","SAButton"..i,-375,-305,i)
		end

		if i < 30 and i > 20 then
			if i <= 24 then
				local x = Bx + (i - 21) * 80;
				local y = By;
				SAButton[i] = CreateSAButton(HSA,"BOTTOM","SAButton"..i,x,y,i)
			end

			if i > 24 and i <= 27 then
				local x = Bx + 40 + (i - 25) * 80;
				local y = By + 70;
				SAButton[i] = CreateSAButton(HSA,"BOTTOM","SAButton"..i,x,y,i)
			end

			if i > 27 and i <= 29 then
				local x = Bx + 80 + (i - 28) * 80;
				local y = By + 140;
				SAButton[i] = CreateSAButton(HSA,"BOTTOM","SAButton"..i,x,y,i)
			end
		end

		if i == 30 then
			SAButton[i] = CreateSAButtonX(HSA,"BOTTOM","SAButton"..i,5,265,i)
		end

		SAButton[i].ID = tostring(i)
		SAButton[i].Act = false
	end
end

function SAHandleMenuScript()
HSA.CLOSE = SACreateXXButton(HSA,"CLOSE")
HSAB.ON = SACreateMenuButton("HSABON",-310,100,"Interface\\ICONS\\matrix")		--打开按钮图标
HSAB.OFF = SACreateMenuButton("HSABOFF",-310,100,"Interface\\ICONS\\Hide")	--关闭按钮图标
HSAB.OFF:Hide()
SAPrepareScript(HSAB.ON,"卡牌收集",function() SAHandleButtonScript(HSAB,"GLMENU") end)
SAPrepareScript(HSAB.OFF,"关闭界面",function() SAHandleButtonScript(HSAB,"GLMENU") end)
SAPrepareScript(HSA.CLOSE,"关闭界面",function() SAHandleButtonScript(HSA,"CLOSE") end)
end

function SABScriptHandle(self,button)
	if button == "LeftButton" then
		local ctype,itemid,itemlink = GetCursorInfo()
		if ctype == "item" then
			if self.Act then
				if not self.itemlink then
					local msg = tostring(self.ID).."#"..itemid;
					SendAddonMessage("CSA_ITEM_TO_SLOT",msg,"GUILD", UnitName("player"))
				else
					print("请先把法阵中的灵石取出,才可放入新的灵石!")
				end
			else
				print("未激活状态不能镶嵌")
			end
		end

		if ctype == nil then
			local msg = tostring(self.ID);
			if not self.Act then
				SendAddonMessage("CSA_BUY_SLOT_ITEM",msg,"GUILD",UnitName("player"))
			else
				if self.itemid then
					SendAddonMessage("CSA_REMOVE_SLOT_ITEM",msg,"GUILD",UnitName("player"))
				end
			end
		end
	end
end

function SABScripts()
	for i = 1,30 do
		SAPrepareScript(SAButton[i],SAGetItemInSlotText(SAButton[i]),SABScriptHandle,SAButton[i].itemlink)
	end
end

function SASendMutualOp()
	SendAddonMessage("CSA_DATA_ALL","","GUILD", UnitName("player"))
end

function SAEvent(self, event, h, msg, classtype, sender)
	if event == "CHAT_MSG_ADDON" then
		if h == "SSA_INIT_DATA" then
			local list = SASplit(msg,"#")
			local id = tonumber(list[1]);
			local act = tonumber(list[2]);
			if act == 1 then
				SACreateActTexs(SAButton[id],"ActTex",id);
				local itemid = tonumber(list[3]);
				if itemid and itemid > 0 then
					SAButton[id].itemid = itemid;
					local link;
					link = list[4];
					if link then
						SAButton[id].itemlink = link
						SAButton[id].tex = SACreateItemTexs(SAButton[id],"ItemTex",GetItemIcon(itemid));
					end
				end
			end
		end

		if h == "SSA_XQ_DATA" then
			local list = SASplit(msg,"#")
			local id = tonumber(list[1]);
			local act = tonumber(list[2]);
			local itemid = tonumber(list[3]);
			local link = list[4];
			SAButton[id].itemid = itemid;
			SAButton[id].itemlink = link;
			SAButton[id].Act = true
			SAButton[id].tex = SACreateItemTexs(SAButton[id],"ItemTex",GetItemIcon(itemid));
		end

		if h == "SSA_CX_DATA" then
			local list = SASplit(msg,"#")
			local id = tonumber(list[1]);
			local pname = SAButton[id]:GetName();
			SAButton[id].itemlink = nil;
			SAButton[id].itemid = nil;
			_G[pname.."quanquan"]:Hide()
			_G[pname.."quanquan"] = nil
			SAButton[id].tex:Hide()
			SAButton[id].tex = nil
			_G[pname.."ItemTex"] = nil
			SAPrepareScript(SAButton[id],SAGetItemInSlotText(SAButton[id]),SABScriptHandle,SAButton[id].itemlink)
		end

		if h == "SSA_BUY_DATA" then
			local list = SASplit(msg,"#")
			local id = tonumber(list[1]);
			local act = tonumber(list[2]);
			if act == 1 then
				SAButton[id].Act = true;
				SACreateActTexs(SAButton[id],"ActTex",id);
				print("第 "..id.." 号法阵已经成功激活");
			end
		end
	end
end

HSA = CreateHSA("HSA");

SAHandleMenuScript()

SAGenerateSAButtons()

SABScripts()

local MsgReceiversSA = CreateFrame("Frame")
MsgReceiversSA:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiversSA:SetScript("OnEvent", SAEvent)

local SALoad = CreateFrame("Frame")
SALoad:RegisterEvent("PLAYER_LOGIN")
SALoad:SetScript("OnEvent", SASendMutualOp)
