--商城
local Supermarket = {}  

--Texture素材
local T = 
{
	ParentFrameBackgroundLeft	= "Interface\\ICONS\\progress_frame_left",
	ParentFrameBackgroundRight	= "Interface\\ICONS\\progress_frame_right",
	ParentFrameTitle			= "Interface\\ICONS\\bt",
	SubFrameBackground			= "Interface\\ICONS\\yuanjiaolan",
	TabClassFrameBackground		= "Interface\\ICONS\\LineTab1",
	ItemClassButton				= "Interface\\ICONS\\_button_h",
	FontBackground				= "Interface\\ICONS\\languangditu",
	ButtonHighlighLight1		= "Interface\\BUTTONS\\CheckButtonHilight",
	ButtonHighlighLight2		= "Interface\\ICONS\\button_h2",
	ButtonHighlighLight3		= "Interface\\BUTTONS\\UI-Listbox-Highlight",
	Font						= "Interface\\Fonts\\FRIZQT_.TTF",
	SupermarketButton			= "Interface\\ICONS\\shangchengon",
	SupermarketOffButton		= "Interface\\ICONS\\shangchengoff",
	JiFen						= "Interface\\ICONS\\jf1",
	JinBi						= "Interface\\ICONS\\jinbi",
	Vip							= "Interface\\ICONS\\Vip",
	ItemExchange				= "Interface\\ICONS\\wpdh",
	AlliancePVP					= "Interface\\ICONS\\lmry",
	HordePVP					= "Interface\\ICONS\\blry",
	AlliancePVPPoint			= "Interface\\ICONS\\lmryd",
	HordePVPPoint				= "Interface\\ICONS\\blryd",
	ArenaPVP					= "Interface\\ICONS\\jjc",
	ArenaPVPPoint				= "Interface\\ICONS\\jjd",
	EXP							= "Interface\\ICONS\\bigxp",
	XP							= "Interface\\ICONS\\xp"
}

--接收服务端数据Table key = itemid(ItemId除外)
local ItemId,ItemLink,TabClass,ItemClass,ItemSubClass,ItemPrice,ItemReq,ItemReqVal,ReqItem,ReqItemVal,ReqItemLink = {},{},{},{},{},{},{},{},{},{},{}

--接收服务端数据Table Key = ++i
local TabNum,ItemNum,ItemSubNum = {},{},{}

--商城元素非ButtonTable	
local SubFrame,ItemSellInfo,ItemName,ReqVipInfo,FontBg,ItemSubPos,ItemGPS,ItemPosition = {},{},{},{},{},{},{},{}

--商城元素ButtonTable (ItemInfoButton是显示ItemInfo,无OnClic效果 只有OnEnter及OnLeave)
local ItemInfoButton,ItemSellButton,TabClassButton,ItemClassButton,ItemSubClassButton,ReqItemButton = {},{},{},{},{},{}

--商城其余Button非Table	ItemPos记录物品在SubFrame中的位置
--SupermarketButton,HomeButton,UpButton,DownButton,CloseButton
local TabFrame,ItemPos,PageElement = {},{},{}

--页面计数 都是从1开始计数
C1 = 1

--


function Event(self, event, h, msg, classtype, sender)	
    if event == "CHAT_MSG_ADDON" and sender == UnitName("player") then
	
		if h == "RL" and msg == "ALL" then
			ClearAllData()
		end
	
        --local NewMsg = string.format("消息头 = %s 消息 = %s", h, msg)
		if h == "商城物品信息" then		
		
		local list = Split(msg,"；")
		--table.insert中key 不能为字符串只能为整型数字
		ItemId[C1]				= list[1]			--存Itemid Key = ++i
		ItemLink[list[1]] 		= list[2]			--存ItemLink Key = ItemId
		TabClass[list[1]] 		= list[3]			--存TabClass Key = ItemId
		ItemClass[list[1]] 		= list[4]			--存物品类型"数字"以itemid(字符串)为key
		ItemSubClass[list[1]] 	= list[5]			--存物品子类型"数字"以itemid(字符串)为key	
		ItemPrice[list[1]] 		= list[6]			--存物品贩售价格"数字"以itemid(字符串)为key
		ItemReq[list[1]]		= list[7]			--存Req Key = ItemId
		ItemReqVal[list[1]]		= list[8]			--存ReqVal Key = ItemId
		C1 = C1 + 1
		
		
		if list[3] == "3" then
			--print(list[1])
			local s1, s2, s3, s4, s5, s6, s7, s8, s9, s10 = strsplit(":", string.match(list[9], "item[%-?%d:]+"))
			local id = tostring(s2)
			ReqItemLink[list[1]]	= list[9]
			ReqItemVal[list[1]]		= list[10]
			ReqItem[list[1]]		= id
		end
		
		--if list[1] == "50001" then
			--local s1, s2, s3, s4, s5, s6, s7, s8, s9, s10 = strsplit(":", string.match(list[2], "item[%-?%d:]+"))
			--local id = tonumber(s2)
			--print("拆分后的ItemId = "..id)
		--end

		end
		
		if h == "商城销售分类" then 
			TabNum = Split(msg,":")		--这里接收的是 1 or 2
		end
		
		if h == "商城物品类型" then
			ItemNum = Split(msg,":")	--这里接收的是销售分类*100 + 物品分类
		end
		
		if h == "商城物品子类型" then
			ItemSubNum = Split(msg,":")	--这里接收的是销售分类*1000 +物品分类*100 + 子分类
		end
    end
end


--分词器
function Split(szFullString, szSeparator)  
	local nFindStartIndex = 1  
	local nSplitIndex = 1  
	local nSplitArray = {}  
	while true do  
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)  
		   if not nFindLastIndex then  
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))  
			break  
		   end  
		   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)  
		   nFindStartIndex = nFindLastIndex + string.len(szSeparator)  
		   nSplitIndex = nSplitIndex + 1  
	end  
	return nSplitArray  
end 


--创建可移动商城主窗口 参数数据类型 待补
function Supermarket:CreateParentFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile)
	
local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
	f:SetSize(Length, Width)
	if InheritsFrame == nil then 
		f:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 32, insets = { left = 10, right = 10, top = 10, bottom = 10 }});
	end
	f:SetPoint(Point,Ofsx,Ofsy)
	--只有主窗口才能移动
	if ParentFrame == UIParent then
		f:RegisterForDrag("LeftButton")
		f:SetToplevel(true)
		f:SetClampedToScreen(true)
		f:SetMovable(true)
		f:EnableMouse(true)
		f:SetScript("OnDragStart", f.StartMoving)
		f:SetScript("OnHide", f.StopMovingOrSizing)
		f:SetScript("OnDragStop", f.StopMovingOrSizing)
	end
	if ParentFrame ~= UIParent then
		f:Show()
	else
		f:Hide()
	end
	return f
end


--创建依附商城主窗口的阵列窗口及其它窗口 参数数据类型 待补
function Supermarket:CreateSubFrame(FrameType,Name,ParentFrame,InheritsFrame,Length,Width,Point,Ofsx,Ofsy,BgFile,EdgeFile,IsSubFrame)
	local f = CreateFrame(FrameType,Name,ParentFrame,InheritsFrame)
		f:SetSize(Length, Width)
		f:SetBackdrop({ bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 12, insets = { left = 1, right = 1, top = 1, bottom = 1 }})
		
		if IsSubFrame == false then 
			f:SetPoint(Point,Ofsx,Ofsy)
		else
			if Point == "TOPRIGHT" then
			f:SetPoint(Point,-7.5-(Length-15)*Ofsx,-30-(Width-15)*Ofsy)	
			else 
				if Point == "TOPLEFT" then
				f:SetPoint(Point,40+(Length+8)*Ofsx,-25-(Width-7)*Ofsy)
				end
			end
		end
		
		if IsSubFrame == true then	--设置透明度
		f:SetBackdropColor(255/255,255/255,255/255,0.5)
		f:Hide()
		end
		return f;
end


--创建商城文字描述 参数数据类型 待补
function Supermarket:CreateFontString(Frame,Name,Inherits,Fonts,Fsize,Point,Ofsxm,Ofsym,Msg,rgb)
	local fs = Frame:CreateFontString(Name,Inherits)
		if Fonts == "" then 
			fs:SetFontObject(GameFontNormalLarge)
			else
			fs:SetFont(Fonts, Fsize)
		end
		
		if rgb ~= "" then
			local r,g,b = self:ColorPicker(strsplit(",",rgb))
			fs:SetTextColor(r,g,b)
		end	
		fs:SetPoint(Point,Ofsxm,Ofsym)
		fs:SetText(Msg)
		return fs;
end


--创建商城按钮 参数数据类型 待补
function Supermarket:CreateButton(Frame,Name,ParentFrame,InheritsFrame,Length,Width,Text,Point,Ofsxm,Ofsym,BgFile,EdgeFile,IsHideButton)
		local Button = CreateFrame(Frame, Name, ParentFrame, InheritsFrame)
		if InheritsFrame == nil then 
			Button:SetBackdrop({bgFile = BgFile,edgeFile = EdgeFile, tile = false, tileSize = 0, edgeSize = 22, insets = { left = 1, right = 1, top = 1, bottom = 1 }});
		end
		Button:SetSize(Length, Width)
		Button:SetPoint(Point, Ofsxm, Ofsym)
		Button:EnableMouse(true)
		if BgFile == T["ItemClassButton"] then
			Button:SetHighlightTexture(T["ButtonHighlighLight2"])
		elseif  InheritsFrame == "UIPanelButtonTemplate2" then
			Button:SetHighlightTexture(T["ButtonHighlighLight3"])
		else
			Button:SetHighlightTexture(T["ButtonHighlighLight1"])
		end
		local ButtonFontStr  = Button:CreateFontString("ButtonFontStr")
		ButtonFontStr:SetFont(T["Font"], 15)	--需要设置字体
		ButtonFontStr:SetAllPoints(Button)
		ButtonFontStr:SetText(Text)
		--if normaltexture ~= "" then
			--Button:SetNormalTexture(normaltexture)
		--end
		
		if IsHideButton == true then
			Button:Hide()
		end
		
		return Button;
end


--处理商城按钮事件
function Supermarket:PrepareScript(object, text, script, itemlink)
	  if text then	--如果text存在 那么显示在对象的右边并对齐(ANCHOR_RIGHT)
        object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetText(text); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
      end
	  
	  if itemlink then
		object:SetScript("OnEnter", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:SetHyperlink(itemlink); GameTooltip:Show() end)
        object:SetScript("OnLeave", function() GameTooltip:SetOwner(object, "ANCHOR_RIGHT"); GameTooltip:Hide() end)
	  end
	  
	  if type(script) == "function" then	--type(script) 返回script的类型 以字符串形式
      object:SetScript("OnClick", script)
    elseif type(script) == "table" then
      for k,v in pairs(script) do
        object:SetScript(unpack(v))
      end
    end
end


--
function Supermarket:HandleButtonScript(Button,Type)
	
	--商城按钮
	if Type == "SupermarketButton" then
	
		if Button == SupermarketButton then		--当按下的按钮是商城按钮时
		
			if self:IsId() == false then		--当没有接收到服务端发送的数据时
			
				--请求发服务端再发一次数据
				SendAddonMessage("ADDON_SupermarketData","All","GUILD", UnitName("player"))
				
				--[[

				self:ShowTapFrame()				--初始化销售分类外框
				
				self:ShowClassButton()			--初始化分类按钮
				
				if IsCreateSubFrame == false then
				
				self:ShowSubFrame()				--初始化所有阵列框架
				
				self:ShowSubFrameElement()		--初始化所有阵列框架元素
				
				IsCreateSubFrame = true
				
				end
				
				self:InitPage()					--初始化SubFrame页面
				
				local v = TabNum[1] 
				
				self:ShowButton(v,"Item")		--显示第一Tab第一item按钮
				
				SupermarketFrame:Show()			--展示主窗口
				
				SupermarketButton:Hide()		--隐藏本身按钮
				
				SupermarketOffButton:Show()		--显示灰色商城按钮
				
				]]--	
			else	--顺序不能错
				
				if IsCreate == false then

				self:ShowTapFrame()				--初始化销售分类外框

				self:ShowClassButton()			--初始化分类按钮
								
				self:ShowSubFrame()				--初始化所有阵列框架

				self:ShowSubFrameElement()		--初始化所有阵列框架元素

				IsCreate = true					--关闭创建SubFrame & ClassButton & TabFrame

				end
				
				
				local v = TabNum[1]

				TabClassButton[v]:LockHighlight()
				
				self:ShowButton(v,"Item")		--显示第一Tab第一item按钮
				
				self:InitPage()					--初始化SubFrame页面
				
				SupermarketFrame:Show()			--展示主窗口
				
				SupermarketButton:Hide()		--隐藏本身按钮
				
				SupermarketOffButton:Show()		--显示灰色商城按钮
				
			end
		end
	end
	
	--首页按钮
	if Type == "HomeButton" then
		if Button == HomeButton then
		
			local v1,v2,v3,p = self:GetPage()		--获取当前页面参数
			
			self:HideSubFramePage(v1,v2,v3,p)		--隐藏当前页面
			
			self:ShowSubFramePage(v1,v2,v3,1)		--显示新的页面
		
		end
	end
	
	--上一页按钮
	if Type == "UpButton" then
		if Button == UpButton then
			
			local v1,v2,v3,p = self:GetPage()		--获取当前页面参数
			
			self:HideSubFramePage(v1,v2,v3,p)		--隐藏当前页面
			
			local page = self:DecreasePage(p)		--递减页面数
			
			self:ShowSubFramePage(v1,v2,v3,page)	--显示新的页面
		
		end
	end
	
	--下一页按钮
	if Type == "DownButton" then
		if Button == DownButton then
			
			local v1,v2,v3,p = self:GetPage()		--获取当前页面参数
			
			self:HideSubFramePage(v1,v2,v3,p)		--隐藏当前页面
			
			--self:HideAllSubFrame()				--隐藏所有SubFrame
			
			local page = self:IncreasePage(p)		--递增页面数
			
			self:ShowSubFramePage(v1,v2,v3,page)	--显示新的页面
			
		end
	end
	
	--叉叉按钮
	if Type == "CloseButton" then
		if Button == CloseButton then
		
			self:HideAllSubFrame()			--隐藏所有SubFrame
				
			self:HideAllButton("Item")		--隐藏所有分类按钮
			
			self:HideAllButton("ItemSub")	--隐藏所有子分类按钮
			
			SupermarketFrame:Hide()			--隐藏主窗口
			
			SupermarketButton:Show()		--显示商城按钮
			
			SupermarketOffButton:Hide()		--隐藏本身按钮
		
		end
	end
	
	--灰色商城按钮
	if Type == "SupermarketOffButton" then
		if Button == SupermarketOffButton then
				
				self:HideAllSubFrame()			--隐藏所有SubFrame
				
				self:HideAllButton("Item")		--隐藏所有分类按钮
				
				self:HideAllButton("ItemSub")	--隐藏所有子分类按钮
				
				SupermarketFrame:Hide()			--隐藏主窗口
				
				SupermarketButton:Show()		--显示商城按钮
				
				SupermarketOffButton:Hide()		--隐藏本身按钮
				
		end
	end
	
	--销售分类按钮
	if Type == "TabClassButton" then

		for k,v in pairs(TabNum) do
			if Button == TabClassButton[v] then

				TabClassButton[v]:LockHighlight()	--锁定高亮

				self:RestoreAllButtonPos()		--恢复所有ItemButton原有位置

				self:HideAllButton("Item")		--隐藏所有分类按钮

				self:HideAllButton("ItemSub")	--隐藏所有子分类按钮

				self:HideAllSubFrame()			--隐藏所有SubFrame

				self:ShowButton(v,"Item")		--显示属于本身的Item按钮

				local v2 = self:SearchButtonFirstVal(v,"Item")		--获取属于本身的第一个Item按钮的v2

				local Itemv = tonumber(v)*100+tonumber(v2)			--获取第一个Item按钮的v值
				
				local v3 = self:SearchButtonFirstVal(tostring(Itemv),"ItemSub")	--获取属于本身的第一个ItemSub按钮的v3

				self:ShowSubFramePage(v,v2,v3,1)					--显示第一页

			else
			
				TabClassButton[v]:UnlockHighlight() --取消高亮
			end
		end
	end
	
	--物品分类按钮()
	if Type == "ItemClassButton" then
	
		if IsIButtonHide == true then
		
			local ov1 = ""
			
			for k,v in pairs(ItemNum) do
				if Button == ItemClassButton[v] then
					ov1,_ = self:SplitMultipleNumber(v,"100")
					ItemClassButton[v]:UnlockHighlight()		--取消按钮高亮
					
					self:RestoreButtonPos(ItemClassButton[v],v,"Item")	--恢复ItemButton原有位置
					
					self:HideAllButton("ItemSub")				--隐藏所有所有ItemSubButton
				else
					
					--ItemClassButton[v]:Show()					--显示其它ItemButton
				end
			end
			
			--只显示同TabClass的按钮
			for k,v in pairs(ItemNum) do
				if ItemClassButton[v] ~= Button	then
					local nv1,_ = self:SplitMultipleNumber(v,"100")
					if nv1 == ov1 then
						ItemClassButton[v]:Show()
					end
				end
			end
			
			IsIButtonHide = false								--改变属性
		else
		
			for k,v in pairs(ItemNum) do
			
				if Button == ItemClassButton[v] then
					
					local v1,v2 = self:SplitMultipleNumber(v,"100")
					
					local v3 = self:SearchButtonFirstVal(v,"ItemSub")
					
					self:TopButtonPos(ItemClassButton[v],"Item")
					
					ItemClassButton[v]:LockHighlight()			--锁定按钮高亮
					
					self:HideAllButton("ItemSub")				--隐藏所有所有ItemSubButton

					self:ShowButton(v2,"ItemSub")				--显示属于本Button的ItemSubButton

					self:HideAllSubFrame()						--隐藏所有所有SubFrame
					
					--local ov1,ov2,ov3,page = self:GetPage()
					
					--self:HideSubFramePage(ov1,ov2,ov3,page)	--隐藏当前显示页面
					
					self:ShowSubFramePage(v1,v2,v3,1)			--显示新的页面
					
				else
				
					ItemClassButton[v]:UnlockHighlight()		--取消按钮高亮
					
					ItemClassButton[v]:Hide()					--隐藏其余所有ItemButton
				end
				
				IsIButtonHide = true							--改变属性
			end
		end	
	end


	--物品子分类按钮
	if Type == "ItemSubClassButton" then
		for k,v in pairs(ItemSubNum) do
			if Button == ItemSubClassButton[v] then
				
				--local ov1,ov2,ov3,page = self:GetPage()				--获取当前显示页面参数
				
				--self:HideSubFramePage(ov1,ov2,ov3,page)				--隐藏当前显示页面
				
				self:HideAllSubFrame()									--隐藏所有所有SubFrame
				
				local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")		--获取新页面类型参数
				
				ItemSubClassButton[v]:LockHighlight()					--锁定按钮高亮
				
				self:ShowSubFramePage(v1,v2,v3,1)						--显示新的页面
				
			else
			
				ItemSubClassButton[v]:UnlockHighlight()					--取消按钮高亮
			end	
		end
	end
	
	 
	if Type == "ItemSellButton" then
		for a = 1,#ItemId do
			local j = ItemId[a]
			if Button == ItemSellButton[j] then
				--发送购买的ItemId 给服务端
				SendAddonMessage("ADDON_SupermarketSellItemId",j,"GUILD", UnitName("player"))
			end
		end
	end
end


--隐藏SubFrame根据页数及传参
function Supermarket:HideSubFramePage(v1,v2,v3,P)
	local c = self:TableMax(SubFrame[v1][v2][v3])

	if c > 9 then
		c = 9
	end
	
	for i = 1 + ( c *(P - 1)), c * P do
		if SubFrame[v1][v2][v3][i] ~= nil then
		SubFrame[v1][v2][v3][i]:Hide()
		end
	end
	
end


--隐藏所有SubFrame
function Supermarket:HideAllSubFrame()

	for i = 1,#ItemId do
	
		local j		= ItemId[i]					
		local c1 	= TabClass[j]		
		local c2 	= ItemClass[j]		
		local c3 	= ItemSubClass[j]	
		local b		= ItemPos[j]
		
		SubFrame[c1][c2][c3][b]:Hide()
	end

end


--隐藏所有按钮依据指定类型
function Supermarket:HideAllButton(Class)
	if Class == "ItemSub" then
		for k,v in pairs(ItemSubNum) do
			ItemSubClassButton[v]:Hide()
			ItemSubClassButton[v]:UnlockHighlight()
		end
	end
	
	if Class == "Item" then
		for k,v in pairs(ItemNum) do
			ItemClassButton[v]:Hide()
			ItemClassButton[v]:UnlockHighlight()
			IsIButtonHide = false
		end
	end
end


--显示指定按钮 依据传值及类型
function Supermarket:ShowButton(Val,Class)

	if Class == "ItemSub" then
		for k,v in pairs(ItemSubNum) do							
			local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")
			if Val == v2 then
	
				ItemSubClassButton[v]:Show()
				
			end
		end
	end
	
	if Class == "Item" then
		for k,v in pairs(ItemNum) do
			local v1,v2 = self:SplitMultipleNumber(v,"100")	
			if Val == v1 then
				if ItemClassButton[v] ~= nil then
				ItemClassButton[v]:Show()
				
				else
				
				end
			end
		end
	end
	
	if Class == "Tab" then
		--留存
	end
	
end


function Supermarket:ShowPgaeVal(v1,v2,v3,page)

	local p = self:GetMaxPage(v1,v2,v3)	
	local str = ("第 "..page.." 页 ".."共 "..p.." 页")
	PageShowInfo:SetText(str)
end


--搜索按钮数组的第一个值
function Supermarket:SearchButtonFirstVal(Val,Class)
	
	if Class == "ItemSub" then
		local s1,s2 = self:SplitMultipleNumber(Val,"100")
		for k,v in pairs(ItemSubNum) do							
			local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")
			if s1 == v1 and s2 == v2 then
				return v3
			end
		end
	end
	
	if Class == "Item" then
		for k,v in pairs(ItemNum) do
			local v1,v2 = self:SplitMultipleNumber(v,"100")
			if Val == v1 then
				return v2
			end
		end
	end
	
	if Class == "Tab" then
		--留存
	end
	
	
end


--获取出售物品文字输出
function Supermarket:GetItemSellStr(name,tex,price)
	local str = ""
	if price == "0" or price == nil then
		--if name ~= "物品兑换" and name ~= "金币" and name ~= "积分" then
			--str = "价格：".."        *    "..price
		--end
	else
		if name ~= "物品兑换" then
		str = "价格："..name.." |T"..tex..":20|t"..price
		end
	end
	
	if name == "物品兑换" then
		str = "价格：".."        *    "..price
	end
	
	return str
	
end


--获取销售分类贴图
function Supermarket:GetTabClassTex(tabclass)
	if tabclass == "1" then
		return T["JiFen"]
	end
	
	if tabclass == "2" then
		return T["JinBi"]
	end
	
	if tabclass == "3" then
		return T["ItemExchange"]
	end
	
	if tabclass == "4" then
		local factionGroup = UnitFactionGroup("player") 
		if factionGroup == "Alliance" then
			return T["AlliancePVP"]
		else
			return T["HordePVP"]
		end
	end
	
	if tabclass == "5" then
		return T["ArenaPVP"]
	end
	
	if tabclass == "6" then
		return T["EXP"]
	end
	
end


--
function Supermarket:GetTabClassLittleTex(tabclass)
	if tabclass == "1" then
		return T["JiFen"]
	end
	
	if tabclass == "2" then
		return T["JinBi"]
	end
	
	if tabclass == "3" then
		return T["ItemExchange"]
	end
	
	if tabclass == "4" then
		local factionGroup = UnitFactionGroup("player") 
		if factionGroup == "Alliance" then
			return T["AlliancePVPPoint"]
		else
			return T["HordePVPPoint"]
		end
	end
	
	if tabclass == "5" then
		return T["ArenaPVPPoint"]
	end
	
	if tabclass == "6" then
		return T["XP"]
	end
end


--获取商城物品购买需求限制
function Supermarket:GetReqStr(req,val)
	if req == "VIP" then
	local str = ""
		if val == "0" or val == nil then
		
		else
		str = "需求:VIP |T"..T["Vip"]..":18|t"..val.."级"
		end
	return str
	end
end


--获取销售分类名称
function Supermarket:GetTabClassName(tabclass)
	if tabclass == "1" then
		return "积分"
	end
	if tabclass == "2" then
		return "金币"
	end
	if tabclass == "3" then
		return "物品兑换"
	end 
	if tabclass == "4" then
		return "荣誉点"
	end 
	if tabclass == "5" then
		return "竞技点"
	end 
	if tabclass == "6" then
		return "经验值"
	end 	
end


--获取物品一级类型名称
function Supermarket:GetItemOneTypeName(itemclass)
	if itemclass == "1" then
	return "英雄变身"
	elseif itemclass == "2" then
	return "技能"
	elseif itemclass == "3" then
	return "武器"
	elseif itemclass == "4" then
	return "防具"
	elseif itemclass == "5" then
	return "珠宝"
	elseif itemclass == "6" then
	return "消耗品"
	elseif itemclass == "7" then
	return "杂项"
	end
end


--获取物品二级类型名称
function Supermarket:GetItemTwoTypeName(itemclass,itemsubclass)
	if self:GetItemOneTypeName(itemclass) == "英雄变身" then
		if itemsubclass == "1" then
		return "近战英雄"
		elseif itemsubclass == "2" then
		return "法系英雄"
		elseif itemsubclass == "3" then
		return "远程英雄"
		elseif itemsubclass == "4" then
		return "其他英雄"
		end
	elseif self:GetItemOneTypeName(itemclass) == "技能" then	
		if itemsubclass == "1" then
		return "英雄技能"
		elseif itemsubclass == "2" then
		return "宠物技能"
		elseif itemsubclass == "3" then
		return "通用技能"
		elseif itemsubclass == "4" then
		return "其他技能"
		end
	elseif self:GetItemOneTypeName(itemclass) == "武器" then	
		if itemsubclass == "1" then
		return "近战武器"
		elseif itemsubclass == "2" then
		return "远程武器"
		elseif itemsubclass == "3" then
		return "法术武器"
		elseif itemsubclass == "4" then
		return "其他武器"
		end
	elseif self:GetItemOneTypeName(itemclass) == "防具" then
		if itemsubclass == "1" then
		return "布甲"
		elseif itemsubclass == "2" then
		return "皮甲"
		elseif itemsubclass == "3" then
		return "锁甲"
		elseif itemsubclass == "4" then
		return "板甲"
		elseif itemsubclass == "5" then
		return "首饰"
		elseif itemsubclass == "6" then
		return "盾牌"
		elseif itemsubclass == "7" then
		return "其他防具"
		end
	elseif self:GetItemOneTypeName(itemclass) == "珠宝" then
		if itemsubclass == "1" then
		return "1级"
		elseif itemsubclass == "2" then
		return "2级"
		elseif itemsubclass == "3" then
		return "3级"
		elseif itemsubclass == "4" then
		return "4级"
		elseif itemsubclass == "5" then
		return "5级"
		elseif itemsubclass == "6" then
		return "6级"
		elseif itemsubclass == "7" then
		return "7级"
		elseif itemsubclass == "8" then
		return "8级"
		elseif itemsubclass == "9" then
		return "9级"
		elseif itemsubclass == "10" then
		return "10级"
		end 
	elseif self:GetItemOneTypeName(itemclass) == "消耗品" then
		if itemsubclass == "1" then
		return "药剂"
		elseif itemsubclass == "2" then
		return "卷轴"
		elseif itemsubclass == "3" then
		return "附魔"
		elseif itemsubclass == "4" then
		return "其他消耗品"
		end
	elseif self:GetItemOneTypeName(itemclass) == "杂项" then	
		if itemsubclass == "1" then
		return "草药"
		elseif itemsubclass == "2" then
		return "矿石"
		elseif itemsubclass == "3" then
		return "小宠物"
		elseif itemsubclass == "4" then
		return "坐骑"
		elseif itemsubclass == "5" then
		return "其他杂项"
		end
	end
end


--拆分Key或者value的值,以100或者1000为倍数
function Supermarket:SplitMultipleNumber(msg,m)

	if m == "100" then

		local a,b = math.modf((tonumber(msg)/100))	
		b = b*100
		if b < 1 then	--预防未知错误
			b = 1
		end
		
		local b1,b2 = math.modf(b)
		
		if b2 >0.5 then
			b = math.ceil(b)	--预防未知错误
		else
			b = math.floor(b)	--预防未知错误
		end

		return tostring(a),tostring(b)	---先输出上级,再输出下级

	end
	
	if m == "1000" then
		
		local a,a1 = math.modf((tonumber(msg)/1000))
		a1 = a1 * 10
		
		local b,c = math.modf(a1)
		c = c*100
		if c < 1 then 		--预防未知错误
			c = 1
		end
		local c1,c2 = math.modf(c)
		if c2 > 0.5 then
			c = math.ceil(c)	--预防未知错误
		else
			c = math.floor(c)	--预防未知错误
		end
		
		return tostring(a),tostring(b),tostring(c)
		
	end
end


--保存设置需要显示使用的类型页面
function Supermarket:SetPage(v1,v2,v3,Page)
	m_V1,m_V2,m_V3,m_Page = v1,v2,v3,Page
end


--获取当前所在类型的页面
function Supermarket:GetPage()
	return m_V1,m_V2,m_V3,m_Page
end


--置顶按钮位置
function Supermarket:TopButtonPos(button,class)
		
		if class == "Item" then
			button:SetPoint("TOPLEFT",10,-68)
		end
end


--恢复单个ItemButton原有位置
function Supermarket:RestoreButtonPos(button,v,class)
		
		if class == "Item" then
			local str = ItemGPS[v]
			local pos = Split(str,",")
			button:SetPoint("TOPLEFT",tonumber(pos[1]),tonumber(pos[2]))
		end
end


--恢复所有ItemButton原有位置
function Supermarket:RestoreAllButtonPos()
		
	for k,v in pairs(ItemNum) do
		local str = ItemGPS[v]
		local pos = Split(str,",")
		ItemClassButton[v]:SetPoint("TOPLEFT",tonumber(pos[1]),tonumber(pos[2]))
	end
		
end


--获取ItemSub最大页面
function Supermarket:GetMaxPage(v1,v2,v3)
	local count = self:TableMax(SubFrame[v1][v2][v3])	

	local page = 0
	if count / 9  < 1 then	--如果SubFrame数<9 那么页面数只有1页
		page = page + 1
	else					--如果其>9 那么页面数 = 商(取整) count%9~=0 + 1
		page = page + math.floor(count/9)
		if count%9 ~= 0 then
			page = page + 1
		end
	end

	return page
	
end


--递增页面
function Supermarket:IncreasePage(Page)

		Page = Page + 1
		
		return Page
end


--递减页面
function Supermarket:DecreasePage(Page)
		
		Page = Page - 1
		
		return Page
end	


--定义ItemSub按钮排版自上而下的序号
function Supermarket:DefineItemSubPos()

	for k,v in pairs(ItemNum) do 
		local b = 1		--初始化b值
		local v1,v2 = self:SplitMultipleNumber(v,"100")					--拆分每个ItemNum的v值
		for key,val in pairs(ItemSubNum) do								--遍历ItemSubNum
			val1,val2,val3 = self:SplitMultipleNumber(val,"1000")		--拆分每一个遍历ItemSubNum的v值
			if v1 == val1 and val2 == v2 then							--如果拆分的val2 等于 v2 时
				ItemSubPos[val] = b										--记录它的位置
				b = b +1												--递增b
			end 
		end
	end
	
end


--定义ItemSub按钮排版自上而下的序号
function Supermarket:DefineItemPosition()

	for k,v in pairs(TabNum) do 
		local b = 1		--初始化b值
		for key,val in pairs(ItemNum) do						--遍历ItemSub
			local v1,v2 = self:SplitMultipleNumber(val,"100")	--拆分每个ItemNum的v值
			if v == v1 then										--如果拆分的v等于v1时
				ItemPosition[val] = b							--记录它的位置
				b = b +1										--递增b
			end 
		end
	end
	
end


--获取Table的Key
function Supermarket:GetTableKey(array,object)
	for k,v in pairs(array) do
		if v == object then
			return k
		end
	end
end


--根据参数定义多维数组函数
function Supermarket:DefinitionSubFrame(tabclass,itemcalss,itemsubclass)
	
	if SubFrame[tabclass] == nil then
		SubFrame[tabclass] = {}
	end
	if SubFrame[tabclass][itemcalss] == nil then
		SubFrame[tabclass][itemcalss] = {}
	end
	if SubFrame[tabclass][itemcalss][itemsubclass] == nil then
		SubFrame[tabclass][itemcalss][itemsubclass] = {}
	end
end


--计算Table总共有多少个元素
function Supermarket:CountTable(Table)
	local count = 0
	if type(Table) == "table"then 
		for i,val in pairs(Table) do
			count = count + 1
		end
	end
	return count
end


--颜色
function Supermarket:ColorPicker(r,g,b)
	local red,green,blue
	red = r/255
	green = g/255
	blue = b/255
	return red,green,blue
end


--数组最大值
function Supermarket:TableMax(t)
	
	local _max = nil
	for k,v in pairs(t) do
		if _max == nil then

			_max = k
		end
		if _max < k then

			_max = k
		end
	end

	return _max
end


--初始化页面并显示 (适用于商城按钮)
function Supermarket:InitPage()

	local v = ItemSubNum[1]				--获取第一个子分类v值
	
	local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")	--拆分v值

	self:ShowSubFramePage(v1,v2,v3,1)	--显示商城首页

	self:SetPage(v1,v2,v3,1)			--保存类型页面

end


--检查是否接收到数据
function Supermarket:IsId()
	if ItemId[1] == nil then
		return false
	else
		return true
	end
end


--显示SubFrame根据页数及传参
function Supermarket:ShowSubFramePage(v1,v2,v3,Page)
	
	local p = self:GetMaxPage(v1,v2,v3)
	
	local c = self:TableMax(SubFrame[v1][v2][v3])
	
	if c > 9 then							--不允许超过9个
		c = 9
	end

	if Page > p then
		Page = p							--不可超过最大页数
	end
	
	if Page <= 0 then						--不可低于最小页数
		Page = 1
	end
	
	for i = 1 + (c * (Page - 1)), c * Page do
		if SubFrame[v1][v2][v3][i] ~= nil then
			SubFrame[v1][v2][v3][i]:Show()		--显示SubFrame
		end
	end
	
	self:ShowPgaeVal(v1,v2,v3,Page)
	
	self:SetPage(v1,v2,v3,Page)				--保存类型页面

end


--展示创建商城全部阵列框架 (细节待优化，BUG待修复)
function Supermarket:ShowSubFrame()	

	--定义多维数组
	for k,v in pairs(ItemSubNum) do
		local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")	--拆分v值
		self:DefinitionSubFrame(v1,v2,v3)
	end

	local c,d = 1,1

	for a = 1,#ItemId do

		local j		= ItemId[a]					--这一步很重要
		local c1 	= TabClass[j]		
		local c2 	= ItemClass[j]		
		local c3 	= ItemSubClass[j]	
		local b 	= 0
		
		--以下是一个循环
		--print("b = "..b)
		
		
		
		if j == "50248" then
		--print("c1 & c2 & c3 & b = "..c1.." & "..c2.." & "..c3.." & "..b.." ItemId = "..j)
		end
		
		repeat		
		b = b +1
		until(SubFrame[c1][c2][c3][b] == nil)--这里只有是空的才创建
		
		if b == 1 then			--如果b = 1 那么证明这个子分类才开始创建
			if c > 1 then
				c =1			--重置行数
			end
			if d > 1 then
				d = 1 			--重置列数
			end
		end
		
		if c > 3 then	--限制行数以及计算坐标 横向
			c = 1
		end
		
		if d > 3 then	--限制列数以及计算坐标 纵向
			d = 1
		end
			
		--创建阵列
		local _name = tostring(c1..c2..c3..b)
		SubFrame[c1][c2][c3][b] = self:CreateSubFrame("Frame","SubFrame".._name,SupermarketFrame,nil,290,220,"TOPLEFT",c-1,d-1,T["SubFrameBackground"],"",true)
		ItemPos[j]  = b		--存储每个item所在类型的位置
		--print("c1 & c2 & c3 & b = "..c1.." & "..c2.." & "..c3.." & "..b.." ItemId = "..j)
		b = 0	--重新计算
		
		if c == 3 then	--进位递增计算
			d = d +1
		end
		
		c = c +1		--递增计算
		
		
	end	
	
end


--展示创建商城SubFrame中的元素
function Supermarket:ShowSubFrameElement()

	for a = 1,#ItemId do
		local j = ItemId[a]
		local c1 = TabClass[j]
		local c2 = ItemClass[j]
		local c3 = ItemSubClass[j]
		local b = ItemPos[j]
		local req = ItemReq[j]
		local reqval = ItemReqVal[j]
		local reqitemid,reqiremval,reqitemlink,S;
		
		
		
		
		--展示创建商城全部物品信息 
		ItemInfoButton[j] = self:CreateButton("Button","ItemInfoButton"..j,SubFrame[c1][c2][c3][b],nil,80,80,"|T"..GetItemIcon(tonumber(j))..":58|t","TOPLEFT",20,-20,"","")
		
		--展示创建商城全部物品名称
		ItemName[j] = self:CreateFontString(SubFrame[c1][c2][c3][b],"ItemName"..j,"",T["Font"],18,"TOP",36.5,-40,ItemLink[j],"")
		
		--展示创建商城物品购买按钮
		ItemSellButton[j] = self:CreateButton("Button","ItemSellButton"..j,SubFrame[c1][c2][c3][b],"UIPanelButtonTemplate2",80,40,"购买","Bottom",0,15,"","")
		
		
		--展示创建商城全部物品贩售价格
		
		--if c1 == "3" or c1 == "4"or c1 == "5"or c1 == "6"then  --QQ2283219390的要求
		
		if c1 == "3" then
			reqitemid = ReqItem[j]
			reqiremval = ReqItemVal[j]
			reqitemlink = ReqItemLink[j]
			ReqItemButton[j] = self:CreateButton("Button","ReqItemButton"..j,SubFrame[c1][c2][c3][b],nil,30,30,"|T"..GetItemIcon(tonumber(reqitemid))..":22|t","Center",8,-12,"","")
			S = self:GetItemSellStr(self:GetTabClassName(c1),self:GetTabClassLittleTex(c1),reqiremval)
		else
			S = self:GetItemSellStr(self:GetTabClassName(c1),self:GetTabClassLittleTex(c1),ItemPrice[j])
		end
		
		
		--("售价："..self:GetTabClassName(c1).." |T"..self:GetTabClassTex(c1)..":20|t"..ItemPrice[j])
		
		ItemSellInfo[j] = self:CreateFontString(SubFrame[c1][c2][c3][b],"ItemSellInfo"..j,"",T["Font"],16,"Center",0,-12,S,"238,250,5")
		
		local R = self:GetReqStr(req,reqval)

		ReqVipInfo[j] = self:CreateFontString(SubFrame[c1][c2][c3][b],"ReqVipInfo"..j,"",T["Font"],16,"Center",0,-40,R,"255,165,0")
		
		--展示创建商城全部物品边框
		
		--展示创建商城全部物品贩售描述文字底框(不好看)
		--FontBg[j] = self:CreateSubFrame("Frame","FontBg"..j,SubFrame[c1][c2][c3][b],nil,285,62,"Center",0,-20,T["FontBackground"],"",false)
	end

	--初始化SubFrame按钮脚本
	self:InitSubFrameButtons()
end


--展示创建TabClass的窗框
function Supermarket:ShowTapFrame()
	for k,v in pairs(TabNum) do
		TabFrame[v] = self:CreateSubFrame("Frame","TabFrame"..v,SupermarketFrameR,nil,100,120,"TOPRIGHT",86,-40-(100-12)*(k-1),T["TabClassFrameBackground"],"",false)
	end
end


--展示创建所有分类按钮
function Supermarket:ShowClassButton()
	--local a,b = 1,2
	
	--创建销售分类按钮
	for k,v in pairs(TabNum) do
		TabClassButton[v] = self:CreateButton("Button","TabClassButton"..v,TabFrame[v],nil,80,76,"","CENTER",0,0,self:GetTabClassTex(v),"")	
	end
	
	--定义Item按钮排版自上而下的序号
	self:DefineItemPosition()
	
	--创建物品分类按钮(创建后隐藏)  这里的K要注意了 要和下面的一样再做个Pos定义
	for k,v in pairs(ItemNum) do
		local v1,v2 = self:SplitMultipleNumber(v,"100") 
		ItemClassButton[v] = self:CreateButton("Button","ItemClassButton"..v,SupermarketFrameR,nil,242,32,self:GetItemOneTypeName(v2),"TOPLEFT",10,-68-(28*(ItemPosition[v]-1)),T["ItemClassButton"],"",true,true)
		--这里的k 需要像下面一样改成一个 ItemPos[v]
		local str = (tostring(10)..","..tostring((-68-(28)*(ItemPosition[v]-1))))

		ItemGPS[v] = str
	end
	
	--定义ItemSub按钮排版自上而下的序号,k = ItemSubNum is v
	self:DefineItemSubPos()
	
	--创建物品子分类按钮(创建后隐藏)
	for k,v in pairs(ItemSubNum) do
		local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")
		local val = tostring(tonumber(v1)*100+tonumber(v2))
		ItemSubClassButton[v] = self:CreateButton("Button","ItemSubClassButton"..v,ItemClassButton[val],nil,242,32,self:GetItemTwoTypeName(v2,v3),"Bottom",0,-28-(28*(ItemSubPos[v]-1)),T["ItemClassButton"],"",true)
	end
	
	--初始化分类按钮脚本
	self:InitClassButtons()
end


--展示创建其它按钮
function Supermarket:ShowOtherButton()

	--SupermarketButton,HomeButton,UpButton,DownButton,CloseButton
	SupermarketButton = self:CreateButton("Button","SupermarketButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketButton"],"")
	
	HomeButton = self:CreateButton("Button","HomeButton",SupermarketFrame,"UIPanelButtonTemplate2",80,40,"首页","BottomLeft",100,25,"","")
	
	UpButton = self:CreateButton("Button","UpButton",SupermarketFrame,"UIPanelButtonTemplate2",80,40,"上一页","Bottom",-90,25,"","")
	
	DownButton = self:CreateButton("Button","DownButton",SupermarketFrame,"UIPanelButtonTemplate2",80,40,"下一页","BottomRight",-120,25,"","")
	
	CloseButton = self:CreateButton("Button","CloseButton",SupermarketFrame,"UIPanelCloseButton",32,32,"","TOPRIGHT",240,-10,"","")
	
	SupermarketOffButton = self:CreateButton("Button","SupermarketOffButton",UIParent,nil,64,64,"","BottomRight",0,0,T["SupermarketOffButton"],"",true)
	
	--初始化其它按钮脚本
	self:InitOtherButtons()
	
end


--初始化其它按钮脚本
function Supermarket:InitOtherButtons()
	
	--商城按钮
	 self:PrepareScript(SupermarketButton,	"打开商城",		function()	Supermarket:HandleButtonScript(SupermarketButton,"SupermarketButton") end)
	 
	 --关闭商城按钮
	 self:PrepareScript(SupermarketOffButton,	"关闭商城",		function()	Supermarket:HandleButtonScript(SupermarketOffButton,"SupermarketOffButton") end)
	 
	
	--首页按钮
	 self:PrepareScript(HomeButton,	nil,		function()	Supermarket:HandleButtonScript(HomeButton,"HomeButton") end)
	 
	--上一页按钮 
	 self:PrepareScript(UpButton,	nil,		function()	Supermarket:HandleButtonScript(UpButton,"UpButton") end)
	
	--下一页按钮
	 self:PrepareScript(DownButton,	nil,		function()	Supermarket:HandleButtonScript(DownButton,"DownButton") end)
	 
	 --叉叉按钮
	 self:PrepareScript(CloseButton,	nil,		function()	Supermarket:HandleButtonScript(CloseButton,"CloseButton") end)
	
end


--初始化分类按钮
function Supermarket:InitClassButtons()
	
	--销售分类按钮
	for k,v in pairs(TabNum) do
		self:PrepareScript(TabClassButton[v],	Supermarket:GetTabClassName(v),	function()	Supermarket:HandleButtonScript(TabClassButton[v],"TabClassButton") end)
	end
	
	--物品分类按钮
	for k,v in pairs(ItemNum) do
		local v1,v2 = self:SplitMultipleNumber(v,"100") 
		self:PrepareScript(ItemClassButton[v],	nil,	function()	Supermarket:HandleButtonScript(ItemClassButton[v],"ItemClassButton") end)
	end
	
	--物品子分类按钮
	for k,v in pairs(ItemSubNum) do
		local v1,v2,v3 = self:SplitMultipleNumber(v,"1000")
		--local val = tostring(tonumber(v1)*100+tonumber(v2))
		self:PrepareScript(ItemSubClassButton[v],	nil,	function()	Supermarket:HandleButtonScript(ItemSubClassButton[v],"ItemSubClassButton") end)
	end
	
end


--初始化SubFrame按钮脚本
function Supermarket:InitSubFrameButtons()

	for a = 1,#ItemId do
		
		local j = ItemId[a]
		local c1 = TabClass[j]
		
		--物品展示按钮
		self:PrepareScript(ItemInfoButton[j],	nil,	nil,	ItemLink[j])

		--购买按钮
		self:PrepareScript(ItemSellButton[j],	nil,	function() Supermarket:HandleButtonScript(ItemSellButton[j],"ItemSellButton") end)
		
		if c1 == "3" then 
		local link = ReqItemLink[j]
			self:PrepareScript(ReqItemButton[j],	nil,	nil,	link)
		end

	end

end


--初始化商城
function Supermarket:OnInitialize()
	
	--IsIButtonHide:是否其它ItemButton都隐藏了 false = No true = Yes
	IsIButtonHide = false
	
	--SubFrame & ClassButton & TabFrame 只创建一次。所以,初始化的时候是没有创建的，按下商城按钮后开始创建
	IsCreate = false
	
	--初始化商城主框
	SupermarketFrame = self:CreateParentFrame("Frame","SupermarketFrame",UIParent,nil,950,730,"CENTER",0,0,T["ParentFrameBackgroundLeft"],"")
	
	--初始化商城主框右
	SupermarketFrameR = self:CreateParentFrame("Frame","SupermarketFrameR",SupermarketFrame,nil,270,760,"TOPRIGHT",250,30,T["ParentFrameBackgroundRight"],"")
	
	--初始化商城标题
	SupermarketTitle = self:CreateParentFrame("Frame","SupermarketTitle",SupermarketFrame,nil,500,130,"TOP",0,90,T["ParentFrameTitle"],"")
	
	--页面显示信息
	PageShowInfo = self:CreateFontString(SupermarketFrame,"PageShowInfo","",T["Font"],16,"Bottom",115,35,"","255,165,0")
	
	--初始化其它按钮
	self:ShowOtherButton()
	
end


--清空数组函数
function ClearGoods(inArray)

--不能单纯的用这个清理所有数组 因为key不一样
    --for i=1,#inArray do
       -- print("=======ClearGoods======" .. i)
	for k,v in pairs(inArray) do
        if inArray[k] ~= nil then
            --inArray[k]:removeFromParent();
			table.remove(inArray,k)
        end
    end
    inArray = nil;
    inArray = {};
end


function ClearAllData()
	ClearGoods(Supermarket)
	ClearGoods(ItemId)
	ClearGoods(ItemLink)
	ClearGoods(TabClass)
	ClearGoods(ItemClass)
	ClearGoods(ItemSubClass)
	ClearGoods(ItemPrice)
	ClearGoods(ItemReq)
	ClearGoods(ItemReqVal)
	ClearGoods(ReqItem)
	ClearGoods(ReqItemVal)
	ClearGoods(ReqItemLink)
	ClearGoods(TabNum)
	ClearGoods(ItemNum)
	ClearGoods(ItemSubNum)
	ClearGoods(SubFrame)
	ClearGoods(ItemSellInfo)
	ClearGoods(ItemName)
	ClearGoods(ReqVipInfo)
	ClearGoods(FontBg)
	ClearGoods(ItemSubPos)
	ClearGoods(ItemGPS)
	ClearGoods(ItemPosition)
	ClearGoods(ItemInfoButton)
	ClearGoods(ItemSellButton)
	ClearGoods(TabClassButton)
	ClearGoods(ItemClassButton)
	ClearGoods(ItemSubClassButton)
	ClearGoods(ReqItemButton)
	ClearGoods(TabFrame)
	ClearGoods(ItemPos)
	ClearGoods(PageElement)
end


Supermarket:OnInitialize()
--print(UnitFactionGroup("player"))

local MsgReceiver = CreateFrame("Frame")
MsgReceiver:RegisterEvent("CHAT_MSG_ADDON")
MsgReceiver:SetScript("OnEvent", Event)



--[[
function send()
	SendAddonMessage("ADDON_SupermarketData","All","GUILD", UnitName("player"))
end

local MsgReceiver1 = CreateFrame("Frame")
MsgReceiver1:RegisterEvent("PLAYER_ENTERING_WORLD")
MsgReceiver1:SetScript("OnEvent", send)
]]--